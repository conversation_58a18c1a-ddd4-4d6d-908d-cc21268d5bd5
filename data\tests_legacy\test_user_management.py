from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from django.contrib.auth import get_user_model

from data.models import UserManagement, Workspace, Verification

User = get_user_model()

class TestViewOnlyUserPassword(TestCase):
    def setUp(self):
        self.client = Client()
        
        # Create admin user and workspace
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='adminpass123',
            first_name='Admin',
            is_staff=True
        )
        
        # Create regular user
        self.regular_user = User.objects.create_user(
            username='regular',
            email='<EMAIL>',
            password='regularpass123',
            first_name='Regular'
        )
        
        self.workspace = Workspace.objects.create(name='Test Workspace')
        self.workspace.user.add(self.admin_user, self.regular_user)
        
        # Create admin user management
        self.admin_management = UserManagement.objects.create(
            user=self.admin_user,
            workspace=self.workspace,
            type=UserManagement.RoleType.ADMIN
        )
        
        # Create regular user management (non-admin)
        self.regular_management = UserManagement.objects.create(
            user=self.regular_user,
            workspace=self.workspace,
            type=UserManagement.RoleType.STAFF
        )
        
        # Create verification for both users
        Verification.objects.create(
            user=self.admin_user,
            workspace=self.workspace,
            verified=True
        )
        Verification.objects.create(
            user=self.regular_user,
            workspace=self.workspace,
            verified=True
        )
        
        # Login as admin by default for tests that need it
        self.client.login(username='admin', password='adminpass123')
    
    def test_admin_can_create_view_only_user(self):
        """Test admin can create a view-only user with password"""
        # Create view-only user via the view
        response = self.client.post(reverse('user_management_manage'), {
            'create_view_only_user': '1',
            'email': '<EMAIL>',
            'name': 'NewViewOnly',
            'view_only_password': 'secure123',
        }, follow=True)
        
        # Check if the user was created
        self.assertEqual(response.status_code, 200)
        
        # Check if success message was added
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('successfully created' in str(message) for message in messages))
        
        # Check if user was created with correct type
        try:
            user = User.objects.get(email='<EMAIL>')
            user_management = UserManagement.objects.get(user=user, workspace=self.workspace)
            self.assertEqual(user_management.type, UserManagement.RoleType.VIEW_ONLY)
        except (User.DoesNotExist, UserManagement.DoesNotExist):
            self.fail("View-only user was not created")
    
    def test_non_admin_cannot_create_view_only_user(self):
        """Test non-admin cannot create view-only users"""
        # Login as regular user
        self.client.logout()
        self.client.login(username='regular', password='regularpass123')
        
        # Try to create view-only user
        response = self.client.post(reverse('user_management_manage'), {
            'create_view_only_user': '1',
            'email': '<EMAIL>',
            'name': 'Unauthorized',
            'view_only_password': 'test123',
        }, follow=True)
        
        # Should be redirected or get permission denied
        self.assertIn(response.status_code, [302, 403])
        
        # Verify user was not created
        self.assertFalse(User.objects.filter(email='<EMAIL>').exists())
    
    def test_admin_can_update_view_only_password(self):
        """Test admin can update view-only user's password"""
        # Create a view-only user first
        view_only_user = User.objects.create_user(
            username='viewonly',
            email='<EMAIL>',
            password='oldpassword',
            first_name='ViewOnly'
        )
        user_management = UserManagement.objects.create(
            user=view_only_user,
            workspace=self.workspace,
            type=UserManagement.RoleType.VIEW_ONLY
        )
        user_management.set_view_only_password('oldpassword')
        user_management.save()
        
        # Update password as admin
        response = self.client.post(reverse('user_management_manage'), {
            'update_user_management': '1',
            'user_id': view_only_user.id,
            'view_only_password': 'newsecure123',
        }, follow=True)
        
        # Verify success
        self.assertEqual(response.status_code, 200)
        
        # Refresh from DB and verify password was updated
        user_management.refresh_from_db()
        view_only_user.refresh_from_db()
        
        self.assertTrue(view_only_user.check_password('newsecure123'))
        self.assertEqual(user_management.get_view_only_password(), 'newsecure123')
    
    def test_non_admin_cannot_update_view_only_password(self):
        """Test non-admin cannot update view-only user's password"""
        # Create a view-only user first
        view_only_user = User.objects.create_user(
            username='viewonly',
            email='<EMAIL>',
            password='oldpassword',
            first_name='ViewOnly'
        )
        user_management = UserManagement.objects.create(
            user=view_only_user,
            workspace=self.workspace,
            type=UserManagement.RoleType.VIEW_ONLY
        )
        
        # Login as regular user
        self.client.logout()
        self.client.login(username='regular', password='regularpass123')
        
        # Try to update password
        response = self.client.post(reverse('user_management_manage'), {
            'update_user_management': '1',
            'user_id': view_only_user.id,
            'view_only_password': 'hacked123',
        }, follow=True)
        
        # Should be denied
        self.assertIn(response.status_code, [302, 403])
        
        # Verify password was not changed
        view_only_user.refresh_from_db()
        user_management.refresh_from_db()
        self.assertTrue(view_only_user.check_password('oldpassword'))
        self.assertNotEqual(user_management.get_view_only_password(), 'hacked123')
    
    def test_view_only_password_encryption(self):
        """Test that view-only passwords are properly encrypted"""
        # Create a view-only user
        view_only_user = User.objects.create_user(
            username='encrypt_test',
            email='<EMAIL>',
            password='testpass123',
            first_name='EncryptTest'
        )
        user_management = UserManagement.objects.create(
            user=view_only_user,
            workspace=self.workspace,
            type=UserManagement.RoleType.VIEW_ONLY
        )
        
        # Set and get password
        test_password = 'secure_password_123!'
        user_management.set_view_only_password(test_password)
        user_management.save()
        
        # Verify the stored password is encrypted
        self.assertNotEqual(user_management.view_only_password, test_password)
        self.assertIsNotNone(user_management.view_only_password)
        
        # Verify decryption works
        self.assertEqual(user_management.get_view_only_password(), test_password)
    
    def test_login_with_view_only_password(self):
        """Test that a view-only user can log in with their password"""
        # Create a view-only user
        view_only_user = User.objects.create_user(
            username='viewonly2',
            email='<EMAIL>',
            password='viewonlypass',
            first_name='View Only 2'
        )
        self.workspace.user.add(view_only_user)
        
        user_management = UserManagement.objects.create(
            user=view_only_user,
            workspace=self.workspace,
            type=UserManagement.RoleType.VIEW_ONLY
        )
        user_management.set_view_only_password('viewonlypass')
        user_management.save()
        
        # Test login with the client
        logged_in = self.client.login(username='viewonly2', password='viewonlypass')
        self.assertTrue(logged_in, "View-only user should be able to log in with their password")
        
    def tearDown(self):
        """Clean up test data"""
        # Delete all users except the test users we created in setUp
        User.objects.exclude(
            username__in=['admin', 'regular', 'viewonly', 'encrypt_test', 'viewonly2']
        ).delete()
        
        # Clear all workspaces
        Workspace.objects.all().delete()
        
        # Clear all user management records
        UserManagement.objects.all().delete()
        
        # Clear all verifications
        Verification.objects.all().delete()
        
        # Log out the test client
        self.client.logout()
