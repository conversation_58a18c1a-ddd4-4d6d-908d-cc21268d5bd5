import ast
from datetime import timedelta
import traceback

from asgiref.sync import sync_to_async
from hatchet_sdk import Context

from data.models import TransferHistory, User
from utils.bgjobs.handler import (
    aio_set_bg_job_completed,
    aio_set_bg_job_failed,
    aio_set_bg_job_running,
)
from utils.logger import logger
from utils.nextengine import import_nextengine_orders as nextengine_import_util

from ..models import ImportOrdersNextEnginePayload
from ..workflows import import_nextengine_orders


class MockRequest:
    """Mock request object for NextEngine utility function"""

    def __init__(self, user, lang="ja"):
        self.user = user
        self.LANGUAGE_CODE = lang
        self.lang = lang


@import_nextengine_orders.task(
    name="ImportNextEngineOrdersTask",
    execution_timeout=timedelta(hours=5),
    schedule_timeout=timedelta(hours=1),
)
async def import_nextengine_orders_task(
    input: ImportOrdersNextEnginePayload, ctx: Context
) -> dict:
    """
    Child task for importing NextEngine orders
    """
    logger.info("Run NextEngine orders import child task")
    logger.info(f"Input: {input}")

    history_id = input.history_id
    task = None

    try:
        task = await TransferHistory.objects.select_related("workspace", "user").aget(
            id=history_id
        )
        task.keep_alive = True
        await task.asave()
    except TransferHistory.DoesNotExist:
        logger.warning("TransferHistory does not exist")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])
        return

    if input.background_job_id:
        await aio_set_bg_job_running(input.background_job_id)

    try:
        # Get user object for request reconstruction
        user = await User.objects.aget(id=input.user_id)

        # Create mock request object (NextEngine utility requires request context)
        request = MockRequest(user=user, lang="ja")

        # Process mapping fields
        mapping_custom_fields = (
            input.mapping_custom_fields
            if input.mapping_custom_fields and input.mapping_custom_fields != "None"
            else "{}"
        )

        if isinstance(mapping_custom_fields, str):
            try:
                mapping_custom_fields = ast.literal_eval(mapping_custom_fields)
            except (ValueError, SyntaxError):
                logger.warning(
                    f"Failed to parse mapping_custom_fields: {mapping_custom_fields}"
                )
                mapping_custom_fields = {}

        # Call the NextEngine import utility
        result = await sync_to_async(nextengine_import_util)(
            input.channel_id, request, mapping_custom_fields
        )

        if result is not False:
            logger.info("Successfully imported NextEngine orders")
            if task:
                await task.arefresh_from_db()
                task.status = "completed"
                await task.asave(update_fields=["status"])

            if input.background_job_id:
                await aio_set_bg_job_completed(input.background_job_id)
        else:
            logger.error("NextEngine import returned False")
            if task:
                await task.arefresh_from_db()
                task.status = "failed"
                await task.asave(update_fields=["status"])

            if input.background_job_id:
                await aio_set_bg_job_failed(input.background_job_id)

    except User.DoesNotExist:
        logger.error(f"User {input.user_id} does not exist")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])

    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error importing Shopify orders: {str(e)}")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])
