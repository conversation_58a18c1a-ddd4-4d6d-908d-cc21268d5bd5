{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

<div class="border-0 card shadow-none rounded-0 bg-white">
    <div class="card-header" id="kt_help_header">
        <h5 class="{% include "data/utility/card-header.html" %}">
            {% if association_label %}
                {% if LANGUAGE_CODE == 'ja'%}
                    アソシエーションの管理
                {% else %}
                    Manage Association
                {% endif %}
                {% if association_count > 0 %}
                    <span class="badge badge-light-primary ms-2">{{ association_count }} 
                        {% if LANGUAGE_CODE == 'ja'%}
                            件の関連付け
                        {% else %}
                            associations
                        {% endif %}
                    </span>
                {% endif %}
            {% else %}
                {% if LANGUAGE_CODE == 'ja'%}
                    アソシエーションを作成する
                {% else %}
                    Create Association
                {% endif %}
            {% endif %}
        </h5>
        <div class="card-toolbar">
            <button type="button" class="btn btn-sm btn-icon explore-btn-dismiss me-n5" data-kt-drawer-dismiss="true">
                <span class="svg-icon svg-icon-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="black"></rect>
                        <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="black"></rect>
                    </svg>
                </span>
            </button>
        </div>
    </div>

    <div class="card-body">
        <form method="POST" action="{% host_url 'manage_association_label' host 'app' %}">
            {% csrf_token %}

            {% if association_label %}
            <input hidden name="association_label_id" value="{{association_label.id}}">
            {% endif %}
            <input hidden name="object_source" value="{{object_source}}">

            <!-- A pair of labels section -->
            {% comment %} <p class="text-muted mb-3" style="font-size: 14px;">
                {% if LANGUAGE_CODE == 'ja'%}
                内部名
                {% else %}
                Internal Name
                {% endif %}
                : {{association_label.id}}
            </p> {% endcomment %}
            {% if association_label.created_by_sanka %}
                <div class="mb-4">
                    
                    <span class="fs-5 fw-bolder text-active-primary ms-0 mb-3 py-0 border-0">
                        {% if LANGUAGE_CODE == 'ja'%}
                        ラベル
                        {% else %}
                        Label
                        {% endif %}
                    </span>
                    {% comment %} <p class="text-muted mb-3" style="font-size: 14px;">
                        {% if LANGUAGE_CODE == 'ja'%}
                        オブジェクトは異なるラベルで関連づけられています。
                        {% else %}
                        The objects are associated with different labels.
                        {% endif %}
                    </p> {% endcomment %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-semibold">
                                FROM:
                                {{association_label.object_source|page_group_to_object_singular:request.LANGUAGE_CODE}}

                            </label>
                            <div class="sanka-label-container">
                                <input class="form-control sanka-input" type="text" name="source_object" disabled
                                    value="{{association_label.label|page_group_to_object_singular:request.LANGUAGE_CODE}}"
                                />
                            </div>                        
                        </div>

                        {% for object_target in association_label.object_target|split:"," %}
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-semibold">
                                    FROM:
                                    {{object_target|page_group_to_object_singular:request.LANGUAGE_CODE}}
                                </label>

                                {% get_association_label_on_target_object association_label object_target as target_label %}
                                <div class="sanka-label-container">
                                    <input class="form-control sanka-input" type="text" name="target_ohject" disabled
                                        value="{{target_label|page_group_to_object_singular:request.LANGUAGE_CODE}}"
                                    />
                                </div>
                            </div>

                        {% endfor %}

                        
                        
                    </div>
                </div>
            {% else %}

                <!-- Dynamic Label Pairs Section -->
                <div class="mb-5" id="dynamic-label-pairs">
                    <span class="fs-5 fw-bolder text-active-primary ms-0 mb-3 py-0 border-0">
                        {% if LANGUAGE_CODE == 'ja'%}
                        ラベル
                        {% else %}
                        Label
                        {% endif %}
                    </span>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-semibold">
                                FROM: {{object_source|page_group_to_object_singular:request.LANGUAGE_CODE}}
                            </label>
                            <div class="sanka-label-container">
                                <input class="form-control sanka-input" type="text" name="label" 
                                    placeholder="{% if LANGUAGE_CODE == 'ja' %}ソースラベル{% else %}Source Label{% endif %}"
                                    {% if association_label %}value="{{association_label.label}}"{% endif %}
                                />
                            </div>                        
                        </div>
                        
                        <div class="col-md-6 mb-3" id="target-labels-container">
                            <!-- Target labels rendered directly from label_pair data -->
                            {% if association_label.label_pair %}
                                {% for target_value, target_label in association_label.label_pair.items %}
                                    <div class="mb-3">
                                        <label class="form-label fw-semibold">
                                            FROM: {{target_value|page_group_to_object_singular:request.LANGUAGE_CODE}}
                                        </label>
                                        <div class="sanka-label-container">
                                            <input class="form-control sanka-input" type="text" 
                                                name="target_label_{{target_value}}" 
                                                placeholder="{% if LANGUAGE_CODE == 'ja' %}ターゲットラベル{% else %}Target Label{% endif %}"
                                                data-target="{{target_value}}"
                                                value="{{target_label}}"
                                            />
                                        </div>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endif %}

            <div class="mb-5 {% if property.id == 'counter_category' or property.id == 'category'%}d-none{% endif%}">
                <span class="fs-5 fw-bolder text-active-primary ms-0 mb-3 py-0 border-0">
                    {% if LANGUAGE_CODE == 'ja'%}
                    関連オブジェクト
                    {% else %}
                    Association objects
                    {% endif %}
                </span>

                <select name="object_target"
                    {% if association_label.created_by_sanka %}disabled{% endif %}
                    class="h-40px form-select border select2-this {% if association_label.created_by_sanka %}bg-gray-200{% endif %}"
                    data-control="select2" multiple
                    hx-get="{% host_url 'load_target_labels' host 'app' %}"
                    hx-trigger="htmx-change"
                    hx-swap="innerHTML"
                    hx-target="#target-labels-container"
                    hx-include="[name='object_source'], [name='association_label_id']">

                    {% for object_target in ASSOCIATION_OBJECT_TARGETS %}
                        <option value="{{object_target}}"
                            {% if object_target|to_str|in_list:association_label.object_target %}
                                selected
                            {% endif %}
                            >

                            {% if LANGUAGE_CODE == 'ja' %}
                                {{object_source|page_group_to_object_singular:request.LANGUAGE_CODE}}から{{object_target|page_group_to_object_singular:request.LANGUAGE_CODE}}
                            {% else %}
                                {{object_source|page_group_to_object_singular:request.LANGUAGE_CODE}}-to-{{object_target|page_group_to_object_singular:request.LANGUAGE_CODE}}
                            {% endif %}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="mb-3 project_selector {% if constant.TYPE_OBJECT_TASK not in association_label.object_target or not association_label %}d-none{% endif %}">
                <label class="fs-5 fw-bold mb-2">
                    <span class=""> 
                        {% if LANGUAGE_CODE == 'ja'%}
                        プロジェクトID
                        {% else %}
                        Project ID
                        {% endif %}
                    </span>
                </label>
        
                <select
                    class="h-40px form-select form-select-solid border select2-this"
                    id="project-selector-{{association_label.id}}"
                    data-placeholder="{% if LANGUAGE_CODE == 'ja'%}プロジェクトを選択{% else %}Select Project{% endif %}"
                    name="project_target"
                    >
                    {% with projects=request|get_project_objects %}
                        {% for project in projects %}
                            <option value="{{ project.id }}" {% if association_label.project_target == project.id|to_str or association_label.created_by_sanka and project.default %} selected {% endif %}>
                                {{project.title}}
                            </option>
                        {% endfor %}
                    {% endwith %}
                </select>
            </div>

            <div class="mb-5 d-flex fs-6">
                <input class="form-check-input cursor-pointer" type="checkbox" id="one-to-one-association-type" name="one_to_one_association_type"
                {% if association_label.association_type == "one_to_one" %}checked{% endif %}
                {% if association_label.created_by_sanka %}disabled{% endif %}>
                <label for="one-to-one-association-type" class="ms-4 mb-3 cursor-pointer">{% if LANGUAGE_CODE == 'ja' %}関連するレコードを一つに限定する{% else %}Only one record to be associated{% endif %}
                </label>
            </div>


            {% if association_label.label == constant.TYPE_OBJECT_INVENTORY or association_label.label == constant.TYPE_OBJECT_INVENTORY_TRANSACTION %}
                <div class="mb-4">
                    <span>
                        {% if language_code == 'ja' %}
                            アソシエーション在庫は一つの商品に限定されます。
                        {% else %}
                            Association inventory is limited to a single item.
                        {% endif %}
                    </span>
                </div>
            {% endif %}

            {% if not association_label.created_by_sanka %}
            <div class="d-flex">
                <button id='property_update' type="submit" class="btn btn-dark me-3">
                {% if association_label %}
                    {% if LANGUAGE_CODE == 'ja'%}
                    更新
                    {% else %}
                    Update
                    {% endif %}
                {% else %}
                    {% if LANGUAGE_CODE == 'ja'%}
                    作成
                    {% else %}
                    Create
                    {% endif %}
                {% endif %}
                </button>

                {% comment %} Delete button {% endcomment %}
                {% if association_label %}
                    <button name="delete-association-label" type="submit" class="btn btn-danger" 
                        {% if association_count > 0 %}
                            disabled                  
                        {% endif %}
                        >
                        {% if LANGUAGE_CODE == 'ja'%}
                        削除
                        {% else %}
                        Delete
                        {% endif %}
                    </button>
                {% endif %}
            
            </div>
            {% endif %}
            
        </form>

        {% if association_count > 0 %}
        <div class="mt-4">
            <h6>
                {% if LANGUAGE_CODE == 'ja'%}
                アソシエーションメンバー ({{association_count}}件)
                {% else %}
                Association Members ({{association_count}})
                {% endif %}
            </h6>
            
            <div hx-get="{% host_url 'get_association_label_members' host 'app' %}" 
                hx-vals='{"association_label_id": "{{association_label.id}}", "object_source": "{{object_source}}", "per_page": "10", "page": "1" }'
                hx-target="#association-label-members-content"
                hx-trigger="load"
                hx-indicator="#loading-members"
            >
                <div id="loading-members" class="text-center py-3">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span class="ms-2">Loading association members...</span>
                </div>
                <div id="association-label-members-content"></div>
            </div>
        </div>
        {% endif %}

    </div>

        

    </div>
</div>

<script>
    // Initialize Select2 with HTMX integration
    $('.select2-this').each(function() {
        try { $(this).select2('destroy'); } catch(e) {}
        $(this).select2().on('select2:select select2:unselect', function (e) {
            var selectElement = $(this).closest('select').get(0);
            selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
        });
        
        // HTMX will only be used for dynamic changes, not initial load
    });
    
    // Also initialize project selector with Select2
    $('select[name="project_target"]').each(function() {
        try { $(this).select2('destroy'); } catch(e) {}
        $(this).select2();
    });

    // Function to toggle project selector visibility based on object_target selection
    function toggleProjectSelector() {
        const objectTargetSelect = document.querySelector('select[name="object_target"]');
        const projectSelector = document.querySelector('.project_selector');
        
        if (objectTargetSelect && projectSelector) {
            // Get selected values (for multiple select)
            const selectedValues = Array.from(objectTargetSelect.selectedOptions).map(option => option.value);
            
            // Check if TYPE_OBJECT_TASK is selected
            const taskTypeSelected = selectedValues.includes('{{constant.TYPE_OBJECT_TASK}}');
            
            if (taskTypeSelected) {
                projectSelector.classList.remove('d-none');
            } else {
                projectSelector.classList.add('d-none');
            }
        }
    }

    // Function to toggle dynamic label pairs based on object_target selection
    {% comment %} function toggleDynamicLabelPairs() {
        const objectTargetSelect = document.querySelector('select[name="object_target"]');
        const dynamicLabelPairs = document.getElementById('dynamic-label-pairs');
        const regularLabel = document.querySelector('input[name="label"]').closest('.mb-5');
        
        if (objectTargetSelect && dynamicLabelPairs) {
            // Get selected values (for multiple select)
            const selectedValues = Array.from(objectTargetSelect.selectedOptions).map(option => option.value);
            
            // Check if this is an existing association with label pairs
            const hasExistingLabelPairs = {% if association_label and association_label.label_pair %}true{% else %}false{% endif %};
            
            // Show dynamic mode if there are current selections OR existing label pairs
            const shouldShowDynamic = selectedValues.length > 0 || hasExistingLabelPairs;
            
            console.log('toggleDynamicLabelPairs:', {
                selectedValues: selectedValues,
                hasExistingLabelPairs: hasExistingLabelPairs,
                shouldShowDynamic: shouldShowDynamic
            });
            
            if (shouldShowDynamic) {
                // Show dynamic label pairs and hide regular label input
                dynamicLabelPairs.style.display = 'block';
                if (regularLabel) regularLabel.style.display = 'none';
                
                // Source label and main label are now the same input
            } else {
                // Hide dynamic label pairs and show regular label input
                dynamicLabelPairs.style.display = 'none';
                if (regularLabel) regularLabel.style.display = 'block';
            }
        }
    } {% endcomment %}

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        toggleProjectSelector();
        //toggleDynamicLabelPairs();
        
        // HTMX load is now handled in Select2 initialization above
    });

    // Listen for Select2 changes
    $('select[name="object_target"]').on('select2:select select2:unselect', function() {
        toggleProjectSelector();
        //toggleDynamicLabelPairs();
        
        console.log('Object target changed:', $(this).val());
    });

    function check_property(elm){
        {% if existing_custom_property %}
            var existing_custom_property = {{existing_custom_property|safe}}
            if (existing_custom_property.includes(elm.value.trim())) {
                // Code to execute if the value is in the array
                var existing_name = document.getElementById('existing-error')
                existing_name.classList.remove('d-none')

                var property_update = document.getElementById('property_update')
                if (property_update){property_update.disabled = true;}
            } else {
                // Code to execute if the value is not in the array
                var existing_name = document.getElementById('existing-error')
                existing_name.classList.add('d-none')
                var property_update = document.getElementById('property_update')
                if (property_update){property_update.disabled = false;}
            }
        {% endif %}
    }
</script>

