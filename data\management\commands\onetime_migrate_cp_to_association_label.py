from data.models import Workspace,TYPE_OBJECT_ORDER,TYPE_OBJECT_DELIVERY_NOTE,TYPE_OBJECT_ITEM,TYPE_OBJECT_BILL,TYPE_OBJECT_ESTIMATE,TYPE_OBJECT_INVENTORY,TYPE_OBJECT_CASE,TYPE_OBJECT_INVENTORY_TRANSACTION,TYPE_OBJECT_INVENTORY_WAREHOUSE,TYPE_OBJECT_CONTACT,TYPE_OBJECT_COMPANY,TYPE_OBJECT_PURCHASE_ORDER,TYPE_OBJECT_SUBSCRIPTION,TYPE_OBJECT_USER_MANAGEMENT,TYPE_OBJECT_BILL,TYPE_OBJECT_INVOICE,TYPE_OBJECT_RECEIPT,TYPE_OBJECT_TASK,AssociationLabel,AssociationLabelObject,ShopTurboOrdersName<PERSON>ustomField
from django.core.management.base import BaseCommand
from data.accounts.association_labels import auto_create_necessary_association_label_setting
from utils.properties.properties import get_page_object

mapping_cf_type_to_association_label = {
    "contact":TYPE_OBJECT_CONTACT,
    "company":TYPE_OBJECT_COMPANY,
    "customer":TYPE_OBJECT_COMPANY,
    "purchase_order":TYPE_OBJECT_PURCHASE_ORDER,
    "subscription":TYPE_OBJECT_SUBSCRIPTION,
    "user_management":TYPE_OBJECT_USER_MANAGEMENT,
    "bill_objects":TYPE_OBJECT_BILL,
    "invoice_objects":TYPE_OBJECT_INVOICE,
    "order_objects":TYPE_OBJECT_ORDER,
    "task":TYPE_OBJECT_TASK,
    "warehouse_objects":TYPE_OBJECT_INVENTORY_WAREHOUSE
}

class Command(BaseCommand):
    help = 'Migrate custom properties to association labels for specified object types.'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--object-types',
            nargs='+',
            choices=['order', 'case', 'contact','company','inventory_transaction','inventory','subscription','estimate','invoice','payment','purchase_order','item', 'bill','delivery_note','all'],
            default=['all'],
            help='Object types to process (default: all). Options: order, case, contact, company, subscription, inventory_transaction,inventory,estimate,invoice,payment,purchase_order,item,bill,delivery_note all',
        )
        parser.add_argument(
            '--workspace',
            type=str,
            help='Process only specific workspace by name (optional)',
        )

    def handle(self, *args, **options):
        object_types = options['object_types']
        workspace_name = options['workspace']
        
        # Map object type strings to constants
        object_type_mapping = {
            'order': TYPE_OBJECT_ORDER,
            'case': TYPE_OBJECT_CASE,
            'contact': TYPE_OBJECT_CONTACT,
            'company': TYPE_OBJECT_COMPANY,
            'inventory_transaction': TYPE_OBJECT_INVENTORY_TRANSACTION,
            'inventory': TYPE_OBJECT_INVENTORY,
            'subscription': TYPE_OBJECT_SUBSCRIPTION,
            'estimate': TYPE_OBJECT_ESTIMATE,
            'invoice': TYPE_OBJECT_INVOICE,
            'payment': TYPE_OBJECT_RECEIPT,
            'purchase_order': TYPE_OBJECT_PURCHASE_ORDER,
            'item': TYPE_OBJECT_ITEM,
            'bill': TYPE_OBJECT_BILL,
            'delivery_note': TYPE_OBJECT_DELIVERY_NOTE
        }
        
        # Determine which object types to process
        if 'all' in object_types:
            types_to_process = [TYPE_OBJECT_ORDER, TYPE_OBJECT_CASE, TYPE_OBJECT_CONTACT,TYPE_OBJECT_COMPANY,TYPE_OBJECT_INVENTORY_TRANSACTION,TYPE_OBJECT_INVENTORY,TYPE_OBJECT_SUBSCRIPTION,TYPE_OBJECT_ESTIMATE,TYPE_OBJECT_INVOICE,TYPE_OBJECT_RECEIPT,TYPE_OBJECT_PURCHASE_ORDER,TYPE_OBJECT_ITEM,TYPE_OBJECT_BILL,TYPE_OBJECT_DELIVERY_NOTE]
            type_names = ['order', 'case', 'contact','company','inventory_transaction','inventory','subscription','estimate','invoice','payment','purchase_order','item','bill','delivery_note']
        else:
            types_to_process = [object_type_mapping[obj_type] for obj_type in object_types]
            type_names = object_types
        
        print("Starting migrate custom property to association label script...")
        print("="*60)
        print(f"Object types: {', '.join(type_names)}")
        
        # Filter workspaces if specific workspace is requested
        workspaces = Workspace.objects.all()
        if workspace_name:
            workspaces = workspaces.filter(name__icontains=workspace_name)
            if not workspaces.exists():
                print(f"No workspace found matching '{workspace_name}'")
                return
        
        print(f"Found {workspaces.count()} workspace(s) to process")
        
        for workspace in workspaces:
            print(f"\n🏢 Processing workspace: {workspace.name}")
            print("-" * 40)
            
            #loop for object
            for object_type in types_to_process:
                print(f"   📋 Processing object: {object_type}")
                page_obj = get_page_object(object_type, 'en')
                # Get custom fields with detailed logging
                custom_fields_name = page_obj['custom_model'].objects.filter(
                    workspace=workspace, 
                    type__in=["contact",
                              "company",
                              "customer",
                              "purchase_order",
                              "subscription",
                              "user_management",
                              "bill_objects",
                              "invoice_objects",
                              "order_objects",
                              "task",
                              "warehouse_objects"]
                )
                
                print(f"📋 Found {custom_fields_name.count()} custom fields to migrate")
                
                if custom_fields_name.count() == 0:
                    print("⚠️  No custom fields found for this workspace")
                    continue
                    
                for idx, custom_field in enumerate(custom_fields_name, 1):
                    print(f"\n📝 Processing custom field {idx}/{custom_fields_name.count()}")
                    print(f"   Name: {custom_field.name}")
                    print(f"   Type: {custom_field.type}")
                    print(f"   ID: {custom_field.id}")
                    
                    try:
                        # Create association label
                        print(f"   🔄 Creating association label...")
                        Association_Label, created = AssociationLabel.objects.get_or_create(
                            workspace=workspace,
                            label=custom_field.name,
                            object_source=object_type,
                            object_target=mapping_cf_type_to_association_label[custom_field.type],
                            association_type='one_to_one',
                            created_by_sanka=False,
                            defaults={'label_ja': custom_field.name}  # Set label_ja for new records
                        )
                        
                        if created:
                            print(f"   ✅ Association label created: {Association_Label.label}")
                        else:
                            print(f"   ♻️  Association label already exists: {Association_Label.label}")
                        
                        # Migrate record data
                        related_name = page_obj['custom_value_model']._meta.model_name + "_set"
                        print(f"   📊 Looking for related values using: {related_name}")

                        custom_field_values = getattr(custom_field, related_name, []).all()
                        print(f"   📊 Found {custom_field_values.count()} {object_type} values to migrate")
                        
                        migrated_count = 0
                        error_count = 0
                        
                        for _value_cf in custom_field_values:
                            try:
                                print(f"      🔄 Processing {object_type} value ID: {_value_cf.id}")
                                
                                page_object = get_page_object(mapping_cf_type_to_association_label[_value_cf.field_name.type], 'en')
                                base_model = page_object['base_model']
                                
                                print(f"      📖 Looking for {base_model.__name__} with ID: {_value_cf.value}")
                                
                                custom_field_relation =  getattr(_value_cf, page_obj['custom_value_relation'], []) 
                                if _value_cf.value:
                                    # Handle multiple UUIDs separated by semicolons
                                    value_ids = _value_cf.value.split(';') if ';' in _value_cf.value else [_value_cf.value]
                                    
                                    for value_id in value_ids:
                                        value_id = value_id.strip()  # Remove any whitespace
                                        if not value_id:
                                            continue
                                            
                                        print(f"      📖 Looking for {base_model.__name__} with ID: {value_id}")
                                        
                                        try:
                                            object_target = base_model.objects.filter(id=value_id).first()
                                            if object_target:
                                                print(f"      ✅ Found target object: {object_target}")
                                                AssociationLabelObject.create_association(
                                                    custom_field_relation,
                                                    object_target, 
                                                    workspace, 
                                                    Association_Label
                                                )
                                                migrated_count += 1
                                                print(f"      ✅ Association created successfully")
                                            else:
                                                print(f"      ⚠️  Target object not found for ID: {value_id}")
                                                error_count += 1
                                        except Exception as e:
                                            print(f"      ⚠️  Invalid UUID format for ID: {value_id} - {str(e)}")
                                            error_count += 1
                                    
                                    if len(value_ids) > 1:
                                        print(f"      📊 Processed {len(value_ids)} UUIDs from multi-value field")
                                else:
                                    print(f"      ⚠️  No value found for custom field")
                                    error_count += 1
                                    
                            except Exception as e:
                                print(f"      ❌ Error processing {object_type} value {_value_cf.id}: {str(e)}")
                                error_count += 1
                        
                        print(f"   📊 Migration summary for '{custom_field.name}':")
                        print(f"      ✅ Successfully migrated: {migrated_count}")
                        print(f"      ❌ Errors/Skipped: {error_count}")
                        print(f"      📈 Total processed: {custom_field_values.count()}")
                        
                    except Exception as e:
                        print(f"   ❌ Error processing custom field '{custom_field.name}': {str(e)}")
                        continue
                
                print(f"\n🏁 Completed processing workspace: {workspace.name}")
                print("-" * 40)
        
        print("\n" + "="*60)
        print("🎉 MIGRATION COMPLETED!")
        print(f"✅ Processed {workspaces.count()} workspace(s)")
        print("="*60)



            