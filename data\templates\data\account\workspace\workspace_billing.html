{% load i18n %}
{% load custom_tags %}
{% load hosts %}
{% load humanize %}
{% load tz %}

<div class="">
	<div class="d-flex align-items-center justify-content-between h-80px w-100 p-10 py-8 position-sticky" style="background-color: #FAFAFA !important;">
		<div class="tw-text-title-header-object  tw-leading-[150%] tw-text-[#8F8E93] tw-mr-1 fw-bolder fs-3 mt-0 text-dark tw-text-ellipsis tw-overflow-hidden">
            {% if LANGUAGE_CODE == 'ja'%}
            プラン & 請求管理
            {% else %}
            Plan & Billing
            {% endif %}
        </div>
    </div>
    
    <div class="px-lg-10 px-0">
    {% if permission == 'hide' %}
        {% include 'data/static/no-access-page.html' %}
    {% else %}
    <div class="mb-5">
        <div class="row pb-5 gx-10">
            <div class="col-8">
                <div class="border-bottom-1 border-bottom py-5">
                    <h3 class="fs-3 fw-bolder text-dark card-label fw-bolder text-dark">
                        {% if LANGUAGE_CODE == 'ja' %}
                        プラン概要
                        {% else %}
                        Plan Overview
                        {% endif %}
    
                    </h3>
                    <div class="fs-5 fw-bold text-dark card-label text-dark">
                        {% if LANGUAGE_CODE == 'ja' %}
                        プラン
                        {% else %}
                        Plan
                        {% endif %}
                        :
                        {% if workspace.subscription == "free" %}
                            {% if LANGUAGE_CODE == 'ja' %}
                            スターター
                            {% else %}
                            Starter
                            {% endif %}
                        {% elif workspace.subscription == "standard" %}
                            {% if LANGUAGE_CODE == 'ja' %}
                            スタンダード
                            {% else %}
                            Standard
                            {% endif %}
                        {% else %}
                            {% if LANGUAGE_CODE == 'ja' %}
                            エンタープライズ
                            {% else %}
                            Enterprise
                            {% endif %}

                        {% endif %}
                    </div>
    
                    <div class="fs-5 fw-bold text-dark card-label text-dark">
                        {% if LANGUAGE_CODE == 'ja' %}
                        ステータス: {% translate_lang workspace.subscription_status|title LANGUAGE_CODE %} 
                        {% else %}
                        Status: {% translate_lang workspace.subscription_status|title LANGUAGE_CODE %} 
                        {% endif %}
                    </div>
    
                    <div class="fs-5 fw-bold text-dark card-label text-dark">
                    {% if LANGUAGE_CODE == 'ja' %}請求通貨{% else %}Billing Currency{% endif %}: 
                    {% if workspace.payment_currency %}
                        {{workspace.payment_currency|get_currencies_text_symbol}}
                    {% elif workspace.country_code == 'JP' %}
                        {% with currency_='JPY' %}
                            {{currency_|get_currencies_text_symbol}}
                        {% endwith %}
                    {% else %}
                        {% with currency_='USD' %}
                            {{currency_|get_currencies_text_symbol}}
                        {% endwith %}
                    {% endif %}
                    </div>
                    

                    <div class="fs-5 fw-bold text-dark card-label text-dark">
                        {% if LANGUAGE_CODE == 'ja' %}請求頻度{% else %}Billing Frequency{% endif %}: 

                        {% if workspace.subscription_frequency == 'monthly' %}
                            {% if LANGUAGE_CODE == 'ja' %}毎月{% else %}Monthly{% endif %}
                        {% elif workspace.subscription_frequency == 'quarterly' %}
                            {% if LANGUAGE_CODE == 'ja' %}四半期ごと{% else %}Quarterly{% endif %}
                        {% elif workspace.subscription_frequency == 'annually' %}
                            {% if LANGUAGE_CODE == 'ja' %}年ごと{% else %}Annually{% endif %}
                        {% else %}
                            {% if LANGUAGE_CODE == 'ja' %}毎月{% else %}Monthly{% endif %}
                        {% endif %}
                            

                        {% if workspace.subscription_period != None %}
                            {% if LANGUAGE_CODE == 'ja' %}毎{% else %}Every{% endif %}

                            {{workspace.subscription_period}} 
                        {% endif %}

                        {% if workspace.subscription_frequency == 'monthly' %}
                            {% if LANGUAGE_CODE == 'ja' %}ヶ月{% else %}Month{% endif %}
                        {% elif workspace.subscription_frequency == 'quarterly' %}
                            {% if LANGUAGE_CODE == 'ja' %}四半期{% else %}Quarter{% endif %}
                        {% elif workspace.subscription_frequency == 'annually' %}
                            {% if LANGUAGE_CODE == 'ja' %}年{% else %}Year{% endif %}
                        {% endif %}
                    </div>
                    
                    <div class="fs-5 fw-bold text-dark card-label text-dark">
                        {% if LANGUAGE_CODE == 'ja' %}請求費用{% else %}Billing Amount{% endif %}: 

                            {% if workspace.subscription == 'partner' %}
                                {% if LANGUAGE_CODE == 'ja' %}請求書{% else %}Invoice{% endif %}
                            {% else %}
                                {% if workspace.payment_currency %}
                                    {{workspace.payment_currency|get_currency_symbol}} 
                                {% endif %}

                                {% if subscription_plan.cost %}
                                    {% if workspace.payment_currency|lower == "usd" %}
                                        {{subscription_plan.cost|floatformat:"2g"|intcomma:False}}
                                    {% else %}
                                        {{subscription_plan.cost|floatformat:"0g"|intcomma:False}}
                                    {% endif %}
                                {% else %}
                                0
                                {% endif %}
                            {% endif %}
                    </div>

                    {% if workspace.subscription != 'partner' %}
                    <div class="fs-5 fw-bold text-dark card-label text-dark">
                        {% if LANGUAGE_CODE == 'ja' %}次回の更新{% else %}Next Renewal{% endif %}: 
                        {% if workspace.payment_currency %}
                            {{workspace.payment_currency|get_currency_symbol}} 
                        {% endif %}
                        {% if subscription_plan.cost %}
                            {% if workspace.payment_currency|lower == "usd" %}
                                {{subscription_plan.cost|floatformat:"2g"}}
                            {% else %}
                                {{subscription_plan.cost|floatformat:"0g"|intcomma:False}}
                            {% endif %}
                        {% else %}
                        0
                        {% endif %}

                        {% if next_renewal_date %}
                            {% if LANGUAGE_CODE == 'ja' %}
                            / {% local_time workspace.subscription_period_ended workspace.timezone '%m月%d日' %}
                            {% else %}
                            / {% local_time workspace.subscription_period_ended workspace.timezone '%d %B' %}
                            {% endif %}
                        {% endif %}
                    </div>
                    {% endif %}
                    
                    <div class="mt-5">    
                        {% if workspace.subscription != 'partner' and user_role == 'admin' %}
                            {% if workspace.subscription_status == 'active' or workspace.subscription_status == 'overdue' %} 
                                    <button class="btn bg-gray-100" id="change-subscription-btn" hx-get="{% host_url 'workspace_billing_change_plan' host 'app' %}"
                                        hx-target="#change-subscription-content">
                                        {% if LANGUAGE_CODE == 'ja' %}
                                        サブスクリプションの管理
                                        {% else %}
                                        Manage Subscriptions
                                        {% endif %}
                                    </button>
                            {% else %}
                                    <button class="btn btn-dark" data-toggle="modal" data-target="#start-subscriptions">
                                        {% if LANGUAGE_CODE == 'ja' %}
                                        サブスクリプションの管理
                                        {% else %}
                                        Manage Subscriptions
                                        {% endif %}
                                    </button>
                            {% endif %}
                        {% endif %}
                    </div>
                    

                </div>


                <div class="mt-5">
                    <h3 class=" fs-3 fw-bolder text-dark card-label fw-bolder text-dark">
                        {% if LANGUAGE_CODE == 'ja' %}
                        利用状況
                        {% else %}
                        Usage
                        {% endif %}

                    </h3>

                    <div class="fs-6 my-4">
                        {% for base_pricing in base_pricings %}
                        <div class="text-dark card-label text-dark">
                            
                                {% if LANGUAGE_CODE == 'ja' %}
                                {{base_pricing.title_ja}}:
                                {% else %}
                                {{base_pricing.title}}:
                                {% endif %}
                                
                                {% if base_pricing.tier == workspace.subscription or base_pricing.category == 'users' %}
                                    <span class="{% if not workspace|workspace_has_quota:base_pricing.category %}fw-bolder text-danger{% endif %}">{{workspace|get_workspace_usage:base_pricing.category|floatformat:"g"|intcomma:False}}/{{workspace|get_storage_limit:base_pricing.category|floatformat:"g"|intcomma:False}}</span>
                                {% else %}
                                    <span>{{workspace|get_workspace_usage:base_pricing.category|floatformat:"g"|intcomma:False}}</span>
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>
                </div>

                <div class="mt-5">             
                </div>

                {% comment %} Modal {% endcomment %}
                <div class="modal fade" id="pay-invoice-subscriptions" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered" role="document">
                        <div class="modal-content">
                            <div class="subcription-activation">
                             
                                <form action="{% host_url 'workspace_manual_billing' host 'app'  %}" method="post">
                                    {% csrf_token %}
                                    <div class="modal-header d-flex align-items-center">
                                
                                        <h5 class="modal-title" id="exampleModalLongTitle">
                                            {% if LANGUAGE_CODE == 'ja' %}
                                            支払い確認 (請求書の生成)
                                            {% else %}
                                            Payment Confirmations (Generate Invoice) 
                                            {% endif %}
                                        </h5>
                                        <button type="button" class="btn btn-sm btn-icon me-n5" class="close" data-dismiss="modal" aria-label="Close">
                                            <span class="svg-icon svg-icon-2">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                    <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="black"></rect>
                                                    <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="black"></rect>
                                                </svg>
                                            </span>
                                        </button>
                                    </div>

                                    <div class="modal-body">
                                        <p class="mb-0 items-center">
                                        {% if LANGUAGE_CODE == 'ja' %}
                                        サブスクリプション料を支払いますか?請求書を作成させていただきます。通知ボックスをチェックしてください
                                        {% else %}
                                        Are you sure to make a payment the subscription? We will create invoice for you. Please check the notification.
                                        {% endif %}
                                        </p>
                                    </div>
                                
                                    <input type="hidden" name="pay">
                                
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-white border" data-dismiss="modal">
                                            {% if LANGUAGE_CODE == 'ja' %}
                                            戻る
                                            {% else %}
                                            Back
                                            {% endif %}
                                        </button>
                                        <button type="submit" class="btn btn-dark">
                                            {% if LANGUAGE_CODE == 'ja' %}
                                            支払う
                                            {% else %}
                                            Pay
                                            {% endif %}
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div> 

                <div class="modal fade" id="payment-method-unaivailable" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered" role="document">
                        <div class="modal-content">
                            <div class="subcription-activation">
                             
                                    <div class="modal-header d-flex align-items-center">
                                
                                        <h5 class="modal-title" id="exampleModalLongTitle">
                                            {% if LANGUAGE_CODE == 'ja' %}
                                            利用できない支払い方法
                                            {% else %}
                                            Payment Method Unavailable
                                            {% endif %}
                                        </h5>
                                        <button type="button" class="btn btn-sm btn-icon me-n5" class="close" data-dismiss="modal" aria-label="Close">
                                            <span class="svg-icon svg-icon-2">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                    <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="black"></rect>
                                                    <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="black"></rect>
                                                </svg>
                                            </span>
                                        </button>
                                    </div>

                                    <div class="modal-body">
                                        <p class="mb-0 items-center">
                                        {% if LANGUAGE_CODE == 'ja' %}
                                        クレジット カードを添付/再添付してください。
                                        {% else %}
                                        Please, Attach/Reattach your credit card!
                                        {% endif %}
                                        </p>
                                    </div>
                          
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-white border" data-dismiss="modal">
                                            {% if LANGUAGE_CODE == 'ja' %}
                                            戻る
                                            {% else %}
                                            Back
                                            {% endif %}
                                        </button>
                                    </div>
                                
                            </div>
                        </div>
                    </div>
                </div> 

                <div class="modal fade" id="pay-subscriptions" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered" role="document">
                        <div class="modal-content">
                            <div class="subcription-activation">
                             
                                <form action="{% host_url 'workspace_manual_billing' host 'app'  %}" method="post">
                                    {% csrf_token %}
                                    <div class="modal-header d-flex align-items-center">
                                
                                        <h5 class="modal-title" id="exampleModalLongTitle">
                                            {% if LANGUAGE_CODE == 'ja' %}
                                            支払い確認
                                            {% else %}
                                            Payment Confirmations
                                            {% endif %}
                                        </h5>
                                        <button type="button" class="btn btn-sm btn-icon me-n5" class="close" data-dismiss="modal" aria-label="Close">
                                            <span class="svg-icon svg-icon-2">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                    <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="black"></rect>
                                                    <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="black"></rect>
                                                </svg>
                                            </span>
                                        </button>
                                    </div>

                                    <div class="modal-body">
                                        <p class="mb-0 items-center">
                                        {% if LANGUAGE_CODE == 'ja' %}
                                        サブスクリプション料金を支払いますか?ご登録されているクレジットカードに直接請求されます。
                                        {% else %}
                                        Are you sure to make a payment the subscription? This will charge your Credit Card Directly.
                                        {% endif %}
                                        </p>
                                    </div>
                                
                                    <input type="hidden" name="pay">
                                
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-white border" data-dismiss="modal">
                                            {% if LANGUAGE_CODE == 'ja' %}
                                            戻る
                                            {% else %}
                                            Back
                                            {% endif %}
                                        </button>
                                        <button type="submit" class="btn btn-dark">
                                            {% if LANGUAGE_CODE == 'ja' %}
                                            支払う
                                            {% else %}
                                            Pay
                                            {% endif %}
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div> 

                <div class="modal fade" id="cancel-subscriptions" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered" role="document">
                        <div class="modal-content">
                            <div class="subcription-activation">
                             
                                <form action="{% host_url 'workspace_subscription' host 'app'  %}" method="post">
                                    {% csrf_token %}
                                    <div class="modal-header d-flex align-items-center">
                                
                                        <h5 class="modal-title" id="exampleModalLongTitle">
                                            {% if LANGUAGE_CODE == 'ja' %}
                                            サブスクリプションの停止
                                            {% else %}
                                            Cancel Confirmations
                                            {% endif %}
                                        </h5>
                                        <button type="button" class="btn btn-sm btn-icon me-n5" class="close" data-dismiss="modal" aria-label="Close">
                                            <span class="svg-icon svg-icon-2">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                    <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="black"></rect>
                                                    <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="black"></rect>
                                                </svg>
                                            </span>
                                        </button>
                                    </div>

                                    <div class="modal-body">
                                        <p class="mb-0 items-center">
                                        {% if LANGUAGE_CODE == 'ja' %}
                                        サブスクリプションを停止してもよろしいでしょうか？サブスクリプションが終了すると、Sankaのアプリケーションはご利用できなくなります。
                                         {% else %}
                                        Are you sure to cancel the subscription? Your applications will be blocked after your Subscription has ended.
                                        {% endif %}
                                        </p>
                                    </div>

                                    <input type="hidden" name="cancel">
                                
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-white border" data-dismiss="modal">
                                            {% if LANGUAGE_CODE == 'ja' %}
                                            戻る
                                            {% else %}
                                            Back
                                            {% endif %}
                                        </button>
                                        <button type="submit" class="btn btn-danger">
                                            {% if LANGUAGE_CODE == 'ja' %}
                                            サブスクリプションを停止する
                                            {% else %}
                                            Cancel Subscription
                                            {% endif %}
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div> 

                <div class="modal fade" id="start-subscriptions" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered" role="document">
                        <div class="modal-content">
                            <div class="subcription-activation">
                             
                                <form action="{% host_url 'workspace_subscription' host 'app'  %}" method="post">
                                    {% csrf_token %}
                                    <div class="modal-header d-flex align-items-center">
                                
                                        <h5 class="modal-title" id="exampleModalLongTitle">
                                            {% if LANGUAGE_CODE == 'ja' %}
                                            サブスクリプションの再開
                                            {% else %}
                                            Reactivte Subscription
                                            {% endif %}
                                        </h5>
                                        <button type="button" class="btn btn-sm btn-icon me-n5" class="close" data-dismiss="modal" aria-label="Close">
                                            <span class="svg-icon svg-icon-2">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                    <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="black"></rect>
                                                    <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="black"></rect>
                                                </svg>
                                            </span>
                                        </button>
                                    </div>


                                    <div class="modal-body">
                                        <p class="mb-0 items-center">
                                        {% if LANGUAGE_CODE == 'ja' %}
                                        一度再開すると、サブスクリプションは毎月自動的に延長されていきます。
                                        {% else %}
                                        Once reactivated, the subscription will extended automatically every month.
                                        {% endif %}
                                        </p>
                                    </div>
                                
                                    <input type="hidden" name="active">
                                
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-white border" data-dismiss="modal">
                                            {% if LANGUAGE_CODE == 'ja' %}
                                            キャンセル
                                            {% else %}
                                            Cancel
                                            {% endif %}
                                        </button>
                                        <button type="submit" class="btn btn-dark">
                                            {% if LANGUAGE_CODE == 'ja' %}
                                            再開
                                            {% else %}
                                            Reactivate
                                            {% endif %}
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div> 

                {% if workspace.subscription != 'partner' and user_role == 'admin' %}
                    <div class="card-title py-5 border-top">
                        <h3 id="payment-methods" class="fs-3 fw-bolder text-dark fw-bolder text-dark">
                            {% if LANGUAGE_CODE == 'ja' %}
                            お支払い方法
                            {% else %}
                            Payment Methods
                            {% endif %}
                        </h3>
                        <div class="d-flex row my-5 w-100 gx-5 row-eq-height">
                            {% for payment_method in payment %}
                            <div class="col-12 col-lg-4 mb-5 mb-lg-0">
                                <div class="px-6 py-5 rounded border shadow h-100">
                                    <div class="d-flex  justify-content-between">
                                        <span class="svg-icon svg-icon-muted svg-icon-2hx">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M22 7H2V11H22V7Z" fill="currentColor"/>
                                                <path opacity="0.3" d="M21 19H3C2.4 19 2 18.6 2 18V6C2 5.4 2.4 5 3 5H21C21.6 5 22 5.4 22 6V18C22 18.6 21.6 19 21 19ZM14 14C14 13.4 13.6 13 13 13H5C4.4 13 4 13.4 4 14C4 14.6 4.4 15 5 15H13C13.6 15 14 14.6 14 14ZM16 15.5C16 16.3 16.7 17 17.5 17H18.5C19.3 17 20 16.3 20 15.5C20 14.7 19.3 14 18.5 14H17.5C16.7 14 16 14.7 16 15.5Z" fill="currentColor"/>
                                            </svg>
                                        </span>
                                        <a href={% host_url 'workspace_delete_payment_method' payment_method.id host 'app' %}>
                                            <span class="svg-icon svg-icon-muted svg-icon-2hx">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M19 6L6 19M6 6L19 19" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                                </svg>
                                            </span>
                                        </a>
                                    </div>
                                    
                                    <div class="font-bold text-xl ml-2">**** {{payment_method.card.last4}}</div>
                                    
                                    <div class="flex-col">
                                        <div class="d-flex fw-bolder text-gray-700 text-base">
                                            <div class=" me-5">
                                                {% if LANGUAGE_CODE == 'ja' %}
                                                カードブランド
                                                {% else %}
                                                Card Brand
                                                {% endif %}
                                            </div>
                                            <div class="">
                                                {{payment_method.card.brand|title}}
                                            </div>
                                        </div>
                                        <div class="d-flex">
                                            <div class="text-gray-700 text-base font-bold">
                                                {% if LANGUAGE_CODE == 'ja' %}
                                                有効期限
                                                {% else %}
                                                Expired Date
                                                {% endif %}
                
                                            </div>
                                            <div class="text-gray-700 text-base">
                                                {{payment_method.card.exp_month}}/{{payment_method.card.exp_year}}
                                            </div>
                
                                        </div>
                
                                    </div>
                                    <div class="mt-3">
                                        {% if payment_method.id == workspace.stripe_default_payment_method %}
                                        <span class="d-inline-block bg-gray-200 rounded-3 px-3 py-1 text-sm font-semibold text-gray-700 mr-2 mb-2">
                                            {% if LANGUAGE_CODE == 'ja' %}
                                            デフォルト
                                            {% else %}
                                            Default
                                            {% endif %}
                                        </span>
                                        {% else %}
                                        <a href="{% host_url 'workspace_set_default_payment_method' payment_method.id host 'app' %}" class="inline-block bg-green-500 rounded-full px-3 py-1 text-sm font-semibold text-white mr-[2px] mb-2">
                                            <span class="btn-primary d-inline-block rounded-3 px-3 py-1 text-sm font-semibold text-black-700 mr-2 mb-2">
                                                {% if LANGUAGE_CODE == 'ja' %}
                                                デフォルト
                                                {% else %}
                                                Set Default Payment
                                                {% endif %}
                                            </span>
                                        </a>
                                        {% endif %}
                                    </div>
                    
                                </div>
                            </div>
                            {% endfor %}
                            <div class="col-4">
                                <div class="px-6 py-4 rounded border shadow w-150px min-h-150px ">
                                    <a href="{% host_url 'stripe_checkout' host 'app'  %}?plan=setup_payment"  class="min-h-150px  rounded d-flex justify-content-center align-items-center cursor-pointer">
                                        <span class="svg-icon svg-icon-muted svg-icon-2hx">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="5" fill="currentColor"/>
                                            <rect x="10.8891" y="17.8033" width="12" height="2" rx="1" transform="rotate(-90 10.8891 17.8033)" fill="currentColor"/>
                                            <rect x="6.01041" y="10.9247" width="12" height="2" rx="1" fill="currentColor"/>
                                            </svg>
                                        </span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}

            </div>
            
            
            <div class="col-12 col-lg-4 mt-5">
                <div class="mb-10 tw-bg-gray-200 fw-bolder p-10 rounded-3">
                    {% if workspace.subscription == "partner" %}
                        <div class="my-[10px] flex">
                            <p class="mb-0 items-center">
                                {% if LANGUAGE_CODE == 'ja' %}
                                あなたのプランは現在：<b>エンタープライズ</b>です。請求書でのお支払いとなります。
                                {% else %}
                                You are currently under <b>Enterprise Plan</b>.<br> This account is being charged through invoices.
                                {% endif %}
                            </p>
                        </div>
                    {% endif %}
                    <div class="my-5">
                        <p class=" items-center">
                            {% if LANGUAGE_CODE == 'ja' %}
                            料金情報については、<a target="_blank" href="{% host_url 'pricing' host 'static' %}">こちら</a>をご覧ください。<br/>
                            ご不明な点があれば、<a href="{% host_url 'contact' host 'static' %}">お問い合わせください</a>。
                            {% else %}
                            For pricing information, please visit <a target="_blank" href="{% host_url 'pricing' host 'static' %}">Sanka's pricing page</a>.<br/>
                            Should you have any questions, please <a target="_blank" href="{% host_url 'contact' host 'static' %}">{% trans "contact us" %}</a>.
                            {% endif %}
                        </p>
                    </div>
                </div> 
            </div>
        </div>
    </div>

    <div class="border-top pt-10 card-title mb-20">
        <h3 class="fs-3 fw-bolder text-dark card-label fw-bolder text-dark">
            {% if LANGUAGE_CODE == 'ja' %}
            請求履歴
            {% else %}
            Billing History
            {% endif %}
        </h3>
        <div>

            <table class="table table-row-bordered table-row-dashed gy-4 align-middle">
                <thead class="{% include "data/utility/table-header.html" %}">
                    <tr>
                        <th class="">
                            {% if LANGUAGE_CODE == 'ja' %}
                            請求期間
                            {% else %}
                            BILLING CYCLE
                            {% endif %}
                        </th>
                        <th class="">
                            {% if LANGUAGE_CODE == 'ja' %}
                            種類
                            {% else %}
                            TYPE
                            {% endif %}
                        </th>
                        <th class="">
                            {% if LANGUAGE_CODE == 'ja' %}
                            金額
                            {% else %}
                            AMOUNT
                            {% endif %}
                        </th>
                        <th class="">
                            {% if LANGUAGE_CODE == 'ja'%}
                            ステータス
                            {% else %}
                            STATUS
                            {% endif %}
                        </th>
                        <th class="min-w-100px">
                            {% if LANGUAGE_CODE == 'ja'%}
                            支払日
                            {% else %}
                            PAYMENT DATE
                            {% endif %}
                        </th>
                        <th class="min-w-100px">
                            {% if LANGUAGE_CODE == 'ja'%}
                            領収書
                            {% else %}
                            RECEIPT
                            {% endif %}
                        </th>
                    </tr>
                </thead>
                <tbody class="fs-6">
                    {% for payment_his in payment_history %}
                    <tr>
                        <td>
                            {% if payment_his.payment_cycle %}
                                {{payment_his.payment_cycle}}
                            {% endif %}
                        </td>
                        <td>
                            {% if payment_his.payment_type == 'top_up_credit' %}
                                {% if LANGUAGE_CODE == 'ja'%}
                                クレジット購入
                                {% else %}
                                Credit Purchase
                                {% endif %}

                            {% elif payment_his.payment_type == 'usage' %}
                                {% if LANGUAGE_CODE == 'ja'%}
                                ご利用料金
                                {% else %}
                                Usage
                                {% endif %}

                            {% endif %}
                        </td>

                        <td>
                            {% if workspace.currencies|string_list_to_list|first|lower == "jpy" %}
                                {% if payment_his.total_jpy %}
                                ¥{{payment_his.total_jpy|intcomma:False}}
                                
                                {% endif %}
                            {% else %}
                                {% if payment_his.total_usd %}
                                ${{payment_his.total_usd|floatformat:2}}
                                {% endif %}
                            {% endif %}
                        </td>
                        <td>
                            {% if payment_his.status == 'paid' %}
                            
                                {% if LANGUAGE_CODE == 'ja'%}
                                支払い済み
                                {% else %}
                                Paid
                                {% endif %}

                            {% elif payment_his.status == 'unpaid' %}
                                {% if LANGUAGE_CODE == 'ja'%}
                                    未払い
                                    {% else %}
                                    Not Paid

                                {% endif %}

                            {% endif %}
                        </td>
                        <td>
                            {% if payment_his.created_at %}
                                {% local_time payment_his.created_at workspace.timezone %}
                            {% endif %}
                        </td>
                        <td>
                            {% if payment_his.reciept_url %}
                                <a href= {{payment_his.reciept_url}} class="fs-5">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    確認する
                                    {% else %}
                                    View
                                    {% endif %}
        
                                </a> 
                            {% endif %}
                        </td>
                        
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% if not payment_history %}
                {% if LANGUAGE_CODE == 'ja' %}
                請求履歴がありません。
                {% else %}
                There's no billing record.
                {% endif %}
            {% endif %}
        </div>
    </div>
    {% endif %}


    </div>


    
</div>

<div class="modal fade" id="change-subscription-summary-modal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered mw-800px" role="document">
        <div class="modal-content">
            <div id="change-subscription-summary" class="p-8"></div>
        </div>
    </div>
</div> 

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
<!-- Bootstrap JavaScript -->
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>


