import uuid
from cryptography.fernet import <PERSON><PERSON><PERSON>, InvalidToken
from django.db import models
from django.utils import timezone
from django.contrib.auth.models import User

from data.constants.constant import *
from data.constants.properties_constant import *
from data.models.constant import *

from sanka.storage_backends import PrivateMediaStorage
from django.conf import settings

fernet = Fernet(settings.FERNET_KEY)

class UserManagement(models.Model):
    class RoleType(models.TextChoices):
        ADMIN = 'admin', 'Admin'
        STAFF = 'staff', 'Staff'
        VIEW_ONLY = 'view_only', 'View Only'
        SUPPORT = 'support', 'Support'
        PARTNER = 'partner', 'Partner'


    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    type = models.TextField(choices=RoleType.choices,
                            max_length=20, default=RoleType.STAFF)
    view_only_password = models.TextField(null=True, blank=True)

    permission = models.JSONField(null=True, blank=True)

    # Extended from Employee
    location = models.CharField(max_length=256, null=True, blank=True)
    date_from = models.DateField(null=True, blank=True)
    date_to = models.DateField(null=True, blank=True)

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    id_user = models.IntegerField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if not self.id_user:
            # Generate a new order_id if it doesn't exist
            last_ = UserManagement.objects.filter(
                workspace=self.workspace, id_user__isnull=False).order_by('id_user').last()
            if last_:
                if last_.id_user:
                    try:
                        last_ = int(last_.id_user)
                    except:
                        last_ = 0

                    next_id = last_ + 1
                else:
                    next_id = 1
            else:
                # If it's the first id, start with 1
                next_id = 1

            # Format the id as a four-digit string
            self.id_user = f"{next_id:04d}"
        super().save(*args, **kwargs)
    
    def get_view_only_password(self):
        try:
            if self.view_only_password:
                return fernet.decrypt(self.view_only_password.encode()).decode()
            return None
        except InvalidToken:
            return None
        
    def set_view_only_password(self, raw_password: str):
        
        if not raw_password:
            self.view_only_password = None
            return

        self.view_only_password = fernet.encrypt(raw_password.encode()).decode()

# UserManagementProperty


class UserManagementFile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    usermanagement = models.ForeignKey(
        UserManagement, on_delete=models.CASCADE, null=True, blank=True)
    file = models.FileField(verbose_name="User Management File",
                            upload_to="usermanagement-files", storage=PrivateMediaStorage())
    created_at = models.DateTimeField(default=timezone.now)


SHOPTURBO_NUMBER_FORMAT = [
    ('number', 'Number'),
    ('%', '%'),
    ('usd', 'USD'),
    ('jpy', 'JPY'),
    ('idr', 'IDR'),
]


class UserManagementNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(choices=OBJECTS_CF_TYPE,
                            max_length=20, null=True, blank=True)
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True)
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    show_badge = models.BooleanField(default=False)
    badge_color = models.CharField(max_length=50, null=True, blank=True)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    order = models.IntegerField(null=True, default=0, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True)


class UserManagementValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        UserManagementNameCustomField, on_delete=models.CASCADE, null=True, blank=True)
    usermanagement = models.ForeignKey(UserManagement, on_delete=models.CASCADE,
                                       null=True, blank=True, related_name='usermanagement_custom_field_relations')
    value = models.CharField(max_length=500, null=True, blank=True)
    file = models.FileField(verbose_name="Custom Field file",
                            upload_to="usermanagement-customfield-files", null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)


class UserManagementValueFile(models.Model):

    class PostMediaType(models.TextChoices):
        IMAGE = 'image', 'Image'
        VIDEO = 'video', 'Video'

    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(verbose_name="User Management Custom Field file",
                            upload_to="usermanagement-custom-field-files")
    valuecustomfield = models.ForeignKey(
        UserManagementValueCustomField, on_delete=models.CASCADE, null=True, blank=True)
    media_type = models.CharField(
        max_length=50, choices=PostMediaType.choices, default=PostMediaType.IMAGE)
    created_at = models.DateTimeField(default=timezone.now)


class UserReference(models.Model):
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    platform = models.CharField(
        choices=PLATFORM, max_length=100, null=True, blank=True)
    reference_id = models.CharField(max_length=500, null=True, blank=True)
    reference_value = models.TextField(null=True, blank=True)
    description = models.TextField(null=True, blank=True)

    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.platform) + str(' - ') + str(self.reference_value)


class Verification(models.Model):
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_query_name="verification"
    )
    verified = models.BooleanField(default=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.SET_NULL, null=True, blank=True)  # last workspace
    profile_photo = models.FileField(
        upload_to="profile-photos", null=True, blank=True)
    language = models.CharField(max_length=10, null=True, blank=True)
    timezone = models.CharField(
        choices=TIMEZONES, max_length=64, null=True, blank=True)
    admin = models.BooleanField(default=False)
    phone_number = models.CharField(max_length=256, null=True, blank=True)
    signup_param = models.CharField(max_length=1024, null=True, blank=True)
    utm_source = models.CharField(max_length=255, null=True, blank=True)
    utm_campaign = models.CharField(max_length=255, null=True, blank=True)
    utm_medium = models.CharField(max_length=255, null=True, blank=True)
    utm_term = models.CharField(max_length=255, null=True, blank=True)
    utm_content = models.CharField(max_length=255, null=True, blank=True)
    saml_login = models.BooleanField(default=False)

    def __str__(self):
        return 'Verification for: ' + self.user.username


VERIFICATION_CODE_STATUS = [
    ('generated', 'Generated'),
    ('used', 'Used'),
]


class VerificationCode2(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField()
    code = models.CharField(max_length=20)
    status = models.CharField(
        max_length=20, choices=VERIFICATION_CODE_STATUS, default='generated')
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return 'Verification for: ' + str(self.email)


class Preference(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True)

    # All the fields below contains list of strings seperated by comma. ex: marketing,recruiting,PR,...
    interest = models.CharField(max_length=256, null=True, blank=True)
    interest_ja = models.CharField(max_length=256, null=True, blank=True)
    latest_automation_category = models.TextField(null=True, blank=True)
    latest_automation_category_ja = models.TextField(null=True, blank=True)
    latest_visited_page = models.TextField(null=True, blank=True)
    latest_visited_page_ja = models.TextField(null=True, blank=True)

    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        if self.user:
            return self.user.email
        else:
            return str(self.id)
