from pydantic import BaseModel, Field
from typing import Optional, List

class ExportOrdersPayload(BaseModel):
    user: str
    platform: str
    channel_id: str
    order_ids: List[str]
    mapping_association_custom_fields: Optional[str] = None
    mapping_custom_fields: Optional[str] = None
    mapping_contact_custom_fields: Optional[str] = None
    mapping_status_custom_fields: Optional[str] = None
    history_id: str
    lang: Optional[str] = Field(default="ja")
    background_job_id: Optional[str] = Field(default="")
    mapping_id: Optional[str] = Field(default="")
    # Export-specific fields
    export_format: Optional[str] = Field(default="default")
    include_customer_data: Optional[bool] = Field(default=True)
    include_item_data: Optional[bool] = Field(default=True)
    # HubSpot specific fields
    additional_action: Optional[str] = Field(default="")
    filter_output: Optional[str] = Field(default="")
    update_hubspot: Optional[List[str]] = Field(default_factory=list)
    set_contact_as_company: Optional[bool] = Field(default=False)


class ExportOrdersShopifyPayload(BaseModel):
    """Payload model for Shopify-specific order export child workflow"""
    user: str
    channel_id: str
    order_ids: List[str]
    mapping_contact_custom_fields: Optional[str] = None
    history_id: str
    background_job_id: Optional[str] = Field(default="")


class ExportOrdersEcforcePayload(BaseModel):
    """Payload model for ECforce-specific order export child workflow"""
    user: str
    channel_id: str
    order_ids: List[str]
    history_id: str
    background_job_id: Optional[str] = Field(default="")


class ExportOrdersNextenginePayload(BaseModel):
    """Payload model for NextEngine-specific order export child workflow"""
    user: str
    channel_id: str
    order_ids: List[str]
    mapping_custom_fields: Optional[str] = None
    mapping_field: Optional[str] = None
    update_hubspot: Optional[List[str]] = Field(default_factory=list)
    history_id: str
    background_job_id: Optional[str] = Field(default="")


class ExportOrdersFreeePayload(BaseModel):
    """Payload model for Freee-specific order export child workflow"""
    user: str
    channel_id: str
    order_ids: List[str]
    mapping_contact_custom_fields: Optional[str] = None
    history_id: str
    background_job_id: Optional[str] = Field(default="")


class ExportOrdersYahooShoppingPayload(BaseModel):
    """Payload model for Yahoo Shopping-specific order export child workflow"""
    user: str
    channel_id: str
    order_ids: List[str]
    history_id: str
    background_job_id: Optional[str] = Field(default="")


class ExportOrdersHubspotPayload(BaseModel):
    """Payload model for HubSpot-specific order export child workflow"""
    user: str
    channel_id: str
    order_ids: List[str]
    mapping_custom_fields: Optional[str] = None
    mapping_association_custom_fields: Optional[str] = None
    mapping_status_custom_fields: Optional[str] = None
    lang: Optional[str] = Field(default="ja")
    history_id: str
    background_job_id: Optional[str] = Field(default="")
    set_contact_as_company: Optional[bool] = Field(default=False)
    use_csv_export: Optional[bool] = Field(default=True)
    
    
class ExportOrdersHubspotPayloadV2(BaseModel):
    """Payload model for HubSpot-specific order export child workflow"""
    background_job_id: str


class ExportOrdersIntegrationsPayload(BaseModel):
    """Payload model for general integration platforms order export child workflow"""
    user: str
    platform: str
    channel_id: str
    order_ids: List[str]
    mapping_association_custom_fields: Optional[str] = None
    mapping_custom_fields: Optional[str] = None
    mapping_contact_custom_fields: Optional[str] = None
    mapping_status_custom_fields: Optional[str] = None
    history_id: str
    lang: Optional[str] = Field(default="ja")
    background_job_id: Optional[str] = Field(default="")
    mapping_id: Optional[str] = Field(default="")


class ExportOrdersResultPayload(BaseModel):
    """Result payload model for child workflow results"""
    success: bool
    platform: str
    message: Optional[str] = None
    error: Optional[str] = None
    exported_count: Optional[int] = Field(default=0)