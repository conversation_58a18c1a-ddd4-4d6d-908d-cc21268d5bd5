"""
Hatchet workflow definitions for import orders with child spawning
"""

from utils.bgjobs.hatchet_client import hatchet
from .models import (
    ImportOrdersSalesforcePayloadV2,
    ImportOrdersShopifyPayload,
    ImportShopifyOrdersSubprocessPayload,
    ImportOrdersHubSpotPayload,
    ImportHubSpotOrdersSubprocessPayload,
    ImportOrdersSquarePayload,
    ImportOrdersAmazonPayload,
    ImportOrdersEcforcePayload,
    ImportOrdersRakutenPayload,
    ImportOrdersEccubePayload,
    ImportOrdersMakeshopPayload,
    ImportOrdersYahooShoppingPayload,
    ImportOrdersBcartPayload,
    ImportBcartOrdersSubprocessPayload,
    ImportOrdersWoocommercePayload,
    ImportOrdersEbayPayload,
    ImportOrdersStripePayload,
    ImportOrdersNextEnginePayload,
)

# Main orchestrator DAG workflow (parent)
import_shopify_orders = hatchet.workflow(
    name="ImportShopifyOrdersWorkflow",
    description="Import Shopify Orders Child Workflow",
    input_validator=ImportOrdersShopifyPayload,
)

import_shopify_orders_subprocess = hatchet.workflow(
    name="ImportShopifyOrdersSubprocessWorkflow",
    description="Import Shopify Orders Subprocess Child Workflow",
    input_validator=ImportShopifyOrdersSubprocessPayload,
)

import_hubspot_orders = hatchet.workflow(
    name="ImportHubSpotOrdersWorkflow",
    description="Import HubSpot Orders Child Workflow",
    input_validator=ImportOrdersHubSpotPayload,
)

import_hubspot_orders_subprocess = hatchet.workflow(
    name="ImportHubSpotOrdersSubprocessWorkflow",
    description="Import HubSpot Orders Subprocess Child Workflow",
    input_validator=ImportHubSpotOrdersSubprocessPayload,
)

# Individual integration workflows
import_square_orders = hatchet.workflow(
    name="ImportSquareOrdersWorkflow",
    description="Import Square Orders Child Workflow",
    input_validator=ImportOrdersSquarePayload,
)

import_amazon_orders = hatchet.workflow(
    name="ImportAmazonOrdersWorkflow",
    description="Import Amazon Orders Child Workflow",
    input_validator=ImportOrdersAmazonPayload,
)

import_ecforce_orders = hatchet.workflow(
    name="ImportEcforceOrdersWorkflow",
    description="Import Ecforce Orders Child Workflow",
    input_validator=ImportOrdersEcforcePayload,
)

import_rakuten_orders = hatchet.workflow(
    name="ImportRakutenOrdersWorkflow",
    description="Import Rakuten Orders Child Workflow",
    input_validator=ImportOrdersRakutenPayload,
)

import_eccube_orders = hatchet.workflow(
    name="ImportEccubeOrdersWorkflow",
    description="Import EC-CUBE Orders Child Workflow",
    input_validator=ImportOrdersEccubePayload,
)

import_makeshop_orders = hatchet.workflow(
    name="ImportMakeshopOrdersWorkflow",
    description="Import Makeshop Orders Child Workflow",
    input_validator=ImportOrdersMakeshopPayload,
)

import_yahoo_shopping_orders = hatchet.workflow(
    name="ImportYahooShoppingOrdersWorkflow",
    description="Import Yahoo Shopping Orders Child Workflow",
    input_validator=ImportOrdersYahooShoppingPayload,
)

import_bcart_orders = hatchet.workflow(
    name="ImportBcartOrdersWorkflow",
    description="Import B-Cart Orders Child Workflow",
    input_validator=ImportOrdersBcartPayload,
)

import_bcart_orders_subprocess = hatchet.workflow(
    name="ImportBcartOrdersSubprocessWorkflow",
    description="Import B-Cart Orders Subprocess Child Workflow",
    input_validator=ImportBcartOrdersSubprocessPayload,
)

import_woocommerce_orders = hatchet.workflow(
    name="ImportWoocommerceOrdersWorkflow",
    description="Import WooCommerce Orders Child Workflow",
    input_validator=ImportOrdersWoocommercePayload,
)

import_salesforce_opportunities = hatchet.workflow(
    name="ImportSalesforceOpportunitiesWorkflow",
    description="Import Salesforce Opportunities Child Workflow",
    input_validator=ImportOrdersSalesforcePayloadV2,
)

import_ebay_orders = hatchet.workflow(
    name="ImportEbayOrdersWorkflow",
    description="Import eBay Orders Child Workflow",
    input_validator=ImportOrdersEbayPayload,
)

import_stripe_orders = hatchet.workflow(
    name="ImportStripeOrdersWorkflow",
    description="Import Stripe Orders Child Workflow",
    input_validator=ImportOrdersStripePayload,
)

import_nextengine_orders = hatchet.workflow(
    name="ImportNextEngineOrdersWorkflow",
    description="Import NextEngine Orders Child Workflow",
    input_validator=ImportOrdersNextEnginePayload,
)
