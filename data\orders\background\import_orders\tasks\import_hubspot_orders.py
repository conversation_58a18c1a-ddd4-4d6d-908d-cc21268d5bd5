import ast
import asyncio
from datetime import timedelta
import json
import traceback

from asgiref.sync import sync_to_async
from hatchet_sdk import Context, WorkflowRunRef

from data.models import Channel, TransferHistory
from utils.bgjobs.handler import (
    aio_set_bg_job_completed,
    aio_set_bg_job_failed,
    aio_set_bg_job_running,
)
from utils.hubspot import refresh_hubspot_token

# from utils.hubspot import import_hubspot_orders_as_deals as hubspot_import_as_deals_util
from utils.hubspot.async_import_hubspot import (
    aio_import_hubspot_orders_as_deals as aio_hubspot_import_as_deals_util,
)
from utils.hubspot.hubspot_import_utils import get_hubspot_client
from utils.logger import logger

from ..models import (
    ImportHubSpotOrdersSubprocessPayload,
    ImportOrdersHubSpotPayload,
)
from ..workflows import import_hubspot_orders_subprocess
from ..workflows import import_hubspot_orders


@import_hubspot_orders.task(
    name="ImportHubSpotOrdersTask",
    execution_timeout=timedelta(hours=5),
    schedule_timeout=timedelta(hours=1),
)
async def import_hubspot_orders_task(
    input: ImportOrdersHubSpotPayload, ctx: Context
) -> dict:
    """
    Child task for importing HubSpot orders
    """
    logger.info("Run HubSpot orders import child task")
    logger.info(f"Input: {input}")

    history_id = input.history_id
    task = None

    try:
        task = await TransferHistory.objects.select_related("workspace", "user").aget(
            id=history_id
        )
        task.keep_alive = True
        await task.asave()
    except TransferHistory.DoesNotExist:
        logger.warning("TransferHistory does not exist")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])

    if input.background_job_id:
        await aio_set_bg_job_running(input.background_job_id)

    try:
        # Process mapping fields
        mapping_custom_fields = (
            input.mapping_custom_fields
            if input.mapping_custom_fields and input.mapping_custom_fields != "None"
            else {}
        )
        mapping_status_custom_fields = (
            input.mapping_status_custom_fields
            if input.mapping_status_custom_fields
            and input.mapping_status_custom_fields != "None"
            else {}
        )

        # Parse string mappings if needed
        if isinstance(mapping_custom_fields, str):
            mapping_custom_fields = ast.literal_eval(mapping_custom_fields)
        if isinstance(mapping_status_custom_fields, str):
            mapping_status_custom_fields = ast.literal_eval(
                mapping_status_custom_fields
            )

        # Process other fields
        last_index = input.last_index
        if last_index == "None" or not last_index:
            last_index = None

        hubspot_filter_import = input.hubspot_filter_import
        try:
            if hubspot_filter_import:
                hubspot_filter_import = ast.literal_eval(hubspot_filter_import)
        except (ValueError, SyntaxError):
            hubspot_filter_import = None

        # Call appropriate HubSpot import function
        if input.as_deal:
            await aio_hubspot_import_as_deals_util(
                input.channel_id,
                mapping_custom_fields=mapping_custom_fields,
                how_to_import=input.how_to_import,
                lang=input.lang,
            )
        else:
            # Handle pagination with child workflows
            success = await _handle_hubspot_pagination_async(
                input=input,
                task=task,
                mapping_custom_fields=mapping_custom_fields,
                mapping_status_custom_fields=mapping_status_custom_fields,
                last_index=last_index,
                hubspot_filter_import=hubspot_filter_import,
            )

            if not success:
                logger.error("Failed to process HubSpot orders with child workflows")
                if input.background_job_id:
                    await aio_set_bg_job_failed(input.background_job_id)
                if task:
                    task.status = "failed"
                    await task.asave(update_fields=["status"])

        logger.info("Successfully imported HubSpot orders")

        if task:
            await task.arefresh_from_db()
            task.status = "completed"
            await task.asave(update_fields=["status"])

        if input.background_job_id:
            await aio_set_bg_job_completed(input.background_job_id)

    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error importing HubSpot orders: {str(e)}")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])


async def _handle_hubspot_pagination_async(
    input: ImportOrdersHubSpotPayload,
    task: TransferHistory,
    mapping_custom_fields: dict,
    mapping_status_custom_fields: dict,
    last_index: int,
    hubspot_filter_import: dict,
) -> bool:
    """
    Handle HubSpot pagination by spawning child workflows for each page (async version)
    """
    try:
        # Get the channel
        channel = await Channel.objects.select_related("workspace").aget(
            id=input.channel_id
        )

        # Prepare properties to fetch
        properties = [
            "dealname",
            "amount",
            "deal_currency_code",
            "dealstage",
            "createdate",
            "closedate",
        ]
        contact_properties = ["firstname", "lastname", "email", "phone"]
        company_properties = ["name", "email", "phone", "domain"]

        # Add custom fields to properties
        if mapping_custom_fields:
            for field in mapping_custom_fields:
                if field not in [
                    "deal_name",
                    "deal_stage",
                    "issue_date",
                    "billing_date",
                    "partner_display_name",
                ]:
                    if "|deal" in field:
                        field = field.replace("|deal", "")
                        if field not in properties:
                            properties.append(field)
                    elif "|contact" in field:
                        field = field.replace("|contact", "")
                        if field not in contact_properties:
                            contact_properties.append(field)
                    elif "|company" in field:
                        field = field.replace("|company", "")
                        if field not in company_properties:
                            company_properties.append(field)

        # Start pagination
        after = str(last_index) if last_index else None
        has_more = True
        page_count = 0

        logger.info(f"Starting HubSpot pagination with after={after}")

        hubspot_task_result = []

        while has_more:
            page_count += 1
            logger.info(f"Processing page {page_count}, after={after}")

            try:
                # Make API call to check if there are deals
                await sync_to_async(refresh_hubspot_token)(channel.id)
                api_client = await sync_to_async(get_hubspot_client)(channel)

                while True:
                    try:
                        response = await sync_to_async(
                            api_client.crm.deals.basic_api.get_page
                        )(
                            limit=10,
                            after=after,
                            properties=properties,
                            associations=["contacts", "companies"],
                        )
                        break
                    except Exception as e:
                        if hasattr(e, "status") and e.status == 429:
                            logger.warning("Rate limit hit, retrying in 3s...")
                            await asyncio.sleep(3)
                        else:
                            logger.error(f"Error fetching deals: {e}")
                            raise

                if not response.results:
                    logger.info("No more results, breaking pagination loop")
                    break

                logger.info(
                    f"About to create child workflow for {len(response.results)} deals"
                )

                workspace = channel.workspace

                # Create sub-history for this page
                sub_history = await TransferHistory.objects.acreate(
                    workspace=workspace,
                    user=task.user if task and task.user else None,
                    status="pending",
                    type="import_orders",
                    name=f"Import Hubspot Deals. Subprocess of {task.id if task else 'unknown'}",
                    parent=task,
                )

                # Create payload for subprocess
                subprocess_payload = ImportHubSpotOrdersSubprocessPayload(
                    channel_id=input.channel_id,
                    filter_deal_stage="",  # Not used in subprocess
                    mapping_custom_fields=json.dumps(mapping_custom_fields),
                    how_to_import=input.how_to_import_hubspot,
                    lang=input.lang,
                    how_to_import_items=input.how_to_import,
                    key_item_field=input.key_item_field,
                    mapping_status_custom_fields=json.dumps(
                        mapping_status_custom_fields
                    ),
                    last_index=str(last_index) if last_index else "",
                    hubspot_filter_import=json.dumps(hubspot_filter_import),
                    history_id=str(sub_history.id),
                    after=after or "",
                    background_job_id=input.background_job_id,
                )

                # Note: For now, we'll trigger the subprocess synchronously
                # In a real implementation, you would spawn this as a child workflow
                # For simplicity, we'll trigger it directly

                # Call the subprocess task directly
                hatchet_task = await import_hubspot_orders_subprocess.aio_run_no_wait(
                    subprocess_payload
                )

                logger.info(
                    f"[DEBUG] New Hubspot subprocess is running with id: {hatchet_task.workflow_run_id}"
                )

                # Check pagination
                if (
                    response.paging
                    and response.paging.next
                    and response.paging.next.after
                ):
                    after = response.paging.next.after
                    logger.info(f"Moving to next page with after={after}")
                else:
                    has_more = False
                    logger.info("No more pages, ending pagination loop")

                # Store the result of the task
                hubspot_task_result.append(hatchet_task)

            except Exception as e:
                logger.error(f"Error in pagination loop: {str(e)}")
                logger.error(
                    f"Error in pagination loop traceback: {traceback.format_exc()}"
                )
                return False

        # Waiting for all subprocesses to complete
        results = []
        logger.info(
            f"Waiting for {len(hubspot_task_result)} HubSpot subprocesses to complete"
        )
        for task in hubspot_task_result:
            assert isinstance(task, WorkflowRunRef), (
                "Expected task to be of type WorkflowRunRef"
            )
            result = await task.aio_result()
            result = (
                result.get("importhubspotorderssubprocesstask", result)
                if isinstance(result, dict)
                else result
            )

            results.append(result)

        # Check results of all subprocesses
        all_success = True
        logger.info(f"Checking results of {len(results)} HubSpot subprocesses")
        for index, result in enumerate(results):
            logger.info(f"Subprocess at index {index} result: {result}")
            if not result.get("success", False):
                error_message = result.get("error", "Unknown error")
                logger.error(f"Subprocess at index {index} failed: {error_message}")
                all_success = False

        logger.info(f"Successfully processed {page_count} pages")
        return all_success

    except Exception:
        logger.error(
            f"Error in _handle_hubspot_pagination_async: {traceback.format_exc()}"
        )
        return False
