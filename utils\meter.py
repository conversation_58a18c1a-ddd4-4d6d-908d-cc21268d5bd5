import math

from django.db.models import Exists, OuterRef
from django.db.models.signals import post_save
from django.utils import timezone

from data.models import (
    Meter,
    ShopTurboItems,
    ShopTurboOrders,
    ShopTurboSubscriptions,
    Contact,
    Company,
    ShopTurboInventory,
    InventoryTransaction,
    InventoryWarehouse,
    Invoice,
    DeliverySlip,
    Estimate,
    Receipt,
    PurchaseOrders,
    Bill,
    Expense,
    UserManagement,
    Slip,
    JournalEntry,
    Workspace,
    UsageLimit,
    BasePricing,
    User,
)

from data.constants.constant import (
    ITEM_USAGE_CATEGORY,
    ORDER_USAGE_CATEGORY,
    SUBSCRIPTION_USAGE_CATEGORY,
    CONTACT_USAGE_CATEGORY,
    COMPANY_USAGE_CATEGORY,
    INVENTORY_USAGE_CATEGORY,
    INVENTORY_TRANSACTION_USAGE_CATEGORY,
    WAREHOUSE_USAGE_CATEGORY,
    INVOICE_USAGE_CATEGORY,
    DELIVERY_SLIP_USAGE_CATEGORY,
    ESTIMATE_USAGE_CATEGORY,
    RECEIPT_USAGE_CATEGORY,
    PURCHASE_ORDER_USAGE_CATEGORY,
    BILL_USAGE_CATEGORY,
    EXPENSE_USAGE_CATEGORY,
    USER_USAGE_CATEGORY,
    SLIP_USAGE_CATEGORY,
    JOURNAL_USAGE_CATEGORY,
    STARTER_PRICING_TIER,
    MONTHLY,
)

STORAGE_USAGE_TO_MODELS = {
    ITEM_USAGE_CATEGORY: ShopTurboItems,
    ORDER_USAGE_CATEGORY: ShopTurboOrders,
    SUBSCRIPTION_USAGE_CATEGORY: ShopTurboSubscriptions,
    CONTACT_USAGE_CATEGORY: Contact,
    COMPANY_USAGE_CATEGORY: Company,
    INVENTORY_USAGE_CATEGORY: ShopTurboInventory,
    INVENTORY_TRANSACTION_USAGE_CATEGORY: InventoryTransaction,
    WAREHOUSE_USAGE_CATEGORY: InventoryWarehouse,
    INVOICE_USAGE_CATEGORY: Invoice,
    DELIVERY_SLIP_USAGE_CATEGORY: DeliverySlip,
    ESTIMATE_USAGE_CATEGORY: Estimate,
    RECEIPT_USAGE_CATEGORY: Receipt,
    PURCHASE_ORDER_USAGE_CATEGORY: PurchaseOrders,
    BILL_USAGE_CATEGORY: Bill,
    EXPENSE_USAGE_CATEGORY: Expense,
    USER_USAGE_CATEGORY: UserManagement,
    SLIP_USAGE_CATEGORY: Slip,
    JOURNAL_USAGE_CATEGORY: JournalEntry,
}

MODELS_TO_STORAGE_USAGE = {
    ShopTurboItems: ITEM_USAGE_CATEGORY,
    ShopTurboOrders: ORDER_USAGE_CATEGORY,
    ShopTurboSubscriptions: SUBSCRIPTION_USAGE_CATEGORY,
    Contact: CONTACT_USAGE_CATEGORY,
    Company: COMPANY_USAGE_CATEGORY,
    ShopTurboInventory: INVENTORY_USAGE_CATEGORY,
    InventoryTransaction: INVENTORY_TRANSACTION_USAGE_CATEGORY,
    InventoryWarehouse: WAREHOUSE_USAGE_CATEGORY,
    Invoice: INVOICE_USAGE_CATEGORY,
    Estimate: ESTIMATE_USAGE_CATEGORY,
    DeliverySlip: DELIVERY_SLIP_USAGE_CATEGORY,
    Receipt: RECEIPT_USAGE_CATEGORY,
    PurchaseOrders: PURCHASE_ORDER_USAGE_CATEGORY,
    Bill: BILL_USAGE_CATEGORY,
    Expense: EXPENSE_USAGE_CATEGORY,
    Slip: SLIP_USAGE_CATEGORY,
    JournalEntry: JOURNAL_USAGE_CATEGORY,
}


def get_pricing_type(model):
    """Return one of pricing type [entries, records, processes] for a model.

    Args:
        model (class): model

    Returns:
        str: pricing type
    """
    if model in []:
        return "entries"

    # condition for processes here

    return "records"


def is_using_usage_status_field(model):
    """Return True if the model is using usage status field instead of status field.

    Args:
        model (class): model

    Returns:
        bool
    """
    return model in [
        Invoice,
        DeliverySlip,
        Receipt,
        Estimate,
        InventoryTransaction,
        InventoryWarehouse,
        PurchaseOrders,
        Bill,
        Expense,
        JournalEntry,
    ]


def add_usage(workspace: Workspace, category: str, total=1):
    if workspace.subscription == "partner":
        return
    meter = Meter.objects.get(workspace=workspace, category=category)
    meter.count += total
    meter.save()


def release_usage(workspace: Workspace, category: str, total=1):
    if workspace.subscription == "partner":
        return
    meter = Meter.objects.get(workspace=workspace, category=category)
    meter.count -= total
    meter.save()


def has_quota(workspace: Workspace, category: str):
    """Return True if subscription is partner plan or quota is available for this workspace on the category."""

    if workspace.subscription == "partner" and category != "users":
        return True
    usage_limit, created = UsageLimit.objects.get_or_create(
        workspace=workspace, category=category
    )
    if created:
        try:
            # Use .first() instead of .get() to handle potential duplicates gracefully
            base_pricing = (
                BasePricing.objects.filter(
                    tier=STARTER_PRICING_TIER,
                    category=category,
                    payment_frequency=MONTHLY,
                    is_base_plan=False,
                )
                .order_by("created_at")
                .first()
            )
            limit = base_pricing.limit if base_pricing else 100
        except Exception:
            limit = 100
        usage_limit.value = limit
        usage_limit.save()
    meter, created = Meter.objects.get_or_create(workspace=workspace, category=category)
    if created or meter.count is None:
        meter.count = 0
        meter.save()
    return meter.count < usage_limit.value


async def aio_has_quota(workspace: Workspace, category: str):
    """Return True if subscription is partner plan or quota is available for this workspace on the category."""

    if workspace.subscription == "partner" and category != "users":
        return True
    usage_limit, created = await UsageLimit.objects.aget_or_create(
        workspace=workspace, category=category
    )
    if created:
        try:
            # Use .first() instead of .get() to handle potential duplicates gracefully
            base_pricing = (
                await BasePricing.objects.filter(
                    tier=STARTER_PRICING_TIER,
                    category=category,
                    payment_frequency=MONTHLY,
                    is_base_plan=False,
                )
                .order_by("created_at")
                .afirst()
            )
            limit = base_pricing.limit if base_pricing else 100
        except Exception:
            limit = 100
        usage_limit.value = limit
        await usage_limit.asave()
    meter, created = await Meter.objects.aget_or_create(
        workspace=workspace, category=category
    )
    if created or meter.count is None:
        meter.count = 0
        await meter.asave()
    return meter.count < usage_limit.value


def sync_usage(workspace: Workspace, category: str):
    if workspace.subscription == "partner" and category != "users":
        return

    meter, _ = Meter.objects.get_or_create(workspace=workspace, category=category)
    model = STORAGE_USAGE_TO_MODELS[category]

    # Build base query
    queryset = model.objects.filter(workspace=workspace)

    # Add date filter if needed
    filter_date = {}
    if get_pricing_type(model) == "entries":
        filter_date["created_at__lte"] = timezone.now()
        filter_date["created_at__gte"] = timezone.now().replace(
            day=1, hour=0, minute=0, second=0
        )
        queryset = queryset.filter(**filter_date)

    # Apply status filtering based on model type
    if model == UserManagement:
        # Special case: UserManagement uses 'type' field, not status
        # Exclude view-only, support, and partner roles from usage count
        queryset = queryset.exclude(
            type__in=[
                UserManagement.RoleType.VIEW_ONLY,
                UserManagement.RoleType.SUPPORT,
                UserManagement.RoleType.PARTNER,
            ]
        )
    else:
        # For all other models: count only "active" or NULL status
        from django.db.models import Q

        # Determine which status field to use
        if is_using_usage_status_field(model):
            status_field = "usage_status"
        else:
            status_field = "status"

        # Only count records where status is "active" or NULL
        status_filter = Q(**{f"{status_field}": "active"}) | Q(
            **{f"{status_field}__isnull": True}
        )
        queryset = queryset.filter(status_filter)

    meter.count = queryset.count()
    meter.save()


def calculate_prorated_discount(total_cost, flooring_cost=True):
    if timezone.now().day == 1:
        return 0

    today = timezone.now().date()
    first_day_of_this_month = today.replace(day=1, month=today.month)
    first_day_of_next_month = today.replace(day=1, month=today.month + 1)
    total_days_of_this_month = (
        first_day_of_next_month - first_day_of_this_month
    ).days - 1
    usage_days = (first_day_of_next_month - today).days - 1
    total_prorate_days = usage_days / total_days_of_this_month
    total_cost_prorated = total_prorate_days * total_cost
    if flooring_cost:
        return total_cost - math.floor(total_cost_prorated)
    return total_cost - total_cost_prorated


def archive_objects_by_limit(workspace, category):
    if category in [ORDER_USAGE_CATEGORY]:
        return

    meter, _ = Meter.objects.get_or_create(workspace=workspace, category=category)
    usage_limit, _ = UsageLimit.objects.get_or_create(
        workspace=workspace, category=category
    )

    model = STORAGE_USAGE_TO_MODELS[category]
    filter_status = {"status": "archived"}
    if is_using_usage_status_field(model):
        filter_status = {"usage_status": "archived"}
    if model == UserManagement:
        # exclude view-only, support, and partner roles from usage count
        filter_status = {
            "type__in": [
                UserManagement.RoleType.VIEW_ONLY,
                UserManagement.RoleType.SUPPORT,
                UserManagement.RoleType.PARTNER,
            ]
        }
    if model == User:
        active_items = (
            model.objects.filter(workspace=workspace)
            .order_by("-date_joined")
            .values("id")
        )
    else:
        active_items = (
            model.objects.filter(workspace=workspace)
            .exclude(**filter_status)
            .order_by("-created_at")
            .values("id")
        )
    meter.count = active_items.count()
    if meter.count > usage_limit.value:
        if model == User:
            for user in model.objects.filter(workspace=workspace).exclude(
                id__in=active_items[: usage_limit.value]
            ):
                workspace.user.remove(user)
        else:
            model.objects.filter(workspace=workspace).exclude(**filter_status).exclude(
                id__in=active_items[: usage_limit.value]
            ).update(**filter_status)
        meter.count = usage_limit.value
    meter.save()


def _storage_usage_counter(sender, instance, created, **kwargs):
    # print('count storage usage.')
    if not instance.workspace:
        return
    sync_usage(instance.workspace, MODELS_TO_STORAGE_USAGE[sender])


# Connect post_save function to models
for val in MODELS_TO_STORAGE_USAGE:
    post_save.connect(_storage_usage_counter, val)


def get_workspace_available_storage(workspace, category):
    if workspace.subscription == "partner":
        if workspace.is_saml:
            if category not in [USER_USAGE_CATEGORY]:
                return None
        else:
            return None

    model = STORAGE_USAGE_TO_MODELS[category]

    # Build base query
    queryset = model.objects.filter(workspace=workspace)

    # Apply status filtering based on model type
    if model == UserManagement:
        # Special case: UserManagement uses 'type' field, not status
        # Exclude view-only, support, and partner roles from usage count
        queryset = queryset.exclude(
            type__in=[
                UserManagement.RoleType.VIEW_ONLY,
                UserManagement.RoleType.SUPPORT,
                UserManagement.RoleType.PARTNER,
            ]
        )
    else:
        # For all other models: count only "active" or NULL status
        from django.db.models import Q

        # Determine which status field to use
        if is_using_usage_status_field(model):
            status_field = "usage_status"
        else:
            status_field = "status"

        # Only count records where status is "active" or NULL
        status_filter = Q(**{f"{status_field}": "active"}) | Q(
            **{f"{status_field}__isnull": True}
        )
        queryset = queryset.filter(status_filter)

    storage_usage = queryset.count()

    try:
        storage_limit = UsageLimit.objects.get(
            workspace=workspace, category=category
        ).value
    except UsageLimit.MultipleObjectsReturned:
        # Handle multiple records - use the first one
        usage_limit = (
            UsageLimit.objects.filter(workspace=workspace, category=category)
            .order_by("created_at")
            .first()
        )
        storage_limit = usage_limit.value if usage_limit else 0
    except UsageLimit.DoesNotExist:
        # If no limit exists, return 0 available storage
        return 0

    res = storage_limit - storage_usage
    return res if res > 0 else 0


def initiate_usage_limit(workspace, category):
    meter, created = Meter.objects.get_or_create(workspace=workspace, category=category)
    if created:
        meter.count = 0
        meter.save()

    usage_limit, created = UsageLimit.objects.get_or_create(
        workspace=workspace, category=category
    )
    if created:
        try:
            # Use .first() instead of .get() to handle potential duplicates gracefully
            base_pricing = (
                BasePricing.objects.filter(
                    tier=STARTER_PRICING_TIER,
                    category=category,
                    payment_frequency=MONTHLY,
                    is_base_plan=False,
                )
                .order_by("created_at")
                .first()
            )
            limit = base_pricing.limit if base_pricing else 100
        except Exception:
            limit = 100
        usage_limit.value = limit
        usage_limit.save()
        archive_objects_by_limit(workspace, category)


def get_workspaces_without_category(category):
    """
    Returns workspaces that don't have UsageLimit record with targetted category.

    Args:
        category (str): The usage limit category to filter by.

    Returns:
        QuerySet: A QuerySet of Workspace objects.
    """

    return (
        Workspace.objects.exclude(subscription="partner")
        .annotate(
            has_category=Exists(
                UsageLimit.objects.filter(workspace=OuterRef("pk"), category=category)
            )
        )
        .filter(has_category=False)
    )
