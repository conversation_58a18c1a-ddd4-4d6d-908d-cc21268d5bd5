from django import template

from threading import current_thread

from data.constants.constant import OBJECT_GROUP_TYPE
from data.constants.properties_constant import (
    TYPE_OBJECT_CUSTOM_OBJECT,
    TYPE_OBJECT_TASK,
)
from data.models.campaign import EmailTemplate
from data.models.customer import Contact, Company, Worker
from data.models.customobject import CustomObject
from data.models.deals import Deals
from data.models.expensebill import Invoice, Slip, PurchaseItems, PurchaseOrders, Bill
from data.models.item import ShopTurboItems
from data.models.inventory import InventoryWarehouse
from data.models.integration import Channel
from data.models.message import Message
from data.models.subscription import ShopTurboSubscriptions
from data.models.task import Task
from data.models.user import UserManagement
from data.models.workspace import ObjectManager
from data.models.workflowaction import Action

from utils.logger import logger
from utils.properties.properties import (
    get_page_object,
    get_object_display_based_columns,
    get_prefix_rel,
    base_model_to_object_type,
)
from utils.utility import is_valid_uuid, get_workspace
from utils.inventory import get_and_update_inventory_amount

register = template.Library()
_requests = {}


@register.filter()
def get_object_from_basemodel_id(id, page_group_type):
    page_obj = get_page_object(page_group_type)
    try:
        obj = page_obj["base_model"].objects.get(id=id)
        return obj
    except:
        return None


@register.filter()
def object_group_type(value, request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)

    if value:
        if is_valid_uuid(value):
            custom_object = CustomObject.objects.filter(
                id=value, workspace=workspace
            ).first()
            if custom_object:
                return custom_object.name
            return ""
        else:
            try:
                return OBJECT_GROUP_TYPE[value][lang]
            except:
                pass
            try:
                custom_object = CustomObject.objects.filter(
                    slug=value, workspace=workspace
                ).first()
                return custom_object.name
            except:
                pass
    return value


@register.filter(name="get_contact_obj")
def get_contact_obj(contact_id):
    if contact_id:
        if is_valid_uuid(contact_id):
            try:
                contact = Contact.objects.get(id=contact_id)
                return contact
            except:
                return
    return


@register.filter(name="get_company_obj")
def get_company_obj(company_id):
    if company_id:
        if is_valid_uuid(company_id):
            try:
                company = Company.objects.get(id=company_id)
                return company
            except:
                return
    return


@register.filter(name="get_subscriptions_obj")
def get_subscriptions_obj(subscriptions_id):
    if subscriptions_id:
        if is_valid_uuid(subscriptions_id):
            try:
                subs = ShopTurboSubscriptions.objects.get(id=subscriptions_id)
                return subs
            except:
                return
    return


@register.filter(name="get_invoice_obj")
def get_invoice_obj(invoice_id):
    if invoice_id:
        if is_valid_uuid(invoice_id):
            try:
                invoice = Invoice.objects.get(id=invoice_id)
                return invoice
            except:
                return
    return


@register.filter(name="return_slip_obj")
def return_slip_obj(request=None):
    workspace = get_workspace(request.user)
    obj = Slip.objects.create(workspace=workspace)
    obj.delete()
    return obj  # Only to get object for property


@register.filter(name="get_subscription_obj")
def get_subscription_obj(object_id):
    if object_id:
        if is_valid_uuid(object_id):
            try:
                object = ShopTurboSubscriptions.objects.get(id=object_id)
                return object
            except:
                return
    return


@register.filter(name="get_item_obj")
def get_item_obj(item_id, workspace=None):
    """This function is to get item object from item_id
    And this function will check if the item_id is a valid uuid
    - Args:
        item_id (str): item id
        workspace (Workspace): An optional workspace to filter on.
    - Returns:
        item_obj (ShopTurboItems): item object
    """
    logger.debug(
        f"DEBUG get_item_obj: Called with item_id='{item_id}', workspace={workspace}"
    )

    if not item_id or not is_valid_uuid(item_id):
        logger.debug(f"DEBUG get_item_obj: Invalid item_id or not UUID: '{item_id}'")
        return None
    try:
        query = ShopTurboItems.objects

        # If workspace is explicitly provided, use it
        if workspace:
            query = query.filter(workspace=workspace)
            logger.debug(f"DEBUG get_item_obj: Using explicit workspace: {workspace}")
        else:
            # Try to get workspace from current request context
            try:
                request = get_current_request()
                if (
                    request
                    and hasattr(request, "user")
                    and request.user.is_authenticated
                ):
                    workspace = get_workspace(request.user)
                    if workspace:
                        query = query.filter(workspace=workspace)
                        logger.debug(
                            f"DEBUG get_item_obj: Using request workspace: {workspace}"
                        )
                    else:
                        logger.debug(
                            "DEBUG get_item_obj: No workspace found in request context"
                        )
                else:
                    logger.debug("DEBUG get_item_obj: No valid request context")
            except Exception as e:
                logger.error(f"Error getting workspace context in get_item_obj: {e}")
                logger.debug(
                    f"DEBUG get_item_obj: Error getting workspace context: {e}"
                )
                # Continue without workspace filter as fallback

        item = query.get(id=item_id)
        logger.debug(f"DEBUG get_item_obj: Found item: {item.name} (ID: {item.id})")
        return item
    except ShopTurboItems.DoesNotExist:
        logger.debug(f"DEBUG get_item_obj: Item not found for ID: {item_id}")
        return None
    except Exception as e:
        logger.error(f"Error in get_item_obj for item_id {item_id}: {e}")
        logger.debug(f"DEBUG get_item_obj: Exception: {e}")
        return None


@register.filter(name="get_channel_obj")
def get_channel_obj(channel_id):
    if channel_id:
        if is_valid_uuid(channel_id):
            try:
                # Note: This template filter doesn't have access to request/workspace context
                # Consider passing workspace as a parameter if workspace filtering is needed
                channel = Channel.objects.get(id=channel_id)
                return channel
            except:
                return None
    return None


@register.filter()
def get_warehouse_obj(id):
    try:
        return InventoryWarehouse.objects.get(id=id)
    except:
        return None


@register.simple_tag()
def get_task_obj(request=None, task_id=None, project_id=None):
    workspace = get_workspace(request.user)
    if task_id:
        if project_id:
            tasks = (
                Task.objects.filter(
                    workspace=workspace, title__isnull=False, project_target=project_id
                )
                .exclude(id=task_id)
                .order_by("-id")
            )
        else:
            tasks = (
                Task.objects.filter(workspace=workspace, title__isnull=False)
                .exclude(id=task_id)
                .order_by("-id")
            )
    else:
        if project_id:
            tasks = Task.objects.filter(
                workspace=workspace, title__isnull=False, project_target=project_id
            ).order_by("-id")
        else:
            tasks = Task.objects.filter(
                workspace=workspace, title__isnull=False
            ).order_by("-id")
    return tasks


@register.filter(name="get_items_obj")
def get_items_obj(request=None):
    workspace = get_workspace(request.user)
    shopturbo_items = ShopTurboItems.objects.filter(
        workspace=workspace, status="active"
    ).order_by("-item_id")
    return shopturbo_items


@register.filter(name="get_purchase_item_obj")
def get_purchase_item_obj(request=None):
    workspace = get_workspace(request.user)
    purchase_order_items = PurchaseItems.objects.filter(
        workspace=workspace, usage_status="active"
    ).order_by("-id_item")
    return purchase_order_items


@register.filter()
def get_bill_obj(id):
    if id and is_valid_uuid(id):
        try:
            return Bill.objects.get(id=id)
        except:
            pass
    return None


@register.filter()
def get_purchase_order_obj(id):
    if id and is_valid_uuid(id):
        try:
            return PurchaseOrders.objects.get(id=id)
        except:
            pass
    return None


@register.filter()
def get_cases_deals_obj(id):
    if id and is_valid_uuid(id):
        try:
            return Deals.objects.get(id=id)
        except:
            pass
    return None


@register.filter(name="get_template_obj")
def get_template_obj(template_id):
    if template_id:
        if is_valid_uuid(template_id):
            try:
                template = EmailTemplate.objects.get(id=template_id)
                return template
            except:
                return
    return


@register.filter(name="get_reply_line_message")
def get_reply_line_message(reply_token):
    if reply_token:
        try:
            message = Message.objects.filter(
                reply_token=reply_token, contact_sender__isnull=False
            ).first()
            return message
        except:
            return
    return


@register.filter()
def get_workspace_obj(request):
    return get_workspace(request.user)


@register.filter()
def get_object(page_group_type, id):
    if not is_valid_uuid(id):
        return None

    try:
        page_obj = get_page_object(page_group_type)
        return page_obj["base_model"].objects.get(id=id)
    except Exception as e:
        logger.debug(f"... ERROR === custom_tags.py -- 3961: {e}")
        return None


@register.filter()
def get_object_id(obj, page_group_type):
    page_obj = get_page_object(page_group_type)
    if "id_field" in page_obj:
        if page_obj["id_field"]:
            return getattr(obj, page_obj["id_field"])
    return 0


@register.filter()
def get_custom_model(page_group_type, id):
    if not is_valid_uuid(id):
        return None

    try:
        page_obj = get_page_object(page_group_type)
        custom_model = page_obj["custom_model"]

        return custom_model.objects.get(id=id)
    except Exception as e:
        logger.debug(f"... ERROR === custom_tags.py -- 3961: {e}")
        return None


@register.filter()  # For Task Obj
def get_production_line_value(task, custom_model_id, object_type=TYPE_OBJECT_TASK):
    if not is_valid_uuid(custom_model_id):
        return None

    try:
        page_obj = get_page_object(object_type)
        custom_model = page_obj["custom_model"]
        custom_value_model = page_obj["custom_value_model"]

        custom_model_name = (
            custom_model.objects.filter(id=custom_model_id).order_by("order").first()
        )
        custom_value = custom_value_model.objects.filter(
            field_name=custom_model_name, task=task
        ).first()
        if custom_value:
            return custom_value.value
        else:
            return ""
    except Exception as e:
        logger.debug(
            f'... ERROR === custom_tags.py "get_production_line_value" -- 3961: {e}'
        )
        return ""


@register.filter()
def get_list_objects(page_group_type, workspace):
    try:
        page_obj = get_page_object(page_group_type)
        base_model = page_obj["base_model"]
        return base_model.objects.filter(workspace=workspace).order_by("-created_at")
    except Exception as e:
        logger.debug(f"... ERROR === custom_tags.py -- 3852: {e}")
        return []


@register.simple_tag(takes_context=True)
def get_object_display(context, object, page_group_type, object_manager=None):
    request = context["request"]
    workspace = get_workspace(request.user)

    om = None
    if object_manager:
        om = object_manager
    elif page_group_type == TYPE_OBJECT_CUSTOM_OBJECT:
        om, created = ObjectManager.objects.get_or_create(
            workspace=workspace,
            custom_object=object.custom_object,
            page_group_type=page_group_type,
        )
    else:
        om, created = ObjectManager.objects.get_or_create(
            workspace=workspace, page_group_type=page_group_type
        )

    try:
        if not om.column_display:
            id_field = get_page_object(page_group_type)["id_field"]
            om.column_display = id_field
            om.save()
        columns = om.column_display.split(",")
    except Exception as e:
        logger.debug(f"ERROR === custom_tags.py: {e}")
        return ""

    try:
        res = get_object_display_based_columns(
            page_group_type, object, columns, workspace.timezone, request.LANGUAGE_CODE
        )
    except Exception as e:
        logger.debug(f"ERROR === custom_tags.py: {e}")
        # DiscordNotification().send_message(str(e))
        return ""

    return res


@register.simple_tag()
def get_location_object(request):
    workspace = get_workspace(request.user)
    return InventoryWarehouse.objects.filter(
        workspace=workspace, usage_status="active"
    ).order_by("-id_iw")


@register.filter()
def get_related_custom_object_name(column):
    if "related_custom_object" in column:
        related_custom_object = column.split("|")
        if len(related_custom_object) == 3:
            return related_custom_object[1]
    return ""


@register.filter()
def get_task(task_id):
    try:
        return Task.objects.get(id=task_id)
    except:
        return None


@register.filter()
def get_user_management(user_management_id):
    try:
        return UserManagement.objects.get(id=user_management_id)
    except:
        return None


@register.filter()
def get_employee(employee_id):
    try:
        return Worker.objects.get(id=employee_id)
    except:
        return None


@register.filter
def get_action_name(action_uid, lang):
    try:
        action = Action.objects.get(uid=action_uid)
    except:
        return ""
    if lang == "ja":
        return action.title_ja
    return action.title


@register.filter
def get_item(dictionary, key):
    if dictionary:
        return dictionary.get(key, None)
    return None


@register.simple_tag
def get_item_with_prefix(base_source, dictionary, key):
    if dictionary:
        if " - " in key:
            rel, key = key.split(" - ", 1)
            prefix = get_prefix_rel(base_source, rel)
            if "custom_field__" in key:
                key = key.replace("custom_field__", "")
                tmp_prefix = prefix.replace("__", "")
                key = f"{tmp_prefix}_cf_{key}"
            else:
                key = f"{prefix}{key}"

        key = f"{rel} - {key}"
        return dictionary.get(key, None)
    return None


@register.filter()
def get_inventory_warehouse_name(obj_id, request=None):
    if not obj_id:
        return ""
    warehouse = InventoryWarehouse.objects.get(pk=obj_id)
    if warehouse.location:
        return warehouse.location

    name = ""
    if warehouse.warehouse:
        name += "-" + warehouse.warehouse
    if warehouse.floor:
        name += "-" + warehouse.floor
    if warehouse.zone:
        name += "-" + warehouse.zone
    if warehouse.aisle:
        name += "-" + warehouse.aisle
    if warehouse.rack:
        name += "-" + warehouse.rack
    if warehouse.shelf:
        name += "-" + warehouse.shelf
    if warehouse.bin:
        name += "-" + warehouse.bin
    if name:
        if name[0] == "-":
            name = name[1:]
        warehouse.location = name
        warehouse.save()
        return name
    return name


@register.filter()
def get_inventory_future_amount_display(inventory):
    try:
        # Get the status from the request
        request = get_current_request()
        status = request.GET.get("status") if request else None

        # Filter transactions based on status
        if status == "archived":
            last_transaction = (
                inventory.inventorytransaction_set.filter(usage_status="archived")
                .order_by("-transaction_date", "-created_at")
                .first()
            )
        else:
            last_transaction = (
                inventory.inventorytransaction_set.filter(usage_status="active")
                .order_by("-transaction_date", "-created_at")
                .first()
            )

        if last_transaction:
            logger.debug(
                f"[DEBUG inventory] Future amount from transaction for inventory {inventory.id}: {last_transaction.transaction_amount}"
            )
            return last_transaction.transaction_amount
        else:
            # If no transactions exist, fall back to total_inventory field instead of initial_value
            fallback_value = (
                inventory.total_inventory
                if inventory.total_inventory is not None
                else inventory.initial_value
            )
            logger.debug(
                f"[DEBUG inventory] No transactions for future amount, using fallback for inventory {inventory.id}: {fallback_value}"
            )
            return fallback_value
    except Exception as e:
        logger.debug(
            f"[ERROR inventory] Error in get_inventory_future_amount_display for inventory {inventory.id if inventory else 'None'}: {e}"
        )
        return 0


@register.filter()
def get_and_update_inventory_amount_display(inventory):
    return get_and_update_inventory_amount(inventory)


@register.filter()
def get_object_relation_by_custom_object(rows, custom_object):
    try:
        return rows.filter(custom_object=custom_object)
    except Exception as e:
        logger.debug("[DEBUG] get_object_relation_by_custom_object: %s", e)
        return []


def get_current_request():
    """Get the current request from thread local storage."""
    t = current_thread()
    if t not in _requests:
        return None
    return _requests[t]


@register.filter
def get(dict_obj, key):
    return dict_obj.get(key, "")


@register.filter
def index(list_or_tuple, index):
    try:
        return list_or_tuple[index]
    except:
        return ""


@register.filter
def content_type_to_page_group_type(content_type):
    """
    Convert a Django ContentType model name to the corresponding page_group_type.

    Args:
        content_type: Django ContentType instance

    Returns:
        str: The corresponding TYPE_OBJECT_* constant or None if not found
    """
    if not content_type:
        return None

    try:
        # Get the model class from the content type
        model_class = content_type.model_class()
        if not model_class:
            return None

        # Use the existing base_model_to_object_type function to get the page_group_type
        return base_model_to_object_type(model_class)
    except Exception as e:
        logger.debug(f"Error converting content type to page group type: {e}")
        return None
