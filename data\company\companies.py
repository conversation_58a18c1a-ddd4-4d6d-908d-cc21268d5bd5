import ast
import re
import json
import logging

from django.core.paginator import EmptyPage, PageNotAnInteger, Paginator
from django.db.models import Case, IntegerField, OuterRef, Subquery, Sum, When, Q
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import redirect, render
from django_hosts.resolvers import reverse

from data.commerce import get_page_object
from data.constants.contact_constant import (
    CONTACT_APP_SLUG,
    CONTACT_APP_TARGET,
)
from data.constants.properties_constant import (
    OBJECT_TYPE_TO_SLUG,
    TYPE_OBJECT_COMPANY,
    TYPE_OBJECT_ORDER,
)
from data.models import (
    AdvanceSearchFilter,
    AppSetting,
    Company,
    CompanyFile,
    CompanyNameCustomField,
    CompanyNotes,
    CompanyPlatforms,
    CompanyValueCustomField,
    Contact,
    Log,
    PropertySet,
    SocialAccountLink,
    View,
    ViewFilter,
)
from utils.decorator import login_or_hubspot_required
from utils.eccube import *
from utils.filter import build_view_filter
from utils.freee_bg_jobs.freee import *
from utils.hubspot import *
from utils.logger import logger
from utils.project import get_ordered_views
from utils.properties.properties import get_default_property_set
from utils.salesforce import *
from utils.serializer import *
from utils.twitter import *
from utils.utility import is_valid_uuid, modular_view_filter, get_permission_filter
from utils.workspace import get_permission

from data.company.background.export_csv_company import (
    export_csv_company,
    ExportCSVCompanyPayload,
)
from utils.bgjobs.handler import create_bg_job, add_hatchet_run_id

logger = logging.getLogger(__name__)


@login_or_hubspot_required
def companies(request, id=None):
    lang = request.LANGUAGE_CODE
    target = TYPE_OBJECT_COMPANY
    page_group_type = "company"
    workspace = get_workspace(request.user)

    page_obj = get_page_object(TYPE_OBJECT_COMPANY, lang)
    base_model = page_obj["base_model"]
    custom_value_model = page_obj["custom_value_model"]
    custom_value_relation = page_obj["custom_value_relation"]
    custom_value_file_model = page_obj["custom_value_file_model"]
    custom_value_file_relation = page_obj["custom_value_file_relation"]
    additional_filter_fields = page_obj["additional_filter_fields"]
    id_field = page_obj["id_field"]

    if request.method == "GET":
        if lang == "ja":
            page_title = "企業"
        else:
            page_title = "Companies"

        permission = get_permission(object_type=page_group_type, user=request.user)
        if not permission:
            permission = DEFAULT_PERMISSION

        if permission == "hide":
            context = {
                "permission": permission,
                "page_title": page_title,
                "object_type": TYPE_OBJECT_COMPANY,
                "view_id": None,
                "view_filter": None,
                "property_sets": None,
                "set_id": None,
                "menu_key": None,
                "open_drawer": None,
            }
            return render(request, "data/companies/companies.html", context)

        # view
        config_view = None
        view_filter = None
        app_setting, _ = AppSetting.objects.get_or_create(
            workspace=workspace, app_target=CONTACT_APP_TARGET
        )

        if not app_setting.search_setting_company:
            app_setting.search_setting_company = "name,email"
            app_setting.save()

        view_id = request.GET.get("view_id", None)
        company_view_id = view_id
        views = get_ordered_views(workspace, target, user=request.user)
        view_filter = modular_view_filter(
            workspace,
            target,
            view_id=view_id,
            column_view=DEFAULT_COLUMNS_COMPANY.copy(),
            user=request.user,
        )

        companies_columns = view_filter.column

        # Determine records per page from view_filter
        records_per_page = view_filter.pagination
        if (
            not records_per_page
        ):  # If view_filter.pagination is None or 0 or empty string
            records_per_page = 25  # Default value
        else:
            try:
                records_per_page = int(records_per_page)
                if records_per_page <= 0:  # ensure positive integer
                    records_per_page = 25
            except ValueError:
                records_per_page = 25  # Default if conversion fails

        # This offset is used for calculating paginator_item_begin correctly
        # (page_number - 1) * records_per_page + 1
        # The existing formula structure: (records_per_page * page_number) - (records_per_page - 1)
        records_per_page_offset_for_calc = records_per_page - 1

        if not config_view:
            config_view = "list"

        Log.objects.create(
            workspace=workspace,
            user=request.user,
            page_name="companies",
            sub_page_name="all companies",
        )
        filter_conditions = Q(workspace=workspace)

        filter_conditions &= get_permission_filter(permission, request.user)
        # Handle status filter for archived companies
        status = request.GET.get("status")
        if status == "archived":
            filter_conditions &= Q(status="archived")
        else:
            # By default, show only active companies
            filter_conditions &= Q(status="active")

        advance_search = AdvanceSearchFilter.objects.filter(
            workspace=workspace, object_type=TYPE_OBJECT_COMPANY, type="default"
        ).first()
        if not advance_search:
            advance_search = None

        if advance_search:
            if advance_search.search_settings:
                app_setting.search_setting_company = advance_search.search_settings
                app_setting.save()

        search_q = request.GET.get("q")
        if search_q:
            match_special_char = re.search(r"#(\d+)", search_q)
            if match_special_char:
                number = match_special_char.group(1)  # Get the number part
                if number:
                    filter_conditions &= Q(company_id=number)
            else:
                search_q = search_q.lower()

                search_keys = search_q.split()
                search_filters = Q()

                for search_q in search_keys:
                    search_filters |= Q(company_id__icontains=search_q)
                    if app_setting.search_setting_company:
                        search_fields = app_setting.search_setting_company.split(",")
                        for s_field in search_fields:
                            search_filters |= apply_search_setting(
                                "company", view_filter, s_field, search_q
                            )
                filter_conditions &= search_filters

        advance_search_filter = None
        if advance_search:
            if advance_search.is_active:
                advance_search_filter = advance_search.search_filter
                advance_search_filter_status = advance_search.search_filter_status
                if advance_search_filter_status:
                    advance_search_filter = {
                        k: v
                        for k, v in advance_search_filter.items()
                        if v["value"] != ""
                        and advance_search_filter_status.get(k) != "False"
                    }
                else:
                    advance_search_filter = {
                        k: v
                        for k, v in advance_search_filter.items()
                        if v["value"] != ""
                    }
                try:
                    if (
                        advance_search_filter.get("usage_status", {}).get("value")
                        == "all"
                    ):
                        del advance_search_filter["usage_status"]
                except KeyError:
                    pass

                view_filter.filter_value = advance_search_filter

        filter_conditions = build_view_filter(
            filter_conditions, view_filter, TYPE_OBJECT_COMPANY, user=request.user
        )
        all_companies_qs = (
            Company.objects.filter(filter_conditions, workspace=workspace)
            .order_by("-created_at")
            .distinct()
        )

        # Apply sorting based on view filter settings
        companies_base_qs = Company.objects.filter(
            filter_conditions, workspace=workspace
        )
        try:
            if view_filter.sort_order_by:
                order_method = view_filter.sort_order_method
                order_by = view_filter.sort_order_by

                if is_valid_uuid(order_by):
                    field_name = CompanyNameCustomField.objects.filter(
                        id=order_by
                    ).first()
                    if field_name:
                        custom_value_subquery = CompanyValueCustomField.objects.filter(
                            company=OuterRef("pk"), field_name=field_name
                        )

                        if field_name.type in ["date", "date_time"]:
                            custom_value_subquery = custom_value_subquery.values(
                                "value_time"
                            )[:1]
                        else:
                            custom_value_subquery = custom_value_subquery.values(
                                "value"
                            )[:1]

                        companies_base_qs = companies_base_qs.annotate(
                            custom_value=Subquery(custom_value_subquery)
                        )

                        if order_method == "asc":
                            companies_base_qs = (
                                companies_base_qs.distinct("custom_value", "id")
                                .values("id", "custom_value")
                                .order_by("custom_value")
                            )
                        else:
                            companies_base_qs = (
                                companies_base_qs.distinct("custom_value", "id")
                                .values("id", "custom_value")
                                .order_by("-custom_value")
                            )
                else:
                    # Standard field sorting
                    if order_method == "asc":
                        companies_base_qs = (
                            companies_base_qs.distinct(order_by, "id")
                            .values("id")
                            .order_by(order_by)
                        )
                    else:
                        companies_base_qs = (
                            companies_base_qs.distinct(order_by, "id")
                            .values("id")
                            .order_by("-" + order_by)
                        )
            else:
                companies_base_qs = (
                    companies_base_qs.distinct("company_id", "id")
                    .values("id")
                    .order_by("-company_id")
                )
        except Exception as e:
            companies_base_qs = (
                companies_base_qs.distinct("company_id", "id")
                .values("id")
                .order_by("-company_id")
            )

        company_id = request.GET.get("company_id", None)

        # Property Set
        default_property_set = get_default_property_set(
            TYPE_OBJECT_COMPANY, workspace, lang
        )  # default property set ---- DO NOT DELETE!!!
        property_sets = PropertySet.objects.filter(
            workspace=workspace, target=TYPE_OBJECT_COMPANY
        ).order_by("created_at")

        set_id = request.GET.get("set_id")
        if view_filter.view.form:
            set_id = view_filter.view.form.id
        else:
            if not set_id:
                property_set_default = property_sets.filter(as_default=True).first()
                if property_set_default:
                    set_id = property_set_default.id

        paginator = Paginator(companies_base_qs, records_per_page)
        page_number = request.GET.get("page", 1)
        try:
            page_content = paginator.page(page_number)
        except PageNotAnInteger:
            page_content = paginator.page(1)
            page_number = 1
        except EmptyPage:
            page_content = paginator.page(paginator.num_pages)
            page_number = paginator.num_pages

        # Get the actual company objects for the current page
        company_ids = page_content.object_list
        if company_ids and hasattr(company_ids[0], "get"):
            # If we have a values queryset, extract the IDs
            company_ids = [item["id"] for item in company_ids]

        # Preserve the order by using the sorted IDs
        if company_ids:
            # Create a case statement to preserve the order
            preserved_order = Case(
                *[When(pk=pk, then=pos) for pos, pk in enumerate(company_ids)],
                output_field=IntegerField(),
            )
            companies_on_page = Company.objects.filter(
                id__in=company_ids, workspace=workspace
            ).order_by(preserved_order)
        else:
            companies_on_page = Company.objects.none()

        # Calculate custom field formula All - NOW ONLY FOR THE CURRENT PAGE
        company_name_custom_fields = CompanyNameCustomField.objects.filter(
            workspace=workspace, type="formula", choice_value__isnull=False
        )
        for company_name_custom_field in company_name_custom_fields:
            for company in companies_on_page:  # Iterate over companies_on_page
                result = calculate_math(company_name_custom_field, company, "company")
                if result:
                    CustomFieldValue, _ = CompanyValueCustomField.objects.get_or_create(
                        field_name=company_name_custom_field, company=company
                    )
                    CustomFieldValue.value = float(result)
                    CustomFieldValue.save()

        paginator_item_begin = (
            records_per_page * int(page_number)
        ) - records_per_page_offset_for_calc
        paginator_item_end = records_per_page * int(page_number)
        if paginator_item_end > paginator.count:
            paginator_item_end = paginator.count

        members = []
        groups = request.user.group_set.all()
        for group in groups:
            ids = group.user.all().values_list("id", flat=True)
            members.extend(ids)

        members = list(set(members))
        members = ",".join(str(id) for id in members)

        context = {
            "companies_columns": companies_columns,
            "companies": companies_on_page,  # Use companies_on_page
            "workspace": workspace,
            "search_q": request.GET.get("q", ""),
            "config_view": config_view,
            "page_title": page_title,
            "app_slug": CONTACT_APP_SLUG,
            "paginator_item_begin": paginator_item_begin,
            "paginator_item_end": paginator_item_end,
            "page_content": page_content,
            "paginator": paginator,
            "view_id": company_view_id,
            "company_id": company_id,
            "views": views,
            "view_filter": view_filter,
            "target": target,
            "object_type": TYPE_OBJECT_COMPANY,  # Add object_type for template consistency
            "permission": permission,
            "property_sets": property_sets,
            "set_id": set_id,
            "advance_search": advance_search,
            "advance_search_filter": advance_search_filter,
            "group_members": members,
        }

        return render(request, "data/companies/companies.html", context)

    # Special Functionality
    elif request.method == "POST":
        # Download
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_COMPANY]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_COMPANY
            )
            .order_by("order", "created_at")
            .first()
        )
        if module:
            module_slug = module.slug

        if "download-contacts-button" in request.POST:
            history = TransferHistory.objects.create(
                workspace=workspace,
                user=request.user,
                status="running",
                type="export_company",
            )

            encoded_format = request.POST.get("encoded_format", None)
            columns = request.POST.get("column", None)
            view_id = request.POST.get("view_id", None)

            filter_dictionary = {}
            filter_types = request.POST.getlist("filter_type", None)
            filter_options = request.POST.getlist("filter_options", None)
            filter_values = request.POST.getlist("filter_value", None)
            for idx, filter_type in enumerate(filter_types):
                filter_dictionary[str(filter_type)] = {
                    "key": filter_options[idx],
                    "value": filter_values[idx],
                }

            filter_dictionary = json.dumps(filter_dictionary)

            object_ids = request.POST.getlist("object_ids")
            object_ids = json.dumps(object_ids) if object_ids else None
            payload = ExportCSVCompanyPayload(
                user_id=str(request.user.id),
                workspace_id=str(workspace.id),
                view_id=str(view_id),
                history_id=str(history.id),
                columns=columns,
                filter_dictionary=filter_dictionary,
                encoded_format=encoded_format,
                record_ids=object_ids,
                language=lang,
                target=TYPE_OBJECT_COMPANY,
            )

            job_id = create_bg_job(
                workspace,
                request.user,
                "export_csv_company",
                transfer_history=history,
                payload=payload.model_dump(mode="json"),
            )
            payload.background_job_id = job_id

            # Log export job parameters for debugging
            logger.info(
                f"EXPORT_JOB: Starting companies export for user {request.user.email} in workspace {workspace.id}"
            )
            logger.info(
                f"EXPORT_JOB: Export parameters - function: export_csv_company, job_id: {job_id}"
            )
            logger.info(
                f"EXPORT_JOB: View ID: {view_id}, History ID: {history.id}, Language: {lang}"
            )
            logger.info(f"EXPORT_JOB: Columns: {columns}")
            logger.info(f"EXPORT_JOB: Filter dictionary: {filter_dictionary}")

            ref = None
            try:
                ref = export_csv_company.run_no_wait(input=payload)
            except Exception as e:
                logger.error(
                    f"EXPORT_JOB: Exception occurred during export_csv_company: {str(e)}",
                    exc_info=True,
                )
                ref = None

            is_running = None
            if ref:
                logger.info(
                    f"EXPORT_JOB: Background job submitted successfully for user {request.user.email}"
                )
                add_hatchet_run_id(job_id, ref)
                is_running = True
            else:
                logger.error(
                    f"EXPORT_JOB: Failed to submit background job for user {request.user.email}"
                )
                is_running = False

            if is_running:
                logger.info(
                    f"EXPORT_JOB: Successfully submitted export job for user {request.user.email}"
                )
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="企業のエクスポートジョブが正常に送信されました。CSVはメールで送信されます。",
                        type="success",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Export job submitted successfully, CSV file will be sent to your email.",
                        type="success",
                    )
            else:
                logger.error(
                    f"EXPORT_JOB: Failed to submit companies export job for user {request.user.email} in workspace {workspace.id}"
                )
                logger.error(
                    f"EXPORT_JOB: trigger_bg_job returned falsy value: {is_running}"
                )
                logger.error(
                    f"EXPORT_JOB: Parameters used: {payload.model_dump(mode='json')}"
                )
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="エクスポートジョブの送信中にエラーが発生しました。サポートに連絡してください。",
                        type="error",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="There is an error while submitting the export job. Please contact support.",
                        type="error",
                    )

            if module:
                return HttpResponseRedirect(
                    request.META.get(
                        "HTTP_REFERER",
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module,
                                "object_slug": module_object_slug,
                            },
                        ),
                    )
                )
            return HttpResponseRedirect(
                request.META.get("HTTP_REFERER", reverse("main", host="app"))
            )
            
        # NOTE: cancel_task
        elif "cancel_task" in request.POST:
            task_id = request.POST.get("task_id", None)
            if task_id:
                history = TransferHistory.objects.filter(id=task_id).first()
                if history:
                    history.status = "canceled"
                    history.save(update_fields=["status"])

                    job = BackgroundJob.objects.filter(transfer_history=history).first()
                    if job:
                        job.status = BackgroundJob.BackgroundJobStatus.CANCELED
                        if job.hatchet_run_id:
                            try:
                                from utils.bgjobs.hatchet_client import hatchet
                                from hatchet_sdk import BulkCancelReplayOpts

                                bulk_cancel_by_ids = BulkCancelReplayOpts(
                                    ids=[str(job.hatchet_run_id)]
                                )
                                hatchet.runs.bulk_cancel(bulk_cancel_by_ids)
                            except Exception as e:
                                logger.error(f"Error canceling hatchet run: {e}")
                        job.save(update_fields=["status"])

        elif "bulk_restore_companies" in request.POST:
            if "flag_all" in request.POST:
                if request.POST.get("flag_all") == "true":
                    companies = Company.objects.filter(workspace=workspace)
                else:
                    companies = Company.objects.filter(
                        id__in=request.POST.getlist("checkbox")
                    )
            else:
                companies = Company.objects.filter(
                    id__in=request.POST.getlist("checkbox")
                )

            companies.update(status="active")

            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))
        elif "bulk_delete_companies" in request.POST:
            if "flag_all" in request.POST:
                if request.POST.get("flag_all") == "true":
                    companies = Company.objects.filter(workspace=workspace)
                else:
                    companies = Company.objects.filter(
                        id__in=request.POST.getlist("checkbox")
                    )
            else:
                companies = Company.objects.filter(
                    id__in=request.POST.getlist("checkbox")
                )

            companies.update(status="archived")

            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))
        elif "bulk_permanent_delete_companies" in request.POST:
            from utils.meter import sync_usage
            from data.constants.constant import COMPANY_USAGE_CATEGORY
            from utils.utility import build_redirect_url
            
            logger.info(
                f"PERMANENT DELETE: Handler triggered by user {request.user.email} in workspace {workspace.id}"
            )
            logger.info(f"PERMANENT DELETE: Request GET params: {dict(request.GET)}")
            logger.info(f"PERMANENT DELETE: Request POST params: {dict(request.POST)}")
            
            # Get view_id from request for redirect
            view_id = request.POST.get("view_id")
            
            # Safety check: Only allow permanent deletion when viewing archived companies
            # Check both GET and POST for status parameter
            status = request.GET.get("status") or request.POST.get("status")
            if status != "archived":
                logger.warning(
                    f"PERMANENT DELETE: Blocked - not on archived page. Status from GET: {request.GET.get('status')}, Status from POST: {request.POST.get('status')}"
                )
                # Redirect back with error message if not on archived page
                return HttpResponseRedirect(
                    request.META.get("HTTP_REFERER", reverse("main", host="app"))
                )
            
            # Get companies to permanently delete
            checkbox_ids = request.POST.getlist("checkbox")
            logger.info(f"PERMANENT DELETE: Checkbox IDs received: {checkbox_ids}")
            
            if "flag_all" in request.POST:
                if request.POST.get("flag_all") == "true":
                    companies = Company.objects.filter(workspace=workspace, status="archived")
                    logger.info(f"PERMANENT DELETE: flag_all=true, found {companies.count()} archived companies")
                else:
                    companies = Company.objects.filter(
                        id__in=checkbox_ids,
                        workspace=workspace,
                        status="archived"
                    )
                    logger.info(f"PERMANENT DELETE: flag_all=false, found {companies.count()} companies from checkboxes")
            else:
                companies = Company.objects.filter(
                    id__in=checkbox_ids,
                    workspace=workspace,
                    status="archived"
                )
                logger.info(f"PERMANENT DELETE: No flag_all, found {companies.count()} companies from checkboxes")
            
            if companies.exists():
                # Collect information for logging before deletion
                company_ids = list(companies.values_list("id", flat=True))
                company_names = list(companies.values_list("name", flat=True))
                
                logger.info(
                    f"PERMANENT DELETE: About to delete {companies.count()} companies"
                )
                
                # Delete related data before deleting companies
                # Delete related custom fields
                CompanyValueCustomField.objects.filter(
                    company__in=companies
                ).delete()
                
                # Delete related social account links if they exist
                if hasattr(companies.first(), 'links'):
                    SocialAccountLink.objects.filter(
                        company__in=companies
                    ).delete()
                
                # Handle M2M relationships - contacts will be automatically handled by Django
                # But log the affected contacts
                affected_contacts = Contact.objects.filter(companies__in=companies).distinct()
                if affected_contacts.exists():
                    logger.info(
                        f"PERMANENT DELETE: {affected_contacts.count()} contacts will be unlinked from deleted companies"
                    )
                
                # Delete related files
                CompanyFile.objects.filter(company__in=companies).delete()
                
                # Delete related notes
                CompanyNotes.objects.filter(company__in=companies).delete()
                
                # Delete related platforms
                CompanyPlatforms.objects.filter(company__in=companies).delete()
                
                # Finally, permanently delete the companies themselves
                deleted_count = companies.count()
                companies.delete()
                
                # Log the permanent deletion for audit purposes
                logger.info(
                    f"Permanently deleted {deleted_count} companies from workspace {workspace.id}: "
                    f"IDs {company_ids}, Names: {company_names}, User: {request.user.email}"
                )
                
                # Note: ID sequence is preserved automatically by SequenceTracker
                # The auto-increment counter is maintained and not reset when records are deleted
                
                # Update usage metrics
                sync_usage(workspace, COMPANY_USAGE_CATEGORY)
            
            # Calculate appropriate page after bulk permanent delete operation
            # Simple inline function to get appropriate page number after bulk operations
            try:
                appropriate_page = int(request.GET.get("page", 1))
                appropriate_page = max(1, appropriate_page)  # Ensure page is at least 1
            except (ValueError, TypeError):
                appropriate_page = 1
            
            # Redirect with preserved pagination
            if module:
                redirect_url = build_redirect_url(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    ),
                    view_id=view_id,
                    page=appropriate_page,
                    status="archived"  # Keep on archived page
                )
                return redirect(redirect_url)
            
            redirect_url = build_redirect_url(
                reverse("main", host="app"),
                page=appropriate_page,
                status="archived"
            )
            return redirect(redirect_url)
        elif "update-view-button" in request.POST:
            view_id = request.POST.get("view_id", None)
            view_name = request.POST.get("view_name", None)
            view_table = request.POST.get("view", None)
            archive = request.POST.get("archive", None)
            column = request.POST.get("column", None)
            kanban_unlisted = (
                True
                if request.POST.get("kanban_unlisted", False) == "on"
                else request.POST.get("kanban_unlisted", False)
            )
            status_selector = request.POST.get("status-selector", None)

            order_by = request.POST.get("order-by", None)
            sort_method = request.POST.get("sort-method", None)

            property_set_is_default_input = request.POST.get(
                "property_set_default", None
            )

            # update
            if view_id:
                view = View.objects.filter(id=view_id).first()
                if view.is_private and (view.user == request.user):
                    view.is_private = bool(request.POST.get("set-private-view", False))
                    view.user = request.user if view.is_private else None
                    view.save()
            # create
            else:
                is_private = bool(request.POST.get("set-private-view", False))
                user = request.user if is_private else None

                view = View.objects.create(
                    workspace=workspace,
                    title=view_name,
                    target=TYPE_OBJECT_COMPANY,
                    is_private=is_private,
                    user=user,
                )

            view_filter, _ = ViewFilter.objects.get_or_create(view=view)

            if property_set_is_default_input:
                default_set = PropertySet.objects.get(id=property_set_is_default_input)
                view.form = default_set
                default_set.as_default = True
                default_set.save()
                # check default
                PropertySet.objects.filter(
                    workspace=default_set.workspace, target=default_set.target
                ).exclude(id=default_set.id).update(as_default=False)

            if view_name:
                view.title = view_name

            if view_table:
                view_filter.view_type = view_table

            pagination_value = request.POST.get("pagination", None)
            if pagination_value and pagination_value.isdigit():
                view_filter.pagination = int(pagination_value)
            else:
                view_filter.pagination = 25  # Default value if not provided or invalid

            view_filter.kanban_unlisted = kanban_unlisted
            view_filter.archive = archive

            # Handle sorting parameters
            if order_by:
                view_filter.sort_order_by = order_by
            else:
                view_filter.sort_order_by = None

            if sort_method:
                view_filter.sort_order_method = sort_method
            else:
                if order_by:
                    view_filter.sort_order_method = "asc"
                else:
                    view_filter.sort_order_method = None

            # convert to normal List
            column = column.split(",")
            if view and column[0]:
                view_filter.column = column
            else:
                view_filter.column = None

            if view_table and status_selector:
                if view_table == "kanban":
                    view_filter.choice_customfield = status_selector
                else:
                    view_filter.choice_customfield = None

            filter_dictionary = {}
            filter_types = request.POST.getlist("filter_type", None)
            filter_options = request.POST.getlist("filter_options", None)
            filter_values = request.POST.getlist("filter_value", None)
            for idx, filter_type in enumerate(filter_types):
                filter_dictionary[str(filter_type)] = {
                    "key": filter_options[idx],
                    "value": filter_values[idx],
                }
            view_filter.filter_value = filter_dictionary
            view_filter.save()
            view.save()

            if view.title:
                if module:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                        + "?view_id="
                        + str(view_filter.view.id)
                    )
                return redirect(reverse("main", host="app"))
            else:
                if module:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                    )
                return redirect(reverse("main", host="app"))

        elif "delete-view-button" in request.POST:
            view_id = request.POST.get("view_id", None)
            if not view_id:
                return HttpResponse(status=400)

            view = View.objects.filter(id=view_id, workspace=workspace).first()
            if not view:
                if module:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                    )
                return redirect(reverse("main", host="app"))
            view.delete()

            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))
        elif "bulk_update_company" in request.POST:
            account_ids = request.POST.getlist("account_id", None)
            company_name = request.POST.get("company_name", None)
            address = request.POST.get("address", None)
            phone_number = request.POST.get("phone_number", None)
            email = request.POST.get("email", None)
            files = request.FILES.getlist("fileUpload")
            urls = request.POST.get("urls", None)
            status = request.POST.get("status", None)

            for account_id in account_ids:
                company = Company.objects.get(id=account_id)
                if "name|checkbox" in request.POST:
                    company.name = company_name

                if "img|checkbox" in request.POST:
                    pass

                if "email|checkbox" in request.POST:
                    company.email = email

                if "url|checkbox" in request.POST:
                    company.url = urls

                if "address|checkbox" in request.POST:
                    company.address = address

                if "phone_number|checkbox" in request.POST:
                    company.phone_number = phone_number

                if "status|checkbox" in request.POST:
                    company.status = status

                company.save()

        elif "bulk_duplicate" in request.POST:
            flag_all = request.POST.get("flag_all")
            if flag_all:
                view_id = request.POST.get("view_id")
                if view_id == "None":
                    view_id = None
                view = View.objects.filter(id=view_id).first()
                if not view:
                    view = View.objects.filter(
                        title=None, target=TYPE_OBJECT_COMPANY, workspace=workspace
                    ).first()
                view_filter = view.viewfilter_set.first()
                filter_conditions = Q(workspace=workspace)
                filter_conditions = build_view_filter(
                    filter_conditions,
                    view_filter,
                    TYPE_OBJECT_COMPANY,
                    force_filter_list=additional_filter_fields,
                )
                objects = base_model.objects.filter(filter_conditions)
            else:
                list_objects = request.POST.getlist("checkbox")
                objects = base_model.objects.filter(id__in=list_objects)
            for obj in objects:
                customFields = custom_value_model.objects.filter(
                    **{custom_value_relation: obj}
                )

                old_obj_id = obj.id
                obj.id = None
                setattr(obj, id_field, None)
                obj.created_at = timezone.now()
                obj.save()

                # keep company
                old_objects = base_model.objects.filter(id=old_obj_id).first()
                for company in old_objects.companies.all():
                    obj.companies.add(company)
                obj.save()

                for field in customFields:
                    old_field_id = field.id
                    field.id = None
                    setattr(field, custom_value_relation, obj)
                    field.save()

                    # duplicate file or image
                    if custom_value_file_relation is None:
                        continue

                    customFieldsValueFile = custom_value_file_model.objects.filter(
                        **{f"{custom_value_file_relation}__id": old_field_id}
                    )
                    if customFieldsValueFile:
                        for value_file in customFieldsValueFile:
                            value_file.id = None
                            value_file.created_at = timezone.now()
                            setattr(value_file, custom_value_file_relation, field)
                            value_file.save()

        if module:
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
            )
        return redirect(reverse("main", host="app"))
    if module:
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
        )
    return redirect(reverse("main", host="app"))


def calculate_math(formula_custom_field, model, type):
    result = 0
    try:
        formula_raws = [
            data["value"]
            for data in ast.literal_eval(formula_custom_field.choice_value)
        ]
        # Checking row on custom field
        formula_as_number = []

        if type == "company":
            for formula_entity in formula_raws:
                shopturboordersnamecustomfield = CompanyNameCustomField.objects.filter(
                    workspace=model.workspace, type="number", name=formula_entity
                ).last()
                if shopturboordersnamecustomfield:
                    value_custom_field = CompanyValueCustomField.objects.filter(
                        field_name=shopturboordersnamecustomfield, company=model
                    ).last()

                    if value_custom_field:
                        if value_custom_field.value:
                            formula_as_number.append(float(value_custom_field.value))
                        else:
                            formula_as_number.append(float(0))

                elif (
                    "sum" in formula_entity.lower() or "count" in formula_entity.lower()
                ):
                    if (
                        "orders" in formula_entity.lower()
                        and "sum" in formula_entity.lower()
                    ):
                        if "total_price" in formula_entity.lower():
                            # sum the orders
                            number = model.shopturboorders_set.aggregate(
                                total_price_sum=Sum("total_price")
                            )["total_price_sum"]
                            if number:
                                formula_as_number.append(float(number))

                    elif (
                        "orders" in formula_entity.lower()
                        and "count" in formula_entity.lower()
                    ):
                        number = model.shopturboorders_set.all().count()
                        if number:
                            formula_as_number.append(float(number))

                    elif (
                        "invoices" in formula_entity.lower()
                        and "sum" in formula_entity.lower()
                    ):
                        if "total_price" in formula_entity.lower():
                            invoices = model.invoice_set.all()
                            total_price = 0
                            for invoice in invoices:
                                invoice.amount = invoice.amount.replace(
                                    "[", ""
                                ).replace("]", "")
                                invoice.amount_item = invoice.amount_item.replace(
                                    "[", ""
                                ).replace("]", "")
                                if invoice.amount:
                                    number_amount = ast.literal_eval(invoice.amount)
                                    total_price += float(number_amount)
                                elif invoice.amount_item:
                                    number_amount_item = ast.literal_eval(
                                        invoice.amount_item
                                    )
                                    total_price += float(number_amount_item)
                            if total_price:
                                formula_as_number.append(float(total_price))
                else:
                    formula_as_number.append(formula_entity)

        # Calculate
        # Iterate through the list
        current_operator = None
        operand = 0
        for idx, entity in enumerate(formula_as_number):
            if entity:
                # warm_up
                if idx == 0:
                    if entity not in ("+", "-", "*", "/"):
                        result = float(entity)

                # Check if the item is an operator
                if isinstance(entity, str) and entity in ("+", "-", "*", "/"):
                    current_operator = entity
                # If there was a previous operator, perform the operation
                else:
                    operand = float(entity)
                    if current_operator:
                        if current_operator == "+":
                            result = result + operand if result is not None else operand
                        elif current_operator == "-":
                            result = (
                                result - operand if result is not None else -operand
                            )
                        elif current_operator == "*":
                            result = result * operand if result is not None else 0
                        elif current_operator == "/":
                            # Ensure not dividing by zero
                            if operand != 0:
                                result = result / operand if result is not None else 0
    except:
        pass

    return result


def apply_search_setting(
    page_group_type,
    view_filter: ViewFilter,
    search_key,
    search_value,
    force_filter_list=None,
):
    if force_filter_list is None:
        force_filter_list = []
    view = view_filter.view
    dummy_view_filter = ViewFilter(
        view=view, filter_value={search_key: {"key": "contains", "value": search_value}}
    )
    return build_view_filter(
        Q(), dummy_view_filter, page_group_type, force_filter_list=force_filter_list
    )


def company_association_drawer(request):
    if request.method == "GET":
        source = request.GET.get("source", None)
        object_id = request.GET.get("object_id", None)
        page = request.GET.get("page", 1)
        view_id = request.GET.get("view_id", None)
        module = request.GET.get("module", None)
        from_object = request.GET.get("from", None)

        if source == TYPE_OBJECT_SUBSCRIPTION:
            obj = ShopTurboSubscriptions.objects.get(id=object_id)
        elif source == TYPE_OBJECT_CONTACT:
            obj = Contact.objects.get(id=object_id)
        elif source == TYPE_OBJECT_ORDER:
            obj = ShopTurboOrders.objects.get(id=object_id)

        context = {
            "source": source,
            "object_id": object_id,
            "page": page,
            "view_id": view_id,
            "obj": obj,
            "module": module,
        }

        if from_object:
            context["from"] = from_object

        return render(
            request, "data/association/default-create-add/company.html", context
        )
    else:
        return HttpResponse(200)
