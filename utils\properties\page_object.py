from data.constants.commerce_meter_constant import (
    COMMERCE_METER_COLUMN_DISPLAY,
    DEFAULT_COLUMNS_COMMERCE_METER,
    DEFAULT_FORM_FIELDS_COMMERCE_METER,
)
from data.constants.constant import (
    ABSENCE_COLUMNS_DISPLAY,
    APPLICANT_COLUMN_DISPLAY,
    BILL_STATUS_DISPLAY,
    CASE_STATUS_DISPLAY,
    COMMERCE_STATUS_DISPLAY,
    COMPANY_COLUMNS_DISPLAY,
    COMPONENT_COLUMNS_DISPLAY,
    CONTACTS_COLUMNS_DISPLAY,
    CONTRACT_COLUMNS_DISPLAY,
    CONVERSATIONS_COLUMNS_DISPLAY,
    CUSTOM_OBJECT_COLUMN_DISPLAY,
    DASHBOARD_COLUMNS_DISPLAY,
    DEALS_COLUMNS_DISPLAY,
    DEFAULT_COLUMNS_ABSENCE,
    DEFAULT_COLUMNS_BILL,
    DEFAULT_COLUMNS_CASE,
    DEFAULT_COLUMNS_COMPANY,
    DEFAULT_COLUMNS_CONTACTS,
    DEFAULT_COLUMNS_CONTRACT,
    DEFAULT_COLUMNS_CONVERSATION,
    DEFAULT_COLUMNS_DASHBOARD,
    DEFAULT_COLUMNS_DELIVERY_SLIP,
    DEFAULT_COLUMNS_ESTIMATE,
    DEFAULT_COLUMNS_EXPENSE,
    DEFAULT_COLUMNS_FORM,
    DEFAULT_COLUMNS_INVOICE,
    DEFAULT_COLUMNS_ITEM,
    DEFAULT_COLUMNS_JOBS,
    DEFAULT_COLUMNS_JOURNAL,
    DEFAULT_COLUMNS_ORDER,
    DEFAULT_COLUMNS_PANEL,
    DEFAULT_COLUMNS_PURCHASE_ORDER,
    DEFAULT_COLUMNS_RECEIPT,
    DEFAULT_COLUMNS_SLIP,
    DEFAULT_COLUMNS_SUBSCRIPTIONS,
    DEFAULT_COLUMNS_TASK,
    DEFAULT_COLUMNS_TRANSACTION,
    DEFAULT_COLUMNS_USER_MANAGEMENT,
    DEFAULT_COLUMNS_WAREHOUSE,
    DEFAULT_COLUMNS_WORKER,
    DEFAULT_COLUMNS_WORKER_REVIEW,
    DEFAULT_COLUMNS_WORKFLOW,
    DEFAULT_COLUMN_APPLICANT,
    DEFAULT_COLUMN_SOCIAL_MEDIA_ALL_POSTS,
    DEFAULT_COLUMN_TIMEGENIE,
    DEFAULT_FORM_FIELDS_EXPENSE,
    DEFAULT_FORM_FIELDS_PURHCASE_ORDER,
    DEFAULT_FORM_FIELD_BILL,
    DEFAULT_FORM_FIELD_CASE,
    DEFAULT_FORM_FIELD_COMMERCE,
    DEFAULT_FORM_FIELD_COMPANY,
    DEFAULT_FORM_FIELD_CONTACTS,
    DEFAULT_FORM_FIELD_DELIVERY_NOTE,
    DEFAULT_FORM_FIELD_INVENTORY,
    DEFAULT_FORM_FIELD_ITEM,
    DEFAULT_FORM_FIELD_JOURNAL,
    DEFAULT_FORM_FIELD_ORDER,
    DEFAULT_FORM_FIELD_SLIP,
    DEFAULT_FORM_FIELD_SUBSCRIPTIONS,
    DEFAULT_FORM_FIELD_TASK,
    DEFAULT_FORM_FIELD_TRANSACTION,
    DEFAULT_FORM_FIELD_WAREHOUSE,
    DEFAULT_FORM_FIELD_WORKFLOW,
    DEFAULT_SLIPS_TYPE_DISPLAY,
    DELIVERY_NOTE_COLUMNS_DISPLAY,
    DISPLAY_COLUMNS_BILL,
    DISPLAY_COLUMNS_EXPENSE,
    DISPLAY_COLUMNS_SOCIAL_MEDIA,
    DISPLAY_COLUMNS_TASK,
    DISPLAY_COLUMNS_WAREHOUSE,
    DISPLAY_COLUMNS_WORKFLOW,
    ESTIMATE_COLUMNS_DISPLAY,
    ESTIMATE_STATUS_DISPLAY,
    EXPENSE_STATUS_DISPLAY,
    FORM_COLUMNS_DISPLAY,
    INVENTORY_COLUMNS_DISPLAY,
    INVENTORY_STATUS_DISPLAY,
    INVENTORY_TRANSACTION_COLUMNS_DISPLAY,
    INVOICE_COLUMNS_DISPLAY,
    ITEMS_COLUMNS_DISPLAY,
    JOBS_COLUMNS_DISPLAY,
    JOBS_STATUS_DISPLAY,
    JOBS_TYPE_DISPLAY,
    JOB_STATUS_DISPLAY,
    JOB_TYPE_DISPLAY,
    JOURNAL_CATEGORY_DISPLAY,
    JOURNAL_COLUMNS_DISPLAY,
    JOURNAL_COUNTER_CATEGORY_DISPLAY,
    JOURNAL_TAX_CATEGORY_DISPLAY,
    ORDERS_COLUMNS_DISPLAY,
    ORDERS_STATUS_DISPLAY,
    PANEL_COLUMNS_DISPLAY,
    PAYMENT_COLUMNS_DISPLAY,
    PURCHASE_ORDER_COLUMNS_DISPLAY,
    PURCHASE_ORDER_STATUS_DISPLAY,
    SLIP_COLUMNS_DISPLAY,
    SUBSCRIPTIONS_COLUMNS_DISPLAY,
    SUBSCRIPTIONS_STATUS_DISPLAY,
    TASKS_STATUS_DISPLAY,
    THREAD_STATUS_DISPLAY,
    TIMEGENIE_COLUMNS_DISPLAY,
    USER_MANAGEMENT_COLUMNS_DISPLAY,
    WORKER_COLUMNS_DISPLAY,
    WORKER_REVIEW_COLUMNS_DISPLAY,
)
from data.constants.properties_constant import (
    OBJ_CUSTOM_FIELD_MAP_PAGE_GROUP_TYPE,
    TYPE_OBJECT_BILL,
    TYPE_OBJECT_CAMPAIGN,
    TYPE_OBJECT_CASE,
    TYPE_OBJECT_CASE_LINE_ITEM,
    TYPE_OBJECT_COMMERCE_METER,
    TYPE_OBJECT_COMPANY,
    TYPE_OBJECT_CONTACT,
    TYPE_OBJECT_CONTRACT,
    TYPE_OBJECT_CONVERSATION,
    TYPE_OBJECT_CUSTOM_OBJECT,
    TYPE_OBJECT_DASHBOARD,
    TYPE_OBJECT_DELIVERY_NOTE,
    TYPE_OBJECT_ESTIMATE,
    TYPE_OBJECT_EXPENSE,
    TYPE_OBJECT_FORM,
    TYPE_OBJECT_INVENTORY,
    TYPE_OBJECT_INVENTORY_TRANSACTION,
    TYPE_OBJECT_INVENTORY_WAREHOUSE,
    TYPE_OBJECT_INVOICE,
    TYPE_OBJECT_ITEM,
    TYPE_OBJECT_JOBS,
    TYPE_OBJECT_JOBS_APPLICANT,
    TYPE_OBJECT_JOURNAL,
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_ORDER_LINE_ITEM,
    TYPE_OBJECT_PANEL,
    TYPE_OBJECT_PURCHASE_ORDER,
    TYPE_OBJECT_RECEIPT,
    TYPE_OBJECT_SLIP,
    TYPE_OBJECT_SUBSCRIPTION,
    TYPE_OBJECT_SUBSCRIPTION_PLATFORM,
    TYPE_OBJECT_TASK,
    TYPE_OBJECT_TIMEGENIE,
    TYPE_OBJECT_USER_MANAGEMENT,
    TYPE_OBJECT_WORKER,
    TYPE_OBJECT_WORKER_ABSENCE,
    TYPE_OBJECT_WORKER_REVIEW,
    TYPE_OBJECT_WORKFLOW,
)
from data.models import (
    Absence,
    AbsenceNameCustomField,
    AbsenceValueCustomField,
    AbsenceValueFile,
    Bill,
    BillNameCustomField,
    BillValueCustomField,
    BillValueFile,
    CommerceMeter,
    CommerceMeterNameCustomField,
    CommerceMeterValueCustomField,
    CommerceMeterValueFile,
    Company,
    CompanyNameCustomField,
    CompanyValueCustomField,
    CompanyValueFile,
    Contact,
    ContactsNameCustomField,
    ContactsValueCustomField,
    ContactsValueFile,
    ContractDocument,
    ContractDocumentNameCustomField,
    ContractDocumentValueCustomField,
    CustomObject,
    CustomObjectPropertyName,
    CustomObjectPropertyRow,
    CustomObjectPropertyValue,
    Deals,
    DealsItems,
    DealsItemsNameCustomField,
    DealsItemsValueCustomField,
    DealsNameCustomField,
    DealsValueCustomField,
    DealsValueFile,
    DeliverySlip,
    DeliverySlipItem,
    DeliverySlipItemsNameCustomField,
    DeliverySlipItemsValueCustomField,
    DeliverySlipNameCustomField,
    DeliverySlipValueCustomField,
    DeliverySlipValueFile,
    Estimate,
    EstimateItem,
    EstimateItemsNameCustomField,
    EstimateItemsValueCustomField,
    EstimateNameCustomField,
    EstimatePurchaseItem,
    EstimateValueCustomField,
    EstimateValueFile,
    Expense,
    ExpenseNameCustomField,
    ExpenseValueCustomField,
    ExpenseValueFile,
    Form,
    InventoryTransaction,
    InventoryTransactionNameCustomField,
    InventoryTransactionValueCustomField,
    InventoryTransactionValueFile,
    InventoryWarehouse,
    InventoryWarehouseNameCustomField,
    InventoryWarehouseValueCustomField,
    InventoryWarehouseValueFile,
    Invoice,
    InvoiceItem,
    InvoiceItemsNameCustomField,
    InvoiceItemsValueCustomField,
    InvoiceNameCustomField,
    InvoiceValueCustomField,
    InvoiceValueFile,
    Job,
    JobApplicantNameCustomField,
    JobApplicantValueCustomField,
    JobApplicantValueFile,
    JobApplication,
    JobNameCustomField,
    JobValueCustomField,
    JobValueFile,
    JournalEntry,
    JournalEntryNameCustomField,
    JournalEntryTransaction,
    JournalEntryValueCustomField,
    JournalEntryValueFile,
    MessageNameCustomField,
    MessageThread,
    MessageValueCustomField,
    Post,
    PostNameCustomField,
    PostValueCustomField,
    PostValueFile,
    PurchaseOrders,
    PurchaseOrdersItem,
    PurchaseOrdersNameCustomField,
    PurchaseOrdersValueCustomField,
    PurchaseOrdersValueFile,
    Receipt,
    ReceiptItem,
    ReceiptItemsNameCustomField,
    ReceiptItemsValueCustomField,
    ReceiptNameCustomField,
    ReceiptValueCustomField,
    ReceiptValueFile,
    Report,
    ReportNameCustomField,
    ReportPanel,
    ReportPanelNameCustomField,
    ReportPanelValueCustomField,
    ReportValueCustomField,
    ShopTurboInventory,
    ShopTurboInventoryNameCustomField,
    ShopTurboInventoryValueCustomField,
    ShopTurboInventoryValueFile,
    ShopTurboItemComponents,
    ShopTurboItems,
    ShopTurboItemsBills,
    ShopTurboItemsNameCustomField,
    ShopTurboItemsOrders,
    ShopTurboItemsOrdersNameCustomField,
    ShopTurboItemsOrdersValueCustomField,
    ShopTurboItemsPrice,
    ShopTurboItemsSubscriptions,
    ShopTurboItemsValueCustomField,
    ShopTurboItemsValueFile,
    ShopTurboOrders,
    ShopTurboOrdersNameCustomField,
    ShopTurboOrdersValueCustomField,
    ShopTurboOrdersValueFile,
    ShopTurboSubscriptionPlatforms,
    ShopTurboSubscriptions,
    ShopTurboSubscriptionsNameCustomField,
    ShopTurboSubscriptionsValueCustomField,
    ShopTurboSubscriptionsValueFile,
    Slip,
    SlipItem,
    SlipNameCustomField,
    SlipValueCustomField,
    SlipValueFile,
    Task,
    TaskCustomFieldName,
    TaskCustomFieldValue,
    TaskItem,
    Track,
    TrackNameCustomField,
    TrackValueCustomField,
    TrackValueFile,
    UserManagement,
    UserManagementNameCustomField,
    UserManagementValueCustomField,
    UserManagementValueFile,
    Worker,
    WorkerNameCustomField,
    WorkerReviews,
    WorkerReviewsNameCustomField,
    WorkerReviewsValueCustomField,
    WorkerReviewsValueFile,
    WorkerValueCustomField,
    WorkerValueFile,
    Workflow,
)
from utils.logger import logger
from utils.properties.model_properties import get_model_columns


def get_page_object(object_type, lang="ja"):
    # Store original object_type for the 'type' field
    original_object_type = object_type
    # Map shorthand object types to full object types
    mapped_type = OBJ_CUSTOM_FIELD_MAP_PAGE_GROUP_TYPE.get(object_type, object_type)
    object_type = mapped_type

    obj = {
        "base_model": None,
        "custom_object": None,
        "custom_model": None,
        "custom_value_model": None,
        "custom_item_model": None,
        "custom_item_value_model": None,
        "custom_line_item_model": None,
        "custom_line_item_value_model": None,
        "additional_filter_fields": [],  # For filtering additional column to be added
        "additional_column_fields": [],  # For managing additional column to be added
        "required_column_fields": [],  # Properties can not removed in column tagify
        "custom_value_relation": None,  # Relation between custom_value_model and base_model
        "custom_value_file_model": None,
        # Relation between custom_value_file_model and custom_value_model
        "custom_value_file_relation": None,
        # Related model of custom_model. Format: {"model":<>, "relation":<>, "type":<custom property type>}
        "custom_value_related_model": [],
        "base_columns": [],  # Get columns as list field of object
        "default_columns": None,  # List columns shown when create a new view menu
        # List form fields shown when create a new view menu
        "default_form_fields": [],
        "columns_display": None,
        "additional_columns_display": None,
        # 'reverse_url': None,            # Redirect URL
        "reverse_query": None,
        "id_field": None,
        "file_name": None,  # Name of download file
        "file_field": None,  # Name of field contains file, use for download
        "custom_relation": None,  # Relation between custom_value_model and custom_model
        "page_title": None,
        "page_type": None,  # For the pricing and usage limit
        # List fields will be apply to search pattern by default
        "search_fields": None,
        "download_formats": [],
        "download_form_url": None,  # Change download form in drawer
        "view_types": [],
        "setting_url": None,  # Setting page
        "setting_type": None,  # Query path at setting_url
        "editable_columns": {},  # Default properties could be editable by user
        "required_properties": [],  # Properties can not removed in property set
        "item_model": None,  # item child for obj
        "field_item_name": None,  # Field name for item
        "manage_obj_link": None,
        "file_upload_to": "None",
        "exclude_custom_types": [],
        # The list types of custom field which can add multiple values
        "multiple_custom_types": [],
        "item_related_name": "",
        "related_data": [],  # Tagify Formula Related Data
        "app_target": None,  # AppSettings app_target
        "parent_object": None,  # Add parent to sub object -> example: job (parent) and applicant (child)
        "lang": lang,
    }

    if object_type == TYPE_OBJECT_ITEM:
        obj["base_model"] = ShopTurboItems
        obj["custom_model"] = ShopTurboItemsNameCustomField
        obj["custom_value_model"] = ShopTurboItemsValueCustomField
        obj["custom_value_relation"] = "items"
        obj["custom_value_file_model"] = ShopTurboItemsValueFile
        obj["file_upload_to"] = "shopturbo-item-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["custom_value_related_model"] = [
            {
                "model": ShopTurboItemComponents,
                "relation": "components",
                "type": "components",
                "columns_display": COMPONENT_COLUMNS_DISPLAY.copy(),
            }
        ]
        obj["base_columns"] = get_model_columns(
            obj["base_model"], excludes=["product_id", "created_at"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_ITEM.copy()
        obj["columns_display"] = ITEMS_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_ITEM.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "item_id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["custom_relation"] = "shopturbo_item_custom_field_relations"
        obj["page_title"] = "商品" if lang == "ja" else "Items"
        obj["search_fields"] = [obj["id_field"], "name"]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [
            ("list", "Table", "テーブル"),
            ("group", "Group Table", "グループテーブル"),
            ("price_table", "Price Table", "価格表"),
        ]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_ITEM
        obj["editable_columns"] = ["name"]
        obj["required_properties"] = ["name"]
        obj["exclude_custom_types"] = [
            "part",
            "date_range",
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]
        obj["multiple_custom_types"] = ["components"]
        obj["related_data"] = ["order"]
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_ORDER:
        obj["base_model"] = ShopTurboOrders
        obj["custom_model"] = ShopTurboOrdersNameCustomField
        obj["custom_value_model"] = ShopTurboOrdersValueCustomField
        obj["item_model"] = ShopTurboItemsOrders
        obj["custom_value_relation"] = "orders"
        obj["custom_value_file_model"] = ShopTurboOrdersValueFile
        obj["custom_line_item_model"] = ShopTurboItemsOrdersNameCustomField
        obj["custom_line_item_value_model"] = ShopTurboItemsOrdersValueCustomField
        obj["file_upload_to"] = "shopturbo-order-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["base_columns"] = get_model_columns(
            obj["base_model"],
            excludes=[
                "item_price_order",
                "kanban_order",
                "shipping_cost_tax_status",
                "created_at",
            ],
            includes=["inventory_transactions", "invoice", "estimate", "owner"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_ORDER.copy()
        obj["columns_display"] = ORDERS_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_ORDER.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "order_id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["custom_relation"] = "shopturbo_custom_field_relations"
        obj["page_title"] = "受注" if lang == "ja" else "Orders"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [
            ("list", "Table", "テーブル"),
            ("kanban", "Kanban", "カンバン"),
            ("item_list", "Line Items", "商品項目"),
        ]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_ORDER
        obj["editable_columns"] = {
            "delivery_status": {
                k: v
                for k, v in ORDERS_STATUS_DISPLAY.items()
                if k not in ["active", "archived"]
            },
            "line_item": "",
        }
        obj["required_properties"] = ["line_item"]
        obj["exclude_custom_types"] = [
            "part",
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]
        obj["pdf_title"] = "受注" if lang == "ja" else "Order"
        obj["default_order_name"] = "デフォルト" if lang == "ja" else "Default"
        obj["related_data"] = [
            TYPE_OBJECT_ITEM,
            TYPE_OBJECT_INVENTORY_TRANSACTION,
            TYPE_OBJECT_PURCHASE_ORDER,
        ]
        obj["pdf_pattern"] = "orderPDF-pattern-"
        obj["field_item_name"] = "order"
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_SUBSCRIPTION:
        obj["base_model"] = ShopTurboSubscriptions
        obj["custom_model"] = ShopTurboSubscriptionsNameCustomField
        obj["custom_value_model"] = ShopTurboSubscriptionsValueCustomField
        obj["item_model"] = ShopTurboItemsSubscriptions
        obj["custom_value_relation"] = "subscriptions"
        obj["custom_value_file_model"] = ShopTurboSubscriptionsValueFile
        obj["file_upload_to"] = "shopturbo-subscription-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["base_columns"] = get_model_columns(
            obj["base_model"],
            includes=[
                "orders",
                "invoices",
                "item_name",
                "customer",
                "durations",
                "line_item",
                "owner",
            ],
            excludes=[
                "tax_applied_to",
                "tax_rate",
                "billing_timing",
                "status",
                "prior_to_next",
                "prior_to_time",
            ],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_SUBSCRIPTIONS.copy() + ["tax"]
        obj["columns_display"] = SUBSCRIPTIONS_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_SUBSCRIPTIONS.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "subscriptions_id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["custom_relation"] = "shopturbo_subscriptions_custom_field_relations"
        obj["page_title"] = "サブスクリプション" if lang == "ja" else "Subscriptions"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_SUBSCRIPTION
        obj["exclude_custom_types"] = [
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]
        obj["editable_columns"] = {"subscription_status": SUBSCRIPTIONS_STATUS_DISPLAY}
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"
        obj["download_form_url"] = "shopturbo_subscriptions"

    elif object_type == TYPE_OBJECT_SUBSCRIPTION_PLATFORM:
        obj["base_model"] = ShopTurboSubscriptionPlatforms
        obj["custom_model"] = None
        obj["custom_value_model"] = None
        obj["custom_value_relation"] = None
        obj["custom_value_file_model"] = None
        obj["file_upload_to"] = None
        obj["custom_value_file_relation"] = None
        obj["base_columns"] = get_model_columns(
            obj["base_model"],
            excludes=["manage_payment_session_id", "payment_setup_id"],
            includes=["source_subscription", "channel"],
        )
        obj["default_columns"] = [
            "platform_id",
            "platform_display_name",
            "platform_status",
            "payment_status",
            "created_at",
        ]
        obj["columns_display"] = {
            "platform_id": {"en": "Platform ID", "ja": "プラットフォームID"},
            "platform_display_name": {
                "en": "Platform Name",
                "ja": "プラットフォーム名",
            },
            "platform_status": {
                "en": "Platform Status",
                "ja": "プラットフォームステータス",
            },
            "payment_status": {"en": "Payment Status", "ja": "支払いステータス"},
            "payment_link": {"en": "Payment Link", "ja": "支払いリンク"},
            "source_subscription": {
                "en": "Source Subscription",
                "ja": "元のサブスクリプション",
            },
            "channel": {"en": "Channel", "ja": "チャネル"},
            "created_at": {"en": "Created At", "ja": "作成日時"},
            "updated_at": {"en": "Updated At", "ja": "更新日時"},
        }
        obj["id_field"] = None
        obj["file_name"] = None
        obj["file_field"] = None
        obj["custom_relation"] = None
        obj["page_title"] = (
            "サブスクリプションプラットフォーム"
            if lang == "ja"
            else "Subscription Platforms"
        )
        obj["search_fields"] = ["platform_id", "platform_display_name"]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_SUBSCRIPTION_PLATFORM
        obj["exclude_custom_types"] = []
        obj["editable_columns"] = {}
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_CASE:
        obj["base_model"] = Deals
        obj["custom_model"] = DealsNameCustomField
        obj["custom_value_model"] = DealsValueCustomField
        obj["custom_value_file_model"] = DealsValueFile
        obj["file_upload_to"] = "deals-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["custom_value_relation"] = "deals"
        obj["base_columns"] = get_model_columns(
            obj["base_model"], excludes=["created_at"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_CASE.copy()
        obj["columns_display"] = DEALS_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_CASE.copy()
        obj["editable_columns"] = {"case_status": CASE_STATUS_DISPLAY, "line_item": ""}
        obj["id_field"] = "deal_id"
        obj["file_name"] = "cases"
        obj["file_field"] = None
        obj["custom_relation"] = "deals_custom_field_relations"
        obj["page_title"] = "案件" if lang == "ja" else "Case"
        obj["search_fields"] = [obj["id_field"], "name"]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [
            ("table", "Table", "テーブル"),
            ("kanban", "Kanban", "カンバン"),
        ]
        obj["setting_url"] = "workspace_setting"
        obj["download_form_url"] = "service_deals"
        obj["setting_type"] = TYPE_OBJECT_CASE
        obj["exclude_custom_types"] = [
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]
        obj["related_data"] = []
        obj["app_target"] = "contacts"

    elif object_type == TYPE_OBJECT_CONTACT:
        obj["base_model"] = Contact
        obj["custom_model"] = ContactsNameCustomField
        obj["custom_value_model"] = ContactsValueCustomField
        obj["custom_value_file_model"] = ContactsValueFile
        obj["file_upload_to"] = "contacts-custom-field-files"
        obj["custom_value_relation"] = "contact"
        obj["base_columns"] = get_model_columns(
            obj["base_model"],
            excludes=["created_at", "url", "status"],
            includes=["owner"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_CONTACTS.copy()
        obj["columns_display"] = CONTACTS_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_CONTACTS.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        # obj['additional_filter_fields'] = ['customers__companies__name']
        obj["id_field"] = "contact_id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["custom_relation"] = "contact_custom_field_relations"
        obj["page_title"] = "連絡先" if lang == "ja" else "Contacts"
        obj["search_fields"] = [obj["id_field"], "name"]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [("table", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_CONTACT
        obj["exclude_custom_types"] = [
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]
        obj["required_properties"] = ["first_name", "last_name"]
        obj["related_data"] = ["journal"]
        obj["app_target"] = "contacts"

    elif object_type == TYPE_OBJECT_COMPANY:
        obj["base_model"] = Company
        obj["custom_model"] = CompanyNameCustomField
        obj["custom_value_model"] = CompanyValueCustomField
        obj["custom_value_file_model"] = CompanyValueFile
        obj["file_upload_to"] = "company-custom-field-files"
        obj["custom_value_relation"] = "company"
        obj["base_columns"] = get_model_columns(
            obj["base_model"], excludes=["created_at", "status"], includes=["owner"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_COMPANY.copy()
        obj["columns_display"] = COMPANY_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_COMPANY.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "company_id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["custom_relation"] = "company_custom_field_relations"
        obj["page_title"] = "企業" if lang == "ja" else "Company"
        obj["search_fields"] = [obj["id_field"], "name"]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [("table", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_COMPANY
        obj["exclude_custom_types"] = [
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]
        obj["related_data"] = ["journal"]
        obj["multiple_custom_types"] = ["hierarchy"]
        obj["app_target"] = "contacts"

    elif object_type == TYPE_OBJECT_CONVERSATION:
        obj["base_model"] = MessageThread
        obj["custom_model"] = MessageNameCustomField
        obj["custom_value_model"] = MessageValueCustomField
        obj["custom_value_relation"] = "message"
        obj["base_columns"] = get_model_columns(
            obj["base_model"], excludes=["created_at"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_CONVERSATION.copy()
        obj["columns_display"] = CONVERSATIONS_COLUMNS_DISPLAY.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["custom_relation"] = None
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_CONVERSATION
        obj["editable_columns"] = {"status": THREAD_STATUS_DISPLAY}
        obj["related_data"] = []

    elif object_type == TYPE_OBJECT_PURCHASE_ORDER:
        obj["base_model"] = PurchaseOrders
        obj["item_model"] = PurchaseOrdersItem
        obj["custom_model"] = PurchaseOrdersNameCustomField
        obj["custom_value_model"] = PurchaseOrdersValueCustomField
        obj["custom_value_file_model"] = PurchaseOrdersValueFile
        obj["file_upload_to"] = "purchase-order-custom-field-files"
        obj["custom_value_relation"] = "purchaseorders"
        obj["additional_filter_fields"] = ["supplier"] + [
            "supplier__company__name",
            "supplier__contact__name",
            "supplier__contact__first_name",
            "supplier__contact__last_name",
        ]
        obj["base_columns"] = get_model_columns(
            obj["base_model"],
            excludes=[
                "usage_status",
                "amount",
                "tax_option",
                "amount_item",
                "items",
                "item_name",
                "item_status",
                "item_price",
                "item_price_without_tax",
            ],
            includes=obj["additional_filter_fields"] + ["inventory_transactions"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_PURCHASE_ORDER.copy()
        obj["columns_display"] = PURCHASE_ORDER_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELDS_PURHCASE_ORDER.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["exclude_custom_types"] = ["purchase_order", "bill_objects"]
        obj["id_field"] = "id_po"
        obj["file_name"] = "purchase_orders"
        obj["file_field"] = None
        obj["custom_relation"] = "purchase_order_field_relations"
        obj["page_title"] = "発注" if lang == "ja" else "Purchase Orders"
        obj["page_type"] = "purchase_orders"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv", "dat"]
        obj["download_form_url"] = "purchaseorder"
        obj["view_types"] = [
            ("list", "Table", "テーブル"),
            ("item_list", "Line Items", "商品項目"),
        ]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_PURCHASE_ORDER
        obj["field_item_name"] = "purchaseorders"
        obj["pdf_pattern"] = "purchaseorderPDF-pattern-"
        obj["pdf_title"] = "発注" if lang == "ja" else "Purchase Orders"
        obj["multiple_custom_types"] = ["bill_objects"]
        obj["editable_columns"] = {
            "status": PURCHASE_ORDER_STATUS_DISPLAY,
            "line_item": "",
        }
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"
        obj["row_detail_url"] = "purchase_order_row_detail"
        obj["manage_obj_link"] = "purchase_manage"

    elif object_type == TYPE_OBJECT_BILL:
        obj["base_model"] = Bill
        obj["item_model"] = ShopTurboItemsBills
        obj["custom_model"] = BillNameCustomField
        obj["custom_value_model"] = BillValueCustomField
        obj["custom_value_relation"] = "bill"
        obj["custom_value_file_model"] = BillValueFile
        obj["file_upload_to"] = "spendpocket-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["base_columns"] = get_model_columns(
            obj["base_model"],
            excludes=["created_at", "updated_at", "bill_type", "notified"],
            includes=["partner", "file", "journal_entry", "owner"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_BILL.copy()
        obj["columns_display"] = DISPLAY_COLUMNS_BILL.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_BILL.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id_bill"
        obj["file_name"] = "bills"
        obj["file_field"] = "file"
        obj["field_item_name"] = "bill"
        obj["custom_relation"] = "bill_custom_field_relations"
        obj["page_title"] = "支払請求" if lang == "ja" else "Bills"
        obj["page_type"] = "bills"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv", "pdf"]
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_BILL
        obj["exclude_custom_types"] = [
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]
        obj["manage_obj_link"] = "bill_manage"
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"
        obj["row_detail_url"] = "bill_row_detail"
        obj["editable_columns"] = {"status": BILL_STATUS_DISPLAY}
        obj["pdf_download_link"] = "bill_pdf_download"
        obj["update_obj_link"] = "update_bill"
        obj["new_obj_link"] = "new_bill"
        obj["drawer_url"] = "bill_drawer"
        obj["pdf_pattern"] = "billPDF-pattern-"
        obj["pdf_title"] = "請求" if lang == "ja" else "Bill"
        obj["page_location"] = "data/invoices/bill-table.html"
        obj["download_form_url"] = "bill"

    elif object_type == TYPE_OBJECT_EXPENSE:  # Expenses
        obj["base_model"] = Expense
        obj["custom_model"] = ExpenseNameCustomField
        obj["custom_value_model"] = ExpenseValueCustomField
        obj["custom_value_relation"] = "expense"
        obj["custom_value_file_model"] = ExpenseValueFile
        obj["file_upload_to"] = "spendpocket-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["additional_column_fields"] = ["description"]
        obj["base_columns"] = get_model_columns(
            obj["base_model"],
            includes=obj["additional_filter_fields"] + ["owner", "partner"],
            excludes=["created_at", "date_received", "display_status", "submitter"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_EXPENSE.copy()
        obj["columns_display"] = DISPLAY_COLUMNS_EXPENSE.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELDS_EXPENSE.copy()

        obj["download_form_url"] = "expense"
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id_pm"
        obj["file_name"] = "expenses"
        obj["file_field"] = "paymentfile_set"
        obj["custom_relation"] = "expense_custom_field_relations"
        obj["page_title"] = "経費" if lang == "ja" else "Expenses"
        obj["page_type"] = "expenses"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv", "pdf"]
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["manage_obj_link"] = "expense_drawer"
        obj["setting_type"] = TYPE_OBJECT_EXPENSE
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"
        obj["row_detail_url"] = "expense_row_detail"
        obj["editable_columns"] = {"status": EXPENSE_STATUS_DISPLAY}

    elif object_type == TYPE_OBJECT_DELIVERY_NOTE:
        obj["base_model"] = DeliverySlip
        obj["item_model"] = DeliverySlipItem
        obj["field_item_name"] = "deliveryslip"
        obj["custom_model"] = DeliverySlipNameCustomField
        obj["custom_value_model"] = DeliverySlipValueCustomField
        obj["custom_item_model"] = DeliverySlipItemsNameCustomField
        obj["custom_item_value_model"] = DeliverySlipItemsValueCustomField
        obj["custom_value_relation"] = "deliveryslip"
        obj["custom_value_file_model"] = DeliverySlipValueFile
        obj["file_upload_to"] = "billing-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["additional_filter_fields"] = [
            "customers__company__name",
            "customers__contact__name",
            "customers__contact__first_name",
            "customers__contact__last_name",
        ]
        obj["base_columns"] = get_model_columns(
            obj["base_model"],
            excludes=[
                "lang",
                "email",
                "amount",
                "amount_item",
                "cc_list",
                "amount",
                "amount_item",
                "tax_list",
                "tax_option",
                "discount_option",
                "discount_tax_option",
                "discount",
            ],
            includes=obj["additional_filter_fields"] + ["owner"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_DELIVERY_SLIP.copy()
        obj["columns_display"] = DELIVERY_NOTE_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_DELIVERY_NOTE.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id_ds"
        obj["file_name"] = "delivery_notes"
        obj["file_field"] = None
        obj["custom_relation"] = None
        obj["page_title"] = "納品" if lang == "ja" else "Delivery Notes"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_DELIVERY_NOTE
        obj["pdf_pattern"] = "deliveryslipPDF-pattern-"
        obj["page_location"] = "data/delivery_note/delivery-note-table.html"
        obj["pdf_download_link"] = "delivery_slip_pdf_download"
        obj["update_obj_link"] = "update_delivery_slip"
        obj["drawer_url"] = "delivery_slip_drawer"
        obj["manage_obj_link"] = "delivery_slip_edit"
        obj["new_obj_link"] = "new_delivery_slip"
        obj["download_form_url"] = "delivery_slips"
        obj["pdf_title"] = "納品" if lang == "ja" else "Delivery Notes"
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"
        obj["editable_columns"] = {"status": COMMERCE_STATUS_DISPLAY, "line_item": ""}
        obj["exclude_custom_types"] = [
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]

    elif object_type == TYPE_OBJECT_RECEIPT:
        obj["base_model"] = Receipt
        obj["item_model"] = ReceiptItem
        obj["custom_model"] = ReceiptNameCustomField
        obj["custom_value_model"] = ReceiptValueCustomField
        obj["field_item_name"] = "receipt"
        obj["custom_value_file_model"] = ReceiptValueFile
        obj["custom_item_model"] = ReceiptItemsNameCustomField
        obj["custom_item_value_model"] = ReceiptItemsValueCustomField
        obj["file_upload_to"] = "billing-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["additional_filter_fields"] = [
            "customers__company__name",
            "customers__contact__name",
            "customers__contact__first_name",
            "customers__contact__last_name",
        ]
        obj["custom_value_relation"] = "receipt"
        obj["base_columns"] = get_model_columns(
            obj["base_model"],
            excludes=[
                "lang",
                "email",
                "amount",
                "amount_item",
                "cc_list",
                "tax_list",
                "tax_option",
                "discount_option",
                "discount_tax_option",
                "discount",
            ],
            includes=obj["additional_filter_fields"] + ["owner"],
        )
        obj["exclude_custom_types"] = [
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]
        obj["default_columns"] = DEFAULT_COLUMNS_RECEIPT.copy()
        obj["columns_display"] = PAYMENT_COLUMNS_DISPLAY.copy()
        obj["additional_columns_display"] = PAYMENT_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_COMMERCE.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id_rcp"
        obj["file_name"] = "receipts"
        obj["file_field"] = None
        obj["custom_relation"] = None
        obj["page_title"] = "入金" if lang == "ja" else "Payments"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_RECEIPT
        obj["pdf_pattern"] = "receiptPDF-pattern-"
        obj["page_location"] = "data/receipt/payment-table.html"
        obj["pdf_download_link"] = "receipt_pdf_download"
        obj["update_obj_link"] = "update_receipt"
        obj["new_obj_link"] = "new_receipt"
        obj["drawer_url"] = "receipt_drawer"
        obj["download_form_url"] = "receipts"
        obj["pdf_title"] = "入金" if lang == "ja" else "Payments"
        obj["manage_obj_link"] = "receipt_edit"
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"
        obj["editable_columns"] = {"status": COMMERCE_STATUS_DISPLAY, "line_item": ""}

    elif object_type == TYPE_OBJECT_ESTIMATE:
        obj["base_model"] = Estimate
        obj["item_model"] = EstimateItem
        obj["purchase_item_model"] = EstimatePurchaseItem
        obj["custom_model"] = EstimateNameCustomField
        obj["custom_value_model"] = EstimateValueCustomField

        obj["custom_item_model"] = EstimateItemsNameCustomField
        obj["custom_item_value_model"] = EstimateItemsValueCustomField

        obj["field_item_name"] = "estimate"
        obj["custom_value_relation"] = "estimate"
        obj["custom_value_file_model"] = EstimateValueFile
        obj["file_upload_to"] = "billing-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["additional_filter_fields"] = [
            "customers__company__name",
            "customers__contact__name",
            "customers__contact__first_name",
            "customers__contact__last_name",
        ]
        obj["base_columns"] = get_model_columns(
            obj["base_model"],
            excludes=[
                "lang",
                "amount",
                "amount_item",
                "tax_list",
                "tax_option",
                "discount_tax_option",
                "old_id_est",
            ],
            includes=obj["additional_filter_fields"]
            + ["invoice", "order_association", "owner"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_ESTIMATE.copy()
        obj["columns_display"] = ESTIMATE_COLUMNS_DISPLAY.copy()
        obj["additional_columns_display"] = ESTIMATE_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_COMMERCE.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id_est"
        obj["file_name"] = "estimates"
        obj["file_field"] = None
        obj["custom_relation"] = "estimate_custom_field_relations"
        obj["page_title"] = "見積" if lang == "ja" else "Estimates"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv", "pdf"]
        obj["download_form_url"] = "estimates"
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_ESTIMATE
        obj["pdf_pattern"] = "estimatePDF-pattern-"
        obj["page_location"] = "data/estimate/estimates-table.html"
        obj["pdf_download_link"] = "estimate_pdf_download"
        obj["drawer_url"] = "estimate_drawer"
        obj["update_obj_link"] = "update_estimate"
        obj["new_obj_link"] = "new_estimate"
        obj["exclude_custom_types"] = [
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]
        obj["pdf_title"] = "見積書" if lang == "ja" else "Estimate"
        obj["manage_obj_link"] = "estimate_edit"
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"
        obj["editable_columns"] = {"status": ESTIMATE_STATUS_DISPLAY, "line_item": ""}

    elif object_type == TYPE_OBJECT_INVOICE:
        obj["base_model"] = Invoice
        obj["item_model"] = InvoiceItem
        obj["custom_model"] = InvoiceNameCustomField
        obj["custom_value_model"] = InvoiceValueCustomField
        obj["custom_value_file_model"] = InvoiceValueFile
        obj["custom_item_model"] = InvoiceItemsNameCustomField
        obj["custom_item_value_model"] = InvoiceItemsValueCustomField
        obj["file_upload_to"] = "billing-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["field_item_name"] = "invoice"
        obj["custom_value_relation"] = "invoice"
        obj["additional_filter_fields"] = [
            "customers__company__name",
            "customers__contact__name",
            "customers__contact__first_name",
            "customers__contact__last_name",
        ]
        obj["base_columns"] = get_model_columns(
            obj["base_model"],
            excludes=[
                "lang",
                "amount",
                "amount_item",
                "tax_list",
                "tax_option",
                "discount_tax_option",
            ],
            includes=obj["additional_filter_fields"]
            + ["associate#receipts", "order_association", "subscription", "owner"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_INVOICE.copy()
        obj["columns_display"] = INVOICE_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_COMMERCE.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id_inv"
        obj["file_name"] = "invoices"
        obj["file_field"] = None
        obj["custom_relation"] = "invoice_custom_field_relations"
        obj["page_title"] = "売上請求" if lang == "ja" else "Invoices"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv", "pdf"]
        obj["download_form_url"] = "invoices"
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_INVOICE
        obj["pdf_pattern"] = "invoicePDF-pattern-"
        obj["page_location"] = "data/invoice/invoices-table.html"
        obj["pdf_download_link"] = "invoice_pdf_download"
        obj["drawer_url"] = "invoice_drawer"
        obj["update_obj_link"] = "update_invoice"
        obj["new_obj_link"] = "new_invoice"
        obj["pdf_title"] = "請求書" if lang == "ja" else "Invoice"
        obj["manage_obj_link"] = "invoice_edit"
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"
        obj["exclude_custom_types"] = [
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]
        obj["editable_columns"] = {"status": COMMERCE_STATUS_DISPLAY, "line_item": ""}

    elif object_type == TYPE_OBJECT_SLIP:
        obj["base_model"] = Slip
        obj["item_model"] = SlipItem
        obj["field_item_name"] = "slip"
        obj["custom_model"] = SlipNameCustomField
        obj["custom_value_model"] = SlipValueCustomField
        obj["custom_value_file_model"] = SlipValueFile
        obj["file_upload_to"] = "billing-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["custom_value_relation"] = "slip"
        obj["additional_filter_fields"] = [
            "customers__company__name",
            "customers__contact__name",
            "customers__contact__first_name",
            "customers__contact__last_name",
        ]
        obj["base_columns"] = get_model_columns(
            obj["base_model"],
            excludes=[
                "lang",
                "email",
                "amount",
                "amount_item",
                "cc_list",
                "amount",
                "amount_item",
                "tax_list",
                "tax_option",
                "discount_option",
                "discount_tax_option",
                "discount",
                "status",
                "input_item",
            ],
            includes=obj["additional_filter_fields"] + ["journal_entry", "owner"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_SLIP.copy()
        obj["columns_display"] = SLIP_COLUMNS_DISPLAY.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id_slip"
        obj["file_name"] = "slips"
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_SLIP.copy()
        obj["file_field"] = None
        obj["custom_relation"] = None
        obj["page_title"] = "伝票" if lang == "ja" else "Slips"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_SLIP
        obj["pdf_pattern"] = "slipPDF-pattern-"
        obj["page_location"] = "data/slip/slip-table.html"
        obj["pdf_download_link"] = "slip_pdf_download"
        obj["update_obj_link"] = "update_slip"
        obj["drawer_url"] = "slip_drawer"
        obj["new_obj_link"] = "new_slip"
        obj["download_form_url"] = "slips"
        obj["editable_columns"] = {"slip_type": DEFAULT_SLIPS_TYPE_DISPLAY}
        obj["pdf_title"] = "伝票" if lang == "ja" else "Slip"
        obj["default_slip_name"] = "売上伝票" if lang == "ja" else "Sales Slips"
        obj["manage_obj_link"] = "slip_edit"
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_CAMPAIGN:
        obj["base_model"] = Post
        obj["custom_model"] = PostNameCustomField
        obj["custom_value_model"] = PostValueCustomField
        obj["custom_value_file_model"] = PostValueFile
        obj["file_upload_to"] = "campaign-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["custom_value_relation"] = "post"
        obj["base_columns"] = DEFAULT_COLUMN_SOCIAL_MEDIA_ALL_POSTS.copy()
        obj["default_columns"] = obj["base_columns"].copy()
        obj["columns_display"] = DISPLAY_COLUMNS_SOCIAL_MEDIA.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["reverse_query"] = None
        obj["id_field"] = None
        obj["file_name"] = "social_media_list"
        obj["file_field"] = None
        obj["custom_relation"] = None
        obj["page_title"] = "SNS" if lang == "ja" else "Social Media"
        obj["search_fields"] = None
        obj["download_formats"] = ["csv"]
        obj["download_form_url"] = None
        obj["view_types"] = [
            ("list", "Table", "テーブル"),
            ("calendar", "Calendar", "カレンダー"),
        ]
        obj["related_data"] = []
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_CAMPAIGN
        obj["app_target"] = "campaigns"

    # elif object_type == TYPE_OBJECT_EMAIL:
    #     obj['base_model'] = Post
    #     obj['custom_model'] = None
    #     obj['custom_value_model'] = None
    #     obj['custom_value_relation'] = None
    #     obj['base_columns'] = DEFAULT_COLUMN_EMAIL.copy()
    #     obj['default_columns'] = obj['base_columns'].copy()
    #     obj['columns_display'] = DISPLAY_COLUMNS_EMAIL.copy()
    #     obj['reverse_url'] = url_name_by_object_type(object_type)
    #     obj['reverse_query'] = 'studio_type=emails'
    #     obj['id_field'] = None
    #     obj['file_name'] = 'email_list'
    #     obj['file_field'] = None
    #     obj['custom_relation'] = None
    #     obj['page_title'] = 'メール' if lang == 'ja' else 'Emails'
    #     obj['search_fields'] = None
    #     obj['download_formats'] = ['csv']
    #     obj['download_form_url'] = None
    #     obj['view_types'] = [('list', 'Table', 'テーブル')]
    #     obj['related_data'] = []

    elif object_type == TYPE_OBJECT_TASK:
        obj["base_model"] = Task
        obj["custom_model"] = TaskCustomFieldName
        obj["custom_value_model"] = TaskCustomFieldValue
        obj["custom_value_relation"] = "task"
        obj["item_model"] = TaskItem
        obj["field_item_name"] = "task"
        obj["item_related_name"] = "item_task"
        obj["base_columns"] = get_model_columns(
            obj["base_model"], excludes=["order", "usage_status"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_TASK.copy()
        obj["columns_display"] = DISPLAY_COLUMNS_TASK.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_TASK.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["reverse_query"] = None
        obj["id_field"] = "task_id"
        obj["file_name"] = "task_list"
        obj["file_field"] = None
        obj["custom_relation"] = "task_custom_field_values"
        obj["page_title"] = "タスク" if lang == "ja" else "Tasks"
        obj["search_fields"] = None
        obj["download_formats"] = ["csv"]
        obj["download_form_url"] = None
        obj["view_types"] = [
            ("list", "Table", "テーブル"),
            ("calendar", "Calendar", "カレンダー"),
            ("gantt_chart", "Gantt Chart", "ガントチャート"),
            (
                "gantt_chart_with_production_line",
                "Gantt Chart (Production Line)",
                "ガントチャート(生産ライン)",
            ),
        ]
        obj["setting_type"] = "task"
        obj["setting_url"] = "workspace_setting"
        obj["editable_columns"] = {"status": TASKS_STATUS_DISPLAY}
        obj["related_data"] = []
        obj["download_form_url"] = "taskflow"

    elif object_type == TYPE_OBJECT_WORKFLOW:
        obj["base_model"] = Workflow
        obj["custom_model"] = None
        obj["custom_value_model"] = None
        obj["custom_value_relation"] = None
        obj["base_columns"] = DEFAULT_COLUMNS_WORKFLOW.copy()
        obj["default_columns"] = DEFAULT_COLUMNS_WORKFLOW.copy()
        obj["columns_display"] = DISPLAY_COLUMNS_WORKFLOW
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_WORKFLOW.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["reverse_query"] = None
        obj["id_field"] = "workflow_id"
        obj["file_name"] = "task_list"
        obj["file_field"] = None
        obj["custom_relation"] = None
        obj["page_title"] = "ワークフロー" if lang == "ja" else "Workflows"
        obj["search_fields"] = None
        obj["download_formats"] = None
        obj["download_form_url"] = None
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["related_data"] = []
        obj["setting_type"] = TYPE_OBJECT_WORKFLOW

    elif object_type == TYPE_OBJECT_INVENTORY:
        obj["base_model"] = ShopTurboInventory
        obj["custom_model"] = ShopTurboInventoryNameCustomField
        obj["custom_value_model"] = ShopTurboInventoryValueCustomField
        obj["custom_value_file_model"] = ShopTurboInventoryValueFile
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["file_upload_to"] = "shopturbo-inventory-custom-field-files"
        obj["custom_value_relation"] = "inventory"
        obj["base_columns"] = get_model_columns(
            obj["base_model"], excludes=["currency", "name"], includes=["owner"]
        )
        obj["default_columns"] = obj["base_columns"].copy()
        obj["columns_display"] = INVENTORY_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_INVENTORY
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["reverse_query"] = None
        obj["id_field"] = "inventory_id"
        obj["file_name"] = "inventory_list"
        obj["file_field"] = None
        obj["custom_relation"] = "shopturbo_inventory_custom_field_relations"
        obj["page_title"] = "在庫" if lang == "ja" else "Inventory"
        obj["search_fields"] = [obj["id_field"], "name"]
        obj["exclude_custom_types"] = [
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]
        obj["download_formats"] = ["csv"]
        obj["download_form_url"] = None
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_INVENTORY
        obj["editable_columns"] = {
            "item": "",
            "inventory_status": INVENTORY_STATUS_DISPLAY,
        }
        obj["view_types"] = [
            ("list", "Table", "テーブル"),
            ("forecast", "Inventory Changes", "在庫推移"),
        ]
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_INVENTORY_WAREHOUSE:
        obj["base_model"] = InventoryWarehouse
        obj["custom_model"] = InventoryWarehouseNameCustomField
        obj["custom_value_model"] = InventoryWarehouseValueCustomField
        obj["custom_value_file_model"] = InventoryWarehouseValueFile
        obj["file_upload_to"] = "inventory-warehouse-custom-field-files"
        obj["custom_value_relation"] = "warehouse"
        obj["base_columns"] = get_model_columns(
            obj["base_model"], excludes=["map_location_id"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_WAREHOUSE.copy()
        obj["columns_display"] = DISPLAY_COLUMNS_WAREHOUSE.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_WAREHOUSE.copy()

        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["reverse_query"] = None
        obj["id_field"] = "id_iw"
        obj["file_name"] = "warehouse_list"
        obj["file_field"] = None
        obj["custom_relation"] = "inventory_warehouse_custom_field_relations"
        obj["page_title"] = "ロケーション" if lang == "ja" else "Locations"
        obj["search_fields"] = [obj["id_field"], "name"]
        obj["download_formats"] = ["csv"]
        obj["download_form_url"] = None
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_INVENTORY_WAREHOUSE
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["exclude_custom_types"] = ["warehouse_objects"]
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_INVENTORY_TRANSACTION:
        obj["base_model"] = InventoryTransaction
        obj["custom_model"] = InventoryTransactionNameCustomField
        obj["custom_value_model"] = InventoryTransactionValueCustomField
        obj["custom_value_file_model"] = InventoryTransactionValueFile
        obj["file_upload_to"] = "inventory-transaction-custom-field-files"
        obj["custom_value_relation"] = "transaction"
        obj["base_columns"] = get_model_columns(
            obj["base_model"],
            excludes=DEFAULT_COLUMNS_TRANSACTION.copy() + ["usage_status"],
            includes=["owner"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_TRANSACTION.copy()
        obj["columns_display"] = INVENTORY_TRANSACTION_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_TRANSACTION.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["reverse_query"] = None
        obj["id_field"] = "transaction_id"
        obj["file_name"] = "transaction_list"
        obj["file_field"] = None
        obj["custom_relation"] = "inventory_transaction_custom_field_relations"
        obj["page_title"] = "入出庫" if lang == "ja" else "Transactions"
        obj["search_fields"] = [obj["id_field"], "name"]
        obj["download_formats"] = ["csv"]
        obj["download_form_url"] = None
        obj["setting_url"] = "workspace_setting"
        obj["exclude_custom_types"] = [
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]
        obj["setting_type"] = TYPE_OBJECT_INVENTORY_TRANSACTION
        obj["view_types"] = [
            ("list", "Table", "テーブル"),
            ("stock_ledger", "Stock Ledger - Moving Average", "商品有高帳 - 移動平均"),
            ("fifo_ledger", "Fifo Ledger - FIFO", "商品有高帳 - FIFO"),
        ]
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_TIMEGENIE or object_type == "attendance":
        obj["base_model"] = Track
        obj["custom_model"] = TrackNameCustomField
        obj["custom_value_model"] = TrackValueCustomField
        obj["custom_value_file_model"] = TrackValueFile
        obj["custom_value_relation"] = "track"
        obj["base_columns"] = get_model_columns(
            Track, excludes=["created_at", "hours", "minutes", "seconds"]
        )
        obj["default_columns"] = DEFAULT_COLUMN_TIMEGENIE.copy()
        obj["columns_display"] = TIMEGENIE_COLUMNS_DISPLAY.copy()
        # obj['reverse_url'] = 'timegenie'
        obj["id_field"] = "track_id"
        obj["file_name"] = "attendance"
        obj["file_field"] = None
        obj["download_formats"] = ["csv"]
        obj["row_detail_url"] = "timegenie_row_detail"
        # obj['download_form_url'] = 'timegenie'
        obj["custom_relation"] = "track_custom_field_value"
        obj["page_title"] = "勤怠" if lang == "ja" else "Attendance"
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = "attendance"
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["app_target"] = "attendance"
        obj["file_upload_to"] = "track-customfield-files"

    elif object_type == "item-price":
        obj["base_model"] = ShopTurboItemsPrice

    elif object_type == TYPE_OBJECT_USER_MANAGEMENT:
        obj["base_model"] = UserManagement
        obj["custom_model"] = UserManagementNameCustomField
        obj["custom_value_model"] = UserManagementValueCustomField
        obj["custom_value_file_model"] = UserManagementValueFile
        obj["file_upload_to"] = "user-management-custom-field-files"
        obj["custom_value_relation"] = "usermanagement"
        obj["base_columns"] = get_model_columns(
            obj["base_model"],
            excludes=["type", "view_only_password", "location", "date_from", "date_to"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_USER_MANAGEMENT.copy()
        obj["columns_display"] = USER_MANAGEMENT_COLUMNS_DISPLAY.copy()
        obj["id_field"] = "id_user"
        obj["page_title"] = "ユーザー管理" if lang == "ja" else "User Manager"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_USER_MANAGEMENT
        obj["manage_obj_link"] = "user_management_drawer"
        obj["row_detail_url"] = "user_management_row_detail"
        obj["edit"] = "user_management_edit"
        obj["default_order_name"] = "デフォルト" if lang == "ja" else "Default"
        obj["related_data"] = []

    elif object_type == TYPE_OBJECT_WORKER:
        obj["base_model"] = Worker
        obj["custom_model"] = WorkerNameCustomField
        obj["custom_value_model"] = WorkerValueCustomField
        obj["custom_value_file_model"] = WorkerValueFile
        obj["file_upload_to"] = "worker-custom-field-files"
        obj["custom_value_relation"] = "worker"
        obj["base_columns"] = get_model_columns(
            obj["base_model"], excludes=["created_at", "team"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_WORKER.copy()
        obj["columns_display"] = WORKER_COLUMNS_DISPLAY.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id_worker"
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["file_name"] = "employee"
        obj["file_field"] = None
        obj["custom_relation"] = None
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_WORKER
        obj["manage_obj_link"] = "worker_manage"
        obj["row_detail_url"] = "worker_row_detail"
        # obj['editable_columns'] = {'status': THREAD_STATUS_DISPLAY}
        obj["download_formats"] = ["csv"]
        obj["field_item_name"] = "worker"
        obj["custom_relation"] = "worker_custom_field_relations"
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_WORKER_REVIEW:
        obj["base_model"] = WorkerReviews
        obj["custom_model"] = WorkerReviewsNameCustomField
        obj["custom_value_model"] = WorkerReviewsValueCustomField
        obj["custom_value_file_model"] = WorkerReviewsValueFile
        obj["file_upload_to"] = "worker-custom-field-files"
        obj["custom_value_relation"] = "worker_review"
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["base_columns"] = get_model_columns(
            obj["base_model"],
            excludes=["created_at", "whats_going_well", "areas_of_improvements"],
            includes=["review"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_WORKER_REVIEW.copy()
        obj["columns_display"] = WORKER_REVIEW_COLUMNS_DISPLAY.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id_worker_review"
        obj["file_name"] = "review"
        obj["file_field"] = None
        obj["custom_relation"] = None
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_WORKER_REVIEW
        obj["manage_obj_link"] = "worker_review_manage"
        obj["row_detail_url"] = "worker_review_row_detail"
        obj["download_formats"] = ["csv"]
        # obj['editable_columns'] = {'status': THREAD_STATUS_DISPLAY}
        obj["field_item_name"] = "worker_review"
        obj["custom_relation"] = "worker_review_custom_field_relations"
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_WORKER_ABSENCE:
        obj["base_model"] = Absence
        obj["custom_model"] = AbsenceNameCustomField
        obj["custom_value_model"] = AbsenceValueCustomField
        obj["custom_value_file_model"] = AbsenceValueFile
        obj["file_upload_to"] = "worker-custom-field-files"
        obj["custom_value_relation"] = "absence"
        obj["view_types"] = [("list", "Table", "テーブル")]
        # Exclude status from base_columns to prevent it from appearing in column options
        obj["base_columns"] = get_model_columns(
            obj["base_model"],
            includes=["approved_by", "requested_by"],
            excludes=["created_at", "requester_name", "status"],  # Added "status" to excludes
        )
        obj["default_columns"] = DEFAULT_COLUMNS_ABSENCE.copy()
        obj["columns_display"] = ABSENCE_COLUMNS_DISPLAY.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id_absence"
        obj["file_name"] = "leaves"
        obj["file_field"] = None
        obj["custom_relation"] = None
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_WORKER_ABSENCE
        obj["manage_obj_link"] = "worker_absence_manage"
        obj["download_formats"] = ["csv"]
        obj["row_detail_url"] = "worker_absence_detail"
        obj["field_item_name"] = "absence"
        obj["custom_relation"] = "absence_custom_field_relations"
        # obj['editable_columns'] = {'status': THREAD_STATUS_DISPLAY}
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_JOURNAL:
        obj["base_model"] = JournalEntry
        obj["item_model"] = JournalEntryTransaction
        obj["custom_model"] = JournalEntryNameCustomField
        obj["custom_value_model"] = JournalEntryValueCustomField
        obj["custom_value_file_model"] = JournalEntryValueFile
        obj["file_upload_to"] = "journal-custom-field-files"
        obj["custom_value_relation"] = "journal"
        obj["page_title"] = "仕訳" if lang == "ja" else "Journal Entries"
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["base_columns"] = get_model_columns(
            obj["base_model"],
            excludes=[
                "settle_choice",
                "created_at",
                "type_journal",
                "amount_debit_with_tax",
                "amount_credit_with_tax",
                "currency",
                "settle_journal",
            ],
            includes=["partner", "owner"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_JOURNAL.copy()
        obj["columns_display"] = JOURNAL_COLUMNS_DISPLAY.copy()
        obj["download_form_url"] = "journal"
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_JOURNAL.copy()
        obj["id_field"] = "id_journal"
        obj["search_fields"] = ["contact__name", "contact__last_name", "company__name"]
        obj["file_name"] = "journal"
        obj["file_field"] = None
        obj["field_item_name"] = "journal"
        obj["custom_relation"] = None
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_JOURNAL
        obj["manage_obj_link"] = "journal_manage_drawer"
        obj["download_formats"] = ["csv"]
        obj["additional_column_fields"] = ["counter_category", "tax_rate"]
        obj["row_detail_url"] = "journal_row_detail"
        obj["editable_columns"] = {
            "category": JOURNAL_CATEGORY_DISPLAY,
            "counter_category": JOURNAL_COUNTER_CATEGORY_DISPLAY,
            "tax_rate": JOURNAL_TAX_CATEGORY_DISPLAY,
        }
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"
        obj["view_types"] = [
            ("table", "Table", "テーブル"),
            ("balance_sheet", "Balance Sheet", "バランスシート"),
            ("profit_and_loss", "Profit and Loss Sheet", "損益計算書"),
            ("payable_list", "Accounts Payable Balance List", "買掛残高一覧表"),
            ("receivable_list", "Accounts Receivable Balance List", "売掛残高一覧表"),
        ]

    elif object_type == TYPE_OBJECT_FORM:
        obj["base_model"] = Form
        obj["custom_value_relation"] = "formroom"
        obj["base_columns"] = get_model_columns(
            obj["base_model"], excludes=["description", "updated_at"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_FORM.copy()
        obj["columns_display"] = FORM_COLUMNS_DISPLAY.copy()
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_FORM
        obj["manage_obj_link"] = "form_manage"
        # obj['row_detail_url'] = 'worker_row_detail'
        obj["field_item_name"] = "formroom"
        obj["custom_relation"] = "formroom_custom_field_relations"
        obj["related_data"] = []

    elif object_type == TYPE_OBJECT_JOBS:
        obj["base_model"] = Job
        obj["custom_model"] = JobNameCustomField
        obj["custom_value_model"] = JobValueCustomField
        obj["custom_value_relation"] = "job"

        obj["custom_value_file_model"] = JobValueFile
        obj["file_upload_to"] = "job-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"

        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["base_columns"] = get_model_columns(
            obj["base_model"],
            excludes=[],
            includes=["preview", "applications", "interview", "scorecard"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_JOBS.copy()
        obj["columns_display"] = JOBS_COLUMNS_DISPLAY.copy()

        obj["id_field"] = "job_id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["field_item_name"] = None
        obj["custom_relation"] = "job_custom_field_relations"
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_JOBS
        obj["editable_columns"] = {
            "status": JOBS_STATUS_DISPLAY,
            "job_type": JOBS_TYPE_DISPLAY,
        }
        obj["manage_obj_link"] = ""
        obj["download_formats"] = ["csv"]
        obj["row_detail_url"] = ""
        obj["related_data"] = []

    elif object_type == TYPE_OBJECT_JOBS_APPLICANT:
        obj["base_model"] = JobApplication
        obj["custom_model"] = JobApplicantNameCustomField
        obj["custom_value_model"] = JobApplicantValueCustomField
        obj["custom_value_relation"] = "applicant"

        obj["custom_value_file_model"] = JobApplicantValueFile
        obj["file_upload_to"] = "job-applicant-customfield-files"
        obj["custom_value_file_relation"] = "valuecustomfield"

        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["base_columns"] = get_model_columns(
            obj["base_model"], excludes=[], includes=[]
        )
        obj["default_columns"] = DEFAULT_COLUMN_APPLICANT.copy()
        obj["columns_display"] = APPLICANT_COLUMN_DISPLAY.copy()
        obj["editable_columns"] = {
            "email": "",
            "status": "",
            "current_company": "",
            "resume": "",
            "phone_number": "",
            "url": "",
        }

        obj["id_field"] = "job_applicant_id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["field_item_name"] = None
        obj["custom_relation"] = "applicant_custom_field_relations"
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_JOBS_APPLICANT
        obj["manage_obj_link"] = ""
        obj["download_formats"] = ["csv"]
        obj["row_detail_url"] = ""
        obj["related_data"] = []
        obj["parent_object"] = TYPE_OBJECT_JOBS

    elif object_type == TYPE_OBJECT_PANEL:
        obj["base_model"] = ReportPanel
        obj["custom_model"] = ReportPanelNameCustomField
        obj["custom_value_model"] = ReportPanelValueCustomField
        obj["custom_value_relation"] = "panels"
        obj["view_types"] = [("Table")]
        obj["base_columns"] = get_model_columns(
            obj["base_model"], excludes=[], includes=["is_deleted", "created_by"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_PANEL.copy()
        obj["columns_display"] = PANEL_COLUMNS_DISPLAY.copy()

        obj["id_field"] = "panel_id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["field_item_name"] = None
        obj["custom_relation"] = None
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_PANEL
        obj["manage_obj_link"] = ""
        obj["download_formats"] = ["csv"]
        obj["row_detail_url"] = ""
        obj["related_data"] = []
        obj["app_target"] = "reports"

    elif object_type == TYPE_OBJECT_DASHBOARD:
        obj["base_model"] = Report
        obj["custom_model"] = ReportNameCustomField
        obj["custom_value_model"] = ReportValueCustomField
        obj["custom_value_relation"] = "reports"
        obj["view_types"] = [("Table")]
        obj["base_columns"] = get_model_columns(
            obj["base_model"], excludes=[], includes=[]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_DASHBOARD.copy()
        obj["columns_display"] = DASHBOARD_COLUMNS_DISPLAY.copy()

        obj["id_field"] = ""
        obj["file_name"] = None
        obj["file_field"] = None
        obj["field_item_name"] = None
        obj["custom_relation"] = None
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_DASHBOARD
        obj["manage_obj_link"] = ""
        obj["download_formats"] = ["csv"]
        obj["row_detail_url"] = ""
        obj["related_data"] = []
        obj["app_target"] = "reports"
        obj["page_type"] = "dashboards"
        obj["page_title"] = "ダッシュボード" if lang == "ja" else "Dashboards"

    elif object_type == TYPE_OBJECT_CONTRACT:
        obj["base_model"] = ContractDocument
        obj["custom_model"] = ContractDocumentNameCustomField
        obj["custom_value_model"] = ContractDocumentValueCustomField
        obj["custom_value_relation"] = "document"

        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["base_columns"] = get_model_columns(
            obj["base_model"], excludes=["email_body", "email_sent_at"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_CONTRACT.copy()
        obj["columns_display"] = CONTRACT_COLUMNS_DISPLAY.copy()

        obj["id_field"] = "contract_id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["app_target"] = "shopturbo"
        obj["page_title"] = "契約" if lang == "ja" else "Contract"
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_CONTRACT
        obj["editable_columns"] = {
            "signer": JOB_STATUS_DISPLAY,
            "signature": JOB_TYPE_DISPLAY,
        }
        obj["manage_obj_link"] = ""
        obj["row_detail_url"] = ""
        obj["related_data"] = []

    elif object_type == TYPE_OBJECT_CUSTOM_OBJECT:
        obj["custom_object"] = CustomObject
        obj["base_model"] = CustomObjectPropertyRow
        obj["custom_model"] = CustomObjectPropertyName
        obj["custom_value_model"] = CustomObjectPropertyValue
        obj["custom_relation"] = "custom_object_property_row_relations"
        obj["default_columns"] = ["row_id", "usage_status", "created_at", "updated_at"]
        obj["columns_display"] = CUSTOM_OBJECT_COLUMN_DISPLAY.copy()
        obj["custom_value_relation"] = "object"
        obj["id_field"] = "row_id"
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"

    elif object_type == TYPE_OBJECT_CASE_LINE_ITEM:
        obj["base_model"] = DealsItems
        obj["custom_model"] = DealsItemsNameCustomField
        obj["custom_value_model"] = DealsItemsValueCustomField
        obj["custom_value_relation"] = "item_deal"
        obj["custom_value_file_model"] = ""
        obj["file_upload_to"] = ""
        obj["custom_value_file_relation"] = ""
        obj["custom_value_related_model"] = []
        obj["base_columns"] = get_model_columns(
            obj["base_model"], excludes=["product_id", "created_at"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_ITEM.copy()
        obj["columns_display"] = ITEMS_COLUMNS_DISPLAY.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["custom_relation"] = "dealsitemsvaluecustomfield_set"
        obj["page_title"] = "商品" if lang == "ja" else "Items"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [
            ("list", "Table", "テーブル"),
            ("group", "Group Table", "グループテーブル"),
        ]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_CASE_LINE_ITEM
        obj["editable_columns"] = ["name"]
        obj["required_properties"] = ["name"]
        obj["exclude_custom_types"] = [
            "date_range",
            "bill_objects",
            "purchase_order",
            "part",
        ]
        obj["multiple_custom_types"] = ["components"]
        obj["related_data"] = ["order"]
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_ORDER_LINE_ITEM:
        obj["base_model"] = ShopTurboItemsOrders
        obj["custom_model"] = ShopTurboItemsOrdersNameCustomField
        obj["custom_value_model"] = ShopTurboItemsValueCustomField
        obj["custom_value_relation"] = "shopturbo_item_orders"
        obj["custom_value_file_model"] = ""
        obj["file_upload_to"] = ""
        obj["custom_value_file_relation"] = ""
        obj["custom_value_related_model"] = []
        obj["base_columns"] = get_model_columns(
            obj["base_model"], excludes=["product_id", "created_at"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_ITEM.copy()
        obj["columns_display"] = ITEMS_COLUMNS_DISPLAY.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["custom_relation"] = ""
        obj["page_title"] = "商品" if lang == "ja" else "Items"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [
            ("list", "Table", "テーブル"),
            ("group", "Group Table", "グループテーブル"),
        ]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_ORDER_LINE_ITEM
        obj["editable_columns"] = ["name"]
        obj["required_properties"] = ["name"]
        obj["exclude_custom_types"] = [
            "date_range",
            "bill_objects",
            "purchase_order",
            "part",
        ]
        obj["multiple_custom_types"] = ["components"]
        obj["related_data"] = ["order"]
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_COMMERCE_METER:
        obj["base_model"] = CommerceMeter
        obj["custom_model"] = CommerceMeterNameCustomField
        obj["custom_value_model"] = CommerceMeterValueCustomField
        obj["custom_value_relation"] = "meter"
        obj["custom_value_file_model"] = CommerceMeterValueFile
        obj["file_upload_to"] = "commerce-meter-field-files"
        obj["custom_value_file_relation"] = ""
        obj["custom_value_related_model"] = []
        obj["base_columns"] = get_model_columns(
            obj["base_model"], excludes=["product_id", "created_at"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_COMMERCE_METER.copy()
        obj["columns_display"] = COMMERCE_METER_COLUMN_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELDS_COMMERCE_METER.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "meter_id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["custom_relation"] = "commerce_meter_custom_field_relations"
        obj["page_title"] = "メーター" if lang == "ja" else "Meter"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [
            ("list", "Table", "テーブル"),
        ]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_COMMERCE_METER
        obj["editable_columns"] = []
        obj["required_properties"] = []
        obj["exclude_custom_types"] = []
        obj["multiple_custom_types"] = []
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"

    else:
        return None

    # Set the type field to match the original object_type parameter
    obj["type"] = original_object_type

    # Add additional Column display (Especially in commerce)
    if obj["additional_columns_display"] and obj["columns_display"]:
        obj["columns_display"].update(obj["additional_columns_display"])
    if not obj["item_related_name"] and obj["item_model"]:
        for field in obj["item_model"]._meta.fields:
            try:
                if field.related_model == obj["base_model"]._meta.model:
                    obj["item_related_name"] = field._related_name
                    break
            except Exception as e:
                logger.error("ERROR: %s", e)
    return obj
