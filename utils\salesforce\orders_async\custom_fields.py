"""
Custom fields manager for Orders import.
Handles dynamic custom field creation and value storage for both Orders and LineItems.
Memory-safe implementation with chunked processing.
"""

from typing import Dict, List, Any

from data.models import (
    ShopTurboOrdersNameCustomField,
    ShopTurboOrdersValueCustomField,
    ShopTurboItemsOrdersNameCustomField,
    ShopTurboItemsOrdersValueCustomField,
)
from utils.error_logger.import_export_logger import ImportExportLogger
from .config import CUSTOM_FIELD_CHUNK_SIZE


class CustomFieldsManager:
    """
    Manages custom fields for Orders and LineItems.
    """

    def __init__(
        self,
        workspace_id: str,
        error_logger: ImportExportLogger,
    ):
        self.workspace_id = workspace_id
        self.error_logger = error_logger

        # Caches for custom field definitions
        self.order_custom_fields: Dict[str, str] = {}  # field_name -> field_id
        self.order_custom_field_types: Dict[str, str] = {}  # field_name -> field_type
        self.line_item_custom_fields: Dict[str, str] = {}  # field_name -> field_id
        self.line_item_custom_field_types: Dict[
            str, str
        ] = {}  # field_name -> field_type

    async def initialize(self) -> None:
        """Initialize custom field caches."""
        await self._load_order_custom_fields()
        await self._load_line_item_custom_fields()

        await self.error_logger.alog_info(
            "CustomFieldsManager",
            f"Initialized custom fields - Orders: {len(self.order_custom_fields)}, "
            f"LineItems: {len(self.line_item_custom_fields)}",
        )

    async def _load_order_custom_fields(self) -> None:
        """Load existing order custom field definitions."""
        try:
            fields = ShopTurboOrdersNameCustomField.objects.filter(
                workspace_id=self.workspace_id,
                name__isnull=False,
            )

            async for field in fields:
                self.order_custom_fields[field.name] = str(field.id)
                # Cache the field type
                if field.type:
                    self.order_custom_field_types[field.name] = field.type
                    # NOTE: Verboose Log
                    # await self.error_logger.alog_info(
                    #     "CustomFieldsManager",
                    #     f"Loaded order field '{field.name}' with type '{field.type}'",
                    # )

        except Exception as e:
            await self.error_logger.alog_warning(
                "CustomFieldsManager", f"Error loading order custom fields: {str(e)}"
            )

    async def _load_line_item_custom_fields(self) -> None:
        """Load existing line item custom field definitions."""
        try:
            fields = ShopTurboItemsOrdersNameCustomField.objects.filter(
                workspace_id=self.workspace_id,
                name__isnull=False,
            )

            async for field in fields:
                self.line_item_custom_fields[field.name] = str(field.id)
                # Cache the field type
                if field.type:
                    self.line_item_custom_field_types[field.name] = field.type
                    # NOTE: Verboose Log
                    # await self.error_logger.alog_info(
                    #     "CustomFieldsManager",
                    #     f"Loaded line item field '{field.name}' with type '{field.type}'",
                    # )

        except Exception as e:
            await self.error_logger.alog_warning(
                "CustomFieldsManager",
                f"Error loading line item custom fields: {str(e)}",
            )

    def determine_field_type(
        self, value: Any, field_name: str = None, is_line_item: bool = False
    ) -> str:
        """
        Determine the type of a custom field based on its value or existing field definition.

        Args:
            value: Field value
            field_name: Optional field name to check for existing type
            is_line_item: Whether this is a line item field

        Returns:
            Field type string
        """
        # First, check if we have an existing field type cached
        if field_name:
            if is_line_item and field_name in self.line_item_custom_field_types:
                existing_type = self.line_item_custom_field_types[field_name]
                # Preserving existing field type
                return existing_type
            elif not is_line_item and field_name in self.order_custom_field_types:
                existing_type = self.order_custom_field_types[field_name]
                # Preserving existing field type
                return existing_type

        # If no existing type, determine from value
        if value is None:
            return "text"

        # Check for boolean
        if isinstance(value, bool) or str(value).lower() in ["true", "false"]:
            return "checkbox"

        # Check for number
        if isinstance(value, (int, float)):
            return "number"

        # Check for date/datetime patterns
        value_str = str(value)
        if any(pattern in value_str for pattern in ["-", "/", "T", "Z"]):
            if "T" in value_str or ":" in value_str:
                return "datetime"
            elif "-" in value_str or "/" in value_str:
                return "date"

        # Check for email
        if "@" in value_str and "." in value_str:
            return "email"

        # Check for URL
        if value_str.startswith(("http://", "https://", "www.")):
            return "url"

        # Check for long text
        if len(value_str) > 255:
            return "textarea"

        # Default to text (which can also be used for choice fields with text values)
        return "text"

    async def process_order_custom_fields(
        self,
        custom_fields_batch: List[Dict[str, Any]],
        opportunity_order_map: Dict[str, str],
    ) -> None:
        """
        Process and save custom fields for orders using bulk operations with memory-safe chunking.

        Args:
            custom_fields_batch: List of custom field data
            opportunity_order_map: Mapping of Opportunity IDs to Order IDs
        """
        if not custom_fields_batch:
            return

        await self.error_logger.alog_info(
            "CustomFieldsManager",
            f"Processing {len(custom_fields_batch)} order custom fields",
        )

        # Group by field name to create field definitions
        fields_to_create = {}
        for field_data in custom_fields_batch:
            field_name = field_data.get("field_name")
            if field_name and field_name not in self.order_custom_fields:
                if field_name not in fields_to_create:
                    fields_to_create[field_name] = field_data.get("field_type", "text")

        # Create new field definitions
        if fields_to_create:
            await self.error_logger.alog_info(
                "CustomFieldsManager",
                f"Creating {len(fields_to_create)} new order field definitions: {list(fields_to_create.keys())}",
            )
            await self._create_order_field_definitions(fields_to_create)

        # Process in chunks to limit memory usage
        chunk_size = CUSTOM_FIELD_CHUNK_SIZE
        total_created = 0
        total_updated = 0

        # Group by order to make chunking more efficient
        fields_by_order = {}
        for field_data in custom_fields_batch:
            salesforce_id = field_data.get("salesforce_id")
            if salesforce_id:
                if salesforce_id not in fields_by_order:
                    fields_by_order[salesforce_id] = []
                fields_by_order[salesforce_id].append(field_data)

        # Process orders in chunks
        order_ids_list = list(fields_by_order.keys())
        for i in range(0, len(order_ids_list), chunk_size):
            chunk_order_ids = order_ids_list[i : i + chunk_size]
            chunk_fields = []

            # Collect all fields for this chunk of orders
            for order_sf_id in chunk_order_ids:
                chunk_fields.extend(fields_by_order[order_sf_id])

            # Process this chunk
            created, updated = await self._process_order_fields_chunk(
                chunk_fields, opportunity_order_map
            )

            total_created += created
            total_updated += updated

            # Log progress for large batches
            if len(order_ids_list) > chunk_size:
                progress = min(i + chunk_size, len(order_ids_list))
                await self.error_logger.alog_info(
                    "CustomFieldsManager",
                    f"Processed {progress}/{len(order_ids_list)} orders' custom fields",
                )

        # Final summary log
        if total_created > 0:
            await self.error_logger.alog_info(
                "CustomFieldsManager",
                f"Created {total_created} order custom field values",
            )
        if total_updated > 0:
            await self.error_logger.alog_info(
                "CustomFieldsManager",
                f"Updated {total_updated} order custom field values",
            )

    async def _process_order_fields_chunk(
        self,
        chunk_fields: List[Dict[str, Any]],
        opportunity_order_map: Dict[str, str],
    ) -> tuple[int, int]:
        """
        Process a chunk of order custom fields with bounded memory usage.

        Args:
            chunk_fields: Custom field data for a chunk of orders
            opportunity_order_map: Mapping of Opportunity IDs to Order IDs

        Returns:
            Tuple of (created_count, updated_count)
        """
        # Collect order IDs and field IDs for this chunk
        chunk_order_ids = set()
        chunk_field_ids = set()

        for field_data in chunk_fields:
            salesforce_id = field_data.get("salesforce_id")
            field_name = field_data.get("field_name")

            if salesforce_id and field_name:
                order_id = opportunity_order_map.get(salesforce_id)
                field_id = self.order_custom_fields.get(field_name)

                if order_id and field_id:
                    chunk_order_ids.add(order_id)
                    chunk_field_ids.add(field_id)

        # Bulk fetch existing values for this chunk only
        existing_values_dict = {}
        if chunk_order_ids and chunk_field_ids:
            existing_values = ShopTurboOrdersValueCustomField.objects.filter(
                orders_id__in=list(chunk_order_ids),
                field_name_id__in=list(chunk_field_ids),
            )

            # Build dictionary for O(1) lookups
            async for existing in existing_values:
                key = (str(existing.orders_id), str(existing.field_name_id))
                existing_values_dict[key] = existing

        # Process field values for this chunk
        values_to_create = []
        values_to_update = []

        for field_data in chunk_fields:
            salesforce_id = field_data.get("salesforce_id")
            field_name = field_data.get("field_name")
            value = field_data.get("value")

            if not salesforce_id or not field_name:
                continue

            # Get order ID from mapping
            order_id = opportunity_order_map.get(salesforce_id)
            if not order_id:
                continue

            # Get field definition ID
            field_id = self.order_custom_fields.get(field_name)
            if not field_id:
                continue

            # Check if value already exists using our dictionary (O(1) lookup!)
            key = (str(order_id), str(field_id))
            existing = existing_values_dict.get(key)

            if existing:
                # Update existing value - handle empty strings and None distinctly
                # Convert the value appropriately
                if value is None:
                    new_value = None
                elif value == "":
                    new_value = ""  # Preserve empty string
                else:
                    new_value = str(value)

                # Only update if the value has actually changed
                if existing.value != new_value:
                    existing.value = new_value
                    values_to_update.append(existing)
            else:
                # Create new value
                values_to_create.append(
                    ShopTurboOrdersValueCustomField(
                        orders_id=order_id,
                        field_name_id=field_id,
                        value=str(value) if value is not None else None,
                    )
                )

        # Bulk create new values
        if values_to_create:
            await self.error_logger.alog_info(
                "CustomFieldsManager",
                f"Creating {len(values_to_create)} new order custom field values",
            )
            await ShopTurboOrdersValueCustomField.objects.abulk_create(
                values_to_create, ignore_conflicts=True
            )

        # Bulk update existing values
        if values_to_update:
            await self.error_logger.alog_info(
                "CustomFieldsManager",
                f"Updating {len(values_to_update)} existing order custom field values",
            )
            await ShopTurboOrdersValueCustomField.objects.abulk_update(
                values_to_update, ["value"]
            )

        return len(values_to_create), len(values_to_update)

    async def process_line_item_custom_fields(
        self,
        custom_fields_batch: List[Dict[str, Any]],
        line_item_map: Dict[str, str],
    ) -> None:
        """
        Process and save custom fields for line items using bulk operations with memory-safe chunking.

        Args:
            custom_fields_batch: List of custom field data
            line_item_map: Mapping of LineItem IDs to ItemOrder IDs
        """
        if not custom_fields_batch:
            return

        # Group by field name to create field definitions
        fields_to_create = {}
        for field_data in custom_fields_batch:
            field_name = field_data.get("field_name")
            if field_name and field_name not in self.line_item_custom_fields:
                if field_name not in fields_to_create:
                    fields_to_create[field_name] = field_data.get("field_type", "text")

        # Create new field definitions
        if fields_to_create:
            await self._create_line_item_field_definitions(fields_to_create)

        # Process in chunks to limit memory usage
        chunk_size = CUSTOM_FIELD_CHUNK_SIZE * 2  # Line items can have more per chunk
        total_created = 0
        total_updated = 0

        # Process line items in chunks
        for i in range(0, len(custom_fields_batch), chunk_size):
            chunk_fields = custom_fields_batch[i : i + chunk_size]

            # Process this chunk
            created, updated = await self._process_line_item_fields_chunk(
                chunk_fields, line_item_map
            )

            total_created += created
            total_updated += updated

            # Log progress for large batches
            if len(custom_fields_batch) > chunk_size:
                progress = min(i + chunk_size, len(custom_fields_batch))
                await self.error_logger.alog_info(
                    "CustomFieldsManager",
                    f"Processed {progress}/{len(custom_fields_batch)} line item custom fields",
                )

        # Final summary log
        if total_created > 0:
            await self.error_logger.alog_info(
                "CustomFieldsManager",
                f"Created {total_created} line item custom field values",
            )
        if total_updated > 0:
            await self.error_logger.alog_info(
                "CustomFieldsManager",
                f"Updated {total_updated} line item custom field values",
            )

    async def _process_line_item_fields_chunk(
        self,
        chunk_fields: List[Dict[str, Any]],
        line_item_map: Dict[str, str],
    ) -> tuple[int, int]:
        """
        Process a chunk of line item custom fields with bounded memory usage.

        Args:
            chunk_fields: Custom field data for a chunk of line items
            line_item_map: Mapping of LineItem IDs to ItemOrder IDs

        Returns:
            Tuple of (created_count, updated_count)
        """
        # Collect item order IDs and field IDs for this chunk
        chunk_item_order_ids = set()
        chunk_field_ids = set()

        for field_data in chunk_fields:
            salesforce_id = field_data.get("salesforce_id")
            field_name = field_data.get("field_name")

            if salesforce_id and field_name:
                item_order_id = line_item_map.get(salesforce_id)
                field_id = self.line_item_custom_fields.get(field_name)

                if item_order_id and field_id:
                    chunk_item_order_ids.add(item_order_id)
                    chunk_field_ids.add(field_id)

        # Bulk fetch existing values for this chunk only
        existing_values_dict = {}
        if chunk_item_order_ids and chunk_field_ids:
            existing_values = ShopTurboItemsOrdersValueCustomField.objects.filter(
                item_order_id__in=list(chunk_item_order_ids),
                field_name_id__in=list(chunk_field_ids),
            )

            # Build dictionary for O(1) lookups
            async for existing in existing_values:
                key = (str(existing.item_order_id), str(existing.field_name_id))
                existing_values_dict[key] = existing

        # Process field values for this chunk
        values_to_create = []
        values_to_update = []

        for field_data in chunk_fields:
            salesforce_id = field_data.get("salesforce_id")
            field_name = field_data.get("field_name")
            value = field_data.get("value")

            if not salesforce_id or not field_name:
                continue

            # Get item order ID from mapping
            item_order_id = line_item_map.get(salesforce_id)
            if not item_order_id:
                continue

            # Get field definition ID
            field_id = self.line_item_custom_fields.get(field_name)
            if not field_id:
                continue

            # Check if value already exists using our dictionary (O(1) lookup!)
            key = (str(item_order_id), str(field_id))
            existing = existing_values_dict.get(key)

            if existing:
                # Update existing value - handle empty strings and None distinctly
                # Convert the value appropriately
                if value is None:
                    new_value = None
                elif value == "":
                    new_value = ""  # Preserve empty string
                else:
                    new_value = str(value)

                # Only update if the value has actually changed
                if existing.value != new_value:
                    existing.value = new_value
                    values_to_update.append(existing)
            else:
                # Create new value
                values_to_create.append(
                    ShopTurboItemsOrdersValueCustomField(
                        workspace_id=self.workspace_id,
                        item_order_id=item_order_id,
                        field_name_id=field_id,
                        value=str(value) if value is not None else None,
                    )
                )

        # Bulk create new values
        if values_to_create:
            await self.error_logger.alog_info(
                "CustomFieldsManager",
                f"Creating {len(values_to_create)} new line item custom field values",
            )
            await ShopTurboItemsOrdersValueCustomField.objects.abulk_create(
                values_to_create, ignore_conflicts=True
            )

        # Bulk update existing values
        if values_to_update:
            await self.error_logger.alog_info(
                "CustomFieldsManager",
                f"Updating {len(values_to_update)} existing line item custom field values",
            )
            await ShopTurboItemsOrdersValueCustomField.objects.abulk_update(
                values_to_update, ["value"]
            )

        return len(values_to_create), len(values_to_update)

    async def _create_order_field_definitions(
        self,
        fields_to_create: Dict[str, str],
    ) -> None:
        """
        Create new order custom field definitions.

        Args:
            fields_to_create: Dictionary of field_name -> field_type
        """
        new_fields = []

        for field_name, field_type in fields_to_create.items():
            new_field = ShopTurboOrdersNameCustomField(
                workspace_id=self.workspace_id,
                name=field_name,
                type=field_type,
            )
            new_fields.append(new_field)

        if new_fields:
            created_fields = await ShopTurboOrdersNameCustomField.objects.abulk_create(
                new_fields, ignore_conflicts=True
            )

            # Update cache with new fields
            for field in created_fields:
                self.order_custom_fields[field.name] = str(field.id)
                if field.type:
                    self.order_custom_field_types[field.name] = field.type

            await self.error_logger.alog_info(
                "CustomFieldsManager",
                f"Created {len(created_fields)} order custom field definitions",
            )

    async def _create_line_item_field_definitions(
        self,
        fields_to_create: Dict[str, str],
    ) -> None:
        """
        Create new line item custom field definitions.

        Args:
            fields_to_create: Dictionary of field_name -> field_type
        """
        new_fields = []

        for field_name, field_type in fields_to_create.items():
            new_field = ShopTurboItemsOrdersNameCustomField(
                workspace_id=self.workspace_id,
                name=field_name,
                type=field_type,
            )
            new_fields.append(new_field)

        if new_fields:
            created_fields = (
                await ShopTurboItemsOrdersNameCustomField.objects.abulk_create(
                    new_fields, ignore_conflicts=True
                )
            )

            # Update cache with new fields
            for field in created_fields:
                self.line_item_custom_fields[field.name] = str(field.id)
                if field.type:
                    self.line_item_custom_field_types[field.name] = field.type

            await self.error_logger.alog_info(
                "CustomFieldsManager",
                f"Created {len(created_fields)} line item custom field definitions",
            )

    async def process_mixed_custom_fields(
        self,
        orders_batch: List[Dict[str, Any]],
        opportunity_order_map: Dict[str, str],
        line_item_map: Dict[str, str],
    ) -> None:
        """
        Process custom fields for both orders and line items from a mixed batch.

        Args:
            orders_batch: List of orders with potential custom fields and line items
            opportunity_order_map: Mapping of Opportunity IDs to Order IDs
            line_item_map: Mapping of LineItem IDs to ItemOrder IDs
        """
        order_custom_fields = []
        line_item_custom_fields = []

        for order_data in orders_batch:
            # Extract order custom fields
            order_cf = order_data.get("custom_fields", {})
            salesforce_id = order_data.get("salesforce_id")

            for field_name, value in order_cf.items():
                # Process both None and non-None values to ensure updates work properly
                order_custom_fields.append(
                    {
                        "salesforce_id": salesforce_id,
                        "field_name": field_name,
                        "field_type": self.determine_field_type(
                            value, field_name=field_name, is_line_item=False
                        ),
                        "value": value,
                    }
                )

            # Extract line item custom fields
            line_items = order_data.get("line_items", [])
            for line_item in line_items:
                line_item_cf = line_item.get("custom_fields", {})
                line_item_sf_id = line_item.get("salesforce_id")

                for field_name, value in line_item_cf.items():
                    # Process both None and non-None values to ensure updates work properly
                    line_item_custom_fields.append(
                        {
                            "salesforce_id": line_item_sf_id,
                            "field_name": field_name,
                            "field_type": self.determine_field_type(
                                value, field_name=field_name, is_line_item=True
                            ),
                            "value": value,
                        }
                    )

        # Process order custom fields
        if order_custom_fields:
            await self.process_order_custom_fields(
                order_custom_fields, opportunity_order_map
            )

        # Process line item custom fields
        if line_item_custom_fields:
            await self.process_line_item_custom_fields(
                line_item_custom_fields, line_item_map
            )
