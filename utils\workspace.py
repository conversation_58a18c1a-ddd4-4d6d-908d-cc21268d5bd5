from typing import Optional

from dateutil.relativedelta import relativedelta
from django.utils import timezone

from data.accounts.association_labels import (
    auto_create_necessary_association_label_setting,
)
from data.constants.constant import (
    APP_TARGET_SLUG,
    DEFAULT_JOURNAL_CATEGORY,
    DEFAULT_PERMISSION,
    DEFAULT_SLIPS_TYPE_DISPLAY,
    SYSTEM_SETTING_TYPE,
)
from data.constants.properties_constant import (
    TYPE_OBJECTS, 
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_CASE,
    TYPE_OBJECT_CONTACT,
    TYPE_OBJECT_COMPANY,
    TYPE_OBJECT_INVENTORY_TRANSACTION,
    TYPE_OBJECT_INVENTORY,
    TYPE_OBJECT_SUBSCRIPTION,
    TYPE_OBJECT_ESTIMATE,
    TYPE_OBJECT_INVOICE,
    TYPE_OBJECT_RECEIPT,
    TYPE_OBJECT_ITEM,
    TYPE_OBJECT_PURCHASE_ORDER,
    TYPE_OBJECT_BILL,
)
from data.models import (
    AppSetting,
    AppSettingChild,
    CustomObject,
    CustomProperty,
    DateFormat,
    JournalEntry,
    ObjectManager,
    Projects,
    PropertySet,
    UserManagement,
    Verification,
    Workspace,
)
from utils.properties.properties import DEFAULT_OBJECT_DISPLAY
from utils.utility import get_workspace


def create_workspace(
    request,
    workspace_title,
    workspace_description,
    workspace_currency,
    user=None,
    subscription="free",
):
    if not user:
        user = request.user

    lang = request.LANGUAGE_CODE

    # Create new workspace
    new_workspace = Workspace.objects.create(
        name=workspace_title,
        description=workspace_description,
        subscription=subscription,
        currencies=str([workspace_currency.upper()]),
    )

    current_datetime = timezone.now()
    subscription_period_start = current_datetime.replace(
        hour=0, minute=0, second=0, microsecond=0
    )
    subscription_period_end = subscription_period_start + relativedelta(days=14)

    new_workspace.subscription_period_started = subscription_period_start
    new_workspace.subscription_period_ended = subscription_period_end
    new_workspace.subscription_status = "preactive"

    if request.LANGUAGE_CODE == "ja":
        new_workspace.country_code = "JP"

    new_workspace.save()

    new_workspace.user.add(user)

    verification = Verification.objects.get(user=user)
    verification.workspace = new_workspace
    verification.save()

    SHOPTURBO_APP_TARGET = "shopturbo"
    # app
    app_targets = APP_TARGET_SLUG.keys()
    for app_target in app_targets:
        app_setting = AppSetting.objects.filter(
            workspace=new_workspace, app_target=app_target
        ).first()
        if not app_setting:
            app_setting, _ = AppSetting.objects.get_or_create(
                workspace=new_workspace, app_target=app_target
            )
        if app_target == SHOPTURBO_APP_TARGET:
            app_setting.search_setting_inventory = "inventory_id,item__name"
            app_setting.search_setting_inventory_transaction = (
                "transaction_id,inventory_id"
            )
            app_setting.search_setting_warehouse = "location,warehouse"
            app_setting.save()

    _ = UserManagement.objects.create(
        type=UserManagement.RoleType.ADMIN, workspace=new_workspace, user=user
    )

    # Added Default Projects on Task
    Projects.objects.get_or_create(workspace=new_workspace, title="")

    ## Creating Default Slipt Type for Slips in Billing App}
    app_setting, _ = AppSetting.objects.get_or_create(
        workspace=new_workspace, app_target=SHOPTURBO_APP_TARGET
    )

    for i, prop in enumerate(DEFAULT_SLIPS_TYPE_DISPLAY):
        property_set = PropertySet.objects.create(
            workspace=new_workspace,
            name=DEFAULT_SLIPS_TYPE_DISPLAY[prop][lang],
            target="slips",
            as_default=True,
        )

        if i > 0:
            property_set.name = DEFAULT_SLIPS_TYPE_DISPLAY[prop][lang]
            property_set.as_default = False

        property_set.children = [
            str(p) for p in DEFAULT_SLIPS_TYPE_DISPLAY[prop]["value"].split(",")
        ]
        property_set.save()

        AppSettingChild.objects.create(
            app_setting=app_setting,
            target_name="slip_slip_type",
            property_set=property_set,
            value=prop,
        )

    # Create Object Manager
    for type_object in TYPE_OBJECTS:
        om, _ = ObjectManager.objects.get_or_create(
            workspace=new_workspace, page_group_type=type_object
        )
        try:
            om.column_display = ",".join(DEFAULT_OBJECT_DISPLAY[type_object])
            om.save()
        except Exception:
            pass

    # Create default Accounting Category and Counter Category
    category_custom_property = CustomProperty.objects.create(
        workspace=new_workspace, model=JournalEntry._meta.db_table, name="category"
    )
    counter_category_custom_property = CustomProperty.objects.create(
        workspace=new_workspace,
        model=JournalEntry._meta.db_table,
        name="counter_category",
    )

    category_result = []
    counter_category_result = {}

    for key, category in DEFAULT_JOURNAL_CATEGORY.copy().items():
        category_data = {"category": category[lang]}
        category_choices_list = []
        for choice in category["choices"]:
            category_list = {"label": choice[lang], "value": choice["value"]}
            category_choices_list.append(category_list)

            counter_category_result[choice["value"]] = (
                f"{choice['counter_category_income']};{choice['counter_category_expense']}"
            )

        category_data["choices"] = category_choices_list

        category_result.append(category_data)

    category_custom_property.value = category_result
    category_custom_property.save()

    counter_category_custom_property.value = counter_category_result
    counter_category_custom_property.save()

    # Create association labels for all supported object types
    SUPPORTED_ASSOCIATION_OBJECT_TYPES = [
        TYPE_OBJECT_ORDER,
        TYPE_OBJECT_CASE,
        TYPE_OBJECT_CONTACT,
        TYPE_OBJECT_COMPANY,
        TYPE_OBJECT_INVENTORY_TRANSACTION,
        TYPE_OBJECT_INVENTORY,
        TYPE_OBJECT_SUBSCRIPTION,
        TYPE_OBJECT_ESTIMATE,
        TYPE_OBJECT_INVOICE,
        TYPE_OBJECT_RECEIPT,
        TYPE_OBJECT_ITEM,
        TYPE_OBJECT_PURCHASE_ORDER,
        TYPE_OBJECT_BILL,
    ]
    
    for object_type in SUPPORTED_ASSOCIATION_OBJECT_TYPES:
        auto_create_necessary_association_label_setting(new_workspace, object_type)

    # Date Format
    default_date_format = "YYYY-MM-DD"
    if lang == "ja":
        default_date_format = "YYYY年MM月DD日"
    DateFormat.objects.get_or_create(
        workspace=new_workspace, is_workspace_level=True, value=default_date_format
    )

    return new_workspace


def get_permission(object_type, user, custom_object: CustomObject | None = None):
    # Check if user is authenticated before proceeding
    if not user or not user.is_authenticated:
        return DEFAULT_PERMISSION

    workspace = get_workspace(user)
    try:
        role = UserManagement.objects.get(user=user, workspace=workspace)
    except UserManagement.DoesNotExist:
        if workspace and user:
            role = UserManagement.objects.create(
                type=UserManagement.RoleType.STAFF, workspace=workspace, user=user
            )
        else:
            return DEFAULT_PERMISSION

    if role.type in [
        UserManagement.RoleType.ADMIN,
        UserManagement.RoleType.SUPPORT,
        UserManagement.RoleType.PARTNER,
    ]:
        object_type_permission = "read|edit|archive"
    else:
        if role.permission:
            permission = role.permission

            if custom_object:
                object_type_permission = permission.get(
                    custom_object.slug, DEFAULT_PERMISSION
                )
            else:
                if object_type in SYSTEM_SETTING_TYPE:
                    object_type_permission = permission.get(object_type, "hide")
                else:
                    object_type_permission = permission.get(
                        object_type, DEFAULT_PERMISSION
                    )
        else:
            if object_type in SYSTEM_SETTING_TYPE:
                object_type_permission = "hide"
            else:
                object_type_permission = DEFAULT_PERMISSION

    # Force view only
    if role.type == UserManagement.RoleType.VIEW_ONLY:
        if "edit" in object_type_permission:
            object_type_permission = "read"
        if "team__edit" in object_type_permission:
            object_type_permission = "read"
        if "archive" in object_type_permission:
            object_type_permission = "read"
        if "team__archive" in object_type_permission:
            object_type_permission = "read"

    return object_type_permission


def get_workspace_currency(workspace: Workspace, currency: Optional[str] = None) -> str:
    """Get the default currency for the workspace if none provided."""
    if not currency:
        currencies = workspace.currencies
        if isinstance(currencies, list) and currencies:
            return currencies[0]
        return "JPY"  # Default to JPY if no currency is set
    return currency.upper()
