"""
Data processor for transforming Salesforce Opportunity and OpportunityLineItem data.
Maps Salesforce fields to Sanka Order model fields.
"""

from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from django.utils import timezone
from django.utils.dateparse import parse_datetime

from utils.error_logger.import_export_logger import ImportExportLogger
from utils.salesforce.orders_async.batch_handler import OrdersBatchHandler
from .config import STAGE_TO_STATUS_MAPPING, DEFAULT_ORDER_STATUS


class OrdersDataProcessor:
    """
    Processes and transforms Salesforce opportunity data to Sanka Orders format.
    """

    def __init__(
        self,
        workspace_id: str,
        channel_id: str,
        error_logger: ImportExportLogger,
    ):
        self.workspace_id = workspace_id
        self.channel_id = channel_id
        self.error_logger = error_logger
        self.field_mapping: Dict[str, Dict] = {}
        self.opportunity_fields: List[str] = []
        self.line_item_fields: List[str] = []

    async def initialize_field_mapping(self, field_mapping: Dict[str, Dict]) -> None:
        """
        Initialize and validate field mapping.

        Args:
            field_mapping: Mapping of Salesforce fields to Sanka fields
        """
        self.field_mapping = field_mapping

        # Separate opportunity and line item fields
        for sf_field, _ in field_mapping.items():
            self.opportunity_fields.append(sf_field)

        await self.error_logger.alog_info(
            "OrdersDataProcessor",
            f"Initialized field mapping with {len(self.opportunity_fields)} opportunity fields "
            f"and {len(self.line_item_fields)} line item fields",
        )

    async def batch_process_opportunities(
        self,
        opportunities: List[Dict[str, Any]],
        field_mapping: Dict[str, Dict],
        mapping_status_custom_fields: Dict[str, str],
        batch_handler: OrdersBatchHandler,
    ) -> List[Dict[str, Any]]:
        """
        Process a batch of opportunities.

        Args:
            opportunities: List of raw Salesforce opportunity records
            field_mapping: Field mapping configuration

        Returns:
            List of processed opportunity data ready for database insertion
        """
        processed_opportunities = []

        for opportunity in opportunities:
            try:
                processed = await self.process_single_opportunity(
                    opportunity, field_mapping, mapping_status_custom_fields, batch_handler
                )
                if processed:
                    processed_opportunities.append(processed)
            except Exception as e:
                await self.error_logger.alog_error(
                    "PROCESSING_ERROR",
                    "OrdersDataProcessor",
                    f"Error processing opportunity {opportunity.get('Id')}: {str(e)}",
                    e,
                )

        await self.error_logger.alog_info(
            "OrdersDataProcessor",
            f"Processed {len(processed_opportunities)} of {len(opportunities)} opportunities",
        )

        return processed_opportunities

    async def process_single_opportunity(
        self,
        opportunity: Dict[str, Any],
        field_mapping: Dict[str, Dict],
        mapping_status_custom_fields: Dict[str, str],
        batch_handler: OrdersBatchHandler,
    ) -> Dict[str, Any]:
        """
        Process a single opportunity record.

        Args:
            opportunity: Raw Salesforce opportunity record
            field_mapping: Field mapping configuration

        Returns:
            Processed opportunity data
        """
        processed = {
            "salesforce_id": opportunity.get("Id"),
            "workspace_id": self.workspace_id,
            "channel_id": self.channel_id,
        }

        # Map standard fields
        processed.update(self._map_standard_opportunity_fields(opportunity, mapping_status_custom_fields))

        # Map custom fields based on field_mapping
        custom_fields = {}

        for sf_field, sanka_field in field_mapping.items():
            # Skip line item fields and skipped fields
            if "|line_item" in sf_field:
                continue

            # Special handling for TotalOpportunityQuantity - will be calculated from line items
            if sf_field == "TotalOpportunityQuantity":
                # Skip for now, will be added when joining with line items
                continue
            
            sf_value = opportunity.get(sf_field)
            
            if sf_field == "OwnerId":
                # await self.error_logger.alog_info("OrdersDataProcessor", "OwnerId: " + opportunity.get("OwnerId"))
                sf_value = batch_handler.user_cache.get(opportunity.get("OwnerId"))

            if sf_value is not None:
                # Check if this is a standard field or custom field
                if sanka_field in self._get_standard_order_fields():
                    processed[sanka_field] = self._transform_field_value(
                        sf_field, sf_value, sanka_field
                    )
                else:
                    # This is a custom field
                    custom_fields[sanka_field] = sf_value

        # Add custom fields to processed data
        if custom_fields:
            processed["custom_fields"] = custom_fields

        return processed

    def _map_standard_opportunity_fields(
        self, opportunity: Dict[str, Any], mapping_status_custom_fields: Dict[str, str]
    ) -> Dict[str, Any]:
        """
        Map standard Salesforce Opportunity fields to Sanka Order fields.

        Args:
            opportunity: Salesforce opportunity record

        Returns:
            Mapped standard fields
        """
        mapped = {}

        # Map standard fields
        # Note: "Name" is now handled as custom field since ShopTurboOrders doesn't have a name field
        # if opportunity.get("Name"):
        #     mapped["name"] = opportunity["Name"]
        mapped["status"] = "active"

        if opportunity.get("Amount"):
            mapped["total_price"] = float(opportunity["Amount"])

        if opportunity.get("CloseDate"):
            mapped["order_at"] = self._parse_date(opportunity["CloseDate"])

        if opportunity.get("StageName"):
            if mapping_status_custom_fields:
                mapped["delivery_status"] = mapping_status_custom_fields.get(opportunity["StageName"], DEFAULT_ORDER_STATUS)
            else:
                mapped["delivery_status"] = STAGE_TO_STATUS_MAPPING.get(
                    opportunity["StageName"], DEFAULT_ORDER_STATUS
                )
            # Note: stage_name is now handled as custom field
            # mapped["stage_name"] = opportunity["StageName"]

        if opportunity.get("Description"):
            mapped["memo"] = opportunity["Description"]

        if opportunity.get("AccountId"):
            mapped["account_id"] = opportunity["AccountId"]

        if opportunity.get("ContactId"):
            mapped["contact_id"] = opportunity["ContactId"]

        # OwnerId is handled as a custom field instead of standard field
        # to avoid mapping Salesforce User IDs to Django User fields

        # Type field from Salesforce is stored as custom field
        # order_type will be determined by presence of line items instead
        # if opportunity.get("Type"):
        #     mapped["order_type"] = opportunity["Type"]

        # Currency will be set to default (JPY) in batch handler if not provided
        # CurrencyIsoCode field not available in non-multi-currency orgs

        # Note: These fields are now handled as custom fields since they don't exist on ShopTurboOrders
        # if opportunity.get("Probability"):
        #     mapped["probability"] = float(opportunity["Probability"])

        # if opportunity.get("IsClosed"):
        #     mapped["is_closed"] = opportunity["IsClosed"] == "true"

        # if opportunity.get("IsWon"):
        #     mapped["is_won"] = opportunity["IsWon"] == "true"

        # Platform information
        mapped["platform"] = "salesforce"

        # Timestamps
        if opportunity.get("CreatedDate"):
            mapped["created_at"] = self._parse_date(opportunity["CreatedDate"])

        if opportunity.get("LastModifiedDate"):
            mapped["updated_at"] = self._parse_date(opportunity["LastModifiedDate"])

        return mapped

    async def batch_process_line_items(
        self,
        line_items: List[Dict[str, Any]],
        field_mapping: Dict[str, Dict],
    ) -> List[Dict[str, Any]]:
        """
        Process a batch of opportunity line items.

        Args:
            line_items: List of raw Salesforce OpportunityLineItem records
            field_mapping: Field mapping configuration

        Returns:
            List of processed line item data
        """
        processed_items = []

        for line_item in line_items:
            try:
                processed = await self.process_single_line_item(
                    line_item, field_mapping
                )
                if processed:
                    processed_items.append(processed)
            except Exception as e:
                await self.error_logger.alog_error(
                    "PROCESSING_ERROR",
                    "OrdersDataProcessor",
                    f"Error processing line item {line_item.get('Id')}: {str(e)}",
                    e,
                )

        await self.error_logger.alog_info(
            "OrdersDataProcessor",
            f"Processed {len(processed_items)} of {len(line_items)} line items",
        )

        return processed_items

    async def process_single_line_item(
        self,
        line_item: Dict[str, Any],
        field_mapping: Dict[str, Dict],
    ) -> Dict[str, Any]:
        """
        Process a single opportunity line item record.

        Args:
            line_item: Raw Salesforce OpportunityLineItem record
            field_mapping: Field mapping configuration

        Returns:
            Processed line item data
        """
        processed = {
            "salesforce_id": line_item.get("Id"),
            "opportunity_id": line_item.get("OpportunityId"),
            "workspace_id": self.workspace_id,
        }

        # Map standard fields
        processed.update(self._map_standard_line_item_fields(line_item))

        # Map custom fields based on field_mapping
        custom_fields = {}

        for sf_field, sanka_field in field_mapping.items():
            # Only process line item fields
            if "|line_item" not in sf_field:
                continue

            # Remove |line_item suffix to get actual field name
            actual_field = sf_field.replace("|line_item", "")
            sf_value = line_item.get(actual_field)

            if sf_value is not None:
                # Check if this is a standard field or custom field
                if sanka_field in self._get_standard_line_item_fields():
                    processed[sanka_field] = self._transform_field_value(
                        actual_field, sf_value, sanka_field
                    )
                else:
                    # This is a custom field
                    custom_fields[sanka_field] = sf_value

        # Add custom fields to processed data
        if custom_fields:
            processed["custom_fields"] = custom_fields

        return processed

    def _map_standard_line_item_fields(
        self, line_item: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Map standard Salesforce OpportunityLineItem fields to Sanka fields.

        Args:
            line_item: Salesforce line item record

        Returns:
            Mapped standard fields
        """
        mapped = {}

        # Map standard fields
        # Note: product_id is used for item FK lookup, not stored as field
        if line_item.get("Product2Id"):
            mapped["product_id"] = line_item["Product2Id"]

        # These fields don't exist on ShopTurboItemsOrders - should be custom fields
        # if line_item.get("PricebookEntryId"):
        #     mapped["pricebook_entry_id"] = line_item["PricebookEntryId"]

        if line_item.get("Quantity"):
            mapped["number_item"] = float(line_item["Quantity"])

        if line_item.get("UnitPrice"):
            mapped["item_price_order"] = float(line_item["UnitPrice"])

        if line_item.get("TotalPrice"):
            mapped["total_price"] = float(line_item["TotalPrice"])

        # These fields don't exist on ShopTurboItemsOrders - should be custom fields
        # if line_item.get("ListPrice"):
        #     mapped["list_price"] = float(line_item["ListPrice"])

        # if line_item.get("Discount"):
        #     mapped["discount"] = float(line_item["Discount"])

        # if line_item.get("Description"):
        #     mapped["memo"] = line_item["Description"]

        # if line_item.get("ServiceDate"):
        #     mapped["service_date"] = self._parse_date(line_item["ServiceDate"])

        # if line_item.get("ProductCode"):
        #     mapped["item_code"] = line_item["ProductCode"]

        if line_item.get("Name"):
            mapped["custom_item_name"] = line_item["Name"]

        # SortOrder doesn't exist on ShopTurboItemsOrders
        # if line_item.get("SortOrder"):
        #     mapped["sort_order"] = int(line_item["SortOrder"])

        # Timestamps
        if line_item.get("CreatedDate"):
            mapped["created_at"] = self._parse_date(line_item["CreatedDate"])

        if line_item.get("LastModifiedDate"):
            mapped["updated_at"] = self._parse_date(line_item["LastModifiedDate"])

        return mapped

    def _transform_field_value(
        self, sf_field: str, sf_value: Any, sanka_field: str
    ) -> Any:
        """
        Transform field value based on field type.

        Args:
            sf_field: Salesforce field name
            sf_value: Salesforce field value
            sanka_field: Sanka field name

        Returns:
            Transformed value
        """
        # Handle date fields
        if any(
            date_keyword in sf_field.lower()
            for date_keyword in ["date", "created", "modified"]
        ):
            return self._parse_date(sf_value)

        # Handle boolean fields
        if isinstance(sf_value, str) and sf_value.lower() in ["true", "false"]:
            return sf_value.lower() == "true"

        # Handle numeric fields
        if any(
            num_keyword in sanka_field.lower()
            for num_keyword in ["price", "amount", "quantity", "discount", "number_item", "item_price_order"]
        ):
            try:
                return float(sf_value)
            except (ValueError, TypeError):
                return sf_value

        return sf_value

    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """
        Parse Salesforce date string to datetime object.

        Args:
            date_str: Date string from Salesforce

        Returns:
            Parsed datetime or None
        """
        if not date_str:
            return None

        try:
            # Try parsing as datetime
            dt = parse_datetime(date_str)
            if dt:
                # Make aware if naive
                if timezone.is_naive(dt):
                    dt = timezone.make_aware(dt)
                return dt

            # Try parsing as date only
            from datetime import datetime

            dt = datetime.strptime(date_str, "%Y-%m-%d")
            return timezone.make_aware(dt)
        except Exception:
            return None

    def _get_standard_order_fields(self) -> List[str]:
        """Get list of standard Order model fields."""
        return [
            "order_id",
            # "name",  # Not a field on ShopTurboOrders - will be stored as custom field
            "company",
            "contact",
            "total_price",
            "total_price_without_tax",
            "tax",
            "currency",
            "status",
            "delivery_status",
            "order_type",
            "order_at",
            "platform",
            "memo",
            "owner",
            # "stage_name",  # Not a field on ShopTurboOrders - will be stored as custom field
            # "probability",  # Not a field on ShopTurboOrders - will be stored as custom field
            # "is_closed",  # Not a field on ShopTurboOrders - will be stored as custom field
            # "is_won",  # Not a field on ShopTurboOrders - will be stored as custom field
            "account_id",
            "contact_id",
            # "owner_id",  # Removed - OwnerId handled as custom field
        ]

    def _get_standard_line_item_fields(self) -> List[str]:
        """Get list of standard LineItem model fields."""
        return [
            # Foreign key fields
            "item",
            "item_variant", 
            "item_price",
            "customer_item_price",
            # Actual fields on ShopTurboItemsOrders
            "custom_item_name",
            "platform_item_id",
            "number_item",
            "item_price_order",
            "item_price_order_tax",
            "total_price",
            "currency",
            "item_status",
            # Note: product_id is used for FK lookup but not a direct field
            "product_id",  # Used for item FK lookup
            # Fields that don't exist on model - should be custom fields:
            # "item_code", "discount", "price_discount", "list_price",
            # "memo", "service_date", "pricebook_entry_id", "sort_order"
        ]

    def extract_custom_fields(
        self,
        record: Dict[str, Any],
        is_line_item: bool = False,
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Extract custom fields from a processed record.

        Args:
            record: Processed record with potential custom_fields
            is_line_item: Whether this is a line item record

        Returns:
            Tuple of (standard_fields, custom_fields)
        """
        custom_fields = record.pop("custom_fields", {})
        return record, custom_fields

    async def join_opportunities_with_line_items(
        self,
        opportunities: List[Dict[str, Any]],
        line_items: List[Dict[str, Any]],
    ) -> List[Dict[str, Any]]:
        """
        Join opportunities with their line items.

        Args:
            opportunities: List of processed opportunities
            line_items: List of processed line items

        Returns:
            List of opportunities with associated line items
        """
        # Create a mapping of opportunity_id to line items
        line_items_by_opp = {}
        for item in line_items:
            opp_id = item.get("opportunity_id")
            if opp_id:
                if opp_id not in line_items_by_opp:
                    line_items_by_opp[opp_id] = []
                line_items_by_opp[opp_id].append(item)

        # Add line items to opportunities and calculate total quantity
        for opportunity in opportunities:
            opp_id = opportunity.get("salesforce_id")
            if opp_id and opp_id in line_items_by_opp:
                opportunity["line_items"] = line_items_by_opp[opp_id]
                # Set order_type to item_order when line items exist
                opportunity["order_type"] = "item_order"
                # Calculate TotalOpportunityQuantity from line items
                total_quantity = sum(
                    float(item.get("number_item", 0)) for item in opportunity["line_items"]
                )

                # Check if TotalOpportunityQuantity was mapped and apply the mapping
                for sf_field, sanka_field in self.field_mapping.items():
                    if sf_field == "TotalOpportunityQuantity":
                        if sanka_field:
                            # Check if it's a standard field or custom field
                            if sanka_field in self._get_standard_order_fields():
                                opportunity[sanka_field] = total_quantity
                            else:
                                # Add to custom fields
                                if "custom_fields" not in opportunity:
                                    opportunity["custom_fields"] = {}
                                opportunity["custom_fields"][sanka_field] = (
                                    total_quantity
                                )
                        break

                # Always store raw value for potential use
                opportunity["total_opportunity_quantity"] = total_quantity
            else:
                opportunity["line_items"] = []
                # Set order_type to manual_order when no line items
                opportunity["order_type"] = "manual_order"
                opportunity["total_opportunity_quantity"] = 0

        await self.error_logger.alog_info(
            "OrdersDataProcessor",
            f"Joined {len(opportunities)} opportunities with line items",
        )

        return opportunities
