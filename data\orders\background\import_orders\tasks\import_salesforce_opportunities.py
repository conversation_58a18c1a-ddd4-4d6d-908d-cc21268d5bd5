import ast
import asyncio
from datetime import timedelta
import traceback

from hatchet_sdk import Context

from data.models import BackgroundJob, TransferHistory
from utils.bgjobs.handler import (
    aio_set_bg_job_completed,
    aio_set_bg_job_failed,
    aio_set_bg_job_running,
)
from utils.logger import logger
from utils.salesforce.orders_async import import_salesforce_orders

from ..models import (
    ImportOrdersSalesforcePayload,
    ImportOrdersSalesforcePayloadV2,
)
from ..workflows import import_salesforce_opportunities


@import_salesforce_opportunities.task(
    name="ImportSalesforceOpportunitiesTask",
    execution_timeout=timedelta(hours=5),  # Increased for large datasets
    schedule_timeout=timedelta(hours=1),
)
async def import_salesforce_opportunities_task(
    input: ImportOrdersSalesforcePayloadV2, ctx: Context
) -> dict:
    """
    Async task for importing Salesforce opportunities with batch processing and checkpoint support.
    """
    logger.info("Run Salesforce opportunities import async task")
    logger.info(f"Input: {input}")

    background_job_id = input.background_job_id

    # Retrieve BackgroundJob
    try:
        bg_job = await BackgroundJob.objects.aget(id=background_job_id)
    except BackgroundJob.DoesNotExist:
        logger.error("BackgroundJob does not exist")
        raise Exception("BackgroundJob does not exist")

    await aio_set_bg_job_running(input.background_job_id)

    payload_dict = bg_job.payload

    # Validate Payload with ImportOrdersSalesforcePayload
    try:
        payload = await asyncio.to_thread(ImportOrdersSalesforcePayload, **payload_dict)
    except Exception as e:
        logger.error(f"Invalid payload: {str(e)}")
        await aio_set_bg_job_failed(input.background_job_id)
        raise Exception(f"Invalid payload: {str(e)}")

    history_id = payload.history_id

    # Validate TransferHistory exists
    try:
        task = await TransferHistory.objects.select_related("workspace", "user").aget(
            id=history_id
        )
        task.keep_alive = True
        await task.asave()
    except TransferHistory.DoesNotExist:
        logger.warning("TransferHistory does not exist")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])
        raise Exception("TransferHistory does not exist")

    try:
        # Process mapping fields
        mapping_custom_fields = (
            payload.mapping_custom_fields
            if payload.mapping_custom_fields and payload.mapping_custom_fields != "None"
            else {}
        )
        mapping_association_custom_fields = (
            payload.mapping_association_custom_fields
            if payload.mapping_association_custom_fields
            and payload.mapping_association_custom_fields != "None"
            else {}
        )
        mapping_status_custom_fields = (
            payload.mapping_status_custom_fields
            if payload.mapping_status_custom_fields
            and payload.mapping_status_custom_fields != "None"
            else {}
        )

        # Parse string mappings if needed
        if isinstance(mapping_custom_fields, str):
            mapping_custom_fields = ast.literal_eval(mapping_custom_fields)
        if isinstance(mapping_association_custom_fields, str):
            mapping_association_custom_fields = ast.literal_eval(
                mapping_association_custom_fields
            )
        if isinstance(mapping_status_custom_fields, str):
            mapping_status_custom_fields = ast.literal_eval(mapping_status_custom_fields)

        # Call the async Salesforce import with context for cancellation support
        success = await import_salesforce_orders(
            channel_id=payload.channel_id,
            field_mapping=mapping_custom_fields,
            user_id=payload.user,
            mapping_custom_fields_association=mapping_association_custom_fields,
            mapping_status_custom_fields=mapping_status_custom_fields,
            lang="en",  # Use English for logging
            transfer_history_id=history_id,
            ctx=ctx,  # Pass context for cancellation support
        )

        if success:
            logger.info("Successfully imported Salesforce opportunities")
            await task.arefresh_from_db()
            task.status = "completed"
            await task.asave(update_fields=["status"])

            await aio_set_bg_job_completed(input.background_job_id)
        else:
            logger.error("Error importing Salesforce opportunities")
            await aio_set_bg_job_failed(input.background_job_id)
            task.status = "failed"
            await task.asave(update_fields=["status"])

    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error importing Salesforce opportunities: {str(e)}")
        await aio_set_bg_job_failed(input.background_job_id)
        task.status = "failed"
        await task.asave(update_fields=["status"])
