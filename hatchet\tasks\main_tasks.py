"""
Central location for all task imports.
Organized by business module for better maintainability.
"""

# Account & System Management
from data.accounts.background.delete_records import delete_records_task
from data.accounts.background.delete_workspace import delete_workspace_task
from data.accounts.background.export_csv_system_log import export_csv_system_log
from data.accounts.background.fix_total_price import fix_total_price

# Case Management
from data.case.background.export_cases import export_cases
from data.case.background.export_csv_cases import export_csv_cases
from data.case.background.import_hubspot_cases import import_hubspot_cases_task
from data.case.background.import_salesforce_cases import import_salesforce_cases_task

# Commerce
from data.commerce.background.send_record_email_action import (
    run_send_record_email_action,
)

# Company Management
from data.company.background.export_csv_company import export_csv_company
from data.company.background.export_hubspot_companies import (
    export_hubspot_companies_task,
)
from data.company.background.export_salesforce_companies import (
    export_salesforce_companies_task,
)
from data.company.background.import_hubspot_companies import (
    import_hubspot_companies_task,
)
from data.company.background.import_salesforce_companies import (
    import_salesforce_companies_task,
)
from data.company.background.import_salesforce_lead_companies import (
    import_salesforce_lead_companies_task,
)

# Contact Management
from data.contact.background.export_csv_contacts import export_csv_contacts
from data.contact.background.export_freee_contacts import export_freee_contacts_task
from data.contact.background.export_hubspot_contacts import export_hubspot_contacts_task
from data.contact.background.export_hubspot_contacts_as_companies import (
    export_hubspot_contacts_as_companies_task,
)
from data.contact.background.export_shopify_contacts import export_shopify_contacts_task
from data.contact.background.import_freee_contacts import import_freee_contacts_task
from data.contact.background.import_hubspot_contacts import import_hubspot_contacts_task
from data.contact.background.import_line_contacts import import_line_contacts_task
from data.contact.background.import_ecforce_contacts import import_ecforce_contacts_task
from data.contact.background.import_salesforce_contacts import (
    import_salesforce_contacts_task,
)
from data.contact.background.import_salesforce_contacts_as_companies import (
    import_salesforce_contacts_as_companies_task,
)
from data.contact.background.import_shopify_contacts import import_shopify_contacts_task

# Custom Objects
from data.custom_object.background.export_csv_custom_object import (
    export_csv_custom_object,
)
from data.custom_object.background.export_hubspot_custom_object import (
    export_hubspot_custom_object_task,
)
from data.custom_object.background.import_hubspot_custom_object import (
    import_hubspot_custom_object_task,
)
from data.custom_object.background.import_salesforce_custom_object import (
    import_salesforce_custom_object_task,
)

# Estimates
from data.estimate.background.export_csv_estimates import export_csv_estimates

# Inventory Management
from data.inventory.background.export_csv_inventory import export_csv_inventory
from data.inventory.background.export_csv_locations import export_csv_locations
from data.inventory_transaction.background.export_csv_inventory_transactions import (
    export_csv_inventory_transactions,
)

# Invoicing
from data.invoice.background.export_csv_invoices import export_csv_invoices
from data.slip.background.export_csv_slips import export_csv_slips
from data.invoice.background.export_invoice_custom_object import (
    export_invoice_custom_object_task,
)
from data.invoice.background.import_salesforce_invoices import (
    import_salesforce_invoices_task,
)

# Item Management
from data.item.background.export_csv_items import export_csv_items
from data.item.background.import_items import (
    import_amazon_items,
    import_eccube_items,
    import_ecforce_items,
    import_freee_items,
    import_hubspot_items,
    import_items,
    import_makeshop_items,
    import_rakuten_items,
    import_shopify_items,
    import_square_items,
    import_stripe_items,
    import_yahoo_shopping_items,
)
from data.item.background.export_items import (
    export_items,
    export_rakuten_items,
    export_amazon_items,
    export_makeshop_items,
    export_yahoo_shopping_inventories,
)
from data.item.background.import_salesforce_items import import_salesforce_items_task
from data.item.background.import_salesforce_co_items import (
    import_salesforce_co_items_task,
)
from data.item.background.export_hubspot_items import export_hubspot_items_task
from data.item.background.shopify_sync_items import (
    shopify_sync_dag,
)

# Order Management
from data.orders.background.export_csv_orders import export_csv_orders
from data.orders.background.export_orders import (
    export_ecforce_orders,
    export_freee_orders,
    export_hubspot_orders,
    export_nextengine_orders,
    export_orders,
    export_shopify_orders,
    export_yahoo_shopping_orders,
)
from data.orders.background.import_orders import (
    import_amazon_orders,
    import_bcart_orders,
    import_bcart_orders_subprocess,
    import_ebay_orders,
    import_eccube_orders,
    import_ecforce_orders,
    import_hubspot_orders,
    import_hubspot_orders_subprocess,
    import_makeshop_orders,
    import_nextengine_orders,
    import_rakuten_orders,
    import_salesforce_opportunities,
    import_shopify_orders,
    import_shopify_orders_subprocess,
    import_square_orders,
    import_stripe_orders,
    import_woocommerce_orders,
    import_yahoo_shopping_orders,
)

# Delivery Notes
from data.delivery_note.background.export_csv_delivery_notes import (
    export_csv_delivery_notes,
)

# Journal
from data.journal.background.export_csv_journal import (
    export_csv_journals,
)

# Purchase Orders
from data.purchase_order.background.export_csv_purchase_orders import (
    export_csv_purchase_orders,
)

# Expenses
from data.expense.background.export_csv_expenses import (
    export_csv_expenses,
)


# Bills
from data.bill.background.export_csv_bills import (
    export_csv_bills,
)

# Subscriptions
from data.subscriptions.background.export_hubspot_subscriptions import (
    export_hubspot_subscriptions_custom_object_task,
)
from data.subscriptions.background.import_hubspot_subscriptions import (
    import_hubspot_subscriptions_task,
)
from data.subscriptions.background.import_salesforce_subscriptions import (
    import_salesforce_subscriptions_task,
)

from data.subscriptions.background.import_ecforce_subscriptions import (
    import_ecforce_subscriptions_task,
)

# Payments
from data.receipt.background.export_csv_payments import export_csv_payments

from data.subscriptions.background.export_csv_subscription import (
    export_csv_subscriptions,
)

# Workflow
from data.workflow.background.run_workflow_action import run_workflow_action_task

# Task
from data.taskflow.background.export_csv_tasks import export_csv_tasks

MAIN_TASKS = [
    # =====================================================
    # ACCOUNT & SYSTEM MANAGEMENT
    # =====================================================
    export_csv_system_log,
    delete_records_task,
    delete_workspace_task,
    fix_total_price,
    # =====================================================
    # CONTACT MANAGEMENT - IMPORTS
    # =====================================================
    import_hubspot_contacts_task,
    import_salesforce_contacts_task,
    import_salesforce_contacts_as_companies_task,
    import_shopify_contacts_task,
    import_freee_contacts_task,
    import_line_contacts_task,
    import_ecforce_contacts_task,
    # =====================================================
    # CONTACT MANAGEMENT - EXPORTS
    # =====================================================
    export_csv_contacts,
    export_hubspot_contacts_task,
    export_hubspot_contacts_as_companies_task,
    export_shopify_contacts_task,
    export_freee_contacts_task,
    # =====================================================
    # COMPANY MANAGEMENT - IMPORTS
    # =====================================================
    import_salesforce_companies_task,
    import_salesforce_lead_companies_task,
    import_hubspot_companies_task,
    # =====================================================
    # COMPANY MANAGEMENT - EXPORTS
    # =====================================================
    export_csv_company,
    export_salesforce_companies_task,
    export_hubspot_companies_task,
    # =====================================================
    # CUSTOM OBJECTS
    # =====================================================
    export_csv_custom_object,
    import_hubspot_custom_object_task,
    import_salesforce_custom_object_task,
    export_hubspot_custom_object_task,
    # =====================================================
    # ORDER MANAGEMENT - IMPORTS
    # =====================================================
    import_shopify_orders,
    import_shopify_orders_subprocess,
    import_hubspot_orders,
    import_hubspot_orders_subprocess,
    import_square_orders,
    import_amazon_orders,
    import_ecforce_orders,
    import_rakuten_orders,
    import_eccube_orders,
    import_makeshop_orders,
    import_yahoo_shopping_orders,
    import_bcart_orders,
    import_bcart_orders_subprocess,
    import_woocommerce_orders,
    import_salesforce_opportunities,
    import_ebay_orders,
    import_stripe_orders,
    import_nextengine_orders,
    # =====================================================
    # ORDER MANAGEMENT - EXPORTS
    # =====================================================
    export_csv_orders,
    export_orders,
    export_shopify_orders,
    export_ecforce_orders,
    export_nextengine_orders,
    export_freee_orders,
    export_yahoo_shopping_orders,
    export_hubspot_orders,
    # =====================================================
    # ITEM MANAGEMENT - IMPORTS
    # =====================================================
    import_items,
    import_shopify_items,
    import_makeshop_items,
    import_rakuten_items,
    import_eccube_items,
    import_yahoo_shopping_items,
    import_square_items,
    import_amazon_items,
    import_stripe_items,
    import_ecforce_items,
    import_freee_items,
    import_hubspot_items,
    import_salesforce_items_task,
    import_salesforce_co_items_task,
    # =====================================================
    # ITEM MANAGEMENT - EXPORTS & SYNC
    # =====================================================
    export_csv_items,
    export_items,
    export_rakuten_items,
    export_amazon_items,
    export_makeshop_items,
    export_yahoo_shopping_inventories,
    shopify_sync_dag,
    export_hubspot_items_task,
    # =====================================================
    # INVENTORY MANAGEMENT
    # =====================================================
    export_csv_inventory,
    export_csv_locations,
    export_csv_inventory_transactions,
    # =====================================================
    # CASE MANAGEMENT
    # =====================================================
    export_csv_cases,
    export_cases,
    import_salesforce_cases_task,
    import_hubspot_cases_task,
    # =====================================================
    # FINANCIAL MANAGEMENT
    # =====================================================
    export_csv_estimates,
    export_csv_invoices,
    export_csv_slips,
    export_csv_journals,
    export_csv_purchase_orders,
    export_csv_expenses,
    export_csv_bills,
    export_csv_delivery_notes,
    import_salesforce_invoices_task,
    export_invoice_custom_object_task,
    export_csv_payments,
    # =====================================================
    # SUBSCRIPTION MANAGEMENT
    # =====================================================
    import_salesforce_subscriptions_task,
    import_hubspot_subscriptions_task,
    export_hubspot_subscriptions_custom_object_task,
    export_csv_subscriptions,
    import_ecforce_subscriptions_task,
    # =====================================================
    # WORKFLOW & AUTOMATION
    # =====================================================
    run_send_record_email_action,
    run_workflow_action_task,
    # =====================================================
    # TASK MANAGEMENT
    # =====================================================
    export_csv_tasks,
]
