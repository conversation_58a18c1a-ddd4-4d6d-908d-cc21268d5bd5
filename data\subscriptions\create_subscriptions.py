from utils.association_utils import add_association
from datetime import datetime, timedelta

from django.http import HttpResponse
from django.shortcuts import redirect, render
from django.utils import timezone
from django.views.decorators.http import require_POST
from django_hosts.resolvers import reverse

from action.action import trigger_next_action
from data.accounts.association_labels import save_association_label
from data.constants.constant import (
    CURRENCY_MODEL,
    ORDER_USAGE_CATEGORY,
    SUBSCRIPTION_USAGE_CATEGORY,
)
from data.constants.properties_constant import (
    OBJECT_TYPE_TO_SLUG,
    TYPE_OBJECT_COMPANY,
    TYPE_OBJECT_CONTACT,
    TYPE_OBJECT_INVOICE,
    TYPE_OBJECT_JOURNAL,
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_SUBSCRIPTION,
    TYPE_OBJECT_WORKFLOW,
)
from data.models import (
    ActionNode,
    ActionTracker,
    AssociationLabel,
    AssociationLabelObject,
    BILLING_TIMING,
    Company,
    Contact,
    FREQ_TIME,
    Invoice,
    JournalEntry,
    Module,
    Notification,
    PRIOR_FREQ_TIME,
    SUBSCRIPTIONS_STATUS_DISPLAY,
    ShopTurboItems,
    ShopTurboItemsDiscount,
    ShopTurboItemsOrders,
    ShopTurboItemsPrice,
    ShopTurboItemsSubscriptions,
    ShopTurboOrders,
    ShopTurboShippingCost,
    ShopTurboSubscriptions,
    WorkflowActionTracker,
    ShopTurboOrdersPlatforms,
    ShopTurboSubscriptionsNameCustomField,
    ShopTurboSubscriptionsValueCustomField,
)
from utils.actions import transfer_output_to_target_input
from utils.decorator import login_or_hubspot_required
from utils.line_items import combine_item_dict_to_list
from utils.list_action_trigger_event import trigger_create_subscriptions_action
from utils.logger import logger
from utils.meter import has_quota
from utils.subscription.auto_gen_invoice import adjust_auto_gen_invoice_schedule
from utils.utility import (
    assign_object_owner,
    build_redirect_url,
    get_redirect_workflow,
    get_workspace,
    is_valid_number,
    is_valid_uuid,
    save_custom_property,
    update_query_params_url,
)


def clean_price_value(price_str):
    """
    Clean price string by removing commas and converting to float.
    Returns None if the string cannot be converted to a valid number.
    """
    if not price_str:
        return None

    try:
        # Remove commas and spaces
        cleaned = str(price_str).replace(",", "").replace(" ", "").strip()
        # Convert to float
        return float(cleaned) if cleaned else None
    except (ValueError, TypeError):
        return None


@login_or_hubspot_required
@require_POST
def shopturbo_create_subscriptions(request):
    target = "commerce_subscription"
    workspace = get_workspace(request.user)
    view_id = request.POST.get("view_id")
    lang = request.LANGUAGE_CODE
    source_url = request.POST.get("source_url")

    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_SUBSCRIPTION]
    module = (
        Module.objects.filter(
            workspace=workspace, object_values__contains=TYPE_OBJECT_SUBSCRIPTION
        )
        .order_by("order", "created_at")
        .first()
    )
    module_slug = None
    if module:
        module_slug = module.slug

    if request.method == "POST":
        if "create_subscriptions" in request.POST:
            owner = request.POST.get("owner", None)
            contact_and_company = request.POST.getlist("contact_and_company", None)
            start_date = request.POST.get("start_date", None)
            end_date = request.POST.get("end_date", None)
            status = request.POST.get("status", None)
            subscription_status = request.POST.get("subscription_status", None)
            freq = request.POST.get("freq", None)
            freq_time = request.POST.get("freq_time", None)
            prior_to = request.POST.get("prior_to", None)
            prior_time = request.POST.get("prior_time", None)
            billing_timing = request.POST.get("billing_timing", None)
            tax_rate = request.POST.get("tax_rate", None)

            items = request.POST.getlist("item", None)
            number_of_items = request.POST.getlist("number_of_items", None)
            items_price_ids = request.POST.getlist("item_price_id", None)

            shipping_checkbox = request.POST.get("shipping_checkbox", None)
            shipping_id = request.POST.get("subscription_shipping", None)

            discount_checkbox = request.POST.get("discount_checkbox", None)
            discount_id = request.POST.get("order_discount", None)

            # subscription_shipping_tax_status = request.POST.get(
            #     "subscription_shipping_tax_status", None)

            subscription_currency = request.POST.get("currency", "USD")

            discount = None
            if discount_id:
                if is_valid_uuid(discount_id):
                    discount = ShopTurboItemsDiscount.objects.filter(
                        id=discount_id
                    ).first()
                else:
                    if is_valid_number(discount_id):
                        discount_number_format = request.POST.get(
                            "discount_number_format", None
                        )
                        discount, _ = ShopTurboItemsDiscount.objects.get_or_create(
                            value=discount_id,
                            discount_type="free_writing_discounts",
                            number_format=discount_number_format,
                        )

            shipping = None
            if shipping_id:
                shipping = ShopTurboShippingCost.objects.filter(id=shipping_id).first()

            if not has_quota(workspace, SUBSCRIPTION_USAGE_CATEGORY):
                if lang == "ja":
                    Notification.objects.create(
                        workspace=get_workspace(request.user),
                        user=request.user,
                        message="制限を超えたため、一部のサブスクリプションを作成できませんでした。サブスクリプション プランをアップグレードしてクォータを増やすか、既存のサブスクリプションの一部をアーカイブしてスペースを解放してください。",
                        type="error",
                    )
                else:
                    Notification.objects.create(
                        workspace=get_workspace(request.user),
                        user=request.user,
                        message="Some subscriptions could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some existing subscriptions to free up space.",
                        type="error",
                    )
                return redirect(reverse("shopturbo_subscriptions", host="app"))

            shopturbo_subscriptions = ShopTurboSubscriptions.objects.create(
                workspace=workspace,
                status="active",
            )
            shopturbo_subscriptions.currency = subscription_currency

            association_label = AssociationLabel.objects.filter(
                workspace=workspace,
                object_source=TYPE_OBJECT_SUBSCRIPTION,
                label__iexact="customer",
            ).first()
            AssociationLabelObject.reset_associations_for_object(
                shopturbo_subscriptions, workspace, association_label
            )
            shopturbo_subscriptions.contact = None
            shopturbo_subscriptions.company = None
            for contact_and_company_id in contact_and_company:
                if contact_and_company_id == "":
                    continue
                if Contact.objects.filter(id=contact_and_company_id):
                    shopturbo_subscriptions.contact = Contact.objects.get(
                        id=contact_and_company_id
                    )
                    shopturbo_subscriptions.save()
                    AssociationLabelObject.create_association(
                        shopturbo_subscriptions,
                        shopturbo_subscriptions.contact,
                        workspace,
                        association_label,
                    )

                elif Company.objects.filter(id=contact_and_company_id):
                    shopturbo_subscriptions.company = Company.objects.get(
                        id=contact_and_company_id
                    )
                    shopturbo_subscriptions.save()
                    AssociationLabelObject.create_association(
                        shopturbo_subscriptions,
                        shopturbo_subscriptions.company,
                        workspace,
                        association_label,
                    )

            if start_date:
                start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
                shopturbo_subscriptions.start_date = start_date
                shopturbo_subscriptions.save()

            if end_date:
                end_date = datetime.strptime(end_date, "%Y-%m-%d").date()
                shopturbo_subscriptions.end_date = end_date

            if status:
                shopturbo_subscriptions.status = status

            if subscription_status:
                shopturbo_subscriptions.subscription_status = subscription_status
                auto_gen_invoice = bool(request.POST.get("auto_gen_invoice", None))
                shopturbo_subscriptions.auto_gen_invoice = auto_gen_invoice
                if auto_gen_invoice:
                    auto_gen_invoice_statuses = request.POST.getlist(
                        "auto_gen_invoice_status", None
                    )
                    shopturbo_subscriptions.auto_gen_invoice_statuses = ",".join(
                        auto_gen_invoice_statuses
                    )

            if freq:
                shopturbo_subscriptions.frequency = freq

            if freq_time:
                shopturbo_subscriptions.frequency_time = freq_time

            if "generate-order" in request.POST:
                if prior_to:
                    shopturbo_subscriptions.prior_to_next = prior_to

            if prior_time:
                shopturbo_subscriptions.prior_to_time = prior_time

            if billing_timing:
                shopturbo_subscriptions.billing_timing = billing_timing

            if tax_rate:
                # Clean tax rate value (remove commas if present)
                cleaned_tax_rate = clean_price_value(tax_rate)
                shopturbo_subscriptions.tax_rate = (
                    cleaned_tax_rate if cleaned_tax_rate is not None else tax_rate
                )

            if discount_checkbox and discount:
                shopturbo_subscriptions.discounts = discount
            else:
                shopturbo_subscriptions.discounts = None

            if shipping_checkbox and shipping:
                shopturbo_subscriptions.shipping_cost = shipping
            else:
                shopturbo_subscriptions.shipping_cost = None

            if billing_timing == "first_day" or billing_timing is None:
                shopturbo_subscriptions.upcoming_invoice_date = start_date
            else:
                if freq_time == "days":
                    shopturbo_subscriptions.upcoming_invoice_date = (
                        start_date + timedelta(days=int(freq))
                    )
                elif freq_time == "weeks":
                    shopturbo_subscriptions.upcoming_invoice_date = (
                        start_date + timedelta(days=int(freq) * 7)
                    )
                elif freq_time == "months":
                    shopturbo_subscriptions.upcoming_invoice_date = (
                        start_date + timedelta(days=int(freq) * 30)
                    )
                elif freq_time == "years":
                    shopturbo_subscriptions.upcoming_invoice_date = (
                        start_date + timedelta(days=int(freq) * 365)
                    )

            # if subscription_shipping_tax_status:
            #     shopturbo_subscriptions.shipping_cost_tax_status = subscription_shipping_tax_status

            assign_object_owner(
                shopturbo_subscriptions, owner, request, TYPE_OBJECT_SUBSCRIPTION
            )

            save_custom_property(request, shopturbo_subscriptions)
            save_association_label(
                request, shopturbo_subscriptions, TYPE_OBJECT_SUBSCRIPTION
            )

            if "generate-order" in request.POST:
                if not has_quota(workspace, ORDER_USAGE_CATEGORY):
                    if request.user.verification.language == "ja":
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="制限を超えたため、受注レコードを作成できませんでした。サブスクリプション プランをアップグレードして割り当てを増やすか、既存のレコードをアーカイブしてスペースを解放してください。",
                            type="error",
                        )
                    else:
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="Order could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some records to free up space.",
                            type="error",
                        )
                    return redirect("shopturbo")

                order = ShopTurboOrders.objects.create(
                    workspace=workspace,
                    status="active",
                    order_type="manual_order",
                    platform="sanka",
                    subscription=shopturbo_subscriptions,
                    company=shopturbo_subscriptions.company,
                    contact=shopturbo_subscriptions.contact,
                    order_at=shopturbo_subscriptions.start_date,
                )

            checker = request.POST.get("item-checker", None)
            # Save the Quick Entry preference
            shopturbo_subscriptions.quick_entry_mode = bool(checker)

            if not checker:
                items_custom_name = request.POST.getlist("items_custom_name", [])
                items_custom_price = request.POST.getlist("items_custom_price", [])
                items_custom_currency = request.POST.getlist(
                    "items_custom_currency", []
                )
                items_custom_number = request.POST.getlist("items_custom_number", [])

                for idx, item in enumerate(items):
                    if item:
                        items_custom_name.insert(idx, "")
                        items_custom_price.insert(idx, "")
                        items_custom_currency.insert(idx, "")
                        items_custom_number.insert(idx, "")

                item_total_price = 0
                if "generate-order" in request.POST:
                    order.order_type = "item_order"

                for idx, item_id in enumerate(items):
                    if not item_id or item_id == "":
                        continue

                    item = ShopTurboItems.objects.get(id=item_id)
                    # Safely access number_of_items with bounds checking
                    number_item = (
                        number_of_items[idx] if idx < len(number_of_items) else 1
                    )

                    shopturbo_item_subscriptions = (
                        ShopTurboItemsSubscriptions.objects.create(
                            item=item,
                            subscriptions=shopturbo_subscriptions,
                            currency=item.currency,
                            number_item=number_item,
                        )
                    )
                    if "generate-order" in request.POST:
                        ShopTurboItemsOrders.objects.create(
                            item=item,
                            order=order,
                            currency=item.currency,
                            number_item=number_item,
                            item_price_order=item.price,
                            total_price=item.price,
                        )

                    shopturbo_subscriptions.currency = item.currency

                    # Safely access items_price_ids with bounds checking
                    if idx < len(items_price_ids) and items_price_ids[idx]:
                        items_price = None
                        if not is_valid_uuid(items_price_ids[idx]):
                            # Clean the price value before saving
                            cleaned_price = clean_price_value(items_price_ids[idx])
                            if cleaned_price is not None:
                                items_price, _ = (
                                    ShopTurboItemsPrice.objects.get_or_create(
                                        workspace=workspace,
                                        item=item,
                                        price=cleaned_price,
                                        currency=item.currency,
                                    )
                                )
                        else:
                            items_price = ShopTurboItemsPrice.objects.filter(
                                id=items_price_ids[idx]
                            ).first()

                        if items_price:
                            shopturbo_item_subscriptions.item_price = items_price
                            shopturbo_item_subscriptions.item_price_order = item.price
                            item.price = items_price.price

                    shopturbo_item_subscriptions.item_price_order = item.price
                    if int(shopturbo_item_subscriptions.number_item) > 1:
                        shopturbo_item_subscriptions.total_price = (
                            shopturbo_item_subscriptions.item_price_order
                            * int(shopturbo_item_subscriptions.number_item)
                        )
                    else:
                        shopturbo_item_subscriptions.total_price = (
                            shopturbo_item_subscriptions.item_price_order
                        )

                    shopturbo_item_subscriptions.save()

                    # Ensure total_price is a float before adding
                    if isinstance(shopturbo_item_subscriptions.total_price, str):
                        item_total_price += float(
                            shopturbo_item_subscriptions.total_price.replace(",", "")
                        )
                    else:
                        item_total_price += (
                            float(shopturbo_item_subscriptions.total_price)
                            if shopturbo_item_subscriptions.total_price is not None
                            else 0.0
                        )

                for idx, item_custom in enumerate(items_custom_name):
                    if item_custom == "" or not item_custom:
                        continue

                    # Safely access items_custom_number with bounds checking
                    if idx < len(items_custom_number):
                        number_item = items_custom_number[idx]
                        if items_custom_number[idx] == "":
                            number_item = 0
                    else:
                        number_item = 0

                    # Safely access items_custom_price with bounds checking
                    if idx < len(items_custom_price):
                        if type(items_custom_price[idx]) is str:
                            if "," in items_custom_price[idx]:
                                items_custom_price[idx] = items_custom_price[
                                    idx
                                ].replace(",", "")
                            items_custom_price[idx] = float(items_custom_price[idx])
                        custom_price = items_custom_price[idx]
                    else:
                        custom_price = 0.0

                    # Safely access items_custom_currency with bounds checking
                    custom_currency = (
                        items_custom_currency[idx]
                        if idx < len(items_custom_currency)
                        else shopturbo_subscriptions.currency
                    )

                    shopturbo_item_subscriptions = (
                        ShopTurboItemsSubscriptions.objects.create(
                            subscriptions=shopturbo_subscriptions,
                            custom_item_name=item_custom,
                            number_item=number_item,
                            currency=custom_currency,
                            item_price_order=custom_price,
                        )
                    )
                    shopturbo_item_subscriptions.save()

                    if float(shopturbo_item_subscriptions.number_item) >= 0.0:
                        shopturbo_item_subscriptions.total_price = (
                            shopturbo_item_subscriptions.item_price_order
                            * float(shopturbo_item_subscriptions.number_item)
                        )
                    else:
                        shopturbo_item_subscriptions.total_price = (
                            shopturbo_item_subscriptions.item_price_order
                        )
                    shopturbo_item_subscriptions.save()

                    # Ensure total_price is a float before adding
                    if isinstance(shopturbo_item_subscriptions.total_price, str):
                        item_total_price += float(
                            shopturbo_item_subscriptions.total_price.replace(",", "")
                        )
                    else:
                        item_total_price += (
                            float(shopturbo_item_subscriptions.total_price)
                            if shopturbo_item_subscriptions.total_price is not None
                            else 0.0
                        )

                if item_total_price > 0:
                    shopturbo_subscriptions.total_price = item_total_price
                else:
                    shopturbo_subscriptions.total_price = 0

                shopturbo_subscriptions.save()

            else:
                currency = request.POST.get("currency", None)
                price = request.POST.get("price", None)
                if type(price) is str:
                    if "," in price:
                        price = price.replace(",", "")

                shopturbo_subscriptions.item = None
                shopturbo_subscriptions.number_item = 1
                shopturbo_subscriptions.currency = currency
                # Convert price to float before assigning
                try:
                    shopturbo_subscriptions.total_price = (
                        float(price) if price is not None else 0.0
                    )
                except (ValueError, TypeError):
                    shopturbo_subscriptions.total_price = 0.0

            if shopturbo_subscriptions.shipping_cost:
                # Ensure total_price is a float before adding shipping cost
                if isinstance(shopturbo_subscriptions.total_price, str):
                    total_price_value = float(
                        shopturbo_subscriptions.total_price.replace(",", "")
                    )
                else:
                    total_price_value = (
                        float(shopturbo_subscriptions.total_price)
                        if shopturbo_subscriptions.total_price is not None
                        else 0.0
                    )

                shopturbo_subscriptions.total_price = total_price_value + float(
                    shopturbo_subscriptions.shipping_cost.value
                )

            if shopturbo_subscriptions.tax_rate:
                # Ensure total_price is a float before multiplication
                try:
                    # Store the original value as total_price_without_tax
                    if isinstance(
                        shopturbo_subscriptions.total_price, (list, tuple, str)
                    ):
                        # If total_price is a sequence or string, convert it to float
                        if isinstance(shopturbo_subscriptions.total_price, str):
                            # Handle string with possible commas
                            total_price_value = float(
                                shopturbo_subscriptions.total_price.replace(",", "")
                            )
                        else:
                            # For other sequences, use the first value if available
                            total_price_value = (
                                float(shopturbo_subscriptions.total_price[0])
                                if shopturbo_subscriptions.total_price
                                else 0.0
                            )
                    else:
                        # If it's already a numeric value, just use it
                        total_price_value = (
                            float(shopturbo_subscriptions.total_price)
                            if shopturbo_subscriptions.total_price is not None
                            else 0.0
                        )

                    # Store the original value as total_price_without_tax
                    shopturbo_subscriptions.total_price_without_tax = total_price_value
                    # Calculate the new total price with tax
                    shopturbo_subscriptions.total_price = total_price_value * (
                        1 + float(shopturbo_subscriptions.tax_rate) / 100
                    )
                except (TypeError, ValueError, IndexError) as e:
                    # Log the error and set default values
                    logger.error(f"Error calculating total price with tax: {e}")
                    shopturbo_subscriptions.total_price_without_tax = 0.0
                    shopturbo_subscriptions.total_price = 0.0
            else:
                # If no tax rate, just ensure total_price is a float and set total_price_without_tax
                try:
                    if isinstance(
                        shopturbo_subscriptions.total_price, (list, tuple, str)
                    ):
                        if isinstance(shopturbo_subscriptions.total_price, str):
                            total_price_value = float(
                                shopturbo_subscriptions.total_price.replace(",", "")
                            )
                        else:
                            total_price_value = (
                                float(shopturbo_subscriptions.total_price[0])
                                if shopturbo_subscriptions.total_price
                                else 0.0
                            )
                        shopturbo_subscriptions.total_price = total_price_value

                    shopturbo_subscriptions.total_price_without_tax = (
                        shopturbo_subscriptions.total_price
                    )
                except (TypeError, ValueError, IndexError) as e:
                    logger.error(f"Error setting total price without tax: {e}")
                    shopturbo_subscriptions.total_price_without_tax = 0.0
                    shopturbo_subscriptions.total_price = 0.0

            shopturbo_subscriptions.save(
                log_data={
                    "user": request.user,
                    "status": "create",
                    "workspace": workspace,
                }
            )
            adjust_auto_gen_invoice_schedule(shopturbo_subscriptions)

            if "generate-order" in request.POST:
                if shopturbo_subscriptions.currency:
                    order.currency = shopturbo_subscriptions.currency
                    order.save()

                if shopturbo_subscriptions.total_price:
                    order.total_price = shopturbo_subscriptions.total_price
                    order.item_price_order = shopturbo_subscriptions.total_price
                    order.total_price_without_tax = shopturbo_subscriptions.total_price
                    total_price_discount = order.item_price_order
                    if discount_checkbox and discount:
                        order.discounts = discount
                        if discount.number_format == "%":
                            total_price_discount = order.item_price_order - (
                                order.item_price_order * (float(discount.value) / 100)
                            )
                        else:
                            total_price_discount = order.item_price_order - float(
                                discount.value
                            )
                        order.total_price_without_tax = total_price_discount
                        order.total_price = total_price_discount
                    order.save()
                if shopturbo_subscriptions.number_item:
                    order.number_item = shopturbo_subscriptions.number_item
                    order.save()
                if shopturbo_subscriptions.shipping_cost:
                    # Create or get equivalent ShopTurboShippingCost for the order
                    order.shipping_cost = shopturbo_subscriptions.shipping_cost
                    order.shipping_cost_tax_status = (
                        shopturbo_subscriptions.shipping_cost_tax_status
                    )
                    order.save()

                if freq:
                    if freq_time == "days":
                        order.order_at = order.order_at + timedelta(days=int(freq))
                    elif freq_time == "weeks":
                        order.order_at = order.order_at + timedelta(days=int(freq) * 7)
                    elif freq_time == "months":
                        order.order_at = order.order_at + timedelta(days=int(freq) * 30)
                    elif freq_time == "years":
                        order.order_at = order.order_at + timedelta(
                            days=int(freq) * 365
                        )

                    if shopturbo_subscriptions.prior_to_next:
                        order_at = order.order_at
                        if shopturbo_subscriptions.prior_to_time == "days":
                            order_at = order_at - timedelta(
                                days=int(shopturbo_subscriptions.prior_to_next)
                            )
                        elif shopturbo_subscriptions.prior_to_time == "weeks":
                            order_at = order_at - timedelta(
                                days=int(shopturbo_subscriptions.prior_to_next) * 7
                            )

                        if order.order_at < order_at:
                            order_at = order.order_at

                        order.order_at = order_at

                    order.save()

            if request.POST.get("type_association", "") == "create-association":
                source = request.POST.get("source")
                module_object_slug = OBJECT_TYPE_TO_SLUG[source]
                module = (
                    Module.objects.filter(
                        workspace=workspace, object_values__contains=source
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                if module:
                    module_slug = module.slug
                if source == TYPE_OBJECT_ORDER:
                    source_object_id = request.POST.get("source_object_id")
                    order = ShopTurboOrders.objects.filter(id=source_object_id).first()
                    order.subscription = shopturbo_subscriptions
                    order.save()
                    return redirect(
                        build_redirect_url(
                            reverse(
                                "load_object_page",
                                host="app",
                                kwargs={
                                    "module_slug": module_slug,
                                    "object_slug": module_object_slug,
                                },
                            ),
                            view_id=view_id,
                            target=source,
                            id=order.id,
                        )
                    )
                elif source == TYPE_OBJECT_INVOICE:
                    source_object_id = request.POST.get("source_object_id")
                    invoice = Invoice.objects.filter(id=source_object_id).first()
                    invoice.subscriptions.add(*[shopturbo_subscriptions])
                    return redirect(
                        build_redirect_url(
                            reverse(
                                "load_object_page",
                                host="app",
                                kwargs={
                                    "module_slug": module_slug,
                                    "object_slug": module_object_slug,
                                },
                            ),
                            view_id=view_id,
                            id=invoice.id,
                        )
                    )
                elif source == TYPE_OBJECT_COMPANY:
                    source_object_id = request.POST.get("source_object_id")
                    company = Company.objects.filter(id=source_object_id).first()
                    if company:
                        shopturbo_subscriptions.company = company
                        shopturbo_subscriptions.save()

                    return redirect(
                        build_redirect_url(
                            reverse(
                                "load_object_page",
                                host="app",
                                kwargs={
                                    "module_slug": module_slug,
                                    "object_slug": module_object_slug,
                                },
                            ),
                            view_id=view_id,
                            id=company.id,
                        )
                    )
                elif source == TYPE_OBJECT_JOURNAL:
                    source_object_id = request.POST.get("source_object_id")
                    journal = JournalEntry.objects.filter(id=source_object_id).first()
                    if journal:
                        journal.subscription = shopturbo_subscriptions
                        journal.save()

                    return redirect(
                        build_redirect_url(
                            reverse(
                                "load_object_page",
                                host="app",
                                kwargs={
                                    "module_slug": module_slug,
                                    "object_slug": module_object_slug,
                                },
                            ),
                            view_id=view_id,
                            id=journal.id,
                        )
                    )

                elif source == TYPE_OBJECT_CONTACT:
                    source_object_id = request.POST.get("source_object_id")
                    contact = Contact.objects.filter(id=source_object_id).first()
                    if contact:
                        shopturbo_subscriptions.contact = contact
                        shopturbo_subscriptions.save()

                    return redirect(
                        build_redirect_url(
                            reverse(
                                "load_object_page",
                                host="app",
                                kwargs={
                                    "module_slug": module_slug,
                                    "object_slug": module_object_slug,
                                },
                            ),
                            view_id=view_id,
                            id=contact.id,
                        )
                    )

            if shopturbo_subscriptions:
                if source_url:
                    source_url = update_query_params_url(
                        source_url,
                        {"target": target, "id": [str(shopturbo_subscriptions.id)]},
                    )
                    return redirect(source_url)
                return redirect(
                    reverse("shopturbo_subscriptions", host="app")
                    + f"?view_id={view_id}"
                    + f"&subscription_id={shopturbo_subscriptions.id}"
                )

    trigger_create_subscriptions_action(
        shopturbo_subscriptions.id, request.user.id, request.LANGUAGE_CODE
    )
    if module:
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
        )
    return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def create_new_subscription_action(request):
    workspace = get_workspace(request.user)
    lang = "en"

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")
        postfix = ""
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            logger.info("Action node does not exist")
            return HttpResponse(status=404)

        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                logger.info("Action node does not exist")
                return HttpResponse(status=404)

        # Convert items_data to list if it exists
        input_data = node.input_data if node and node.input_data else {}
        items_data = input_data.get("items_data", {})
        items_list = (
            combine_item_dict_to_list(items_data, target="context")
            if items_data
            else []
        )
        use_prev_data = input_data.get("use_prev_data", False)
        pass_platform_id = input_data.get("pass_platform_id", False)
        use_order_date = input_data.get("use_order_date", False)

        context = {
            "action_index": action_index,
            "action_slug": action_slug,
            "input_data": input_data,
            "items_data": items_data,  # Original dictionary format
            "items_list": items_list,  # Converted list format
            "SUBSCRIPTIONS_STATUS_DISPLAY": SUBSCRIPTIONS_STATUS_DISPLAY,
            "subscription_object": ShopTurboSubscriptions(workspace=workspace),
            "FREQ_TIME": FREQ_TIME,
            "BILLING_TIMING": BILLING_TIMING,
            "PRIOR_FREQ_TIME": PRIOR_FREQ_TIME,
            "currency_model": CURRENCY_MODEL,
            "workspace": workspace,
            "ShopTurboShippingCosts": ShopTurboShippingCost.objects.filter(
                workspace=workspace
            ),
            "use_prev_data": use_prev_data,
            "pass_platform_id": pass_platform_id,
            "use_order_date": use_order_date,
        }

        return render(
            request,
            "data/shopturbo/subscriptions/actions/create-subscription-action-form.html",
            context,
        )

    # POST
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    action_slug = request.POST.get("action_slug")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")

    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            logger.info("ActionNode does not exist")
            return HttpResponse(status=404)

    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            logger.info("ActionTracker does not exist")
            return HttpResponse(status=404)

    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            logger.info("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    if submit_option == "save":
        # Subscription specific fields from HTML template
        node.valid_to_run = True
        use_prev_data = request.POST.get("use_prev_data" + postfix, None)
        if use_prev_data:
            pass_platform_id = request.POST.get("pass_platform_id" + postfix, None)
            pass_platform_channel_ids = {}
            if pass_platform_id:
                pass_platform_channel_ids = request.POST.getlist(
                    "platform-file-column" + postfix, []
                )
                for channel_id_ in pass_platform_channel_ids:
                    pass_platform_channel_ids[channel_id_] = request.POST.getlist(
                        "platform-sanka-properties-" + channel_id_ + postfix, []
                    )
            contact_company = None
            currency = None
            tax_rate = None
            shipping_checkbox = None
            subscription_shipping = None
            discount_checkbox = None
            discount_id = None
            discount_number_format = None
            discount_tax_option = None
            use_order_date = request.POST.get("use_order_date" + postfix, None)
            if use_order_date:
                start_date = None
            else:
                start_date = request.POST.get("start_date" + postfix, None)
                if not start_date:
                    node.valid_to_run = False
            checker = None
            price = None
            items_data = {}

        else:
            pass_platform_id = None
            pass_platform_channel_ids = {}
            use_order_date = False
            contact_company = request.POST.get("contact_and_company" + postfix, None)
            currency = request.POST.get("currency" + postfix, None)
            tax_rate = request.POST.get("tax_rate" + postfix, None)

            # Shipping and discount fields
            shipping_checkbox = request.POST.get("shipping_checkbox" + postfix, None)
            subscription_shipping = request.POST.get(
                "subscription_shipping" + postfix, None
            )
            discount_checkbox = request.POST.get("discount_checkbox" + postfix, None)
            discount_id = request.POST.get("order_discount" + postfix, None)
            discount_number_format = request.POST.get(
                "discount_number_format" + postfix, None
            )
            discount_tax_option = request.POST.get(
                "discount_tax_option" + postfix, None
            )

            start_date = request.POST.get("start_date" + postfix, None)
            if not start_date:
                node.valid_to_run = False
            try:
                datetime.strptime(start_date, "%Y-%m-%d").date()
            except:
                node.valid_to_run = False

            # Subscription items data (using subscription architecture field names)
            subscriptions_items = request.POST.getlist(
                "subscriptions_items" + postfix, []
            )
            items = request.POST.getlist("item" + postfix, [])
            number_of_items = request.POST.getlist("number_of_items" + postfix, [])
            items_price_ids = request.POST.getlist("item_price_id" + postfix, [])
            item_variants = request.POST.getlist("item-variant" + postfix, [])

            # Custom items (subscription architecture)
            items_custom_name = request.POST.getlist("items_custom_name" + postfix, [])
            items_custom_price = request.POST.getlist(
                "items_custom_price" + postfix, []
            )
            items_custom_currency = request.POST.getlist(
                "items_custom_currency" + postfix, []
            )
            items_custom_number = request.POST.getlist(
                "items_custom_number" + postfix, []
            )

            # Quick entry data
            checker = request.POST.get("item-checker" + postfix, None)
            price_field = "price" + postfix
            price = request.POST.get(price_field, None)
            try:
                # Store subscription items data for later processing
                for idx, item in enumerate(items):
                    if item:
                        items_custom_name.insert(idx, "")
                        items_custom_price.insert(idx, "")
                        items_custom_currency.insert(idx, "")
                        items_custom_number.insert(idx, "")
                    else:
                        subscriptions_items.insert(idx, "")

                items_data = {
                    "items": items,
                    "number_of_items": number_of_items,
                    "items_price_ids": items_price_ids,
                    "item_variants": item_variants,
                    "items_custom_name": items_custom_name,
                    "items_custom_price": items_custom_price,
                    "items_custom_currency": items_custom_currency,
                    "items_custom_number": items_custom_number,
                }
            except Exception:
                logger.info(
                    "[DEBUG] Skip this input as it is read from html, Workflow do not have this input"
                )
                items_data = {}

        end_date = request.POST.get("end_date" + postfix, None)

        subscription_status = request.POST.get("subscription_status" + postfix, None)
        frequency = request.POST.get("frequency" + postfix, None)
        freq_time = request.POST.get("freq_time" + postfix, None)
        billing_timing = request.POST.get("billing_timing" + postfix, None)
        prior = request.POST.get("prior" + postfix, None)
        prior_time = request.POST.get("prior_time" + postfix, None)
        if (
            not subscription_status
            or not frequency
            or not freq_time
            or not billing_timing
            or not prior
            or not prior_time
        ):
            node.valid_to_run = False

        object_type = request.POST.get("object_type" + postfix)
        object_name = request.POST.get("object_name" + postfix)

        input_data = {
            "use_prev_data": use_prev_data,
            "use_order_date": use_order_date,
            "pass_platform_id": pass_platform_id,
            "pass_platform_channel_ids": pass_platform_channel_ids,
            "subscription_status": subscription_status,
            "frequency": frequency,
            "freq_time": freq_time,
            "billing_timing": billing_timing,
            "prior": prior,
            "prior_time": prior_time,
            "contact_company": contact_company,
            "currency": currency,
            "start_date": start_date,
            "end_date": end_date,
            "shipping_checkbox": shipping_checkbox,
            "subscription_shipping": subscription_shipping,
            "discount_checkbox": discount_checkbox,
            "discount_id": discount_id,
            "discount_number_format": discount_number_format,
            "discount_tax_option": discount_tax_option,
            "tax_rate": tax_rate,
            "checker": checker,
            "price": price,
            "items_data": items_data,
        }

        if object_type:
            input_data["object_type"] = object_type
        if object_name:
            input_data["object_name"] = object_name

        node.input_data = input_data
        node.predefined_input = input_data
        node.save()

        return HttpResponse(status=200)
    else:
        if not node:
            return HttpResponse(status=400)

        use_prev_data = node.input_data.get("use_prev_data", None)
        use_order_date = node.input_data.get("use_order_date", None)
        pass_platform_id = node.input_data.get("pass_platform_id", None)
        pass_platform_channel_ids = node.input_data.get(
            "pass_platform_channel_ids", None
        )
        subscription_status = node.input_data.get("subscription_status", None)
        frequency = node.input_data.get("frequency", None)
        freq_time = node.input_data.get("freq_time", None)
        billing_timing = node.input_data.get("billing_timing", None)
        prior = node.input_data.get("prior", None)
        prior_time = node.input_data.get("prior_time", None)
        contact_company = node.input_data.get("contact_company", None)
        currency = node.input_data.get("currency", None)
        start_date = node.input_data.get("start_date", None)
        end_date = node.input_data.get("end_date", None)
        shipping_checkbox = node.input_data.get("shipping_checkbox", None)
        subscription_shipping = node.input_data.get("subscription_shipping", None)
        discount_checkbox = node.input_data.get("discount_checkbox", None)
        discount_id = node.input_data.get("discount_id", None)
        discount_number_format = node.input_data.get("discount_number_format", None)
        discount_tax_option = node.input_data.get("discount_tax_option", None)
        tax_rate = node.input_data.get("tax_rate", None)
        checker = node.input_data.get("checker", None)
        price = node.input_data.get("price", None)
        items_data = node.input_data.get("items_data", None)
        use_prev_data = node.input_data.get("use_prev_data", None)
        subscription_status = node.input_data.get("subscription_status", None)
        frequency = node.input_data.get("frequency", None)
        freq_time = node.input_data.get("freq_time", None)
        billing_timing = node.input_data.get("billing_timing", None)
        prior = node.input_data.get("prior", None)
        prior_time = node.input_data.get("prior_time", None)
        start_date = node.input_data.get("start_date", None)
        end_date = node.input_data.get("end_date", None)
        shipping_checkbox = node.input_data.get("shipping_checkbox", None)
        subscription_shipping = node.input_data.get("subscription_shipping", None)
        discount_checkbox = node.input_data.get("discount_checkbox", None)
        discount_id = node.input_data.get("discount_id", None)
        discount_number_format = node.input_data.get("discount_number_format", None)
        discount_tax_option = node.input_data.get("discount_tax_option", None)
        tax_rate = node.input_data.get("tax_rate", None)
        checker = node.input_data.get("checker", None)
        price = node.input_data.get("price", None)
        items_data = node.input_data.get("items_data", {})

        # Check quota for subscriptions (similar to invoice quota check)
        # You may need to define this constant
        subscription_usage_category = "subscription"
        if not has_quota(workspace, subscription_usage_category):
            entries_name = "Subscriptions"
            msg = f"{entries_name}, could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some existing {entries_name} to free up space."
            if lang == "ja":
                msg = f"{entries_name},制限を超えたため作成できませんでした。サブスクリプション プランをアップグレードしてクォータを増やすか、既存の {entries_name} の一部をアーカイブしてスペースを解放します。"
            Notification.objects.create(
                workspace=workspace, user=request.user, message=msg, type="error"
            )
            return_fn = get_redirect_workflow(
                submit_option, node, workspace, at, wat, status="failed"
            )
            # Make it as failed and don't continue to run the workflow
            at.status = "failed"
            at.completed_at = timezone.now()
            at.save()
            wat.status = "failed"
            wat.save()
            # next_node = None
            # if node:
            #     next_node = node.next_node
            #     at = ActionTracker.objects.filter(id=at.id).first()
            # if next_node:
            #     next_at = ActionTracker.objects.get(
            #         node=next_node, workflow_action_tracker=wat
            #     )
            #     trigger_next_action(
            #         current_action_tracker=at,
            #         workflow_action_tracker=wat,
            #         current_node=node,
            #         user_id=str(request.user.id),
            #         lang=lang,
            #     )
            return redirect(return_fn)

        # Create subscription object
        subscription_obj = ShopTurboSubscriptions.objects.create(
            workspace=workspace,
            currency=currency,
            subscription_status=subscription_status,  # Use subscription_status field
            status="active",  # Use as usage status field
            frequency=int(frequency) if frequency else None,
            frequency_time=freq_time,  # Use frequency_time field
            billing_timing=billing_timing,
            # Use prior_to_next field, convert to float
            prior_to_next=float(prior) if prior else None,
            prior_to_time=prior_time,  # Use prior_to_time field
            start_date=start_date,
            end_date=end_date,
            total_price=0,
        )

        # Create subscription items using proper subscription architecture
        postfix = ""
        if action_index:
            postfix = "_" + action_index

        if not use_prev_data:
            # Get form data with action_index support
            items = items_data.get("items", [])
            number_of_items = items_data.get("number_of_items", [])
            items_price_ids = items_data.get("item_price_ids", [])
            item_variants = items_data.get("item_variants", [])

            # Custom items
            items_custom_name = items_data.get("items_custom_name", [])
            items_custom_price = items_data.get("items_custom_price", [])
            items_custom_currency = items_data.get("items_custom_currency", [])
            items_custom_number = items_data.get("items_custom_number", [])
        else:
            # Extract items from previous order data
            if "order" not in at.input_data:
                logger.error("No order data found in input_data")
                msg = (
                    "Create subscription record: Failed to get the previous order data."
                )
                if lang == "ja":
                    msg = "サブスクリプション レコードの作成: 以前の注文データを取得できませんでした。"
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message=msg,
                    type="error",
                )
                return_fn = get_redirect_workflow(
                    submit_option, node, workspace, at, wat, status="failed"
                )
                # Make it as failed and don't continue to run the workflow
                at.status = "failed"
                at.completed_at = timezone.now()
                at.save()
                wat.status = "failed"
                wat.save()
                return redirect(return_fn)
            try:
                order_id = at.input_data["order"]["order_id"]
                order = ShopTurboOrders.objects.get(
                    order_id=order_id, workspace=workspace
                )

                if use_order_date:
                    start_date = order.order_at.date()

                logger.info(
                    f"[DEBUG] Extracting items from order {order_id} for subscription creation"
                )

                # Initialize lists to store extracted item data
                items = []
                number_of_items = []
                items_price_ids = []
                item_variants = []
                items_custom_name = []
                items_custom_price = []
                items_custom_currency = []
                items_custom_number = []

                # Extract items from order
                order_items = order.shopturboitemsorders_set.all()
                logger.info(
                    f"[DEBUG] Found {order_items.count()} items in order {order_id}"
                )

                if order_items:
                    checker = None
                    for order_item in order_items:
                        if order_item.item:
                            # Regular item from catalog
                            items.append(str(order_item.item.id))
                            number_of_items.append(
                                str(order_item.number_item)
                                if order_item.number_item
                                else "1"
                            )

                            # Handle item price
                            if order_item.item_price:
                                items_price_ids.append(str(order_item.item_price.id))
                            else:
                                items_price_ids.append("")

                            # Handle item variant
                            if order_item.item_variant:
                                item_variants.append(str(order_item.item_variant.id))
                            else:
                                item_variants.append("")

                            # Custom fields for regular items are empty
                            items_custom_name.append("")
                            items_custom_price.append("")
                            items_custom_currency.append("")
                            items_custom_number.append("")

                            logger.info(
                                f"[DEBUG] Added regular item: {order_item.item.name} (ID: {order_item.item.id})"
                            )

                        elif order_item.custom_item_name:
                            # Custom item
                            items.append("")  # No catalog item
                            number_of_items.append("")
                            items_price_ids.append("")
                            item_variants.append("")

                            # Custom item data
                            items_custom_name.append(order_item.custom_item_name)
                            items_custom_price.append(
                                str(order_item.item_price_order)
                                if order_item.item_price_order
                                else "0"
                            )
                            items_custom_currency.append(
                                order_item.currency
                                if order_item.currency
                                else (currency or "USD")
                            )
                            items_custom_number.append(
                                str(order_item.number_item)
                                if order_item.number_item
                                else "1"
                            )

                            logger.info(
                                f"[DEBUG] Added custom item: {order_item.custom_item_name}"
                            )

                else:
                    price = order.total_price
                    checker = "on"
                # Also inherit order-level data if available
                if order.contact:
                    contact_company = str(order.contact.id)
                    logger.info(
                        f"[DEBUG] Inherited contact from order: {order.contact.id}"
                    )
                elif order.company:
                    contact_company = str(order.company.id)
                    logger.info(
                        f"[DEBUG] Inherited company from order: {order.company.id}"
                    )

                if order.currency:
                    currency = order.currency
                    logger.info(
                        f"[DEBUG] Inherited currency from order: {order.currency}"
                    )

                # Inherit shipping cost
                if order.shipping_cost:
                    subscription_shipping = str(order.shipping_cost.id)
                    shipping_checkbox = True
                    logger.info(
                        f"[DEBUG] Inherited shipping cost from order: {order.shipping_cost.name} - {order.shipping_cost.value}"
                    )

                # Inherit discount
                if order.discounts:
                    discount_id = str(order.discounts.id)
                    discount_checkbox = True
                    discount_number_format = order.discounts.number_format
                    if order.discount_tax_option:
                        discount_tax_option = order.discount_tax_option
                    logger.info(
                        f"[DEBUG] Inherited discount from order: {order.discounts.value} ({order.discounts.number_format})"
                    )

                # Inherit tax rate if not already set
                if order.tax:
                    tax_rate = order.tax
                    logger.info(f"[DEBUG] Inherited tax rate from order: {order.tax}%")

                logger.info(
                    f"[DEBUG] Extracted {len([i for i in items if i])} regular items and {len([i for i in items_custom_name if i])} custom items"
                )

            except Exception as e:
                logger.error(f"[ERROR] Error extracting items from order: {str(e)}")
                msg = (
                    "Create subscription record: Failed to get the previous order data."
                )
                if lang == "ja":
                    msg = "サブスクリプション レコードの作成: 以前の注文データを取得できませんでした。"
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message=msg,
                    type="error",
                )
                return_fn = get_redirect_workflow(
                    submit_option, node, workspace, at, wat, status="failed"
                )
                # Make it as failed and don't continue to run the workflow
                at.status = "failed"
                at.completed_at = timezone.now()
                at.save()
                wat.status = "failed"
                wat.save()
                return redirect(return_fn)

        # Check if using quick entry mode (checker)
        subscription_obj.quick_entry_mode = bool(checker)

        if not checker:
            # Handle regular items (not quick entry mode)
            item_total_price = 0

            # Process regular items
            for idx, item_id in enumerate(items):
                if not item_id or item_id == "":
                    continue

                item = ShopTurboItems.objects.get(id=item_id)
                # Safely access number_of_items with bounds checking and convert to int
                number_item_raw = (
                    number_of_items[idx] if idx < len(number_of_items) else 1
                )
                try:
                    # Convert to float first to handle decimal strings, then to int
                    number_item = int(float(number_item_raw)) if number_item_raw else 1
                except (ValueError, TypeError):
                    number_item = 1

                shopturbo_item_subscriptions = (
                    ShopTurboItemsSubscriptions.objects.create(
                        item=item,
                        subscriptions=subscription_obj,
                        currency=item.currency,
                        number_item=number_item,
                    )
                )

                # Handle item variants
                if idx < len(item_variants) and item_variants[idx]:
                    from data.models import ShopTurboItemsVariations

                    item_variant = ShopTurboItemsVariations.objects.filter(
                        id=item_variants[idx]
                    ).first()
                    if item_variant:
                        shopturbo_item_subscriptions.item_variant = item_variant

                # Handle item prices
                if idx < len(items_price_ids) and items_price_ids[idx]:
                    if not is_valid_uuid(items_price_ids[idx]):
                        items_price, _ = ShopTurboItemsPrice.objects.get_or_create(
                            workspace=workspace,
                            item=item,
                            price=items_price_ids[idx],
                            currency=item.currency,
                        )
                    else:
                        items_price = ShopTurboItemsPrice.objects.filter(
                            id=items_price_ids[idx]
                        ).first()
                    if items_price:
                        shopturbo_item_subscriptions.item_price = items_price
                        shopturbo_item_subscriptions.item_price_order = (
                            items_price.price
                        )
                        item.price = items_price.price

                shopturbo_item_subscriptions.item_price_order = item.price
                if int(shopturbo_item_subscriptions.number_item) > 1:
                    shopturbo_item_subscriptions.total_price = (
                        shopturbo_item_subscriptions.item_price_order
                        * int(shopturbo_item_subscriptions.number_item)
                    )
                else:
                    shopturbo_item_subscriptions.total_price = (
                        shopturbo_item_subscriptions.item_price_order
                    )

                shopturbo_item_subscriptions.save()

                # Add to total price
                if isinstance(shopturbo_item_subscriptions.total_price, str):
                    item_total_price += float(
                        shopturbo_item_subscriptions.total_price.replace(",", "")
                    )
                else:
                    item_total_price += (
                        float(shopturbo_item_subscriptions.total_price)
                        if shopturbo_item_subscriptions.total_price is not None
                        else 0.0
                    )

            # Process custom items
            for idx, item_custom in enumerate(items_custom_name):
                if item_custom == "" or not item_custom:
                    continue

                # Safely access items_custom_number with bounds checking and convert to int
                if idx < len(items_custom_number):
                    number_item_raw = items_custom_number[idx]
                    if items_custom_number[idx] == "":
                        number_item = 0
                    else:
                        try:
                            # Convert to float first to handle decimal strings, then to int
                            number_item = int(float(number_item_raw))
                        except (ValueError, TypeError):
                            number_item = 0
                else:
                    number_item = 0

                # Safely access items_custom_price with bounds checking
                if idx < len(items_custom_price):
                    if type(items_custom_price[idx]) is str:
                        if "," in items_custom_price[idx]:
                            items_custom_price[idx] = items_custom_price[idx].replace(
                                ",", ""
                            )
                        items_custom_price[idx] = float(items_custom_price[idx])
                    custom_price = items_custom_price[idx]
                else:
                    custom_price = 0.0

                # Safely access items_custom_currency with bounds checking
                custom_currency = (
                    items_custom_currency[idx]
                    if idx < len(items_custom_currency)
                    else subscription_obj.currency
                )

                shopturbo_item_subscriptions = (
                    ShopTurboItemsSubscriptions.objects.create(
                        subscriptions=subscription_obj,
                        custom_item_name=item_custom,
                        number_item=number_item,
                        currency=custom_currency,
                        item_price_order=custom_price,
                    )
                )

                if float(shopturbo_item_subscriptions.number_item) >= 0.0:
                    shopturbo_item_subscriptions.total_price = (
                        shopturbo_item_subscriptions.item_price_order
                        * float(shopturbo_item_subscriptions.number_item)
                    )
                else:
                    shopturbo_item_subscriptions.total_price = (
                        shopturbo_item_subscriptions.item_price_order
                    )
                shopturbo_item_subscriptions.save()

                # Add to total price
                if isinstance(shopturbo_item_subscriptions.total_price, str):
                    item_total_price += float(
                        shopturbo_item_subscriptions.total_price.replace(",", "")
                    )
                else:
                    item_total_price += (
                        float(shopturbo_item_subscriptions.total_price)
                        if shopturbo_item_subscriptions.total_price is not None
                        else 0.0
                    )

            if item_total_price > 0:
                subscription_obj.total_price = item_total_price
            else:
                subscription_obj.total_price = 0

        else:
            # Quick entry mode - use simple price input
            subscription_obj.item = None
            subscription_obj.number_item = 1
            # Convert price to float before assigning
            try:
                subscription_obj.total_price = (
                    float(price) if price is not None else 0.0
                )
            except (ValueError, TypeError):
                subscription_obj.total_price = 0.0

        # Add shipping cost to total if applicable
        if subscription_obj.shipping_cost:
            # Ensure total_price is a float before adding shipping cost
            if isinstance(subscription_obj.total_price, str):
                total_price_value = float(subscription_obj.total_price.replace(",", ""))
            else:
                total_price_value = (
                    float(subscription_obj.total_price)
                    if subscription_obj.total_price is not None
                    else 0.0
                )

            subscription_obj.total_price = total_price_value + float(
                subscription_obj.shipping_cost.value
            )

        # Handle tax calculations if applicable
        if hasattr(subscription_obj, "tax_rate") and subscription_obj.tax_rate:
            try:
                if isinstance(subscription_obj.total_price, (list, tuple, str)):
                    if isinstance(subscription_obj.total_price, str):
                        total_price_value = float(
                            subscription_obj.total_price.replace(",", "")
                        )
                    else:
                        total_price_value = (
                            float(subscription_obj.total_price[0])
                            if subscription_obj.total_price
                            else 0.0
                        )
                else:
                    total_price_value = (
                        float(subscription_obj.total_price)
                        if subscription_obj.total_price is not None
                        else 0.0
                    )

                subscription_obj.total_price_without_tax = total_price_value
                subscription_obj.total_price = total_price_value * (
                    1 + float(subscription_obj.tax_rate) / 100
                )
            except (TypeError, ValueError, IndexError) as e:
                logger.info(f"Error calculating total price with tax: {e}")
                subscription_obj.total_price_without_tax = 0.0
                subscription_obj.total_price = 0.0
        else:
            try:
                if isinstance(subscription_obj.total_price, (list, tuple, str)):
                    if isinstance(subscription_obj.total_price, str):
                        total_price_value = float(
                            subscription_obj.total_price.replace(",", "")
                        )
                    else:
                        total_price_value = (
                            float(subscription_obj.total_price[0])
                            if subscription_obj.total_price
                            else 0.0
                        )
                    subscription_obj.total_price = total_price_value

                subscription_obj.total_price_without_tax = subscription_obj.total_price
            except (TypeError, ValueError, IndexError) as e:
                logger.info(f"Error setting total price without tax: {e}")
                subscription_obj.total_price_without_tax = 0.0
                subscription_obj.total_price = 0.0

        # Handle shipping cost (after order extraction has potentially set the values)
        if shipping_checkbox and subscription_shipping:
            try:
                shipping_cost = ShopTurboShippingCost.objects.filter(
                    workspace=workspace, id=subscription_shipping
                ).first()
                if shipping_cost:
                    subscription_obj.shipping_cost = shipping_cost
            except Exception as e:
                logger.info(f"Error setting shipping cost: {e}")

        # Handle discount (after order extraction has potentially set the values)
        discount = None
        if discount_checkbox:
            if discount_id:
                if is_valid_uuid(discount_id):
                    discount = ShopTurboItemsDiscount.objects.filter(
                        id=discount_id
                    ).first()
                else:
                    if is_valid_number(discount_id):
                        discount, _ = ShopTurboItemsDiscount.objects.get_or_create(
                            value=discount_id,
                            discount_type="free_writing_discounts",
                            number_format=discount_number_format,
                        )

        # Handle date fields
        if isinstance(start_date, str):
            start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
        subscription_obj.start_date = start_date

        if end_date:
            end_date = datetime.strptime(end_date, "%Y-%m-%d").date()
            subscription_obj.end_date = end_date

        # Handle tax rate (after order extraction has potentially set the value)
        if tax_rate:
            subscription_obj.tax_rate = tax_rate

        # Update currency if it was inherited from order
        if currency and subscription_obj.currency != currency:
            subscription_obj.currency = currency

        # Set discount on subscription
        subscription_obj.discounts = None
        if discount_checkbox and discount:
            subscription_obj.discounts = discount

        # Add contact/company relationships
        if contact_company:
            contact = Contact.objects.filter(
                workspace=workspace, id=contact_company
            ).first()
            association_label = AssociationLabel.objects.filter(
                workspace=workspace,
                object_source=TYPE_OBJECT_SUBSCRIPTION,
                label__iexact="customer",
            ).first()

            if contact:
                subscription_obj.contact = contact
                subscription_obj.save()
                AssociationLabelObject.create_association(
                    subscription_obj,
                    subscription_obj.contact,
                    workspace,
                    association_label,
                )

            company = Company.objects.filter(
                workspace=workspace, id=contact_company
            ).first()
            if company:
                subscription_obj.company = company
                subscription_obj.save()
                AssociationLabelObject.create_association(
                    subscription_obj,
                    subscription_obj.company,
                    workspace,
                    association_label,
                )

        if order:
            logger.info("Adding Association")
            add_association(
                workspace,
                subscription_obj,
                TYPE_OBJECT_SUBSCRIPTION,
                order,
                TYPE_OBJECT_ORDER,
            )
            subscription_obj.orders.add(order)

            if pass_platform_channel_ids:
                for channel_id, sub_property_list in pass_platform_channel_ids.items():
                    if len(sub_property_list) < 1:
                        continue
                    order_platform = ShopTurboOrdersPlatforms.objects.filter(
                        order=order, channel_id=channel_id
                    ).first()
                    if not order_platform:
                        continue
                    logger.info("loghere 2:", order_platform)
                    for sub_property in sub_property_list:
                        field_name = (
                            ShopTurboSubscriptionsNameCustomField.objects.filter(
                                workspace=workspace,
                                name=sub_property,
                            ).first()
                        )
                        logger.info("loghere 3:", field_name)

                        if not field_name:
                            continue
                        field_value, _ = (
                            ShopTurboSubscriptionsValueCustomField.objects.get_or_create(
                                subscriptions=subscription_obj,
                                field_name=field_name,
                            )
                        )
                        field_value.value = order_platform.platform_order_id
                        field_value.save()

        subscription_obj.save()

        # Create output data for next node
        output_data = {"subscriptions": []}
        output_data["subscriptions"].append(
            {"subscription_id": str(subscription_obj.id)}
        )

        at.status = "success"
        at.output_data = output_data
        at.completed_at = timezone.now()
        at.save()

        wat.status = "success"
        wat.save()

        next_node = None
        if node:
            next_node = node.next_node
        if next_node:
            next_at = ActionTracker.objects.get(
                node=next_node, workflow_action_tracker=wat
            )
            transfer_output_to_target_input(at, next_at)
            trigger_next_action(
                current_action_tracker=at,
                workflow_action_tracker=wat,
                current_node=node,
                user_id=str(request.user.id),
                lang=lang,
            )

        module_slug = request.POST.get("module")
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
        if not module_slug:
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
                )
                .order_by("order", "created_at")
                .first()
            )
            if module:
                module_slug = module.slug
            else:
                # Fallback to a default module slug if no workflow module exists
                module_slug = "default"

        # Build redirect URL - handle cases where workflow_history might be None
        redirect_url = reverse(
            "load_object_page",
            host="app",
            kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
        )

        # Add workflow parameter only if workflow_history exists
        if node and hasattr(node, "workflow_history") and node.workflow_history:
            redirect_url += (
                f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
            )

        return redirect(redirect_url)
