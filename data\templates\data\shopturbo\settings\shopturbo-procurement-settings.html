{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% block content %}

<div class="mt-5" style="width: 100%;">
    <div class="mb-10">
        <label class="d-flex align-items-center fs-3 fw-bolder me-3">
            <span class="">

                {{setting_type|page_group_to_object:LANGUAGE_CODE}}

                {% if LANGUAGE_CODE == 'ja' %}
                オブジェクト
                {% else %}
                Object
                {% endif %}
            </span>
        </label>

    </div>



    <div class="mt-5 container-fluid px-0 scroll-y scoll-x">
        {% if setting_type == constant.TYPE_OBJECT_PURCHASE_ORDER %}
            <form method="POST" action="{% host_url 'procurement_settings' host 'app' %}" class="mb-15" id="purchase_order_setting" enctype="multipart/form-data">
                {% csrf_token %}
                <input type="hidden" class="" name="setting_type" value="{{setting_type}}"/>

                <div class="mb-10" 
                    hx-get="{% host_url 'properties' host 'app' %}"
                    hx-vals='{"page_group_type": "{{constant.TYPE_OBJECT_PURCHASE_ORDER}}"}'
                    hx-trigger="load"
                    hx-target="this"
                    hx-swap="innerHTML"
                    >
                </div>

                <div
                    hx-get="{% host_url 'association_labels_settings' host 'app' %}"
                    hx-vals='{"page_group_type": "{{constant.TYPE_OBJECT_PURCHASE_ORDER}}"}'
                    hx-trigger="load"
                ></div>

                <div class="mb-10 d-none">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span class="">
                            {% if LANGUAGE_CODE == 'ja' %}
                            検索設定
                            {% else %}
                            Search Settings
                            {% endif %}
                        </span>
                    </label>

                    <input
                    {% if LANGUAGE_CODE == 'ja' %}
                    placeholder="検索設定"
                    {% else %}
                    placeholder="Search Settings"
                    {% endif %}
                    id="search_setting_purchase_order"
                    name="search_setting_purchase_order" class="form-control" 
                    
                    value="">
                    
                </div>
                
                <div class="mb-10">
                    <div class="mb-5 form-check form-switch form-check-custom form-check-solid">
                        <input id="supplier_lock" autocomplete="off" class="form-check-input" type="checkbox" name="purchase_order_supplier_lock" {% if app_setting.purchase_order_supplier_lock %} checked="checked" {% endif %}
                            onclick="document.getElementById('item-supplier-prop').classList.toggle('d-none')">
                        <label class="form-check-label fs-6 fw-bold ms-3" for="supplier_lock">
                            {% if LANGUAGE_CODE == 'ja' %}
                            仕入先ごとに商品項目をロック
                            {% else %}
                            Lock Line Items based on Supplier
                            {% endif %}
                        </label>

                    </div>
                    <div class="mb-5 {% if not app_setting.purchase_order_supplier_lock %}d-none{% endif %}" id="item-supplier-prop">
                        <label class="form-check-label fs-6 fw-bold" for="supplier_lock">
                            {% if LANGUAGE_CODE == 'ja' %}
                            仕入先プロパティ
                            {% else %}
                            Supplier Property
                            {% endif %}
                        </label>
                        <div>
                            <select class="form-select h-40px select2-this" style="width: 40%" 
                                name="item-supplier"
                                data-placeholder="{% if LANGUAGE_CODE == 'ja' %}仕入先プロパティ{% else %}Supplier Property{% endif %}">
                                {% for prop in supplier_properties %}
                                    <option value="{{prop.id}}" 
                                    {% if app_setting.purchase_order_supplier_properties == prop.id|stringify %}selected{% endif %}
                                        >{{prop.name}} 
                                    {% if prop.type == 'contact' %}
                                        {% if LANGUAGE_CODE == 'ja' %}
                                        (連絡先オブジェクト)
                                        {% else %}
                                        (Contact Object)
                                        {% endif %}
                                    {% else %}
                                        {% if LANGUAGE_CODE == 'ja' %}
                                        (企業オブジェクト)
                                        {% else %}
                                        (Company Object)
                                        {% endif %}
                                    {% endif %}
                                    </option>
                                {% empty %}
                                <option>
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>

                <script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify"></script>
                <script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.polyfills.min.js"></script>
                <script>
                    var searchSettingElm = document.querySelector("#search_setting_purchase_order");
                    var tagify = new Tagify(searchSettingElm, {
                        whitelist: [
                        { "value": "ID", "id": "id_predefined"},
                        {% for column in column_values %}
                            {% with args=column|stringify|add:'|'|add:"purchaseorder" %} 
                                {% with column_display=args|get_column_display:request %}
                                    { "value": "{{column_display.name}}", "id": "{{column_display.id}}" },
                                {% endwith %}
                            {% endwith %}
                        {% endfor %}
                        ],
                        dropdown: {
                            maxItems: 20,           // <- mixumum allowed rendered suggestions
                            classname: "tagify__inline__suggestions", // <- custom classname for this dropdown, so it could be targeted
                            enabled: 0,             // <- show suggestions on focus
                            closeOnSelect: false    // <- do not hide the suggestions dropdown once an item has been selected
                        },
                        enforceWhitelist: true,
                        searchKeys: ['value'],  // very important to set by which keys to search for suggesttions when typing
                        originalInputValueFormat: valuesArr => valuesArr.map(item => item.id).join(','), // Include item.id in the submitted values
                        });
                    
                    tagify.addTags([{ value: "ID", id: "id_predefined", readonly:"true" }]);
                    {% if app_setting.search_setting_purchase_order %}
                        {% with app_setting.search_setting_purchase_order|split:"," as search_setting_value %}
                            {% for value in search_setting_value %}
                                {% if value in column_values %}
                                    {% with args=value|stringify|add:'|'|add:"purchaseorder" %} 
                                        {% with column_display=args|get_column_display:request %}
                                                tagify.addTags([{ value: "{{column_display.name}}", id: "{{column_display.id}}" }]);
                                        {% endwith %}
                                    {% endwith %}
                                {% endif %}
                            {% endfor %}
                        {% endwith %}
                    {% endif %}
                </script>
                
            {% include 'data/partials/pdf-template-list.html' with default_pdf_color=app_setting.purchase_order_pdf_color default_pdf_font_type=app_setting.purchase_order_pdf_font_type default_pdf_template=app_setting.purchase_order_pdf_template preselected_col=app_setting.purchase_order_pdf_line_item_table preselected_header_block=app_setting.purchase_order_pdf_header_block preselected_payment_block=app_setting.purchase_order_pdf_payment_block preselected_send_from_block=app_setting.purchase_order_pdf_send_from_block preselected_send_to_block=app_setting.purchase_order_pdf_send_to_block preselected_ship_to_block=app_setting.purchase_order_pdf_ship_to_block preselected_notes_block=app_setting.purchase_order_pdf_notes_block list_template=list_template pdf_title=app_setting.purchase_order_pdf_title preselected_information_block=app_setting.purchase_order_information_block default_send_from_adds=app_setting.purchase_order_send_from_adds_block default_send_to_adds=app_setting.purchase_order_send_to_adds_block default_sign_1=app_setting.purchase_order_sign_1_block default_sign_2=app_setting.purchase_order_sign_2_block preselected_col_display=app_setting.purchase_order_pdf_line_item_table_display preselected_header_block_display=app_setting.purchase_order_pdf_header_block_display preselected_payment_block_display=app_setting.purchase_order_pdf_payment_block_display preselected_send_from_block_display=app_setting.purchase_order_pdf_send_from_block_display preselected_send_to_block_display=app_setting.purchase_order_pdf_send_to_block_display preselected_notes_block_display=app_setting.purchase_order_pdf_notes_block_display default_line_item_bg_color=app_setting.purchase_order_pdf_line_item_table_bg_color default_line_item_font_color=app_setting.purchase_order_pdf_line_item_table_font_color preselected_information_block_display=app_setting.purchase_order_information_block_display default_line_item_font_size=app_setting.purchase_order_pdf_line_item_table_font_size preselected_user_block=app_setting.purchase_order_pdf_user_block preselected_user_block_display=app_setting.purchase_order_pdf_user_block_display %}
            </form>
            
            <div id="purchase-order-logo-uploads">
                <div class="mb-5" id="">
                    <div class="d-flex justify-content-between">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span >
                                {% if LANGUAGE_CODE == 'ja' %}
                                企業ロゴ
                                {% else %}
                                Company Logo
                                {% endif %}
                            </span>
                            
                        </label>

                        <button type="button" class="btn btn-light-primary btn-md py-1 rounded-1"

                            onclick="document.getElementById('company-logo-column-button').click();"

                        >
                            <span class="fs-7 ps-1 fw-bolder">
                                {% if LANGUAGE_CODE == 'ja' %}
                                他のオブジェクトに適用
                                {% else %}
                                Apply To Other Objects
                                {% endif %}
                            </span>
                        </button>
                    </div>

                    {% include 'data/partials/partial-stamp.html' with image_file=app_setting.purchase_order_logo_file setting_type="purchase_order_logo" target_post_url="procurement_settings"%}
                </div> 
            </div>


            <div id="purchase-order-stamp-uploads">
                <div class="mb-5" id="">
                    <div class="d-flex justify-content-between">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span >
                                {% if LANGUAGE_CODE == 'ja' %}
                                社印
                                {% else %}
                                Company Signature
                                {% endif %}
                            </span>
                            
                        </label>

                        <button type="button" class="btn btn-light-primary btn-md py-1 rounded-1"

                        onclick="document.getElementById('company-signature-column-button').click();"

                        >
                            <span class="fs-7 ps-1 fw-bolder">
                                {% if LANGUAGE_CODE == 'ja' %}
                                他のオブジェクトに適用
                                {% else %}
                                Apply To Other Objects
                                {% endif %}
                            </span>
                        </button>
                    </div>

                    {% include 'data/partials/partial-stamp.html' with image_file=app_setting.purchase_order_stamp_file setting_type="purchase_order_stamp" target_post_url="procurement_settings" %}
                </div> 
            </div>
            <div id="submit-custom-admin-button" class="mt-5">
                <button form="purchase_order_setting" type="submit" name="app_settings" value="{{setting_type}}" class="btn btn-dark">
                    {% if LANGUAGE_CODE == 'ja'%}
                    更新
                    {% else %}
                    Update
                    {% endif %}
                </button>
            </div>
        {% endif %}
        
        {% if setting_type ==  constant.TYPE_OBJECT_BILL %}
            <form method="POST" action="{% host_url 'expense_settings' host 'app' %}" class="mb-15" id="expense_setting" enctype="multipart/form-data">
                {% csrf_token %}
                <input type="hidden" class="" name="setting_type" value="{{setting_type}}"/>
                <div class="">
                    <div class="">
                        <div class="mb-10" 
                            hx-get="{% host_url 'properties' host 'app' %}"
                            hx-vals='{"page_group_type": "{{constant.TYPE_OBJECT_BILL}}"}'
                            hx-trigger="load"
                            hx-target="this"
                            hx-swap="innerHTML"
                            >
                        </div>
                    </div>

                    {% comment %} association label {% endcomment %}
                    <div
                        hx-get="{% host_url 'association_labels_settings' host 'app' %}"
                        hx-vals='{"page_group_type":"{{constant.TYPE_OBJECT_BILL}}" }'
                        hx-trigger="load"
                    ></div>

                    {% include 'data/account/workspace/settings/workspace-object-manager.html' %}

                    <div class="mb-10 d-none">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span class="">
                                {% if LANGUAGE_CODE == 'ja' %}
                                検索設定
                                {% else %}
                                Search Settings
                                {% endif %}
                            </span>
                        </label>
    
                        <input
                        {% if LANGUAGE_CODE == 'ja' %}
                        placeholder="検索設定"
                        {% else %}
                        placeholder="Search Settings"
                        {% endif %}
                        id="search_setting_bill"
                        name="search_setting_bill" class="form-control" 
                        
                        value=""></input>
                    </div>
    
                    <script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify"></script>
                    <script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.polyfills.min.js"></script>
                    <script>
                        var searchSettingElm = document.querySelector("#search_setting_bill");
                        var tagify = new Tagify(searchSettingElm, {
                            whitelist: [
                            { "value": "ID", "id": "id_predefined"},
                            {% for column in column_values %}
                                {% if 'contact_custom_field_relations' in column %}
                                    {% with raw_column=column|split:"|"%}
                                        {% with custom_column=raw_column.1|search_custom_field_object_contacts:request %}
                                            { "value": "{{'contact'|display_column_billing:request}} {{custom_column.name}}", "id": "{{column}}" },
                                        {% endwith %}
                                    {% endwith %} 
                                {% elif 'company_custom_field_relations' in column %}
                                    {% with raw_column=column|split:"|"%}
                                        {% with custom_column=raw_column.1|search_custom_field_object_company:request %}
                                            { "value": "{{'company'|display_column_billing:request}} {{custom_column.name}}", "id": "{{column}}" },
                                        {% endwith %}
                                    {% endwith %} 
                                {% elif 'shopturbo_item_custom_field_relations' in column %}
                                    {% with raw_column=column|split:"|"%}
                                        {% with custom_column=raw_column.1|search_custom_field_object_items:request %}
                                            { "value": "{{'items'|display_column_billing:request}} {{custom_column.name}}", "id": "{{column}}" },
                                        {% endwith %}
                                    {% endwith %} 
                                {% else %}  
                                    {% with column_display=column|display_column_billing:request %}
                                        { "value": "{{column_display|title}}", "id": "{{column}}" },
                                    {% endwith %}
                                {% endif %}
                            {% endfor %}
                            ],
                            dropdown: {
                                maxItems: 20,           // <- mixumum allowed rendered suggestions
                                classname: "tagify__inline__suggestions", // <- custom classname for this dropdown, so it could be targeted
                                enabled: 0,             // <- show suggestions on focus
                                closeOnSelect: false    // <- do not hide the suggestions dropdown once an item has been selected
                            },
                            enforceWhitelist: true,
                            searchKeys: ['value'],  // very important to set by which keys to search for suggesttions when typing
                            originalInputValueFormat: valuesArr => valuesArr.map(item => item.id).join(','), // Include item.id in the submitted values
                            });
                        
                        tagify.addTags([{ value: "ID", id: "id_predefined", readonly:"true" }]);
                        {% if app_setting.search_setting_bill %}
                            {% with app_setting.search_setting_bill|split:"," as search_setting_value %}
                                {% for value in search_setting_value %}
                                    {% if value in column_values %}
                                        {% if 'contact_custom_field_relations' in value %}
                                            {% with raw_column=value|split:"|"%}
                                                {% with custom_column=raw_column.1|search_custom_field_object_contacts:request %}
                                                    tagify.addTags([{ value: "{{'contact'|display_column_billing:request}} {{custom_column.name}}", id: "{{value}}" }]);
                                                {% endwith %}
                                            {% endwith %} 
                                        {% elif 'company_custom_field_relations' in value %}
                                            {% with raw_column=value|split:"|"%}
                                                {% with custom_column=raw_column.1|search_custom_field_object_company:request %}
                                                    tagify.addTags([{ value: "{{'company'|display_column_billing:request}} {{custom_column.name}}", id: "{{value}}" }]);
                                                {% endwith %}
                                            {% endwith %} 
                                        {% elif 'shopturbo_item_custom_field_relations' in value %}
                                            {% with raw_column=value|split:"|"%}
                                                {% with custom_column=raw_column.1|search_custom_field_object_items:request %}
                                                    tagify.addTags([{ value: "{{'items'|display_column_billing:request}} {{custom_column.name}}", id: "{{value}}" }]);
                                                {% endwith %}
                                            {% endwith %} 
                                        {% else %}  
                                            {% if value != 'unset' %}
                                                {% with column_display=value|display_column_billing:request %}
                                                    tagify.addTags([{ value: "{{column_display|title}}", id: "{{value}}" }]);
                                                {% endwith %}
                                            {% endif %}
                                        {% endif %}

                                    {% endif %}
                                {% endfor %}
                            {% endwith %}
                        {% endif %}
                        
                    </script>

                    <div class="mt-5">
                        <div id="submit-custom-admin-button">
                            <button type="submit" name="app_settings" value="bills" class="btn btn-dark">
                                {% if LANGUAGE_CODE == 'ja'%}
                                更新
                                {% else %}
                                Update
                                {% endif %}
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        {% endif %}

        {% if setting_type == constant.TYPE_OBJECT_EXPENSE %} 
            <form method="POST" action="{% host_url 'expense_settings' host 'app' %}" class="mb-15" id="expense_setting" enctype="multipart/form-data">
                {% csrf_token %}
                <input type="hidden" class="" name="setting_type" value="{{setting_type}}"/>
                
                <div class="">
                    <div class="mb-10" 
                        hx-get="{% host_url 'properties' host 'app' %}"
                        hx-vals='{"page_group_type": "{{constant.TYPE_OBJECT_EXPENSE}}" }'
                        hx-trigger="load"
                        hx-target="this"
                        hx-swap="innerHTML"
                        >
                    </div>
                    <div class="input-group mb-5 align-items-center d-flex">
                        <label class="form-check-label cursor-pointer me-3 fw-bolder" for="base_currency">
                            {% if LANGUAGE_CODE == 'ja' %}
                            ベース通貨
                            {% else %}
                            Base Currency
                            {% endif %}
                        </label>                            
                        <div class="">
                            <select name="currency" data-control="select2" data-hide-search="true" id="currency"
                                class="h-40px form-select form-select-solid currency-1">
                                {% if workspace.currencies %}
                                <optgroup
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    label="デフォルト通貨"
                                    {% else %}
                                    label="Default Currency"
                                    {% endif %}
                                >
                                    {% for currency in workspace.currencies|string_list_to_list %}
                                        {% if forloop.counter == 1 %}
                                            <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>                       
                                        {% endif %}
                                    {% endfor %}

                                </optgroup>
                                {% endif %}
                            </select>
                        </div>
                </div>

                {% include 'data/account/workspace/settings/workspace-object-manager.html' %}


                <div class="mb-10 d-none">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span class="">
                            {% if LANGUAGE_CODE == 'ja' %}
                            検索設定
                            {% else %}
                            Search Settings
                            {% endif %}
                        </span>
                    </label>

                    <input
                    {% if LANGUAGE_CODE == 'ja' %}
                    placeholder="検索設定"
                    {% else %}
                    placeholder="Search Settings"
                    {% endif %}
                    id="search_setting_expense"
                    name="search_setting_expense" class="form-control" 
                    
                    value=""></input>
                </div>

                <script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify"></script>
                <script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.polyfills.min.js"></script>
                <script>
                    var searchSettingElm = document.querySelector("#search_setting_expense");
                    var tagify = new Tagify(searchSettingElm, {
                        whitelist: [
                        { "value": "ID", "id": "id_predefined"},
                        {% for column in column_values %}
                        
                            {% if 'contact_custom_field_relations' in column %}
                                {% with raw_column=column|split:"|"%}
                                    {% with custom_column=raw_column.1|search_custom_field_object_contacts:request %}
                                        { "value": "{{'contact'|display_column_expense:request}} - {{custom_column.name}}", "id": "{{column}}" },
                                    {% endwith %}
                                {% endwith %} 
                            {% elif 'company_custom_field_relations' in column %}
                                {% with raw_column=column|split:"|"%}
                                    {% with custom_column=raw_column.1|search_custom_field_object_company:request %}
                                        { "value": "{{'company'|display_column_expense:request}} - {{custom_column.name}}", "id": "{{column}}" },
                                    {% endwith %}
                                {% endwith %} 
                            {% elif 'shopturbo_item_custom_field_relations' in column %}
                                {% with raw_column=column|split:"|"%}
                                    {% with custom_column=raw_column.1|search_custom_field_object_items:request %}
                                        { "value": "{{'items'|display_column_expense:request}} - {{custom_column.name}}", "id": "{{column}}" },
                                    {% endwith %}
                                {% endwith %} 
                            {% else %}  
                                {% with column_display=column|display_column_expense:request %}
                                    { "value": "{{column_display|title}}", "id": "{{column}}" },
                                {% endwith %}
                            {% endif %}
                        {% endfor %}
                        ],
                        dropdown: {
                            maxItems: 20,           // <- mixumum allowed rendered suggestions
                            classname: "tagify__inline__suggestions", // <- custom classname for this dropdown, so it could be targeted
                            enabled: 0,             // <- show suggestions on focus
                            closeOnSelect: false    // <- do not hide the suggestions dropdown once an item has been selected
                        },
                        enforceWhitelist: true,
                        searchKeys: ['value'],  // very important to set by which keys to search for suggesttions when typing
                        originalInputValueFormat: valuesArr => valuesArr.map(item => item.id).join(','), // Include item.id in the submitted values
                        });
                    
                    tagify.addTags([{ value: "ID", id: "id_predefined", readonly:"true" }]);
                    tagify.addTags([{ value: "Partner", id: "partner" }]);
                    {% if app_setting.search_setting_expense %}
                        {% with app_setting.search_setting_expense|split:"," as search_setting_value %}
                            {% for value in search_setting_value %}
                                {% if value in column_values %}
                                    {% if 'contact_custom_field_relations' in value %}
                                        {% with raw_column=value|split:"|"%}
                                            {% with custom_column=raw_column.1|search_custom_field_object_contacts:request %}
                                                tagify.addTags([{ value: "{{'contact'|display_column_expense:request}} {{custom_column.name}}", id: "{{value}}" }]);
                                            {% endwith %}
                                        {% endwith %} 
                                    {% elif 'company_custom_field_relations' in value %}
                                        {% with raw_column=value|split:"|"%}
                                            {% with custom_column=raw_column.1|search_custom_field_object_company:request %}
                                                tagify.addTags([{ value: "{{'company'|display_column_expense:request}} {{custom_column.name}}", id: "{{value}}" }]);
                                            {% endwith %}
                                        {% endwith %} 
                                    {% elif 'shopturbo_item_custom_field_relations' in value %}
                                        {% with raw_column=value|split:"|"%}
                                            {% with custom_column=raw_column.1|search_custom_field_object_items:request %}
                                                tagify.addTags([{ value: "{{'items'|display_column_expense:request}} {{custom_column.name}}", id: "{{value}}" }]);
                                            {% endwith %}
                                        {% endwith %} 
                                    {% else %}  
                                        {% if value != 'unset' %}
                                            {% with column_display=value|display_column_expense:request %}
                                                tagify.addTags([{ value: "{{column_display|title}}", id: "{{value}}" }]);
                                            {% endwith %}
                                        {% endif %}
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                        {% endwith %}
                    {% endif %}
                    
                </script>

                <div class="mt-5">
                    <div id="submit-custom-admin-button">
                        <button type="submit" name="app_settings" value="{{setting_type}}" class="btn btn-dark">
                            {% if LANGUAGE_CODE == 'ja'%}
                            更新
                            {% else %}
                            Update
                            {% endif %}
                        </button>
                    </div>
                </div>
            </form>
        {% endif %}
    </div>
</div>

{% endblock %}

{% block js %}

<script>
    $(document).ready(function () {
        $('.select2-this').select2();
        $('.currency-1').select2();
        $('.currency-1').prop("disabled", true);
        checkbox = document.getElementById('base_currency')
        checkbox.addEventListener('change', function() {
            if (this.checked) {
                document.querySelector('.currency_section').classList.remove('d-none')
            } 
            else {
                
                document.querySelector('.currency_section').classList.add('d-none')
            }
        })
    })
</script>

{% endblock %}
