import random
import re
import ast
import uuid
import urllib
from django_hosts.resolvers import reverse
from django.db.models import Q

from django.contrib import messages
from django.core.paginator import EmptyPage, PageNotAnInteger, Paginator
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.views.decorators.http import require_GET

from data.constants.properties_constant import *
from data.models import (AppSetting, Log, UserManagement, UserManagementNameCustomField, View, ViewFilter, Invitation, User, Notification, CustomObject, Verification, VerificationCode2, SamlIntegration)
from utils.decorator import login_or_hubspot_required
from utils.mail_utils import send_invitation
from utils.meter import get_workspace_available_storage, has_quota
from utils.project import get_ordered_views
from utils.properties.properties import get_page_object
from utils.utility import get_workspace, save_custom_property
from utils.workspace import get_permission
import string
import secrets
from data.constants.constant import *

# Constant
USER_MANAGEMENT_TARGET = 'user_management'
MENU_KEY = 'workspace'


@login_or_hubspot_required
def user_management(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    Log.objects.create(workspace=workspace, user=request.user,
                       page_name='workspace', sub_page_name='workspace_users')

    if request.method == 'GET':
        view_id = request.GET.get('view_id', None)

        page_object = get_page_object(TYPE_OBJECT_USER_MANAGEMENT, lang)
        page_title = page_object['page_title']
        manage_obj_link = page_object['manage_obj_link']
        row_detail_url = page_object['row_detail_url']
        edit = page_object['edit']
        id_field = page_object['id_field']
        default_columns = page_object['default_columns']
        if id_field and id_field not in default_columns:
            default_columns.insert(0, id_field)

        # Get the Role is Admin or Not
        role = UserManagement.objects.filter(
            user=request.user, workspace=workspace).first()
        
        permission = get_permission(
            object_type=TYPE_OBJECT_USER_MANAGEMENT, user=request.user)
        if not permission:
            permission = 'hide'

        if not role or not role.type or permission == 'hide':
            # Will Return Context without permission
            context = {
                'permission': 'hide',
                'page_title': page_title,
            }
            return render(request, 'data/user-management/user-management.html', context)

        app_setting, _ = AppSetting.objects.get_or_create(
            workspace=workspace, app_target=USER_MANAGEMENT_TARGET)
        if not app_setting.search_setting_user_management:
            app_setting.search_setting_user_management = "id_user"
            app_setting.save()

        

        views = get_ordered_views(workspace, TYPE_OBJECT_USER_MANAGEMENT, user=request.user)
        
        if view_id:
            view = View.objects.filter(id=view_id).first()
            if not view or (view.is_private and (not request.user or view.user != request.user)):
                view, _ = View.objects.get_or_create(
                    workspace=workspace,
                    title='main',
                    target=TYPE_OBJECT_USER_MANAGEMENT,
                )
                view_id = view.id
        else:
            # Get Main View
            view, _ = View.objects.get_or_create(
                workspace=workspace,
                title='main',
                target=TYPE_OBJECT_USER_MANAGEMENT,
            )
            view_id = view.id

        view_filter, is_new_view_filter = ViewFilter.objects.get_or_create(
            view=view)
        if is_new_view_filter:
            view_filter.view_type = 'list'
            view_filter.column = default_columns.copy()
            view_filter.save()

        filter_conditions = Q(workspace=workspace)
        query_search = request.GET.get('q', '')
        if query_search:
            match_special_char = re.search(r"(\d+)", query_search)
            if match_special_char:
                number = match_special_char.group(1)  # Get the number part
                if number:
                    filter_conditions &= Q(id_user=number)
            else:
                search_filter = Q()
                search_filter |= Q(user__username__icontains=query_search)
                search_filter |= Q(user__first_name__icontains=query_search)
                search_filter |= Q(user__last_name__icontains=query_search)
                filter_conditions &= search_filter

        users = UserManagement.objects.filter(
            filter_conditions).order_by('user__date_joined')

        # Get pagination settings
        pagination_number = getattr(view_filter, 'pagination', 20) or 20

        # Create paginator
        paginator = Paginator(users, pagination_number)

        # Get requested page
        page = int(request.GET.get('page', 1))

        # Get page content
        try:
            page_content = paginator.page(page)
        except (EmptyPage, PageNotAnInteger):
            page_content = paginator.page(1)
            page = 1

        # Calculate pagination display values
        paginator_item_begin = ((page - 1) * pagination_number) + 1
        paginator_item_end = min(page * pagination_number, paginator.count)

        # Update users with paginated results
        users = page_content.object_list

        context = {
            # Search Value
            'search_q': query_search,

            # View Related
            'view': view,
            'views': views,
            'view_id': view.id,
            'current_view': view,
            'view_filter': view_filter,

            # Constant
            'permission': 'show',
            'menu_key': MENU_KEY,
            'id_field': id_field,
            'page_title': page_title,
            'object_type': TYPE_OBJECT_USER_MANAGEMENT,
            'page_group_type': TYPE_OBJECT_USER_MANAGEMENT,

            # Objects
            'objects': users,

            # Link/URL
            'edit': edit,
            'row_detail_url': row_detail_url,
            'manage_obj_link': manage_obj_link,

            # Pagination
            'page': page,
            'paginator_item_begin': paginator_item_begin,
            'paginator_item_end': paginator_item_end,
            'page_content': page_content,
            'paginator': paginator,
            'pagination_url': reverse('workspace', host='app'),

            # User Role Type
            'role': role.type,

            # Workspace
            'workspace': workspace,

            # permission
            'permission': permission

        }
        return render(request, 'data/user-management/user-management.html', context)


@login_or_hubspot_required
def user_management_row_detail(request, id):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    page_obj = get_page_object(TYPE_OBJECT_USER_MANAGEMENT, lang)
    base_model = page_obj['base_model']
    custom_model = page_obj['custom_model']
    manage_obj_link = page_obj['manage_obj_link']
    id_field = page_obj['id_field']

    object_ = get_object_or_404(base_model, id=id)

    view_id = request.GET.get('view_id', None)
    if view_id:
        view = View.objects.filter(id=view_id).first()
    else:
        # Create Main View
        view, _ = View.objects.get_or_create(
            workspace=workspace, title='main', target=TYPE_OBJECT_USER_MANAGEMENT)
        view_id = view.id

    view_filter, _ = ViewFilter.objects.get_or_create(view=view)
    columns = ast.literal_eval(view_filter.column)
    if 'checkbox' not in columns:
        columns.insert(0, 'checkbox')

    context = {
        'manage_obj_link': manage_obj_link,
        'object': object_,
        'obj_id': getattr(object_, id_field, None),
        'columns': columns,
        'view_id': view_id,
        'selected_id': request.GET.get('selected_id'),
        'page': request.GET.get('page', 1),
        'NameCustomField': custom_model.objects.filter(workspace=workspace).order_by("order"),
        'object_type': TYPE_OBJECT_USER_MANAGEMENT,
        'from_page_object': request.GET.get('from_page_object', TYPE_OBJECT_USER_MANAGEMENT)
    }

    return render(request, 'data/user-management/user-management-row-detail.html', context)


@login_or_hubspot_required
def user_management_drawer(request, id=None):
    """
    Renders the appropriate user management drawer interface based on the requested drawer type.
    
    Depending on the `drawer_type` GET parameter, this view displays:
    - A form for creating a new user management object with custom fields (`create-object`).
    - A management interface for an existing user, including permissions and role management (`manage-object`).
    - A list of pending invitations in the workspace (`invitation`).
    
    Returns an HTTP 400 response if required parameters are missing or invalid, or HTTP 200 for unhandled drawer types.
    """
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    page_obj = get_page_object(TYPE_OBJECT_USER_MANAGEMENT, lang)
    default_columns = page_obj['default_columns']

    drawer_type = request.GET.get('drawer_type', None)
    if not drawer_type:
        return redirect(reverse('main', host='app'))

    view_id = request.GET.get('view_id', None)

    if drawer_type == 'create-object':
        user_management_properties = UserManagementNameCustomField.objects.filter(
            workspace=workspace)
        
        role = UserManagement.objects.filter(
            workspace=workspace, user=request.user).first()
        # Get information for permissions management
        role_myself = role

        permission = 'read|edit|archive'

        # Set permission dictionary
        permissions_dict = {}
        for object_group_type in list(OBJECT_GROUP_TYPE.keys()) + list(SYSTEM_SETTING_TYPE.keys()):
            permissions_dict[object_group_type] = DEFAULT_PERMISSION
            if object_group_type in SYSTEM_SETTING_TYPE.keys():
                permissions_dict[object_group_type] = 'hide'
                            
        # Include custom objects in permissions
        custom_objects = CustomObject.objects.filter(workspace=workspace)
        for custom_object in custom_objects:
            if custom_object.slug not in permissions_dict.keys():
                permissions_dict[custom_object.slug] = DEFAULT_PERMISSION

        if has_quota(workspace, USER_USAGE_CATEGORY):
            workspace_available_seats = get_workspace_available_storage(
                workspace, USER_USAGE_CATEGORY)
        else:
            workspace_available_seats = 0

        context = {
            'object_type': TYPE_OBJECT_USER_MANAGEMENT,
            'user_management_properties': user_management_properties,
            'default_columns': default_columns,
            'view_id': view_id,
            'workspace': workspace,
            'role_myself': role_myself,
            'group_types': OBJECT_GROUP_TYPE,
            'system_setting_type': SYSTEM_SETTING_TYPE,
            'permission': permission,
            'permission_dict': permissions_dict,
            'workspace_available_seats': workspace_available_seats,
            'view_only_password': generate_random_password(),
        }

        return render(request, 'data/user-management/user-management-create.html', context)

    elif drawer_type == 'manage-object':
        from_page_object = request.GET.get('from_page_object', TYPE_OBJECT_USER_MANAGEMENT)
        if not id:
            return HttpResponse(status=400)
        user_management = UserManagement.objects.filter(
            workspace=workspace, id=id).first()
        if not user_management:
            return HttpResponse(status=400)

        page = request.GET.get('page', 1)
        user_management_properties = UserManagementNameCustomField.objects.filter(
            workspace=workspace)

        role = UserManagement.objects.filter(
            workspace=workspace, user=request.user).first()
        # Allow admin, support, and partner users to manage users
        if role.type in [UserManagement.RoleType.ADMIN, UserManagement.RoleType.SUPPORT, UserManagement.RoleType.PARTNER]:
            permission = 'show'
        else:
            permission = get_permission(
                object_type=TYPE_OBJECT_USER_MANAGEMENT, user=request.user)
        if not permission:
            permission = 'hide'

        # Get information for permissions management
        role_myself = role
        admin_myself = None
        if role_myself.type == UserManagement.RoleType.ADMIN and user_management.user == request.user:
            admin_myself = True

        self_convert_staff = False
        able_to_leave = False
        if role_myself.user == user_management.user:
            all_users = User.objects.filter(
                workspaces=workspace).exclude(id=user_management.user.id)
            for user in all_users:
                user_role = UserManagement.objects.get(
                    user=user, workspace=workspace)
                if user_role.type == UserManagement.RoleType.ADMIN:
                    able_to_leave = True
                    if user_management.type == UserManagement.RoleType.ADMIN:
                        self_convert_staff = True
                    break

        # Set permission dictionary
        permissions_dict = {}
        if user_management.type in [UserManagement.RoleType.ADMIN, UserManagement.RoleType.SUPPORT, UserManagement.RoleType.PARTNER]:
            for object_group_type in OBJECT_GROUP_TYPE:
                permissions_dict[object_group_type] = "read|edit|archive"
        else:
            if user_management.permission:
                permissions_dict = user_management.permission
                for object_group_type in list(OBJECT_GROUP_TYPE.keys()) + list(SYSTEM_SETTING_TYPE.keys()):
                    if object_group_type not in permissions_dict.keys():
                        permissions_dict[object_group_type] = DEFAULT_PERMISSION
                        if object_group_type in SYSTEM_SETTING_TYPE.keys():
                            permissions_dict[object_group_type] = 'hide'
            else:
                for object_group_type in list(OBJECT_GROUP_TYPE.keys()) + list(SYSTEM_SETTING_TYPE.keys()):
                    permissions_dict[object_group_type] = DEFAULT_PERMISSION
                    if object_group_type in SYSTEM_SETTING_TYPE.keys():
                        permissions_dict[object_group_type] = 'hide'
                            
        # Include custom objects in permissions
        custom_objects = CustomObject.objects.filter(workspace=workspace)
        for custom_object in custom_objects:
            if custom_object.slug not in permissions_dict.keys():
                if user_management.type in [UserManagement.RoleType.ADMIN, UserManagement.RoleType.SUPPORT, UserManagement.RoleType.PARTNER]:
                    permissions_dict[custom_object.slug] = "read|edit|archive"
                else:
                    permissions_dict[custom_object.slug] = DEFAULT_PERMISSION

        context = {
            'object_type': TYPE_OBJECT_USER_MANAGEMENT,
            'user_management_properties': user_management_properties,
            'default_columns': default_columns,
            'view_id': view_id,
            'workspace': workspace,
            'user_management': user_management,
            'permission': permission,
            'page': page,
            # Permission variables
            'role_myself': role_myself,
            'role_teammate': user_management,
            'admin_myself': admin_myself,
            'self_convert_staff': self_convert_staff,
            'group_types': OBJECT_GROUP_TYPE,
            'permission_dict': permissions_dict,
            'able_to_leave': able_to_leave,
            'custom_objects': custom_objects,
            'system_setting_type': SYSTEM_SETTING_TYPE,
            'from_page_object': from_page_object
        }

        return render(request, 'data/user-management/user-management-manage.html', context)

    elif drawer_type == 'invitation':
        invitations = Invitation.objects.filter(
            workspace=workspace, status='invited')

        context = {
            'invitations': invitations,
            'workspace': workspace,
        }

        return render(request, 'data/user-management/user-management-invitations.html', context)

    return HttpResponse(status=200)


@login_or_hubspot_required
def user_management_manage(request):
    """
    Processes user management actions in a workspace via POST requests.
    
    Supports inviting users, adding SAML users, creating view-only users, removing users, updating user roles and permissions, and performing bulk removals. Enforces workspace seat quotas and role-based permissions, provides localized notifications for success or error states, and redirects to the workspace page after processing.
    """
    source_url = request.POST.get('source_url', '')
    if source_url:
        source_url = urllib.parse.unquote(source_url)
    
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    Log.objects.create(workspace=workspace, user=request.user,
                       page_name='workspace', sub_page_name='workspace_users')

    # Get the role
    role = UserManagement.objects.filter(
        workspace=workspace, user=request.user).first()
    if not role:
        return redirect(reverse('workspace', host='app'))

    # Allow admin, support, and partner users to manage users
    if role.type not in [UserManagement.RoleType.ADMIN, UserManagement.RoleType.SUPPORT, UserManagement.RoleType.PARTNER]:
        if lang == 'ja':
            Notification.objects.create(
                workspace=workspace, user=request.user, message='あなたはユーザー管理の権限を持っていません', type='error')
        else:
            Notification.objects.create(workspace=workspace, user=request.user,
                                        message='You do not have permission to manage users', type='error')
        return redirect(reverse('workspace', host='app'))

    if request.method == 'POST':
        if 'invite_team' in request.POST:
            emails = request.POST.getlist('email')
            role = request.POST.get('role')

            permission = {}
            if role == UserManagement.RoleType.ADMIN:
                 # Set permission dictionary
                for object_group_type in list(OBJECT_GROUP_TYPE.keys()) + list(SYSTEM_SETTING_TYPE.keys()):
                    permission[object_group_type] = "read|edit|archive"

                # Include custom objects in permissions
                custom_objects = CustomObject.objects.filter(workspace=workspace)
                for custom_object in custom_objects:
                    permission[custom_object.slug] = "read|edit|archive"
            else:
                custom_objects = CustomObject.objects.filter(
                    workspace=workspace)
                for custom_object in custom_objects:
                    if custom_object.slug in request.POST:
                        permission[custom_object.slug] = request.POST.get(
                            custom_object.slug, DEFAULT_PERMISSION)
                    else:
                        permission[custom_object.slug] = DEFAULT_PERMISSION
                
                for object_group_type in list(OBJECT_GROUP_TYPE.keys()) + list(SYSTEM_SETTING_TYPE.keys()):
                    rules = []
                    for permission_type in PERMISSION_TYPES:
                        if permission_type == 'hide':
                            if request.POST.get(f'{object_group_type}#hide', None):
                                permission[object_group_type] = 'hide'
                                break
                        else:
                            for scope in PERMISSION_SCOPES:
                                if request.POST.get(f'{object_group_type}#{scope}#{permission_type}', None):
                                    if scope == 'all': 
                                        if permission_type == 'read':
                                            rules.append('read')
                                        elif permission_type == 'edit':
                                            rules.append('edit') 
                                        elif permission_type == 'archive':
                                            rules.append('archive')
                                    else:
                                        rules.append(f'{scope}_{permission_type}')
                            permission[object_group_type] = '|'.join(rules)
            
            if has_quota(workspace, USER_USAGE_CATEGORY):
                workspace_available_seats = get_workspace_available_storage(
                    workspace, USER_USAGE_CATEGORY)
            else:
                workspace_available_seats = 0

            # FIRST_EDIT: filter out emails of users with SUPPORT or PARTNER roles
            eligible_emails = []
            for email in emails:
                user = User.objects.filter(email=email).first()
                if user:
                    user_role = UserManagement.objects.filter(
                        workspace=workspace, user=user
                    ).first()
                    if user_role and user_role.type in (
                        UserManagement.RoleType.SUPPORT,
                        UserManagement.RoleType.PARTNER
                    ):
                        continue  # skip support/partner users
                eligible_emails.append(email)

            print('eligible_emails', eligible_emails)

            # Now enforce the seat limit on the filtered list
            for email in eligible_emails[:workspace_available_seats]:
                if email:
                    user = User.objects.filter(email=email).first()
                    if user:
                        code = ''.join(random.choice('1234567890')
                                       for i in range(6))
                        Invitation.objects.create(workspace=get_workspace(
                            request.user), email=email, role=role, code=code, status="invited", permission=permission)

                        if lang == 'ja':
                            messages.success(request, 'ユーザーにはメールで通知されました。')
                            send_invitation(
                                email, code, get_workspace(request.user), 'ja')
                        else:
                            messages.success(
                                request, 'The user has been notified by email.')
                            send_invitation(
                                email, code, get_workspace(request.user))
                        continue
                    else:
                        # Change + to "%20B"
                        email_split = email.split("@")
                        email_converted = email_split[0].replace(
                            "+", "%2B") + email_split[-1]
                        invitations = Invitation.objects.filter(
                            email=email, workspace=workspace)
                        if not invitations:
                            code = ''.join(random.choice('1234567890')
                                           for i in range(6))
                            Invitation.objects.create(workspace=get_workspace(
                                request.user), email=email, role=role, code=code, permission=permission)
                            VerificationCode2.objects.create(
                                code=code,
                                email=email,
                                status='generated',
                            )
                            if lang == 'ja':
                                Notification.objects.create(workspace=get_workspace(
                                    request.user), user=request.user, message='ユーザーが招待されました。', type='success')
                                send_invitation(
                                    email, code, get_workspace(request.user), 'ja')
                            else:
                                Notification.objects.create(workspace=get_workspace(
                                    request.user), user=request.user, message='The user was invited.', type='success')
                                send_invitation(
                                    email, code, get_workspace(request.user))
                        else:
                            if lang == 'ja':
                                Notification.objects.create(workspace=get_workspace(
                                    request.user), user=request.user, message='ユーザーはすでに招待されています。', type='success')
                            else:
                                messages.warning(
                                    request, 'The user has been already invited.')

            return redirect('workspace')

        elif 'add_saml_user' in request.POST:
            if has_quota(workspace, USER_USAGE_CATEGORY):
                workspace_available_seats = get_workspace_available_storage(
                    workspace, USER_USAGE_CATEGORY)
            else:
                workspace_available_seats = 0

            email = request.POST.get('email')
            name = request.POST.get('name')

            # Get Email Domain
            try:
                email_split = email.split("@")
                domain = email_split[-1]
            except:
                if lang == 'ja':
                    Notification.objects.create(workspace=get_workspace(
                        request.user), user=request.user, message='メールアドレスが正しく入力されていません。', type='error')
                    messages.warning(request, 'メールアドレスが正しく入力されていません。')
                else:
                    Notification.objects.create(workspace=get_workspace(
                        request.user), user=request.user, message='Email is not valid.', type='error')
                    messages.warning(request, 'Email is not valid.')

                return redirect('workspace')

            # Validate Email domain available on SAML
            saml = SamlIntegration.objects.filter(
                domain_name__icontains=domain).first()
            if not saml:
                if lang == 'ja':
                    Notification.objects.create(workspace=get_workspace(
                        request.user), user=request.user, message='SAMLが登録されていません。別のメールアドレスを使用してください。', type='error')
                    messages.warning(
                        request, 'SAMLが登録されていません。別のメールアドレスを使用してください。')
                else:
                    Notification.objects.create(workspace=get_workspace(
                        request.user), user=request.user, message='SAML not registered. Please use another email.', type='error')
                    messages.warning(
                        request, 'SAML not registered. Please use another email.')

                return redirect('workspace')

            if workspace_available_seats > 0:
                user = User.objects.filter(email=email).first()
                if not user:
                    user = User.objects.create_user(
                        username=str(uuid.uuid4()),
                        email=email,
                        first_name=name,
                        password=None,
                    )

                    verification = Verification.objects.create(user=user)
                    verification.saml_login = True
                    verification.verified = True
                    if lang == 'ja':
                        verification.language = 'ja'
                        verification.timezone = 'Asia/Tokyo'
                    else:
                        verification.language = 'en'
                        verification.timezone = 'UTC'

                    verification.workspace = workspace
                    verification.save()

                workspace.user.add(user)
                workspace.save()

                # Make everyone admin
                UserManagement.objects.create(
                    type=UserManagement.RoleType.STAFF, workspace=workspace, user=user)
                user.save()
            else:
                if lang == 'ja':
                    Notification.objects.create(workspace=get_workspace(
                        request.user), user=request.user, message='ワークスペースの空き数がありません。', type='error')
                    messages.warning(request, 'ワークスペースの空き数がありません。')
                else:
                    Notification.objects.create(workspace=get_workspace(
                        request.user), user=request.user, message='No available seats in workspace.', type='error')
                    messages.warning(
                        request, 'No available seats in workspace.')

        elif "create_view_only_user" in request.POST:
            name = request.POST.get('name')
            sanka_password = request.POST.get('view_only_password')
            if not name:
                messages.warning(request, 'Name is required.')
                return redirect('workspace')
            
            sanka_uuid = str(uuid.uuid4())
            # sanka_password = str(uuid.uuid4()).replace('-', '')

            user = User.objects.create_user(
                username=f"{sanka_uuid}",
                email=f"{sanka_uuid}@view-only.sanka.com",
                first_name=name,
                password=sanka_password,
            )

            verification = Verification.objects.create(user=user)
            verification.verified = True
            if lang == 'ja':
                verification.language = 'ja'
                verification.timezone = 'Asia/Tokyo'
            else:
                verification.language = 'en'
                verification.timezone = 'UTC'

            workspace.user.add(user)
            workspace.save()

            verification.workspace = workspace

            # Create UserManagement with view-only role
            obj = UserManagement.objects.create(
                type=UserManagement.RoleType.VIEW_ONLY,
                workspace=workspace,
                user=user
            )
            # Set the view-only password (encrypted)
            obj.set_view_only_password(raw_password=sanka_password)
            obj.save()
            
            # Save verification and user
            verification.save()
            user.save()

            save_custom_property(request, obj)

        elif "remove_user_from_workspace" in request.POST:
            user_management_id = request.POST.get('user_management_id', None)

            if not user_management_id:
                return redirect('workspace')

            try:
                user_management = UserManagement.objects.get(
                    id=user_management_id)
            except UserManagement.DoesNotExist:
                return redirect('workspace')

            user = user_management.user
            if user_management.type == UserManagement.RoleType.VIEW_ONLY:
                user.delete()
            else:
                workspace.user.remove(user)
                user_management.delete()

        elif "update_user_management" in request.POST:
            user_management_id = request.POST.get('user_management_id', None)

            try:
                obj = UserManagement.objects.get(id=user_management_id)
                
                view_only_password = request.POST.get('view_only_password', '')
                if view_only_password:
                    if obj.type == UserManagement.RoleType.VIEW_ONLY:
                        obj.user.set_password(view_only_password)
                        obj.user.save()
                        obj.set_view_only_password(raw_password=view_only_password)
                        obj.save()

                    
                # Process custom properties
                save_custom_property(request, obj)

                # Handle permission updates if present
                if 'update_permissions' in request.POST and request.POST.get('update_permissions') == 'true':
                    permission = {}
                    custom_objects = CustomObject.objects.filter(
                        workspace=workspace)
                    for custom_object in custom_objects:
                        if custom_object.slug in request.POST:
                            permission[custom_object.slug] = request.POST.get(
                                custom_object.slug, DEFAULT_PERMISSION)
                        else:
                            permission[custom_object.slug] = DEFAULT_PERMISSION
                    
                    for object_group_type in list(OBJECT_GROUP_TYPE.keys()) + list(SYSTEM_SETTING_TYPE.keys()):
                        rules = []
                        for permission_type in PERMISSION_TYPES:
                            if permission_type == 'hide':
                                if request.POST.get(f'{object_group_type}#hide', None):
                                    permission[object_group_type] = 'hide'
                                    break
                            else:
                                for scope in PERMISSION_SCOPES:
                                    if request.POST.get(f'{object_group_type}#{scope}#{permission_type}', None):
                                        if scope == 'all': 
                                            if permission_type == 'read':
                                                rules.append('read')
                                            elif permission_type == 'edit':
                                                rules.append('edit') 
                                            elif permission_type == 'archive':
                                                rules.append('archive')
                                        else:
                                            rules.append(f'{scope}_{permission_type}')
                                permission[object_group_type] = '|'.join(rules)
                    obj.permission = permission

                # Handle role change if requested
                if 'change_role' in request.POST:
                    change_role_value = request.POST.get('change_role')

                    if change_role_value == 'convert_to_staff' and obj.type == UserManagement.RoleType.ADMIN:
                        obj.type = UserManagement.RoleType.STAFF

                        # Reset permissions for staff
                        permission = {}
                        for object_group_type in OBJECT_GROUP_TYPE:
                            permission[object_group_type] = DEFAULT_PERMISSION

                        for object_group_type in SYSTEM_SETTING_TYPE.keys():
                            permission[object_group_type] = 'hide'
                                
                        custom_objects = CustomObject.objects.filter(
                            workspace=workspace)
                        for custom_object in custom_objects:
                            permission[custom_object.slug] = DEFAULT_PERMISSION

                        obj.permission = permission

                    elif change_role_value == 'convert_to_admin' and obj.type in [UserManagement.RoleType.STAFF, UserManagement.RoleType.SUPPORT, UserManagement.RoleType.PARTNER]:
                        obj.type = UserManagement.RoleType.ADMIN

                        # Set full permissions for admin
                        permission = {}
                        for object_group_type in OBJECT_GROUP_TYPE:
                            permission[object_group_type] = "read|edit|archive"

                        for object_group_type in SYSTEM_SETTING_TYPE.keys():
                            permission[object_group_type] = 'read|edit|archive'

                        custom_objects = CustomObject.objects.filter(
                            workspace=workspace)
                        for custom_object in custom_objects:
                            permission[custom_object.slug] = "read|edit|archive"

                        obj.permission = permission

                obj.save()

            except UserManagement.DoesNotExist:
                if lang == 'ja':
                    Notification.objects.create(workspace=get_workspace(
                        request.user), user=request.user, message='ユーザー管理情報が見つかりませんでした。', type='success')
                else:
                    Notification.objects.create(workspace=get_workspace(
                        request.user), user=request.user, message='User management cannot be found.', type='success')
                return redirect('workspace')

            if lang == 'ja':
                Notification.objects.create(workspace=get_workspace(
                    request.user), user=request.user, message='更新しました。', type='success')
            else:
                Notification.objects.create(workspace=get_workspace(
                    request.user), user=request.user, message='Updated successfully.', type='success')
            if source_url:
                return redirect(source_url)
            return redirect('workspace')

        elif "remove_from_workspace" in request.POST:
            items = request.POST.getlist('items', [])

            if str(role.id) in items:
                # Cannot remove yourself
                if lang == 'ja':
                    Notification.objects.create(workspace=get_workspace(
                        request.user), user=request.user, message='一括削除で自分自身を削除することはできません。', type='error')
                else:
                    Notification.objects.create(workspace=get_workspace(
                        request.user), user=request.user, message='Cannot remove yourself in bulk remove.', type='error')
                return redirect('workspace')

            # Check if items is list
            if items:
                if isinstance(items, list):
                    for item in items:
                        try:
                            user_management = UserManagement.objects.get(
                                id=item)
                        except UserManagement.DoesNotExist:
                            continue

                        user = user_management.user
                        if user_management.type == UserManagement.RoleType.VIEW_ONLY:
                            user.delete()
                        else:
                            workspace.user.remove(user)
                            user_management.delete()

        return redirect('workspace')


@login_or_hubspot_required
def teammate(request, username):
    workspace = get_workspace(request.user)
    teammate = User.objects.get(username=username)
    menu_key = 'workspace'

    # Will default undefined UserManagement to staff
    try:
        role_teammate = UserManagement.objects.get(
            user=teammate, workspace=get_workspace(request.user))
    except:
        role_teammate = UserManagement.objects.create(user=teammate, workspace=get_workspace(
            request.user), type=UserManagement.RoleType.STAFF)

    # Will default undefined UserManagement to staff
    try:
        role_myself = UserManagement.objects.get(
            user=request.user, workspace=get_workspace(request.user))
    except:
        role_myself = UserManagement.objects.create(user=request.user, workspace=get_workspace(
            request.user), type=UserManagement.RoleType.STAFF)

    if role_myself.type == UserManagement.RoleType.ADMIN and teammate == request.user:
        admin_myself = True
    else:
        admin_myself = None

    self_convert_staff = False
    able_to_leave = False
    if role_myself.user == teammate:
        all_users = User.objects.filter(
            workspace=workspace).exclude(id=teammate.id)
        for user in all_users:
            user_role = UserManagement.objects.get(
                user=user, workspace=workspace)
            if user_role.type == UserManagement.RoleType.ADMIN:
                able_to_leave = True
                if role_teammate.type == UserManagement.RoleType.ADMIN:
                    self_convert_staff = True
                break

    permission = {}
    if role_teammate.type in [UserManagement.RoleType.ADMIN, UserManagement.RoleType.SUPPORT, UserManagement.RoleType.PARTNER]:
        for object_group_type in OBJECT_GROUP_TYPE:
            permission[object_group_type] = "read|edit|archive"
    else:
        if role_teammate.permission:
            permission = role_teammate.permission
            for object_group_type in OBJECT_GROUP_TYPE:
                if object_group_type not in permission.keys():
                    permission[object_group_type] = DEFAULT_PERMISSION
        else:
            for object_group_type in OBJECT_GROUP_TYPE:
                permission[object_group_type] = DEFAULT_PERMISSION

    custom_objects = CustomObject.objects.filter(workspace=workspace)
    for custom_object in custom_objects:
        if custom_object.slug not in permission.keys():
            if role_teammate.type in [UserManagement.RoleType.ADMIN, UserManagement.RoleType.SUPPORT, UserManagement.RoleType.PARTNER]:
                permission[custom_object.slug] = "read|edit|archive"
            else:
                permission[custom_object.slug] = DEFAULT_PERMISSION

    role_teammate.permission = permission
    role_teammate.save()

    context = {
        "teammate": teammate,
        'workspace': workspace,
        'role_teammate': role_teammate,
        'role_myself': role_myself,
        'admin_myself': admin_myself,
        'menu_key': menu_key,
        'self_convert_staff': self_convert_staff,
        'group_types': OBJECT_GROUP_TYPE,
        'permission': permission,
        'able_to_leave': able_to_leave,
        'custom_objects': custom_objects
    }
    return render(request, 'data/partials/teammate.html', context)


@login_or_hubspot_required
def workspace_users_options(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    q = request.GET.get('q')
    page = request.GET.get('page', 1)
    usecase = request.GET.get('usecase', '')

    filter_conditions = Q()
    if q:
        q_id = q
        filter_conditions &= (Q(email__icontains=q_id) | Q(
            first_name__icontains=q_id) | Q(last_name__icontains=q_id))

    rows = workspace.user.all()

    res = []
    ITEMS_PER_PAGE = 30
    paginator = Paginator(rows, ITEMS_PER_PAGE)
    paginated_rows = []
    more_pagination = False
    if page:
        try:
            rows_page_content = paginator.page(page if page else 1)
            paginated_rows = rows_page_content.object_list
            more_pagination = rows_page_content.has_next()
        except EmptyPage:
            pass

    if usecase == 'hubspot-mapping':
        if lang == 'ja':
            res.append({
                'id': 'no-map',
                'text': 'このユーザーをマッピングしない'
            })
            res.append({
                'id': 'create-view-only',
                'text': '閲覧専用Sankaユーザーを作成'
            })
        else:
            res.append({
                'id': 'no-map',
                'text': 'Do not map this user'
            })
            res.append({
                'id': 'create-view-only',
                'text': 'Create Sanka View-Only User'
            })

    for row in paginated_rows:
        try:
            text = f'{row.first_name} - {row.email}'
        except Exception:
            pass

        res.append({
            'id': str(row.id),
            'text': text,
        })

    context = {
        'results': res,
        'pagination': {
            "more": more_pagination
        }
    }

    return JsonResponse(context)


@login_or_hubspot_required
def user_management_options(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    q = request.GET.get('q')
    page = request.GET.get('page', 1)
    purpose = request.GET.get('purpose', '')

    if purpose == 'custom_property_text_list':
        filter_conditions = Q(workspace=workspace, type='text')
        if q:
            filter_conditions &= (Q(name__icontains=q))

        cf_name = UserManagementNameCustomField.objects.filter(
            filter_conditions).order_by('-created_at')
        res = []
        ITEMS_PER_PAGE = 30
        cf_name_paginator = Paginator(cf_name, ITEMS_PER_PAGE)
        paginated_cf_name = []
        more_pagination = False
        if page:
            try:
                cf_name_page_content = cf_name_paginator.page(
                    page if page else 1)
                paginated_cf_name = cf_name_page_content.object_list
                more_pagination = cf_name_page_content.has_next()
            except EmptyPage:
                pass

        for cf in paginated_cf_name:
            try:
                text = f'{cf.name}'
            except Exception:
                pass

            res.append({
                'id': str(cf.id),
                'text': text,
            })
    else:
        filter_conditions = Q(workspace=workspace)
        if q:
            q_id = q
            filter_conditions &= (Q(user__email__icontains=q_id) | Q(
                user__first_name__icontains=q_id) | Q(user__last_name__icontains=q_id))

        rows = UserManagement.objects.filter(filter_conditions)

        res = []
        ITEMS_PER_PAGE = 30
        paginator = Paginator(rows, ITEMS_PER_PAGE)
        paginated_rows = []
        more_pagination = False
        if page:
            try:
                rows_page_content = paginator.page(page if page else 1)
                paginated_rows = rows_page_content.object_list
                more_pagination = rows_page_content.has_next()
            except EmptyPage:
                pass

        for row in paginated_rows:
            try:
                text = f'{row.user.first_name} - {row.user.email}'
            except Exception:
                pass

            res.append({
                'id': str(row.id),
                'text': text,
            })

    context = {
        'results': res,
        'pagination': {
            "more": more_pagination
        }
    }

    return JsonResponse(context)

@require_GET
@login_or_hubspot_required
def autocomplete_user_management(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    term = request.GET.get("term", "").lower()
    filter_conditions = Q(workspace=workspace)
    users = UserManagement.objects.select_related('user')
    
    if term:
        search_filter = Q()
        search_filter |= Q(user__username__icontains=term)
        search_filter |= Q(user__first_name__icontains=term)
        search_filter |= Q(user__last_name__icontains=term)
        filter_conditions &= search_filter 

    users = users.filter(filter_conditions)
    result = [{
        "id": um.user.id,
        "value": um.user.first_name
    } for um in users]

    return JsonResponse(result, safe=False)

def generate_random_password(length=16):
    alphabet = string.ascii_letters + string.digits + string.punctuation
    password = ''.join(secrets.choice(alphabet) for _ in range(length))
    return password