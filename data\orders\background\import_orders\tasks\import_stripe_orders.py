import traceback
from datetime import timedelta

from hatchet_sdk import Context
import stripe

from data.models import Channel, TransferHistory
from asgiref.sync import sync_to_async
from utils.bgjobs.handler import (
    aio_set_bg_job_completed,
    aio_set_bg_job_failed,
    aio_set_bg_job_running,
)
from utils.logger import logger
from utils.stripe.import_orders import import_stripe_orders as stripe_import_util
from utils.stripe.import_orders import (
    sync_stripe_orders_payment_status as stripe_sync_util,
)

from ..models import ImportOrdersStripePayload
from ..workflows import import_stripe_orders


@import_stripe_orders.task(
    name="ImportStripeOrdersTask",
    execution_timeout=timedelta(hours=5),
    schedule_timeout=timedelta(hours=1),
)
async def import_stripe_orders_task(
    input: ImportOrdersStripePayload, ctx: Context
) -> dict:
    """
    Child task for importing Stripe orders
    """
    logger.info("Run Stripe orders import child task")
    logger.info(f"Input: {input}")

    history_id = input.history_id
    task = None

    try:
        task = await TransferHistory.objects.select_related("workspace", "user").aget(
            id=history_id
        )
        task.keep_alive = True
        await task.asave()
    except TransferHistory.DoesNotExist:
        logger.warning("TransferHistory does not exist")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])

    if input.background_job_id:
        await aio_set_bg_job_running(input.background_job_id)

    try:
        # Get the channel object
        channel = await Channel.objects.select_related("workspace", "user").aget(
            id=input.channel_id
        )
    except Channel.DoesNotExist:
        logger.error(f"Channel {input.channel_id} does not exist")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])
        return

    try:
        # Call the appropriate Stripe utility based on mode
        if input.only_payment_status:
            logger.info("Running Stripe payment status sync")
            output_data = await sync_to_async(stripe_sync_util)(
                channel, input.target_property, input.target_value
            )
        else:
            logger.info("Running Stripe orders import")
            output_data = await sync_to_async(stripe_import_util)(
                channel, input.days_ago_filter
            )

        logger.info(f"Stripe operation completed: {output_data}")

        if task:
            await task.arefresh_from_db()
            task.status = "completed"
            await task.asave(update_fields=["status"])

        if input.background_job_id:
            await aio_set_bg_job_completed(input.background_job_id)

    except stripe.AuthenticationError:
        logger.error(f"Stripe authentication failed for channel {input.channel_id}")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])

    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error importing Shopify orders: {str(e)}")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])
