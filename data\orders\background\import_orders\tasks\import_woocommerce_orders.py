import ast
from datetime import timedelta
import traceback

from asgiref.sync import sync_to_async
from hatchet_sdk import Context

from data.models import TransferHistory
from utils.bgjobs.handler import (
    aio_set_bg_job_completed,
    aio_set_bg_job_failed,
    aio_set_bg_job_running,
)
from utils.logger import logger
from utils.woocommerce import import_woocommerce_orders as woocommerce_import_util

from ..models import ImportOrdersWoocommercePayload
from ..workflows import import_woocommerce_orders


@import_woocommerce_orders.task(
    name="ImportWoocommerceOrdersTask",
    execution_timeout=timedelta(hours=2),
    schedule_timeout=timedelta(hours=1),
)
async def import_woocommerce_orders_task(
    input: ImportOrdersWoocommercePayload, ctx: Context
) -> dict:
    """
    Child task for importing WooCommerce orders
    """
    logger.info("Run WooCommerce orders import child task")
    logger.info(f"Input: {input}")

    history_id = input.history_id
    task = None

    try:
        task = await TransferHistory.objects.select_related("workspace", "user").aget(
            id=history_id
        )
        task.keep_alive = True
        await task.asave()
    except TransferHistory.DoesNotExist:
        logger.warning("TransferHistory does not exist")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])

    if input.background_job_id:
        await aio_set_bg_job_running(input.background_job_id)

    try:
        # Process mapping fields
        mapping_custom_fields = (
            input.mapping_custom_fields
            if input.mapping_custom_fields and input.mapping_custom_fields != "None"
            else {}
        )

        # Parse string mappings if needed
        if isinstance(mapping_custom_fields, str):
            mapping_custom_fields = ast.literal_eval(mapping_custom_fields)

        # Call the WooCommerce import utility
        await sync_to_async(woocommerce_import_util)(
            input.channel_id, mapping_custom_fields
        )

        logger.info("Successfully imported WooCommerce orders")

        if task:
            await task.arefresh_from_db()
            task.status = "completed"
            await task.asave(update_fields=["status"])

        if input.background_job_id:
            await aio_set_bg_job_completed(input.background_job_id)

    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error importing woocommerce orders: {str(e)}")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])
