from django.test import TestCase
from django.utils import timezone
from data.models import (
    Workspace,
    Verification,
    ShopTurboItems,
    ShopTurboInventory,
    User,
)
from data.import_.import_inventory import write_inventory


class TestInventoryImportErrorHandling(TestCase):
    """
    Test cases for inventory import error handling.

    These tests verify that the write_inventory function properly handles
    error scenarios and returns appropriate error messages.
    """

    def setUp(self):
        """Set up test data for each test."""
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpassword"
        )

        self.workspace = Workspace.objects.create(name="Test Workspace")

        self.verification = Verification.objects.create(
            user=self.user,
            workspace=self.workspace,
            language="en",
            verified=True,
            timezone="UTC",
        )

        # Create a test item
        self.item1 = ShopTurboItems.objects.create(
            workspace=self.workspace,
            name="Test Item 1",
            item_id=1,
            currency="USD",
            price=100,
            status="active",
        )

    def test_missing_import_mode(self):
        """Test error when import mode is missing"""
        data_dict = {
            "default_property": {"associate#commerce_items": "1", "initial_value": 100},
            "custom_property": {},
            "key_property": {},
            "property_object_relation_key_field": {},
        }

        inventory, errors = write_inventory(self.workspace, self.user, data_dict)

        self.assertIsNone(inventory)
        self.assertEqual(len(errors), 1)
        self.assertIn("Import mode is not specified", errors[0])

    def test_invalid_import_mode(self):
        """Test error when import mode is invalid"""
        data_dict = {
            "how_to_import": "invalid_mode",
            "default_property": {"associate#commerce_items": "1", "initial_value": 100},
            "custom_property": {},
            "key_property": {},
            "property_object_relation_key_field": {},
        }

        inventory, errors = write_inventory(self.workspace, self.user, data_dict)

        self.assertIsNone(inventory)
        self.assertEqual(len(errors), 1)
        self.assertIn("Invalid import mode", errors[0])

    def test_item_not_found_create_mode(self):
        """Test error when item_id is provided but item doesn't exist in create mode"""
        data_dict = {
            "how_to_import": "create",
            "default_property": {
                "associate#commerce_items": "999",  # Non-existent item
                "initial_value": 100,
            },
            "custom_property": {},
            "key_property": {},
            "property_object_relation_key_field": {},
        }

        inventory, errors = write_inventory(self.workspace, self.user, data_dict)

        self.assertIsNone(inventory)
        self.assertEqual(len(errors), 1)
        self.assertIn("Item with ID 999 not found", errors[0])

    def test_item_key_not_found_create_mode(self):
        """Test error when item_id is provided but item doesn't exist in create mode"""
        data_dict = {
            "how_to_import": "create",
            "default_property": {"initial_value": 100},
            "custom_property": {},
            "key_property": {},
            "property_object_relation_key_field": {},
        }

        inventory, errors = write_inventory(self.workspace, self.user, data_dict)

        self.assertIsNone(inventory)
        self.assertEqual(len(errors), 1)
        self.assertIn("Item with ID 999 not found", errors[0])

    def test_missing_key_property_update_mode(self):
        """Test error when key_property is missing in update mode"""
        data_dict = {
            "how_to_import": "update",
            "default_property": {"status": "active"},
            "custom_property": {},
            "property_object_relation_key_field": {},
        }

        inventory, errors = write_inventory(self.workspace, self.user, data_dict)

        self.assertIsNone(inventory)
        self.assertEqual(len(errors), 1)
        self.assertIn("key_property is required for update mode", errors[0])

    def test_inventory_not_found_update_mode(self):
        """Test error when inventory to update is not found"""
        data_dict = {
            "how_to_import": "update",
            "default_property": {"status": "active"},
            "custom_property": {},
            "key_property": {"inventory_id": 999},  # Non-existent inventory
            "property_object_relation_key_field": {},
        }

        inventory, errors = write_inventory(self.workspace, self.user, data_dict)

        self.assertIsNone(inventory)
        self.assertEqual(len(errors), 1)
        self.assertIn("Inventory to update not found", errors[0])

    def test_japanese_error_messages(self):
        """Test that Japanese error messages are returned when user language is Japanese"""
        # Set user language to Japanese
        self.verification.language = "ja"
        self.verification.save()

        data_dict = {
            "default_property": {
                "associate#commerce_items": "999",
                "initial_value": 100,
            },
            "custom_property": {},
            "key_property": {},
            "property_object_relation_key_field": {},
        }

        inventory, errors = write_inventory(self.workspace, self.user, data_dict)

        self.assertIsNone(inventory)
        self.assertEqual(len(errors), 1)
        self.assertIn("インポートモードが指定されていません", errors[0])

    def test_invalid_status_error(self):
        """Test error when invalid status is provided"""
        data_dict = {
            "how_to_import": "create",
            "default_property": {
                "associate#commerce_items": "1",
                "initial_value": 100,
                "status": "invalid_status",
            },
            "custom_property": {},
            "key_property": {},
            "property_object_relation_key_field": {},
        }

        inventory, errors = write_inventory(self.workspace, self.user, data_dict)

        self.assertIsNone(inventory)
        self.assertEqual(len(errors), 1)
        self.assertIn("Invalid status: invalid_status", errors[0])

    def test_invalid_status_error_japanese(self):
        """Test error when invalid status is provided with Japanese language"""
        # Set user language to Japanese
        self.verification.language = "ja"
        self.verification.save()

        data_dict = {
            "how_to_import": "create",
            "default_property": {
                "associate#commerce_items": "1",
                "initial_value": 100,
                "status": "invalid_status",
            },
            "custom_property": {},
            "key_property": {},
            "property_object_relation_key_field": {},
        }

        inventory, errors = write_inventory(self.workspace, self.user, data_dict)

        self.assertIsNone(inventory)
        self.assertEqual(len(errors), 1)
        self.assertIn("無効なステータス: invalid_status", errors[0])

    def test_successful_create_still_works(self):
        """Test that successful inventory creation still works after our changes"""
        data_dict = {
            "how_to_import": "create",
            "default_property": {
                "associate#commerce_items": "1",
                "initial_value": 100,
                "status": "active",
            },
            "custom_property": {},
            "key_property": {},
            "property_object_relation_key_field": {},
        }

        inventory, errors = write_inventory(self.workspace, self.user, data_dict)

        self.assertIsNotNone(inventory)
        self.assertEqual(len(errors), 0)
        self.assertEqual(inventory.initial_value, 100)
        self.assertEqual(inventory.status, "active")
        self.assertTrue(self.item1 in inventory.item.all())

    def test_successful_update_still_works(self):
        """Test that successful inventory update still works after our changes"""
        # First create an inventory
        existing_inventory = ShopTurboInventory.objects.create(
            workspace=self.workspace,
            initial_value=50,
            status="draft",
            date=timezone.now(),
        )
        existing_inventory.item.add(self.item1)

        data_dict = {
            "how_to_import": "update",
            "default_property": {"status": "active"},
            "custom_property": {},
            "key_property": {"inventory_id": existing_inventory.inventory_id},
            "property_object_relation_key_field": {},
        }

        inventory, errors = write_inventory(self.workspace, self.user, data_dict)

        self.assertIsNotNone(inventory)
        self.assertEqual(len(errors), 0)
        self.assertEqual(inventory.status, "active")
        self.assertEqual(inventory.id, existing_inventory.id)
