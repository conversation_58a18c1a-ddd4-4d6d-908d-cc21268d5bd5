import json
import ast
from data.constants.constant import HUBSPOT_DEAL_STAGE_MAPPER
import traceback

import aiohttp
from django.conf import settings
from hubspot import HubSpot
from hubspot.crm.associations import (
    BatchInputPublicObjectId,
    BatchResponsePublicAssociationMultiWithErrors,
    BatchResponsePublicAssociationWithErrors,
)
from hubspot.crm.line_items import ApiException as LineItemsApiException
from hubspot.crm.products import ApiException as ProductsApiException

from data.models import (
    Channel,
    Company,
    CompanyNameCustomField,
    CompanyValueCustomField,
    Contact,
    ContactsNameCustomField,
    ContactsPlatforms,
    ContactsValueCustomField,
    Deals,
    DealsItems,
    DealsItemsNameCustomField,
    DealsItemsValueCustomField,
    DealsNameCustomField,
    DealsPlatforms,
    DealsValueCustomField,
    ShopTurboItems,
    ShopTurboItemsNameCustomField,
    ShopTurboItemsPlatforms,
    ShopTurboItemsPrice,
    ShopTurboItemsValueCustomField,
    TransferHistory,
)
from utils.hubspot.orders.batch_operations import chunks_list


async def aio_import_hubspot_orders_as_deals(
    channel_id: str,
    filter_deal_stage=None,
    mapping_custom_fields=None,
    how_to_import=None,
    lang="ja",
):
    channel = await Channel.objects.aget(pk=channel_id)
    workspace = channel.workspace
    platform = channel.integration.slug
    imported_orders = []
    task = (
        await TransferHistory.objects.filter(
            workspace=workspace, type="import_case", channel=channel
        )
        .order_by("-created_at")
        .afirst()
    )

    try:
        token_url = "https://api.hubapi.com/oauth/v1/token"
        data = {
            "grant_type": "refresh_token",
            "client_id": settings.HUBSPOT_CLIENT_ID,
            "client_secret": settings.HUBSPOT_CLIENT_SECRET,
            "refresh_token": channel.refresh_token,
        }
        async with aiohttp.ClientSession() as session:
            async with session.post(token_url, data=data) as response:
                token_info = await response.json()
                channel.access_token = token_info.get("access_token")
                await channel.asave()

        access_token = (
            token_info.get("access_token")
            if token_info.get("access_token")
            else channel.access_token
        )
        api_client = HubSpot(access_token=access_token)

        deals_api_response = None
        properties = [
            "dealname",
            "amount",
            "deal_currency_code",
            "dealstage",
            "createdate",
        ]
        contact_properties = ["firstname", "lastname", "email", "phone"]
        company_properties = ["name", "email", "phone", "domain"]
        # Initialize hubspot_import_platform_match with a default value
        hubspot_import_platform_match = None
        if how_to_import in ["update", "match", "skip"]:
            hubspot_import_platform_match = (
                await Channel.objects.filter(workspace=workspace)
                .exclude(integration__slug="hubspot")
                .values_list("id", flat=True)
                .alist()
            )

        if mapping_custom_fields:
            if (
                "platform|deal" not in mapping_custom_fields
                or "platform_id|deal" not in mapping_custom_fields
            ):
                hubspot_import_platform_match = None
            for field in mapping_custom_fields:
                if field not in [
                    "deal_name",
                    "deal_stage",
                    "issue_date",
                    "billing_date",
                    "partner_display_name",
                ]:
                    if "|deal" in field:
                        field = field.replace("|deal", "")
                        properties.append(field)
                    if "|contact" in field:
                        field = field.replace("|contact", "")
                        contact_properties.append(field)
                    if "|company" in field:
                        field = field.replace("|company", "")
                        company_properties.append(field)

        deals_api_response = None
        for i in range(0, 3):  # Somehow its success in second try
            try:
                deals_api_response = api_client.crm.deals.basic_api.get_page(
                    limit=100,
                    properties=properties,
                    associations=["contacts", "companies"],
                )
                break
            except:
                pass
        if not deals_api_response:
            return False
        deals = deals_api_response.results
        while deals_api_response.paging is not None:
            next_after = deals_api_response.paging.next.after
            deals_api_response = api_client.crm.deals.basic_api.get_page(
                limit=100,
                after=next_after,
                properties=properties,
                associations=["contacts", "companies"],
            )
            deals.extend(deals_api_response.results)

        deal_to_company = aio_fetch_companies_in_deals(
            channel_id, [deal.id for deal in deals]
        )

        deals = sorted(
            deals,
            key=lambda deal: deal.properties.get("createdate", ""),
            reverse=False,  # Set to True for descending order
        )

        counter = 0
        deal_stages = (
            api_client.crm.pipelines.pipelines_api.get_all("deals").results[0].stages
        )
        updated_deal_stage = False

        if task:
            task.total_number = len(deals)
            await task.asave()

        for deal in deals[::-1]:
            counter += 1
            if counter % 250 == 0 or counter == 1:
                token_url = "https://api.hubapi.com/oauth/v1/token"
                data = {
                    "grant_type": "refresh_token",
                    "client_id": settings.HUBSPOT_CLIENT_ID,
                    "client_secret": settings.HUBSPOT_CLIENT_SECRET,
                    "refresh_token": channel.refresh_token,
                }
                async with aiohttp.ClientSession() as session:
                    async with session.post(token_url, data=data) as response:
                        token_info = await response.json()
                        channel.access_token = token_info.get("access_token")
                        await channel.asave()

                access_token = (
                    token_info.get("access_token")
                    if token_info.get("access_token")
                    else channel.access_token
                )
                api_client = HubSpot(access_token=access_token)

            task = (
                await TransferHistory.objects.filter(
                    workspace=workspace, type="import_case", channel=channel
                )
                .order_by("-created_at")
                .afirst()
            )
            if task.status == "canceled":
                return False
            progress = 100 * (len(deals) - deals.index(deal)) / len(deals)
            task.success_number = counter
            task.progress = progress
            await task.asave()
            deal_company = None

            associated_contact = None
            associated_company = None

            # Check if there are associated contacts
            if deal.associations and "contacts" in deal.associations:
                contact_id = deal.associations["contacts"].results[0].id
                contact_response = api_client.crm.contacts.basic_api.get_by_id(
                    contact_id,
                    properties=contact_properties,  # Desired properties
                )
                associated_contact = contact_response.properties

            # Check if there are associated companies
            if deal.associations and "companies" in deal.associations:
                company_id = deal.associations["companies"].results[0].id
                company_response = api_client.crm.companies.basic_api.get_by_id(
                    company_id,
                    properties=company_properties,  # Desired properties
                )
                associated_company = company_response.properties

            # Add to the deal object (modify based on your use case)
            print("contact_company", associated_contact, associated_company)
            if (
                not filter_deal_stage
            ):  # Takes much time to import deals, skip for workflow tmporary
                if deal_to_company.get(str(deal.id), None):
                    deal_company = api_client.crm.companies.basic_api.get_by_id(
                        company_id=deal_to_company[str(deal.id)]
                    )
                # print("Deal==========:", deal)

            try:
                if filter_deal_stage:
                    if filter_deal_stage in HUBSPOT_DEAL_STAGE_MAPPER:
                        filter_deal_stage = HUBSPOT_DEAL_STAGE_MAPPER[filter_deal_stage]
                    if deal.properties["dealstage"] != filter_deal_stage:
                        continue
                order_id = deal.id
                platform_display_name = deal.properties.get("dealname")
                currency = deal.properties.get("deal_currency_code", "JPY")
                platform_id = order_id

                match_order_platform = None
                match_order_platform_id = None
                if hubspot_import_platform_match:
                    match_order_platform = deal.properties.get("platform")
                    match_order_platform_id = deal.properties.get("platform_id")

                exist_order_platform = None
                if match_order_platform and match_order_platform_id:
                    exist_order_platform = (
                        await DealsPlatforms.objects.filter(
                            channel__workspace=workspace,
                            platform_id=match_order_platform_id,
                        )
                        .select_related("deal")
                        .afirst()
                    )

                if exist_order_platform:
                    if exist_order_platform.channel.id in hubspot_import_platform_match:
                        order_platform, _ = await DealsPlatforms.objects.aget_or_create(
                            channel=channel, platform_id=platform_id
                        )
                        order_platform.deal = exist_order_platform.deal
                        await order_platform.asave()

                        shopturbo_order = exist_order_platform.deal

                        if how_to_import == "skip":
                            await shopturbo_order.asave()
                            continue

                        if channel.ms_refresh_token:
                            if associated_company or associated_contact:
                                if associated_company:
                                    company_id = associated_company.get("hs_object_id")
                                    company_name = associated_company.get("name")
                                    company_email = associated_company.get("email")
                                    company_phone = associated_company.get("phone")
                                else:
                                    company_id = associated_contact.get("hs_object_id")
                                    company_name = associated_contact.get("firstname")
                                    company_email = associated_contact.get("email")
                                    company_phone = associated_contact.get("phone")

                                try:
                                    company, _ = await Company.objects.aget_or_create(
                                        workspace=workspace,
                                        name=company_name,
                                        email=company_email,
                                    )
                                except:
                                    company = await Company.objects.filter(
                                        workspace=workspace,
                                        name=company_name,
                                        email=company_email,
                                    ).afirst()

                                if company_phone:
                                    company.phone_number = company_phone
                                await company.asave()
                                if shopturbo_order.contact:
                                    await shopturbo_order.contact.aclear()
                                await shopturbo_order.company.aadd(company)

                        else:
                            if associated_contact:
                                contact_id = associated_contact.get("hs_object_id")
                                contact_firstname = associated_contact.get("firstname")
                                contact_lastname = associated_contact.get("lastname")
                                contact_email = associated_contact.get("email")
                                contact_phone = associated_contact.get("phone")
                                (
                                    contact_platform,
                                    _,
                                ) = await ContactsPlatforms.objects.aget_or_create(
                                    channel=channel,
                                    platform_id=contact_id,
                                )

                                if contact_platform.contact:
                                    contact = contact_platform.contact
                                else:
                                    try:
                                        (
                                            contact,
                                            _,
                                        ) = await Contact.objects.aget_or_create(
                                            workspace=workspace,
                                            email=contact_email,
                                            name=contact_firstname,
                                        )
                                    except:
                                        contact = await Contact.objects.filter(
                                            workspace=workspace,
                                            email=contact_email,
                                            name=contact_firstname,
                                        ).afirst()

                                    contact_platform.contact = contact
                                    await contact_platform.asave()
                                    if contact_phone:
                                        contact.phone = contact_phone
                                    if contact_lastname:
                                        contact.last_name = contact_lastname
                                    await contact.asave()

                                if shopturbo_order.company:
                                    await shopturbo_order.company.aclear()
                                await shopturbo_order.contact.aadd(contact)

                            elif associated_company:
                                company_id = associated_company.get("hs_object_id")
                                company_name = associated_company.get("name")
                                company_email = associated_company.get("email")
                                company_phone = associated_company.get("phone")

                                try:
                                    company, _ = await Company.objects.aget_or_create(
                                        workspace=workspace,
                                        name=company_name,
                                        email=company_email,
                                    )
                                except:
                                    company = await Company.objects.filter(
                                        workspace=workspace,
                                        name=company_name,
                                        email=company_email,
                                    ).afirst()

                                if company_phone:
                                    company.phone_number = company_phone
                                await company.asave()

                                if shopturbo_order.contact:
                                    await shopturbo_order.contact.aclear()
                                await shopturbo_order.company.aadd(company)

                        shopturbo_order.name = platform_display_name
                        await shopturbo_order.asave()
                        if mapping_custom_fields:
                            print(mapping_custom_fields)
                            for field in mapping_custom_fields:
                                sanka_field = mapping_custom_fields[field]
                                if field in [
                                    "deal_name",
                                    "deal_stage",
                                    "issue_date",
                                    "billing_date",
                                    "partner_display_name",
                                ]:
                                    value = None
                                    if field == "deal_name":
                                        value = deal.properties.get("dealname", None)
                                    if field == "deal_stage":
                                        value = deal.properties.get("dealstage", None)
                                    if field == "issue_date":
                                        value = deal.properties.get("createdate", None)
                                    if field == "billing_date":
                                        value = deal.properties.get("closedate", None)
                                    if field == "partner_display_name":
                                        value = deal_company.properties.get(
                                            "name", None
                                        )

                                    custom_field = (
                                        await DealsNameCustomField.objects.filter(
                                            workspace=workspace, name=sanka_field
                                        ).afirst()
                                    )
                                    if custom_field:
                                        (
                                            custom_value,
                                            _,
                                        ) = await DealsValueCustomField.objects.aget_or_create(
                                            field_name=custom_field,
                                            deals=shopturbo_order,
                                        )
                                        custom_value.value = value
                                        await custom_value.asave()
                                else:
                                    value = None
                                    if "|deal" in field:
                                        field = field.replace("|deal", "")
                                        value = deal.properties.get(field, None)
                                    if "|contact" in field:
                                        field = field.replace("|contact", "")
                                        if associated_contact:
                                            value = associated_contact.get(field, None)
                                    if "|company" in field:
                                        field = field.replace("|company", "")
                                        if associated_company:
                                            value = associated_company.get(field, None)

                                    if "|contact" in sanka_field:
                                        sanka_field = sanka_field.replace(
                                            "|contact", ""
                                        )
                                        try:
                                            if sanka_field in [
                                                "name",
                                                "last_name",
                                                "email",
                                                "phone_number",
                                            ]:
                                                if shopturbo_order.contact:
                                                    contact = await shopturbo_order.contact.afirst()
                                                    if sanka_field == "name":
                                                        contact.name = value
                                                    elif sanka_field == "last_name":
                                                        contact.last_name = value
                                                    elif sanka_field == "email":
                                                        contact.email = value
                                                    elif sanka_field == "phone_number":
                                                        contact.phone_number = value
                                                    await contact.asave()
                                        except:
                                            pass
                                        custom_field = await (
                                            ContactsNameCustomField.objects.filter(
                                                workspace=workspace, name=sanka_field
                                            ).afirst()
                                        )
                                    elif "|company" in sanka_field:
                                        sanka_field = sanka_field.replace(
                                            "|company", ""
                                        )
                                        try:
                                            if sanka_field in [
                                                "name",
                                                "email",
                                                "phone_number",
                                            ]:
                                                if shopturbo_order.company:
                                                    company = await shopturbo_order.company.afirst()
                                                    if sanka_field == "name":
                                                        company.name = value
                                                    elif sanka_field == "email":
                                                        company.email = value
                                                    elif sanka_field == "phone_number":
                                                        company.phone_number = value
                                                    await company.asave()
                                        except:
                                            pass
                                        custom_field = await (
                                            CompanyNameCustomField.objects.filter(
                                                workspace=workspace, name=sanka_field
                                            ).afirst()
                                        )
                                    else:
                                        custom_field = await (
                                            DealsNameCustomField.objects.filter(
                                                workspace=workspace, name=sanka_field
                                            ).afirst()
                                        )
                                    if custom_field:
                                        if isinstance(
                                            custom_field, ContactsNameCustomField
                                        ):
                                            if shopturbo_order.contact:
                                                (
                                                    custom_value,
                                                    _,
                                                ) = await ContactsValueCustomField.objects.aget_or_create(
                                                    field_name=custom_field,
                                                    contact=shopturbo_order.contact.first(),
                                                )
                                                custom_value.value = value
                                                await custom_value.asave()
                                        elif isinstance(
                                            custom_field, CompanyNameCustomField
                                        ):
                                            if shopturbo_order.company:
                                                (
                                                    custom_value,
                                                    _,
                                                ) = await CompanyValueCustomField.objects.aget_or_create(
                                                    field_name=custom_field,
                                                    company=await shopturbo_order.company.afirst(),
                                                )
                                                custom_value.value = value
                                                await custom_value.asave()
                                        else:
                                            (
                                                custom_value,
                                                _,
                                            ) = await DealsValueCustomField.objects.aget_or_create(
                                                field_name=custom_field,
                                                deals=shopturbo_order,
                                            )
                                            custom_value.value = value
                                            await custom_value.asave()
                        continue

                if hubspot_import_platform_match:
                    if how_to_import == "update":
                        continue

                order_platform, _ = await DealsPlatforms.objects.aget_or_create(
                    channel=channel, platform_id=platform_id
                )
                order_platform.object_type = "deal"

                if order_platform.deal:
                    shopturbo_order = order_platform.deal
                    if shopturbo_order.status == "archived":
                        shopturbo_order = await Deals.objects.acreate(
                            workspace=workspace, status="active"
                        )
                else:
                    shopturbo_order = await Deals.objects.acreate(
                        workspace=workspace, status="active"
                    )

                order_platform.deal = shopturbo_order
                order_platform.platform_display_name = platform_display_name
                await order_platform.asave()
                # print("Order Platform:========", order_platform.__dict__)

                shopturbo_order.order_at = deal.properties.get("createdate")
                shopturbo_order.name = platform_display_name
                await shopturbo_order.asave()

                if channel.ms_refresh_token:
                    if associated_company or associated_contact:
                        if associated_company:
                            company_id = associated_company.get("hs_object_id")
                            company_name = associated_company.get("name")
                            company_email = associated_company.get("email")
                            company_phone = associated_company.get("phone")
                        else:
                            company_id = associated_contact.get("hs_object_id")
                            company_name = associated_contact.get("firstname")
                            company_email = associated_contact.get("email")
                            company_phone = associated_contact.get("phone")

                        try:
                            company, _ = await Company.objects.aget_or_create(
                                workspace=workspace,
                                name=company_name,
                                email=company_email,
                            )
                        except:
                            company = await Company.objects.filter(
                                workspace=workspace,
                                name=company_name,
                                email=company_email,
                            ).afirst()

                        if company_phone:
                            company.phone_number = company_phone
                        await company.asave()
                        if shopturbo_order.contact:
                            await shopturbo_order.contact.aclear()
                        await shopturbo_order.company.aadd(company)

                else:
                    if associated_contact:
                        contact_id = associated_contact.get("hs_object_id")
                        contact_firstname = associated_contact.get("firstname")
                        contact_lastname = associated_contact.get("lastname")
                        contact_email = associated_contact.get("email")
                        contact_phone = associated_contact.get("phone")
                        (
                            contact_platform,
                            _,
                        ) = await ContactsPlatforms.objects.aget_or_create(
                            channel=channel,
                            platform_id=contact_id,
                        )

                        if contact_platform.contact:
                            contact = contact_platform.contact
                        else:
                            try:
                                contact, _ = await Contact.objects.aget_or_create(
                                    workspace=workspace,
                                    email=contact_email,
                                    name=contact_firstname,
                                )
                            except:
                                contact = await Contact.objects.filter(
                                    workspace=workspace,
                                    email=contact_email,
                                    name=contact_firstname,
                                ).afirst()

                            contact_platform.contact = contact
                            await contact_platform.asave()
                            if contact_phone:
                                contact.phone = contact_phone
                            if contact_lastname:
                                contact.last_name = contact_lastname
                            await contact.asave()

                        if shopturbo_order.company:
                            await shopturbo_order.company.aclear()
                        await shopturbo_order.contact.aadd(contact)

                    elif associated_company:
                        company_id = associated_company.get("hs_object_id")
                        company_name = associated_company.get("name")
                        company_email = associated_company.get("email")
                        company_phone = associated_company.get("phone")

                        try:
                            company, _ = await Company.objects.aget_or_create(
                                workspace=workspace,
                                name=company_name,
                                email=company_email,
                            )
                        except:
                            company = await Company.objects.filter(
                                workspace=workspace,
                                name=company_name,
                                email=company_email,
                            ).afirst()

                        if company_phone:
                            company.phone_number = company_phone
                        await company.asave()

                        if shopturbo_order.contact:
                            await shopturbo_order.contact.aclear()
                        await shopturbo_order.company.aadd(company)

                batch_input_public_object_id = BatchInputPublicObjectId(
                    inputs=[{"id": deal.id}]
                )
                associations_response = api_client.crm.associations.batch_api.read(
                    from_object_type="deal",
                    to_object_type="line_item",
                    batch_input_public_object_id=batch_input_public_object_id,
                )
                line_item_ids = []
                for assoc in associations_response.results:
                    for to_object in assoc.to:
                        line_item_ids.append(to_object.id)

                for line_item_id in line_item_ids:
                    try:
                        properties = [
                            "name",
                            "amount",
                            "quantity",
                            "hs_product_id",
                            "product_id",
                            "hs_total_discount",
                            "hs_discount_percentage",
                            "hs_sku",
                        ]
                        for field in mapping_custom_fields:
                            if "|line_item" in field and field not in [
                                "name|line_item",
                                "discount|line_item",
                                "product_id|line_item",
                            ]:
                                properties.append(field.replace("|line_item", ""))
                        line_item_response = (
                            api_client.crm.line_items.basic_api.get_by_id(
                                line_item_id, properties=properties
                            )
                        )
                        print("Line Item:", line_item_response)
                        item_price = line_item_response.properties.get("amount")
                        order_item_name = line_item_response.properties.get("name")
                        number_of_item = line_item_response.properties.get("quantity")
                        product_id = line_item_response.properties.get("hs_product_id")
                        product_sku = line_item_response.properties.get("hs_sku")
                        discount = line_item_response.properties.get(
                            "hs_discount_percentage"
                        )
                        discount_amount = line_item_response.properties.get(
                            "hs_total_discount"
                        )

                        # custom product id
                        custom_product_id = line_item_response.properties.get(
                            "product_id"
                        )

                        if product_sku:
                            if product_id:
                                existing_item_order = await DealsItems.objects.filter(
                                    platform_item_id=product_id,
                                    deal=shopturbo_order,
                                    deal_platform=order_platform,
                                ).afirst()
                                if existing_item_order:
                                    existing_item_order.platform_item_id = product_sku
                                    await existing_item_order.asave()
                                    shopturbo_item_order = existing_item_order
                                else:
                                    (
                                        shopturbo_item_order,
                                        _,
                                    ) = await DealsItems.objects.aget_or_create(
                                        platform_item_id=product_sku,
                                        deal=shopturbo_order,
                                        deal_platform=order_platform,
                                    )
                            else:
                                (
                                    shopturbo_item_order,
                                    _,
                                ) = await DealsItems.objects.aget_or_create(
                                    platform_item_id=product_sku,
                                    deal=shopturbo_order,
                                    deal_platform=order_platform,
                                )
                        elif product_id:
                            (
                                shopturbo_item_order,
                                _,
                            ) = await DealsItems.objects.aget_or_create(
                                platform_item_id=product_id,
                                deal=shopturbo_order,
                                deal_platform=order_platform,
                            )
                        else:
                            old_order_name = f"Item {line_item_id}"
                            if await DealsItems.objects.filter(
                                deal=shopturbo_order, custom_item_name=old_order_name
                            ).aexists():
                                shopturbo_item_order = await DealsItems.objects.filter(
                                    deal=shopturbo_order,
                                    custom_item_name=old_order_name,
                                ).afirst()
                            else:
                                (
                                    shopturbo_item_order,
                                    _,
                                ) = await DealsItems.objects.aget_or_create(
                                    custom_item_name=order_item_name,
                                    deal=shopturbo_order,
                                    deal_platform=order_platform,
                                )

                        if number_of_item:
                            shopturbo_item_order.number_item = float(number_of_item)
                        if item_price:
                            item_price = float(item_price) / float(number_of_item)
                            shopturbo_item_order.item_price_deal = float(item_price)
                            shopturbo_item_order.total_price = float(item_price)
                        if currency:
                            shopturbo_item_order.currency = currency

                        if not order_item_name:
                            order_item_name = f"Item {line_item_id}"

                        if not product_id and not product_sku:
                            shopturbo_item_order.custom_item_name = order_item_name

                        await shopturbo_item_order.asave()

                        if mapping_custom_fields:
                            for field in mapping_custom_fields:
                                if "|line_item" in field:
                                    sanka_field = mapping_custom_fields[field].replace(
                                        "|line_item", ""
                                    )
                                    value = None

                                    if field == "name|line_item":
                                        value = (
                                            order_item_name or f"Item {line_item_id}"
                                        )
                                    elif field == "discount|line_item":
                                        value = discount or discount_amount
                                    elif field == "product_id|line_item":
                                        value = custom_product_id or product_id
                                    else:
                                        value = line_item_response.properties.get(
                                            field.replace("|line_item", ""), None
                                        )

                        if product_sku:
                            try:
                                product_response = None

                                try:
                                    (
                                        item_platform,
                                        _,
                                    ) = await (
                                        ShopTurboItemsPlatforms.objects.aget_or_create(
                                            channel=channel,
                                            platform_id=product_sku,
                                            platform_type="default",
                                        )
                                    )
                                except:
                                    item_platform = await (
                                        ShopTurboItemsPlatforms.objects.filter(
                                            channel=channel,
                                            platform_id=product_sku,
                                            platform_type="default",
                                        ).afirst()
                                    )

                                if product_id:
                                    try:
                                        product_response = (
                                            api_client.crm.products.basic_api.get_by_id(
                                                product_id
                                            )
                                        )
                                        # Convert old platform IDs
                                        existing_item_platform = await (
                                            ShopTurboItemsPlatforms.objects.filter(
                                                channel=channel, platform_id=product_id
                                            ).afirst()
                                        )
                                        if existing_item_platform:
                                            existing_item_platform_item = (
                                                existing_item_platform.item
                                            )
                                            if existing_item_platform_item:
                                                item_platform.item = (
                                                    existing_item_platform_item
                                                )
                                                await item_platform.asave()
                                            await existing_item_platform.adelete()
                                    except:
                                        pass

                                update_value = True
                                key_item_field = "None"

                                if item_platform.item:
                                    shopturbo_item = item_platform.item
                                    if key_item_field != "None":
                                        custom_field = await (
                                            DealsItemsNameCustomField.objects.filter(
                                                workspace=workspace, name=key_item_field
                                            ).afirst()
                                        )
                                        custom_value = await (
                                            DealsItemsValueCustomField.objects.filter(
                                                field_name=custom_field,
                                                value=product_sku,
                                            ).afirst()
                                        )
                                        if custom_value:
                                            shopturbo_item = custom_value.items
                                            shopturbo_item.product_id = product_sku
                                        else:
                                            (
                                                custom_value,
                                                _,
                                            ) = await ShopTurboItemsValueCustomField.objects.aget_or_create(
                                                field_name=custom_field,
                                                items=shopturbo_item,
                                            )
                                            custom_value.value = product_sku
                                            await custom_value.asave()
                                else:
                                    if shopturbo_item_order.item:
                                        shopturbo_item = shopturbo_item_order.item
                                        if key_item_field != "None":
                                            custom_field = await ShopTurboItemsNameCustomField.objects.filter(
                                                workspace=workspace, name=key_item_field
                                            ).afirst()
                                            (
                                                custom_value,
                                                _,
                                            ) = await ShopTurboItemsValueCustomField.objects.aget_or_create(
                                                field_name=custom_field,
                                                items=shopturbo_item,
                                            )
                                            custom_value.value = product_sku
                                            await custom_value.asave()
                                    else:
                                        if key_item_field != "None":
                                            custom_field = await ShopTurboItemsNameCustomField.objects.filter(
                                                workspace=workspace, name=key_item_field
                                            ).afirst()
                                            custom_value = await ShopTurboItemsValueCustomField.objects.filter(
                                                field_name=custom_field,
                                                value=product_sku,
                                            ).afirst()
                                            if custom_value:
                                                shopturbo_item = custom_value.items
                                                shopturbo_item.product_id = product_sku
                                            else:
                                                update_value = True
                                                shopturbo_item = await (
                                                    ShopTurboItems.objects.acreate(
                                                        workspace=workspace,
                                                        platform=platform,
                                                        product_id=product_sku,
                                                    )
                                                )
                                                (
                                                    custom_value,
                                                    _,
                                                ) = await ShopTurboItemsValueCustomField.objects.aget_or_create(
                                                    field_name=custom_field,
                                                    items=shopturbo_item,
                                                )
                                                custom_value.value = product_sku
                                                await custom_value.asave()
                                        else:
                                            update_value = True
                                            shopturbo_item = await (
                                                ShopTurboItems.objects.acreate(
                                                    workspace=workspace,
                                                    platform=platform,
                                                    product_id=product_sku,
                                                )
                                            )

                                if product_response:
                                    item_name = product_response.properties.get("name")
                                    item_price = product_response.properties.get(
                                        "price"
                                    )
                                else:
                                    item_name = order_item_name

                                if update_value:
                                    if item_name:
                                        shopturbo_item.name = item_name
                                    if item_price:
                                        shopturbo_item.price = float(item_price)
                                    if currency:
                                        shopturbo_item.currency = currency

                                await shopturbo_item.asave()

                                item_platform.platform_id = product_sku
                                item_platform.item = shopturbo_item
                                await item_platform.asave()

                                (
                                    price,
                                    _,
                                ) = await ShopTurboItemsPrice.objects.aget_or_create(
                                    item=shopturbo_item,
                                    price=float(item_price),
                                    currency=currency,
                                )

                                check_default = ShopTurboItemsPrice.objects.filter(
                                    item=shopturbo_item, default=True
                                )
                                if await check_default.acount() == 0:
                                    price.default = True
                                elif update_value:
                                    async for defaults in check_default:
                                        defaults.default = False
                                        await defaults.asave()
                                    price.default = True
                                price.name = item_name
                                await price.asave()

                                shopturbo_item_order.item = shopturbo_item
                                await shopturbo_item_order.asave()
                            except:
                                traceback.print_exc()
                                pass

                        elif product_id:
                            try:
                                product_response = (
                                    api_client.crm.products.basic_api.get_by_id(
                                        product_id
                                    )
                                )
                                # print("Product:=======", product_response)
                                (
                                    item_platform,
                                    _,
                                ) = await (
                                    ShopTurboItemsPlatforms.objects.aget_or_create(
                                        channel=channel,
                                        platform_id=product_id,
                                        platform_type="default",
                                    )
                                )

                                if item_platform.item:
                                    shopturbo_item = item_platform.item
                                else:
                                    if shopturbo_item_order.item:
                                        shopturbo_item = shopturbo_item_order.item
                                    else:
                                        shopturbo_item = (
                                            await ShopTurboItems.objects.acreate(
                                                workspace=workspace,
                                                platform=platform,
                                                product_id=product_id,
                                                status="active",
                                            )
                                        )

                                item_name = product_response.properties.get("name")
                                item_price = product_response.properties.get("price")

                                if item_name:
                                    shopturbo_item.name = item_name
                                if item_price:
                                    shopturbo_item.price = float(item_price)
                                if currency:
                                    shopturbo_item.currency = currency

                                await shopturbo_item.asave()

                                item_platform.platform_id = product_id
                                item_platform.item = shopturbo_item
                                await item_platform.asave()

                                # print("Item Platform=======:", item_platform.__dict__, shopturbo_item.__dict__)

                                (
                                    price,
                                    _,
                                ) = await ShopTurboItemsPrice.objects.aget_or_create(
                                    item=shopturbo_item,
                                    price=float(item_price),
                                    currency=currency,
                                )

                                check_default = ShopTurboItemsPrice.objects.filter(
                                    item=shopturbo_item, default=True
                                )
                                if await check_default.acount() == 0:
                                    price.default = True
                                price.name = item_name
                                await price.asave()

                                shopturbo_item_order.item = shopturbo_item
                                await shopturbo_item_order.asave()

                            except ProductsApiException as e:
                                print(f"... ERROR when FETCH product from hubspot: {e}")
                        else:
                            print("No product ID found for line item:", line_item_id)
                        # print("Item Order=======:", shopturbo_item_order.__dict__)

                    except LineItemsApiException as e:
                        print(f"... ERROR when FETCH line item from hubspot: {e}")

                await shopturbo_order.asave()

                imported_orders.append(shopturbo_order.id)

                # print("Order:", shopturbo_order.__dict__)
                if mapping_custom_fields:
                    print(mapping_custom_fields)
                    for field in mapping_custom_fields:
                        sanka_field = mapping_custom_fields[field]
                        if field in [
                            "deal_name",
                            "deal_stage",
                            "issue_date",
                            "billing_date",
                            "partner_display_name",
                        ]:
                            value = None
                            if field == "deal_name":
                                value = deal.properties.get("dealname", None)
                            if field == "deal_stage":
                                value = deal.properties.get("dealstage", None)
                            if field == "issue_date":
                                value = deal.properties.get("createdate", None)
                            if field == "billing_date":
                                value = deal.properties.get("closedate", None)
                            if field == "partner_display_name":
                                value = deal_company.properties.get("name", None)

                            custom_field = await DealsNameCustomField.objects.filter(
                                workspace=workspace, name=sanka_field
                            ).afirst()
                            if custom_field:
                                if field == "deal_stage" and not updated_deal_stage:
                                    if not custom_field.choice_value:
                                        available_choice_values = []
                                    else:
                                        available_choice_values = ast.literal_eval(
                                            custom_field.choice_value
                                        )
                                    for stage in deal_stages:
                                        if lang == "ja":
                                            translated_stage = {
                                                "appointmentscheduled": "アポイント設定済み (販売パイプライン)",
                                                "qualifiedtobuy": "購入見込みあり (販売パイプライン)",
                                                "presentationscheduled": "プレゼン予定済み (販売パイプライン)",
                                                "decisionmakerboughtin": "意思決定者の賛同 (販売パイプライン)",
                                                "contractsent": "契約書送付済み (販売パイプライン)",
                                                "closedwon": "成約 (販売パイプライン)",
                                                "closedlost": "クローズした不成立取引 (販売パイプライン)",
                                            }
                                            if stage.id in translated_stage:
                                                stage_dict = {
                                                    "label": translated_stage[stage.id],
                                                    "value": stage.id,
                                                }
                                            else:
                                                stage_dict = {
                                                    "label": stage.label,
                                                    "value": stage.id,
                                                }
                                        else:
                                            stage_dict = {
                                                "label": stage.label,
                                                "value": stage.id,
                                            }

                                        if stage_dict not in available_choice_values:
                                            update_label = False
                                            for choice in available_choice_values:
                                                if (
                                                    choice["value"]
                                                    == stage_dict["value"]
                                                ):
                                                    choice["label"] = stage_dict[
                                                        "label"
                                                    ]
                                                    update_label = True
                                                    break
                                            if not update_label:
                                                available_choice_values.append(
                                                    stage_dict
                                                )

                                    custom_field.choice_value = json.dumps(
                                        available_choice_values
                                    )
                                    custom_field.type = "choice"
                                    await custom_field.asave()
                                    updated_deal_stage = True

                                (
                                    custom_value,
                                    _,
                                ) = await DealsValueCustomField.objects.aget_or_create(
                                    field_name=custom_field, deals=shopturbo_order
                                )
                                custom_value.value = value
                                await custom_value.asave()
                        else:
                            value = None
                            if "|deal" in field:
                                field = field.replace("|deal", "")
                                value = deal.properties.get(field, None)
                            if "|contact" in field:
                                field = field.replace("|contact", "")
                                if associated_contact:
                                    value = associated_contact.get(field, None)
                            if "|company" in field:
                                field = field.replace("|company", "")
                                if associated_company:
                                    value = associated_company.get(field, None)

                            if "|contact" in sanka_field:
                                sanka_field = sanka_field.replace("|contact", "")
                                try:
                                    if sanka_field in [
                                        "name",
                                        "last_name",
                                        "email",
                                        "phone_number",
                                    ]:
                                        if shopturbo_order.contact:
                                            contact = (
                                                await shopturbo_order.contact.afirst()
                                            )
                                            if sanka_field == "name":
                                                contact.name = value
                                            elif sanka_field == "last_name":
                                                contact.last_name = value
                                            elif sanka_field == "email":
                                                contact.email = value
                                            elif sanka_field == "phone_number":
                                                contact.phone_number = value
                                            await contact.asave()
                                except:
                                    pass
                                custom_field = (
                                    await ContactsNameCustomField.objects.filter(
                                        workspace=workspace, name=sanka_field
                                    ).afirst()
                                )
                            elif "|company" in sanka_field:
                                sanka_field = sanka_field.replace("|company", "")
                                try:
                                    if sanka_field in ["name", "email", "phone_number"]:
                                        if shopturbo_order.company:
                                            company = shopturbo_order.company.first()
                                            if sanka_field == "name":
                                                company.name = value
                                            elif sanka_field == "email":
                                                company.email = value
                                            elif sanka_field == "phone_number":
                                                company.phone_number = value
                                            await company.asave()
                                except:
                                    pass
                                custom_field = (
                                    await CompanyNameCustomField.objects.filter(
                                        workspace=workspace, name=sanka_field
                                    ).afirst()
                                )
                            else:
                                custom_field = (
                                    await DealsNameCustomField.objects.filter(
                                        workspace=workspace, name=sanka_field
                                    ).afirst()
                                )
                            if custom_field:
                                if isinstance(custom_field, ContactsNameCustomField):
                                    if shopturbo_order.contact:
                                        (
                                            custom_value,
                                            _,
                                        ) = await ContactsValueCustomField.objects.aget_or_create(
                                            field_name=custom_field,
                                            contact=shopturbo_order.contact.first(),
                                        )
                                        custom_value.value = value
                                        await custom_value.asave()
                                elif isinstance(custom_field, CompanyNameCustomField):
                                    if shopturbo_order.company:
                                        (
                                            custom_value,
                                            _,
                                        ) = await CompanyValueCustomField.objects.aget_or_create(
                                            field_name=custom_field,
                                            company=shopturbo_order.company.first(),
                                        )
                                        custom_value.value = value
                                        await custom_value.asave()
                                else:
                                    (
                                        custom_value,
                                        _,
                                    ) = await (
                                        DealsValueCustomField.objects.aget_or_create(
                                            field_name=custom_field,
                                            deals=shopturbo_order,
                                        )
                                    )
                                    custom_value.value = value
                                    await custom_value.asave()
            except Exception as e:
                traceback.print_exc()
                print(f"... ERROR when FETCH associations from hubspot: {e}")

    except Exception as e:
        print(f"... ERROR when FETCH list deal from hubspot: {e}")
        task = await (
            TransferHistory.objects.filter(workspace=workspace, type="import_order")
            .order_by("-created_at")
            .afirst()
        )
        if task:
            task.error_message = e
            await task.asave()
        return

    return imported_orders


async def aio_fetch_companies_in_deals(channel_id: str, deal_object_ids: list):
    channel = await Channel.objects.aget(pk=channel_id)
    access_token = channel.access_token
    client = HubSpot(access_token=access_token)

    inputs = [{"id": id} for id in deal_object_ids]

    results = {}
    for chunk in chunks_list(inputs, 100):
        try:
            batch_input_public_fetch_associations_batch_request = (
                BatchInputPublicObjectId(inputs=chunk)
            )
            api_response = client.crm.associations.v4.batch_api.get_page(
                from_object_type="deal",
                to_object_type="company",
                batch_input_public_fetch_associations_batch_request=batch_input_public_fetch_associations_batch_request,
            )
            if isinstance(
                api_response, BatchResponsePublicAssociationWithErrors
            ) or isinstance(
                api_response, BatchResponsePublicAssociationMultiWithErrors
            ):
                if api_response.errors[0].errors:
                    print(
                        f"... ERROR === hubspot.py -- 749: {api_response.errors[0].message}"
                    )
            elif len(api_response.results) > 0:
                for _result in api_response.results:
                    results[_result._from.id] = _result.to[0].to_object_id

        except Exception as e:
            print(f"... ERROR === hubspot.py -- 754: {e}")

    return results
