"""
HubSpot CSV importer for Orders (Deals).
Handles bulk import using the /crm/v3/imports API.
"""

import asyncio
import json
from typing import Dict, Optional, Any
from pathlib import Path

import aiohttp

from data.models import Channel
from utils.logger import logger
from utils.hubspot.utils_hubspot import refresh_hubspot_token

from .models import ImportResult, ImportStatus, CSVChunkInfo


class HubSpotOrderCSVImporter:
    """
    Handles CSV import to HubSpot for Orders (Deals) using the bulk import API.
    """

    HUBSPOT_IMPORT_URL = "https://api.hubapi.com/crm/v3/imports"
    OBJECT_TYPE_DEAL = "0-3"
    OBJECT_TYPE_LINE_ITEM = "0-8"
    OBJECT_TYPE_CONTACT = "0-1"
    OBJECT_TYPE_COMPANY = "0-2"

    def __init__(
        self,
        channel_id: str,
        access_token: str,
        import_name: Optional[str] = None,
    ):
        self.channel_id = channel_id
        self.access_token = access_token
        self.import_name = import_name or "Sanka Orders Export"
        self.session: Optional[aiohttp.ClientSession] = None

    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()

    async def import_orders_csv(
        self,
        csv_chunk: CSVChunkInfo,
        mapping_config: Optional[Dict[str, Any]] = None,
    ) -> ImportResult:
        """
        Import a CSV file of orders to HubSpot as Deals.

        Args:
            csv_chunk: Information about the CSV file to import
            mapping_config: Optional field mapping configuration

        Returns:
            ImportResult with import details
        """
        logger.info(f"Starting HubSpot import for orders CSV: {csv_chunk.file_path}")

        # Refresh token before import
        await self._refresh_token()

        # Prepare import configuration
        import_config = self._prepare_order_import_config(csv_chunk, mapping_config)

        # Upload CSV and start import
        import_id = await self._upload_and_import(
            csv_chunk.file_path,
            import_config,
            self.OBJECT_TYPE_DEAL,
        )

        if not import_id:
            return ImportResult(
                import_id="",
                status=ImportStatus.FAILED,
                object_type_id=self.OBJECT_TYPE_DEAL,
                error_messages=["Failed to start import"],
            )

        logger.info(f"Started HubSpot import with ID: {import_id}")

        return ImportResult(
            import_id=import_id,
            status=ImportStatus.PENDING,
            object_type_id=self.OBJECT_TYPE_DEAL,
            total_rows=csv_chunk.row_count,
            import_name=self.import_name,
        )

    async def import_line_items_csv(
        self,
        csv_chunk: CSVChunkInfo,
        mapping_config: Optional[Dict[str, Any]] = None,
    ) -> ImportResult:
        """
        Import a CSV file of line items to HubSpot.

        Args:
            csv_chunk: Information about the CSV file to import
            mapping_config: Optional field mapping configuration

        Returns:
            ImportResult with import details
        """
        logger.info(
            f"Starting HubSpot import for line items CSV: {csv_chunk.file_path}"
        )

        # Refresh token before import
        await self._refresh_token()

        # Prepare import configuration for line items
        import_config = self._prepare_line_item_import_config(mapping_config)

        # Upload CSV and start import
        import_id = await self._upload_and_import(
            csv_chunk.file_path,
            import_config,
            self.OBJECT_TYPE_LINE_ITEM,
        )

        if not import_id:
            return ImportResult(
                import_id="",
                status=ImportStatus.FAILED,
                object_type_id=self.OBJECT_TYPE_LINE_ITEM,
                error_messages=["Failed to start line items import"],
            )

        logger.info(f"Started HubSpot line items import with ID: {import_id}")

        return ImportResult(
            import_id=import_id,
            status=ImportStatus.PENDING,
            object_type_id=self.OBJECT_TYPE_LINE_ITEM,
            total_rows=csv_chunk.row_count,
            import_name=f"{self.import_name} - Line Items",
        )

    async def import_contacts_csv(
        self,
        csv_chunk: CSVChunkInfo,
        mapping_config: Optional[Dict[str, Any]] = None,
    ) -> ImportResult:
        """
        Import a CSV file of contacts to HubSpot.

        Args:
            csv_chunk: Information about the CSV file to import
            mapping_config: Optional field mapping configuration

        Returns:
            ImportResult with import details
        """
        logger.info(f"Starting HubSpot import for contacts CSV: {csv_chunk.file_path}")

        # Refresh token before import
        await self._refresh_token()

        # Prepare import configuration for contacts
        import_config = self._prepare_contact_import_config(csv_chunk, mapping_config)

        # Upload CSV and start import
        import_id = await self._upload_and_import(
            csv_chunk.file_path,
            import_config,
            self.OBJECT_TYPE_CONTACT,
        )

        if not import_id:
            return ImportResult(
                import_id="",
                status=ImportStatus.FAILED,
                object_type_id=self.OBJECT_TYPE_CONTACT,
                error_messages=["Failed to start contacts import"],
            )

        logger.info(f"Started HubSpot contacts import with ID: {import_id}")

        return ImportResult(
            import_id=import_id,
            status=ImportStatus.PENDING,
            object_type_id=self.OBJECT_TYPE_CONTACT,
            total_rows=csv_chunk.row_count,
            import_name=f"{self.import_name} - Contacts",
        )

    async def import_companies_csv(
        self,
        csv_chunk: CSVChunkInfo,
        mapping_config: Optional[Dict[str, Any]] = None,
    ) -> ImportResult:
        """
        Import a CSV file of companies to HubSpot.

        Args:
            csv_chunk: Information about the CSV file to import
            mapping_config: Optional field mapping configuration

        Returns:
            ImportResult with import details
        """
        logger.info(f"Starting HubSpot import for companies CSV: {csv_chunk.file_path}")

        # Refresh token before import
        await self._refresh_token()

        # Prepare import configuration for companies
        import_config = self._prepare_company_import_config(csv_chunk, mapping_config)

        # Upload CSV and start import
        import_id = await self._upload_and_import(
            csv_chunk.file_path,
            import_config,
            self.OBJECT_TYPE_COMPANY,
        )

        if not import_id:
            return ImportResult(
                import_id="",
                status=ImportStatus.FAILED,
                object_type_id=self.OBJECT_TYPE_COMPANY,
                error_messages=["Failed to start companies import"],
            )

        logger.info(f"Started HubSpot companies import with ID: {import_id}")

        return ImportResult(
            import_id=import_id,
            status=ImportStatus.PENDING,
            object_type_id=self.OBJECT_TYPE_COMPANY,
            total_rows=csv_chunk.row_count,
            import_name=f"{self.import_name} - Companies",
        )

    def _read_csv_file(self, csv_path: str) -> bytes:
        """Read CSV file synchronously."""
        with open(csv_path, "rb") as f:
            return f.read()

    async def _upload_and_import(
        self,
        csv_path: str,
        import_config: Dict[str, Any],
        object_type_id: str,
    ) -> Optional[str]:
        """
        Upload CSV file and start import process.

        Args:
            csv_path: Path to CSV file
            import_config: Import configuration
            object_type_id: HubSpot object type ID

        Returns:
            Import ID if successful, None otherwise
        """
        if not self.session:
            self.session = aiohttp.ClientSession()

        try:
            # Use sync file operations wrapped in asyncio.to_thread
            csv_data = await asyncio.to_thread(self._read_csv_file, csv_path)
            filename = Path(csv_path).name

            # Prepare multipart form data
            form_data = aiohttp.FormData()

            # Add CSV file
            form_data.add_field(
                "files", csv_data, filename=filename, content_type="text/csv"
            )

            # Add import request configuration
            import_request = {
                "name": import_config.get("name", self.import_name),
                # "objectTypeId": import_config.get("objectTypeId", self.OBJECT_TYPE_DEAL),
                # "objectTypes": import_config.get("objectTypes", []),
                "files": [
                    {
                        "fileName": filename,
                        "fileFormat": "CSV",
                        "fileImportPage": {
                            "hasHeader": True,
                            "columnMappings": import_config.get("columnMappings", []),
                        },
                    }
                ],
                "dateFormat": "MONTH_DAY_YEAR",
                "marketableContactImport": False,
            }

            form_data.add_field(
                "importRequest",
                json.dumps(import_request),
                content_type="application/json",
            )

            # Make request
            headers = {
                "Authorization": f"Bearer {self.access_token}",
            }

            async with self.session.post(
                self.HUBSPOT_IMPORT_URL,
                data=form_data,
                headers=headers,
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get("id")
                else:
                    error_text = await response.text()
                    logger.error(
                        f"Import failed with status {response.status}: {error_text}"
                    )
                    return None

        except Exception as e:
            logger.error(f"Error during CSV upload and import: {str(e)}")
            return None

    def _prepare_order_import_config(
        self,
        csv_chunk: CSVChunkInfo,
        mapping_config: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Prepare import configuration for orders (deals).

        Args:
            csv_chunk: CSV chunk information
            mapping_config: Optional custom field mappings

        Returns:
            Import configuration dictionary
        """
        import csv

        # Read CSV headers from the actual file
        with open(csv_chunk.file_path, "r", encoding="utf-8") as f:
            reader = csv.reader(f)
            headers = next(reader)

        column_mappings = []


        object_types = [self.OBJECT_TYPE_DEAL]
        # Map each CSV column to HubSpot property
        for column_name in headers:
            property_name = self._map_column_to_property(column_name, mapping_config)

            if property_name:
                mapping = {
                    "columnObjectTypeId": self.OBJECT_TYPE_DEAL,
                    "columnName": column_name,
                    "propertyName": property_name,
                }

                # Special handling for sanka_record_id
                if column_name == "sanka_record_id":
                    mapping["idColumnType"] = "HUBSPOT_ALTERNATE_ID"
                    
                if column_name == "contact__association":
                    mapping["associationIdentifierColumn"] = False
                    mapping["idColumnType"] = "HUBSPOT_ALTERNATE_ID"
                    mapping["columnObjectTypeId"] = self.OBJECT_TYPE_CONTACT
                    mapping["propertyName"] = "sanka_object_id"
                    object_types.append(self.OBJECT_TYPE_CONTACT)
                    
                if column_name == "company__association":
                    mapping["associationIdentifierColumn"] = False
                    mapping["idColumnType"] = "HUBSPOT_ALTERNATE_ID"
                    mapping["columnObjectTypeId"] = self.OBJECT_TYPE_COMPANY
                    mapping["propertyName"] = "sanka_object_id"
                    object_types.append(self.OBJECT_TYPE_COMPANY)

                column_mappings.append(mapping)

        return {
            "name": f"{self.import_name} - Orders",
            "columnMappings": column_mappings,
            "objectTypeId": self.OBJECT_TYPE_DEAL,
            "objectTypes": object_types,
        }

    def _prepare_line_item_import_config(
        self,
        mapping_config: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Prepare import configuration for line items.

        Args:
            mapping_config: Optional custom field mappings

        Returns:
            Import configuration dictionary
        """
        # Default column mappings for line items
        column_mappings = [
            {
                "columnObjectTypeId": self.OBJECT_TYPE_LINE_ITEM,
                "columnName": "sanka_record_id",
                "propertyName": "sanka_record_id",
                "idColumnType": "HUBSPOT_ALTERNATE_ID",
            },
            {
                "columnObjectTypeId": self.OBJECT_TYPE_LINE_ITEM,
                "columnName": "name",
                "propertyName": "name",
            },
            {
                "columnObjectTypeId": self.OBJECT_TYPE_LINE_ITEM,
                "columnName": "description",
                "propertyName": "description",
            },
            {
                "columnObjectTypeId": self.OBJECT_TYPE_LINE_ITEM,
                "columnName": "quantity",
                "propertyName": "quantity",
            },
            {
                "columnObjectTypeId": self.OBJECT_TYPE_LINE_ITEM,
                "columnName": "price",
                "propertyName": "price",
            },
            {
                "columnObjectTypeId": self.OBJECT_TYPE_LINE_ITEM,
                "columnName": "amount",
                "propertyName": "amount",
            },
            {
                "columnObjectTypeId": self.OBJECT_TYPE_LINE_ITEM,
                "columnName": "sku",
                "propertyName": "hs_sku",
            },
            # Association to deal
            {
                "columnObjectTypeId": self.OBJECT_TYPE_LINE_ITEM,
                "columnName": "order_sanka_id",
                "propertyName": "sanka_record_id",
                "idColumnType": "HUBSPOT_ALTERNATE_ID",
                "associationObjectTypeId": self.OBJECT_TYPE_DEAL,
            },
        ]

        # Add custom field mappings if provided
        if mapping_config:
            for sanka_field, hubspot_property in mapping_config.items():
                if not any(m["columnName"] == sanka_field for m in column_mappings):
                    column_mappings.append(
                        {
                            "columnObjectTypeId": self.OBJECT_TYPE_LINE_ITEM,
                            "columnName": sanka_field,
                            "propertyName": hubspot_property,
                        }
                    )

        return {
            "name": f"{self.import_name} - Line Items",
            "columnMappings": column_mappings,
        }

    def _prepare_contact_import_config(
        self,
        csv_chunk: CSVChunkInfo,
        mapping_config: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Prepare import configuration for contacts.

        Args:
            csv_chunk: CSV chunk information
            mapping_config: Optional custom field mappings

        Returns:
            Import configuration dictionary
        """
        import csv

        # Read CSV headers from the actual file
        with open(csv_chunk.file_path, "r", encoding="utf-8") as f:
            reader = csv.reader(f)
            headers = next(reader)

        column_mappings = []

        # Map each CSV column to HubSpot property
        for column_name in headers:
            property_name = None

            # Handle standard contact fields
            if column_name == "sanka_object_id":
                property_name = "sanka_object_id"
            elif column_name in ["firstname", "lastname", "email", "phone", "company"]:
                property_name = column_name
            elif mapping_config and column_name in mapping_config:
                # Use custom field mapping
                property_name = mapping_config[column_name]

            if property_name:
                mapping = {
                    "columnObjectTypeId": self.OBJECT_TYPE_CONTACT,
                    "columnName": column_name,
                    "propertyName": property_name,
                }

                # Special handling for sanka_object_id
                if column_name == "sanka_object_id":
                    mapping["idColumnType"] = "HUBSPOT_ALTERNATE_ID"

                column_mappings.append(mapping)

        return {
            "name": f"{self.import_name} - Contacts",
            "columnMappings": column_mappings,
        }

    def _prepare_company_import_config(
        self,
        csv_chunk: CSVChunkInfo,
        mapping_config: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Prepare import configuration for companies.

        Args:
            csv_chunk: CSV chunk information
            mapping_config: Optional custom field mappings

        Returns:
            Import configuration dictionary
        """
        import csv

        # Read CSV headers from the actual file
        with open(csv_chunk.file_path, "r", encoding="utf-8") as f:
            reader = csv.reader(f)
            headers = next(reader)

        column_mappings = []

        # Map each CSV column to HubSpot property
        for column_name in headers:
            property_name = None

            # Handle standard company fields
            if column_name == "sanka_object_id":
                property_name = "sanka_object_id"
            elif column_name in [
                "name",
                "domain",
                "phone",
                "industry",
                "city",
                "state",
                "country",
            ]:
                property_name = column_name
            elif mapping_config and column_name in mapping_config:
                # Use custom field mapping
                property_name = mapping_config[column_name]

            if property_name:
                mapping = {
                    "columnObjectTypeId": self.OBJECT_TYPE_COMPANY,
                    "columnName": column_name,
                    "propertyName": property_name,
                }

                # Special handling for sanka_object_id
                if column_name == "sanka_object_id":
                    mapping["idColumnType"] = "HUBSPOT_ALTERNATE_ID"

                column_mappings.append(mapping)

        return {
            "name": f"{self.import_name} - Companies",
            "columnMappings": column_mappings,
        }

    def _map_column_to_property(
        self,
        column_name: str,
        mapping_config: Optional[Dict[str, Any]] = None,
    ) -> Optional[str]:
        """
        Map CSV column names to HubSpot property names.

        Args:
            column_name: CSV column name
            mapping_config: Optional custom field mappings

        Returns:
            HubSpot property name or None if unmapped
        """
        # Standard field mappings for known deal properties
        standard_mappings = {
            "sanka_record_id": "sanka_record_id",
            "dealname": "dealname",
            "amount": "amount",
            "closedate": "closedate",
            "dealstage": "dealstage",
            "pipeline": "pipeline",
            "currency": "deal_currency_code",
            # Additional standard order fields
            "order_id": "order_id",
            "platform": "platform",
            "platform_order_id": "platform_order_id",
            "tax": "tax",
            "total_price_without_tax": "total_price_without_tax",
            "shipping_cost": "shipping_cost",
            "discount_amount": "discount_amount",
            "delivery_status": "delivery_status",
            "order_type": "order_type",
            "memo": "memo",
            "company_sanka_id": "company_sanka_id",
            "contact_sanka_id": "contact_sanka_id",
            "hubspot_owner_id": "hubspot_owner_id",
        }

        # Check standard mappings first
        if column_name in standard_mappings:
            return standard_mappings[column_name]

        # For custom fields, the CSV column name IS the HubSpot property name
        # The mapping_config contains HubSpot property -> Sanka field mappings
        # But during CSV export, columns are already named with HubSpot property names
        # So we should return the column name itself as the property name
        
        # Check if this column is a known HubSpot custom property
        # by checking if it exists as a key in mapping_config
        if mapping_config and column_name in mapping_config.keys():
            # The column name is already the HubSpot property name
            return column_name
        
        # For any other column that appears in the CSV (likely custom fields),
        # return the column name itself as the property name
        # This ensures all CSV columns are mapped to avoid the mismatch error
        return column_name

    async def _refresh_token(self):
        """Refresh HubSpot access token"""
        try:
            # Use the existing refresh token utility
            await asyncio.to_thread(refresh_hubspot_token, self.channel_id)
            # Update access token after refresh
            channel = await Channel.objects.aget(pk=self.channel_id)
            self.access_token = channel.access_token
        except Exception as e:
            logger.error(f"Error refreshing HubSpot token: {str(e)}")

    async def check_import_status(self, import_id: str) -> ImportResult:
        """
        Check the status of an import operation.

        Args:
            import_id: HubSpot import ID

        Returns:
            Updated ImportResult
        """
        if not self.session:
            self.session = aiohttp.ClientSession()

        try:
            headers = {
                "Authorization": f"Bearer {self.access_token}",
            }

            url = f"{self.HUBSPOT_IMPORT_URL}/{import_id}"

            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()

                    # Map HubSpot status to our ImportStatus
                    status_map = {
                        "PENDING": ImportStatus.PENDING,
                        "PROCESSING": ImportStatus.PROCESSING,
                        "COMPLETE": ImportStatus.COMPLETE,
                        "FAILED": ImportStatus.FAILED,
                        "CANCELED": ImportStatus.CANCELED,
                    }

                    status = status_map.get(
                        data.get("state", "").upper(), ImportStatus.ERROR
                    )

                    # Extract counters
                    counters = data.get("metadata", {}).get("counters", {})

                    return ImportResult(
                        import_id=import_id,
                        status=status,
                        object_type_id=data.get(
                            "importRequestId", self.OBJECT_TYPE_DEAL
                        ),
                        total_rows=counters.get("TOTAL_ROWS", 0),
                        successful_rows=counters.get("CREATED", 0)
                        + counters.get("UPDATED", 0),
                        failed_rows=counters.get("FAILED", 0),
                        error_messages=data.get("errors", []),
                    )
                else:
                    logger.error(f"Failed to check import status: {response.status}")
                    return ImportResult(
                        import_id=import_id,
                        status=ImportStatus.ERROR,
                        error_messages=[f"API returned status {response.status}"],
                    )

        except Exception as e:
            logger.error(f"Error checking import status: {str(e)}")
            return ImportResult(
                import_id=import_id,
                status=ImportStatus.ERROR,
                error_messages=[str(e)],
            )
