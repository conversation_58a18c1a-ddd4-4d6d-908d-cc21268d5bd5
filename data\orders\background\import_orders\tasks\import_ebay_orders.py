from datetime import timedelta
import traceback

from hatchet_sdk import Context

from data.models import Transfer<PERSON>istory
from asgiref.sync import sync_to_async
from utils.bgjobs.handler import (
    aio_set_bg_job_completed,
    aio_set_bg_job_failed,
    aio_set_bg_job_running,
)
from utils.ebay import import_ebay_orders as ebay_import_util
from utils.logger import logger

from ..models import ImportOrdersEbayPayload
from ..workflows import import_ebay_orders


@import_ebay_orders.task(
    name="ImportEbayOrdersTask",
    execution_timeout=timedelta(hours=2),
    schedule_timeout=timedelta(hours=1),
)
async def import_ebay_orders_task(input: ImportOrdersEbayPayload, ctx: Context) -> dict:
    """
    Child task for importing eBay orders
    """
    logger.info("Run eBay orders import child task")
    logger.info(f"Input: {input}")

    history_id = input.history_id
    task = None

    try:
        task = await TransferHistory.objects.select_related("workspace", "user").aget(
            id=history_id
        )
        task.keep_alive = True
        await task.asave()
    except TransferHistory.DoesNotExist:
        logger.warning("TransferHistory does not exist")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])

    if input.background_job_id:
        await aio_set_bg_job_running(input.background_job_id)

    try:
        # Call the eBay import utility
        await sync_to_async(ebay_import_util)(input.channel_id, input.how_to_import)

        logger.info("Successfully imported eBay orders")

        if task:
            await task.arefresh_from_db()
            task.status = "completed"
            await task.asave(update_fields=["status"])

        if input.background_job_id:
            await aio_set_bg_job_completed(input.background_job_id)

    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error importing eBay orders: {str(e)}")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])
