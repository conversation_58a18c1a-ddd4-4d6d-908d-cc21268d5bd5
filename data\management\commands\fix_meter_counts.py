"""
Management command to fix stale Meter counts by recalculating them from actual data.

Usage:
    python manage.py fix_meter_counts                    # Fix all workspaces
    python manage.py fix_meter_counts --workspace UUID   # Fix specific workspace
    python manage.py fix_meter_counts --category orders  # Fix specific category
    python manage.py fix_meter_counts --dry-run          # Show what would be fixed without making changes
"""

from django.core.management.base import BaseCommand
from django.db.models import Q
from data.models import Workspace, Meter
from data.constants.constant import USAGE_CATEGORIES
from utils.meter import sync_usage


class Command(BaseCommand):
    help = 'Fix stale Meter counts by recalculating them from actual data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--workspace',
            type=str,
            help='Specific workspace ID to fix',
        )
        parser.add_argument(
            '--category',
            type=str,
            help='Specific category to fix (e.g., orders, contacts, etc.)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be fixed without making changes',
        )

    def handle(self, *args, **options):
        workspace_id = options.get('workspace')
        category_filter = options.get('category')
        dry_run = options.get('dry_run')

        self.stdout.write(self.style.WARNING(
            f"{'DRY RUN - ' if dry_run else ''}Fixing Meter counts..."
        ))

        # Get workspaces to process
        if workspace_id:
            # Try to find by ID first (if it's a valid UUID)
            try:
                import uuid
                uuid.UUID(workspace_id)
                workspaces = Workspace.objects.filter(id=workspace_id)
            except ValueError:
                # If not a valid UUID, try to find by name
                workspaces = Workspace.objects.filter(name=workspace_id)
            
            if not workspaces.exists():
                self.stdout.write(self.style.ERROR(f"Workspace {workspace_id} not found"))
                return
        else:
            workspaces = Workspace.objects.all()

        # Get categories to process
        if category_filter:
            categories = [(cat, name) for cat, name in USAGE_CATEGORIES if cat == category_filter]
            if not categories:
                self.stdout.write(self.style.ERROR(f"Category {category_filter} not found"))
                self.stdout.write(f"Available categories: {', '.join([cat for cat, _ in USAGE_CATEGORIES])}")
                return
        else:
            categories = USAGE_CATEGORIES

        total_fixed = 0
        issues_found = 0

        for workspace in workspaces:
            self.stdout.write(f"\nChecking workspace: {workspace.name} (ID: {workspace.id})")
            
            for category, category_name in categories:
                try:
                    # Get current meter
                    meter = Meter.objects.filter(workspace=workspace, category=category).first()
                    
                    if meter:
                        old_count = meter.count
                        
                        # Calculate actual count by running sync_usage
                        if not dry_run:
                            sync_usage(workspace, category)
                            meter.refresh_from_db()
                            new_count = meter.count
                        else:
                            # For dry run, we need to manually calculate what sync_usage would do
                            from utils.meter import STORAGE_USAGE_TO_MODELS, is_using_usage_status_field
                            from django.db.models import Q
                            model = STORAGE_USAGE_TO_MODELS[category]
                            
                            # Build base query
                            queryset = model.objects.filter(workspace=workspace)
                            
                            # Apply status filtering based on model type
                            if category == 'users':
                                from data.models import UserManagement
                                # Special case: UserManagement uses 'type' field, not status
                                queryset = queryset.exclude(type__in=[
                                    UserManagement.RoleType.VIEW_ONLY,
                                    UserManagement.RoleType.SUPPORT,
                                    UserManagement.RoleType.PARTNER
                                ])
                            else:
                                # For all other models: count only "active" or NULL status
                                # Determine which status field to use
                                if is_using_usage_status_field(model):
                                    status_field = 'usage_status'
                                else:
                                    status_field = 'status'
                                
                                # Only count records where status is "active" or NULL
                                status_filter = Q(**{f'{status_field}': 'active'}) | Q(**{f'{status_field}__isnull': True})
                                queryset = queryset.filter(status_filter)
                            
                            new_count = queryset.count()
                        
                        if old_count != new_count:
                            issues_found += 1
                            self.stdout.write(
                                self.style.WARNING(
                                    f"  - {category_name}: {old_count} → {new_count} "
                                    f"(difference: {new_count - old_count})"
                                )
                            )
                            if not dry_run:
                                total_fixed += 1
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"  - Error processing {category}: {str(e)}")
                    )

        self.stdout.write(
            self.style.SUCCESS(
                f"\n{'DRY RUN - Would fix' if dry_run else 'Fixed'} "
                f"{total_fixed if not dry_run else issues_found} meter counts"
            )
        )
        
        if dry_run and issues_found > 0:
            self.stdout.write(
                self.style.WARNING(
                    "\nRun without --dry-run to apply these fixes"
                )
            )