#!/usr/bin/env python
"""
Setup script to create the test database once.
Run this before running tests with --keepdb for fast subsequent runs.
"""

import os
import sys
import django
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# Setup Django with test settings
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "data.tests.test_settings")
django.setup()

from django.db import connection


def setup_test_database():
    """Create test database with all tables."""
    print("Setting up test database...")

    # Ensure database directory exists
    db_path = Path(django.conf.settings.DATABASES["default"]["NAME"])
    db_dir = db_path.parent
    db_dir.mkdir(parents=True, exist_ok=True)
    
    # Check if database already exists
    if db_path.exists():
        response = input(f"Database {db_path} already exists. Recreate? [y/N]: ")
        if response.lower() != "y":
            print("Keeping existing database.")
            return
        else:
            db_path.unlink()
            print(f"Deleted {db_path}")

    # Create tables using syncdb (since migrations are disabled)
    print("Creating tables...")
    with connection.schema_editor() as schema_editor:
        # This will trigger Django to create all tables
        from django.apps import apps

        for app_config in apps.get_app_configs():
            for model in app_config.get_models():
                if model._meta.managed and not model._meta.proxy:
                    try:
                        schema_editor.create_model(model)
                    except Exception as e:
                        print(f"Warning: Could not create table for {model}: {e}")

    print(f"\nTest database created at: {db_path}")
    print("Now you can run tests with --keepdb flag for fast execution:")
    print(
        "  python manage.py test data.tests --settings=data.tests.test_settings --keepdb"
    )
    print("  or")
    print("  pytest data/tests/ --reuse-db")


if __name__ == "__main__":
    setup_test_database()
