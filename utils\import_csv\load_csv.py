import ast
import csv
from io import StringIO
import traceback
import uuid

import chardet
from django.core.files.base import ContentFile as DjangoContentFile
from django.utils import timezone
import pandas as pd
import requests


def get_checkpoint_details(transfer_history):
    """Get checkpoint details from either TransferHistory or ActionTaskHistory"""
    if hasattr(transfer_history, "checkpoint_details"):
        return transfer_history.checkpoint_details
    else:
        return transfer_history.output_data


def set_checkpoint_details(transfer_history, details):
    """Set checkpoint details for either TransferHistory or ActionTaskHistory"""
    if hasattr(transfer_history, "checkpoint_details"):
        transfer_history.checkpoint_details = details
    else:
        transfer_history.output_data = details


from data.constants.constant import INVENTORY_USAGE_CATEGORY, ITEM_USAGE_CATEGORY
from data.constants.properties_constant import (
    TYPE_OBJECT_BILL,
    TYPE_OBJECT_CASE,
    TYPE_OBJECT_COMMERCE_METER,
    TYPE_OBJECT_COMPANY,
    TYPE_OBJECT_DELIVERY_NOTE,
    TYPE_OBJECT_ESTIMATE,
    TYPE_OBJECT_EXPENSE,
    TYPE_OBJECT_INVENTORY,
    TYPE_OBJECT_INVENTORY_TRANSACTION,
    TYPE_OBJECT_INVENTORY_WAREHOUSE,
    TYPE_OBJECT_INVOICE,
    TYPE_OBJECT_JOURNAL,
    TYPE_OBJECT_PURCHASE_ORDER,
    TYPE_OBJECT_RECEIPT,
    TYPE_OBJECT_SLIP,
    TYPE_OBJECT_SUBSCRIPTION,
)
from data.import_.import_bill import write_bill
from data.import_.import_case import write_case
from data.import_.import_commerce import write_dynamic_commerce
from data.import_.import_commerce_meter import write_commerce_meter
from data.import_.import_company import write_company
from data.import_.import_contacts import write_contact
from data.import_.import_custom_object import write_dynamic_custom_object
from data.import_.import_expense import write_expense
from data.import_.import_inventory import write_inventory
from data.import_.import_inventory_transaction import write_inventory_transaction
from data.import_.import_items import write_item
from data.import_.import_journal import write_journal
from data.import_.import_locations import write_location
from data.import_.import_orders import write_order
from data.import_.import_production import write_production
from data.import_.import_purchase_order import write_purchase_order
from data.import_.import_subscriptions import write_subscription
from data.models import (
    CustomObject,
    ImportMappingFields,
    Notification,
    ShopTurboItemsNameCustomField,
    TransferHistory,
    Workspace,
)
from utils.discord import DiscordNotification
from utils.import_ import reorder_dict_with_item_first
from utils.logger import logger
from utils.meter import get_workspace_available_storage, has_quota
from utils.properties.properties import get_page_object
from utils.utility import get_workspace, is_valid_uuid


def load_csv_data(imf_id, user, workspace_id, history_id=None):
    """
    Processes a mapped CSV file and imports its data into the appropriate database models.

    Loads the import mapping, transfer history, and workspace context, then reads the uploaded CSV file, normalizes and validates its contents, and maps CSV columns to internal properties based on user-defined configuration. Iterates through each row, dispatching to the correct write function for each object type (including support for custom objects and journals), and tracks progress, errors, and import status. Generates error notifications and attaches an error CSV if any rows fail to import.

    Args:
        imf_id: The ID of the ImportMappingFields instance containing mapping configuration.
        user: The user performing the import.
        workspace_id: The ID of the workspace where data will be imported.
        history_id: The ID of the TransferHistory record tracking this import (optional).
    """
    lang = user.verification.language

    try:
        workspace = Workspace.objects.get(id=workspace_id)
    except Workspace.DoesNotExist:
        workspace = get_workspace(user)
        return

    try:
        transfer_history = TransferHistory.objects.get(id=history_id)
    except TransferHistory.DoesNotExist:
        # Try ActionTaskHistory as fallback for tests
        try:
            from data.models import ActionTaskHistory

            transfer_history = ActionTaskHistory.objects.get(id=history_id)
        except ActionTaskHistory.DoesNotExist:
            pass
        except ImportError:
            pass
    except Exception:
        if lang == "ja":
            Notification.objects.create(
                workspace=workspace,
                user=user,
                message="履歴ファイルの読み込みに失敗しました。",
                type="error",
            )
        else:
            Notification.objects.create(
                workspace=workspace,
                user=user,
                message="Failed to load the history file.",
                type="error",
            )
        return

    try:
        mapping_storage = ImportMappingFields.objects.get(id=imf_id)
    except ImportMappingFields.DoesNotExist:
        message = "Failed to load the uploaded file."
        if lang == "ja":
            message = "アップロードしたファイルの読み込みに失敗しました"
        Notification.objects.create(
            workspace=workspace, user=user, message=message, type="error"
        )

        transfer_history.status = "canceled"
        transfer_history.error_message = message
        transfer_history.save()
        return

    import_data_types = mapping_storage.import_data_type
    how_to_import = mapping_storage.entry_method
    key_field = mapping_storage.key_field
    custom_key_field = mapping_storage.custom_key_field
    component_key_field = mapping_storage.component_key_field
    property_object_relation_key_field = (
        mapping_storage.property_object_relation_key_field
    )

    if isinstance(import_data_types, str):
        import_data_types = ast.literal_eval(import_data_types)
    if isinstance(how_to_import, str):
        how_to_import = ast.literal_eval(how_to_import)
    if isinstance(key_field, str):
        key_field = ast.literal_eval(key_field)

    if property_object_relation_key_field and isinstance(
        property_object_relation_key_field, str
    ):
        property_object_relation_key_field = ast.literal_eval(
            property_object_relation_key_field
        )

    data_input_pair = mapping_storage.input_pair

    page_object = None
    custom_object = CustomObject.objects.filter(
        slug=mapping_storage.object_type
    ).first()
    if custom_object:
        page_object = get_page_object("custom_object")
    else:
        page_object = get_page_object(mapping_storage.object_type)
    base_model = page_object["base_model"]
    base_model_meta_fields = base_model._meta.fields
    base_model_column_dict = {}
    for base_model_meta_field in base_model_meta_fields:
        base_model_column_dict[base_model_meta_field.name] = (
            base_model_meta_field.get_internal_type()
        )

    # Handle both local files and remote URLs
    file_url = mapping_storage.file.url
    if file_url.startswith("/"):
        # Local file path - read directly from file system
        try:
            with open(mapping_storage.file.path, "rb") as f:
                csv_data = f.read()
            response = type(
                "MockResponse", (), {"status_code": 200, "content": csv_data}
            )()
        except (OSError, AttributeError) as e:
            logger.error(f"Error reading local file: {e}")
            response = type("MockResponse", (), {"status_code": 404})()
    else:
        # Remote URL - use requests
        response = requests.get(file_url)
    df = None

    if response.status_code == 200:
        csv_data = response.content
        csv_data_str = None

        # Try to detect encoding
        try:
            result = chardet.detect(csv_data)
            encoding = result["encoding"] if result else None
        except Exception as e:
            logger.error(f"Encoding detection failed: {e}")
            encoding = None

        # Try decoding with detected encoding first
        if encoding:
            try:
                if encoding.lower() == "shift_jis":
                    csv_data_str = csv_data.decode("shift_jis")
                else:
                    csv_data_str = csv_data.decode(encoding)
            except UnicodeDecodeError as e:
                logger.error(
                    f"Decoding with detected encoding '{encoding}' failed: {e}"
                )
                csv_data_str = None

        # Fallback to UTF-8 if detected encoding failed or wasn't detected
        if csv_data_str is None:
            try:
                csv_data_str = csv_data.decode("utf-8")
            except UnicodeDecodeError as e:
                logger.error(f"UTF-8 decoding failed: {e}")
                # Final fallback to shift_jis with error handling
                try:
                    csv_data_str = csv_data.decode("shift_jis", errors="ignore")
                except Exception as e:
                    logger.error(f"Shift_JIS fallback failed: {e}")
                    # Last resort: use latin-1 which can decode any byte sequence
                    csv_data_str = csv_data.decode("latin-1", errors="replace")

        try:
            df = pd.read_csv(StringIO(csv_data_str), dtype=str)
        except Exception as e:
            logger.error(f"Error parsing CSV: {e}")
            set_checkpoint_details(
                transfer_history,
                {
                    "idx": 0,
                    "status": "completed",
                    "object_id": None,
                    "imported": [],
                },
            )
            transfer_history.status = "error"
            if hasattr(transfer_history, "end_date"):
                transfer_history.end_date = timezone.now()
            # Set output_data for ActionTaskHistory if it has that field
            if hasattr(transfer_history, "output_data"):
                transfer_history.output_data = {"status": "error", "message": str(e)}
            transfer_history.save()
            raise Exception(f"Error parsing CSV: {e}")

    else:
        transfer_history.status = "error"
        transfer_history.end_date = timezone.now()
        # Set output_data for ActionTaskHistory if it has that field
        if hasattr(transfer_history, "output_data"):
            transfer_history.output_data = {
                "status": "error",
                "message": f"Error loading CSV: {response.status_code}",
            }
        transfer_history.save()
        raise Exception(f"Error loading CSV: {response.status_code}")

    # Check if DataFrame is empty
    if df.empty:
        set_checkpoint_details(
            transfer_history,
            {
                "idx": 0,
                "status": "completed",
                "object_id": None,
                "imported": [],
            },
        )
        transfer_history.status = "completed"
        transfer_history.end_date = timezone.now()
        transfer_history.save()
        return

    df = df.loc[:, ~df.columns.isna()]
    df = df.loc[:, df.columns.notna()]
    df = df.loc[:, ~df.columns.str.match(r"Unnamed: \d+")]
    df = df.dropna(axis=0, how="all")
    df = df.fillna("")

    # Replace NaN with None in object (string) columns
    df[df.select_dtypes(include="object").columns] = df.select_dtypes(
        include="object"
    ).where(df.notna(), None)

    df = df.reset_index(drop=True)

    # initialize data
    data_dictionary = {}
    for idx, import_data_type in enumerate(import_data_types):
        data_dictionary[import_data_type] = {
            "how_to_import": how_to_import[idx],
            "key_field": key_field[idx],
            "default_property": {},
            "custom_property": {},
            "key_property": {},
            "property_object_relation_key_field": {},
        }

    column_to_be_merged = []
    key_column = []
    if "item" in import_data_types:
        component_prop_ids = ShopTurboItemsNameCustomField.objects.filter(
            workspace=workspace, type="components"
        ).values_list("id", flat=True)
        for component_prop_id in component_prop_ids:
            for k, val in data_input_pair.items():
                if val[0] != "item":
                    continue
                if str(component_prop_id) in val[1]:
                    column_to_be_merged.append(k)
                for kf in key_field:
                    if kf == val[1]:
                        key_column.append(k)

    # convert to record
    if column_to_be_merged:
        # For component imports, we should NOT merge rows with semicolons
        # Instead, we should process each row as a separate component relationship

        # Group rows by key columns to create component relationships
        grouped_rows = {}
        for idx, row in df.iterrows():
            # Create a key from the key columns
            key_values = tuple(str(row[k]).lower() for k in key_column)

            if key_values not in grouped_rows:
                grouped_rows[key_values] = {"base_row": row.copy(), "components": []}

            # Extract component information from this row
            component_info = {}
            for k in column_to_be_merged:
                component_info[k] = row[k]
            grouped_rows[key_values]["components"].append(component_info)

        # Create final rows with component data properly structured
        merged_rows = []
        for key_values, group_data in grouped_rows.items():
            base_row = group_data["base_row"]
            components = group_data["components"]

            # For each component, create the semicolon-separated values
            for k in column_to_be_merged:
                component_values = [
                    str(comp[k])
                    for comp in components
                    if comp[k] and str(comp[k]).strip()
                ]
                if component_values:
                    merged_value = ";".join(component_values)
                    base_row[k] = merged_value

            merged_rows.append(base_row)

        row_dicts = [row.to_dict() for row in merged_rows]
    else:
        row_dicts = df.to_dict("records")

    normalized_rows = []
    for row in row_dicts:
        normalized_row = {}
        for key, value in row.items():
            # Normalize the key by stripping whitespace and special characters
            normalized_key = str(key).strip()  # Ensure key is a string
            # Replace full-width space (ideographic space)
            normalized_key = normalized_key.replace("\u3000", " ")
            normalized_key = (
                normalized_key.replace("\n", "").replace("\r", "").replace("\t", "")
            ).strip()  # Strip again after replacements
            normalized_row[normalized_key] = value
        normalized_rows.append(normalized_row)

    normalized_input_pair = {}
    # data_input_pair comes from mapping_storage.input_pair, which is set in csv_upload_submit
    # The keys of mapping_storage.input_pair are original file columns from the mapping UI step.
    for key, value in data_input_pair.items():
        normalized_key = str(key).strip()  # Ensure key is a string
        # Replace full-width space (ideographic space)
        normalized_key = normalized_key.replace("\u3000", " ")
        normalized_key = (
            normalized_key.replace("\n", "").replace("\r", "").replace("\t", "")
        ).strip()  # Strip again after replacements
        normalized_input_pair[normalized_key] = value

    row_dicts = normalized_rows
    data_input_pair = normalized_input_pair

    # Validate that we have mapping for all CSV columns
    if row_dicts:
        csv_columns = set(row_dicts[0].keys())
        mapped_columns = set(data_input_pair.keys())
        unmapped_columns = csv_columns - mapped_columns

        if unmapped_columns:
            logger.warning(
                f"Warning: Found unmapped columns in CSV: {unmapped_columns}"
            )
            # Log to task history for user visibility
            if transfer_history:
                current_details = get_checkpoint_details(transfer_history) or {}
                current_details["unmapped_columns"] = list(unmapped_columns)
                set_checkpoint_details(transfer_history, current_details)
                transfer_history.save()

    # Generate Dictionary for writing to Database
    row_pos = 0
    have_error = False
    error_csv_buffer = StringIO()
    error_writer = csv.writer(error_csv_buffer)
    error_writer.writerow(
        [k for k in row_dicts[0].keys()]
        + [
            "エラーメッセージ"
            if user.verification.language == "ja"
            else "error message"
        ]
    )
    # Handle both ActionTaskHistory and TransferHistory
    if not get_checkpoint_details(transfer_history):
        set_checkpoint_details(
            transfer_history,
            {
                "idx": 0,
                "status": "running",
                "object_id": None,
                "imported": [],
            },
        )
        transfer_history.save()

    success_number = 0
    if hasattr(transfer_history, "success_number") and transfer_history.success_number:
        success_number = transfer_history.success_number
    for idx, row in enumerate(row_dicts):
        errors = []  # Reset errors for each row to prevent error accumulation
        # Safely access checkpoint_details with default values
        checkpoint_details = get_checkpoint_details(transfer_history) or {}
        current_idx = checkpoint_details.get("idx", 0)
        current_status = checkpoint_details.get("status", "running")

        if idx < current_idx or current_status == "completed":
            continue
        if current_idx != idx:
            checkpoint_details = get_checkpoint_details(transfer_history) or {}
            checkpoint_details["idx"] = idx
            checkpoint_details["status"] = "running"
            checkpoint_details["object_id"] = None
            set_checkpoint_details(transfer_history, checkpoint_details)
            transfer_history.save()

        row_pos = idx
        progress = 100 * (row_pos + 1) / len(row_dicts)
        if hasattr(transfer_history, "progress"):
            transfer_history.progress = progress
        transfer_history.save()

        # Reset data dictionary for this row
        for import_type in import_data_types:
            if import_type in data_dictionary:
                data_dictionary[import_type]["default_property"] = {}
                data_dictionary[import_type]["custom_property"] = {}
                data_dictionary[import_type]["key_property"] = {}
                data_dictionary[import_type]["property_object_relation_key_field"] = {}
                if "selected_fields" not in data_dictionary[import_type]:
                    data_dictionary[import_type]["selected_fields"] = set()
                else:
                    data_dictionary[import_type]["selected_fields"] = set()

        for col_idx, key in enumerate(row):
            # Check if the key exists in data_input_pair to prevent KeyError
            if key not in data_input_pair:
                # Log the missing key for debugging
                logger.warning(
                    f"Warning: Column '{key}' not found in mapping configuration. Skipping this column."
                )
                continue

            try:
                import_as = data_input_pair[key][0]
                sanka_properties = data_input_pair[key][1]
                ignore = data_input_pair[key][2]
                # Debug column processing if needed
                # if "supplier" in key.lower() or "supplier" in str(sanka_properties).lower():
                #     print(f"[DEBUG CSV MAPPING] Processing column '{key}': import_as='{import_as}', sanka_properties='{sanka_properties}', ignore='{ignore}', value='{row[key]}'")
            except (IndexError, TypeError) as e:
                # Handle malformed mapping data
                logger.error(
                    f"Error: Malformed mapping data for column '{key}': {data_input_pair.get(key, 'N/A')}. Error: {e}"
                )
                continue

            # Validate that import_as exists in data_dictionary
            if import_as not in data_dictionary:
                logger.warning(
                    f"Warning: Import type '{import_as}' not found in data dictionary. Skipping column '{key}'."
                )
                continue

            if ignore == "True":
                continue

            # Add this field to the selected fields set
            data_dictionary[import_as]["selected_fields"].add(sanka_properties)

            if str(row[key]) == "nan":
                row[key] = None

            if not row[key]:
                if "tax" in sanka_properties or "price" in sanka_properties:
                    row[key] = 0
                elif sanka_properties in base_model_column_dict:
                    if (
                        "float" in base_model_column_dict[sanka_properties].lower()
                        or "integer" in base_model_column_dict[sanka_properties].lower()
                    ):
                        row[key] = 0

            if is_valid_uuid(sanka_properties.split("|")[0]):
                # look at the key
                # logger.info(
                #     f"[DEBUG] '{sanka_properties}' is a valid UUID, adding to custom_property"
                # )

                # Special handling for component fields
                # Check if this UUID corresponds to a component field and if the column name suggests it's component data
                field_uuid = sanka_properties.split("|")[0]
                try:
                    custom_field = ShopTurboItemsNameCustomField.objects.get(
                        id=field_uuid, workspace=workspace
                    )
                    if custom_field.type == "components" and key in [
                        "品番コード",
                        "component_code",
                        "component_id",
                    ]:
                        # logger.info(
                        #     f"[DEBUG] Detected component field mapping: {key} -> {custom_field.name} (type: {custom_field.type})"
                        # )
                        # This is component data, treat it as such
                        data_dictionary[import_as]["custom_property"][
                            sanka_properties
                        ] = row[key]
                    else:
                        data_dictionary[import_as]["custom_property"][
                            sanka_properties
                        ] = row[key]
                except ShopTurboItemsNameCustomField.DoesNotExist:
                    # logger.info(
                    #     f"[DEBUG] Custom field {field_uuid} not found, treating as regular property"
                    # )
                    data_dictionary[import_as]["custom_property"][sanka_properties] = (
                        row[key]
                    )
            else:
                # look at the key
                # logger.info(
                #     f"[DEBUG] '{sanka_properties}' is not a UUID, checking if it's a special mapping type"
                # )

                # Check if this is a special component mapping
                if sanka_properties == "構成":
                    # logger.info(
                    #     "[DEBUG] Found component mapping '構成', need to find the actual component field UUID"
                    # )
                    # Find the component custom field for this workspace
                    component_field = ShopTurboItemsNameCustomField.objects.filter(
                        workspace=workspace, type="components"
                    ).first()

                    if component_field:
                        # logger.info(
                        #     f"[DEBUG] Found component field: {component_field.name} (ID: {component_field.id})"
                        # )
                        # Use the actual component field UUID instead of "構成"
                        data_dictionary[import_as]["custom_property"][
                            str(component_field.id)
                        ] = row[key]
                        # logger.info(
                        #     f"[DEBUG] Added component mapping with UUID: {component_field.id} -> {row[key]}"
                        # )
                    # else:
                    #     logger.info(
                    #         "[DEBUG] No component field found in workspace, skipping component mapping"
                    #     )
                else:
                    if "id" in key.lower():
                        if row[key]:
                            # Check if the value is numeric without hyphens
                            if str(row[key]).isdigit():
                                row[key] = int(row[key])
                            # If it contains hyphens, keep it as a string
                            elif "-" in str(row[key]):
                                row[key] = str(row[key])
                            else:
                                # Validate against model field type for better error handling
                                if sanka_properties in base_model_column_dict:
                                    field_type = base_model_column_dict[
                                        sanka_properties
                                    ].lower()
                                    if "integer" in field_type:
                                        # For integer fields, validate that the value can be converted
                                        try:
                                            row[key] = int(row[key])
                                        except (ValueError, TypeError):
                                            # Keep the original value for error reporting in import functions
                                            pass
                    data_dictionary[import_as]["default_property"][sanka_properties] = (
                        row[key]
                    )
                    # Debug order_id processing if needed
                    # if sanka_properties == 'order_id':
                    #     print(f"DEBUG: Set order_id={row[key]} in data_dictionary[{import_as}]['default_property']")

        # insert object related properties
        for import_data_type in import_data_types:
            if import_data_type in data_dictionary:
                data_dictionary[import_data_type][
                    "property_object_relation_key_field"
                ] = property_object_relation_key_field

        # Checking key field
        if data_dictionary:
            for import_data_type in import_data_types:
                if (
                    data_dictionary[import_data_type]["key_field"]
                    in data_dictionary[import_data_type]["custom_property"]
                ):
                    data_dictionary[import_data_type]["key_property"][
                        data_dictionary[import_data_type]["key_field"]
                    ] = data_dictionary[import_data_type]["custom_property"][
                        data_dictionary[import_data_type]["key_field"]
                    ]
                    if (
                        data_dictionary[import_data_type]["how_to_import"]
                        != "create_and_update"
                    ):
                        data_dictionary[import_data_type]["custom_property"].pop(
                            data_dictionary[import_data_type]["key_field"]
                        )

                if (
                    data_dictionary[import_data_type]["key_field"]
                    in data_dictionary[import_data_type]["default_property"]
                ):
                    data_dictionary[import_data_type]["key_property"][
                        data_dictionary[import_data_type]["key_field"]
                    ] = data_dictionary[import_data_type]["default_property"][
                        data_dictionary[import_data_type]["key_field"]
                    ]
                    if "id" in data_dictionary[import_data_type]["key_field"].lower():
                        if data_dictionary[import_data_type]["key_property"][
                            data_dictionary[import_data_type]["key_field"]
                        ]:
                            if is_valid_uuid(
                                data_dictionary[import_data_type]["key_property"][
                                    data_dictionary[import_data_type]["key_field"]
                                ]
                            ):
                                data_dictionary[import_data_type]["key_property"][
                                    data_dictionary[import_data_type]["key_field"]
                                ] = str(
                                    data_dictionary[import_data_type]["key_property"][
                                        data_dictionary[import_data_type]["key_field"]
                                    ]
                                )
                            else:
                                try:
                                    # Try to convert to integer, but handle hyphenated IDs
                                    value = data_dictionary[import_data_type][
                                        "key_property"
                                    ][data_dictionary[import_data_type]["key_field"]]
                                    if "-" in str(value):
                                        # Keep hyphenated IDs as strings
                                        data_dictionary[import_data_type][
                                            "key_property"
                                        ][
                                            data_dictionary[import_data_type][
                                                "key_field"
                                            ]
                                        ] = str(value)
                                    else:
                                        # Convert to integer if possible
                                        data_dictionary[import_data_type][
                                            "key_property"
                                        ][
                                            data_dictionary[import_data_type][
                                                "key_field"
                                            ]
                                        ] = int(value)
                                except ValueError:
                                    # If conversion fails, keep as string
                                    data_dictionary[import_data_type]["key_property"][
                                        data_dictionary[import_data_type]["key_field"]
                                    ] = str(
                                        data_dictionary[import_data_type][
                                            "key_property"
                                        ][
                                            data_dictionary[import_data_type][
                                                "key_field"
                                            ]
                                        ]
                                    )
                    if (
                        data_dictionary[import_data_type]["how_to_import"]
                        != "create_and_update"
                    ):
                        data_dictionary[import_data_type]["default_property"].pop(
                            data_dictionary[import_data_type]["key_field"]
                        )

        if not data_dictionary:
            continue

        # Write to Database

        # Associations inventory and items
        # 1. Reorder data dictionary-> item first and then inventory
        data_dictionary = reorder_dict_with_item_first(data_dictionary)

        contact = None
        company = None
        item = None
        inventory = None
        production = None

        for key in data_dictionary.keys():
            # logger.info(f"key: {key} {data_dictionary[key]}")
            general_obj = None

            if (
                not data_dictionary[key]["default_property"]
                and not data_dictionary[key]["custom_property"]
            ):
                continue

            try:
                if key == "order":
                    # function order
                    general_obj, order_errors = write_order(
                        workspace,
                        user,
                        data_dictionary[key].copy(),
                        transfer_history.id,
                    )
                    if order_errors:
                        errors.extend(order_errors)
                elif key == "contact":
                    # function contact
                    contact = write_contact(
                        workspace, user, data_dictionary[key], transfer_history.id
                    )
                    general_obj = contact
                elif key == TYPE_OBJECT_COMPANY:
                    # function company
                    company = write_company(
                        workspace, user, data_dictionary[key], transfer_history.id
                    )
                    general_obj = company
                elif key == "item":
                    # function item
                    if data_dictionary[key]["how_to_import"] != "update":
                        if not has_quota(workspace, ITEM_USAGE_CATEGORY):
                            break
                        workspace_available_storage = get_workspace_available_storage(
                            workspace, ITEM_USAGE_CATEGORY
                        )
                        if (
                            workspace_available_storage is not None
                            and workspace_available_storage <= 0
                        ):
                            break
                    data_dictionary[key]["custom_key_field"] = custom_key_field
                    data_dictionary[key]["component_key_field"] = (
                        component_key_field  # Add component key field
                    )
                    item_result = write_item(
                        workspace, user, data_dictionary[key], transfer_history.id
                    )
                    # Defensive programming: handle case where write_item might return unexpected format
                    if item_result is None:
                        item, item_errors = None, ["Item creation failed"]
                    elif isinstance(item_result, tuple) and len(item_result) == 2:
                        item, item_errors = item_result
                    else:
                        # Unexpected return format
                        item, item_errors = (
                            None,
                            [
                                f"Unexpected return format from write_item: {type(item_result)}"
                            ],
                        )

                    general_obj = item
                    if item_errors:
                        errors.extend(item_errors)
                    transfer_history.refresh_from_db()
                elif key == TYPE_OBJECT_INVENTORY:
                    # function item
                    if not has_quota(workspace, INVENTORY_USAGE_CATEGORY):
                        break
                    workspace_available_storage = get_workspace_available_storage(
                        workspace, INVENTORY_USAGE_CATEGORY
                    )
                    if (
                        workspace_available_storage is None
                        or workspace_available_storage > 0
                    ):
                        inventory_result = write_inventory(
                            workspace, user, data_dictionary[key], transfer_history.id
                        )
                        # Defensive programming: handle case where write_inventory might return unexpected format
                        if inventory_result is None:
                            inventory, inventory_errors = (
                                None,
                                ["Inventory creation failed"],
                            )
                        elif (
                            isinstance(inventory_result, tuple)
                            and len(inventory_result) == 2
                        ):
                            inventory, inventory_errors = inventory_result
                        else:
                            # Unexpected return format
                            inventory, inventory_errors = (
                                None,
                                [
                                    f"Unexpected return format from write_inventory: {type(inventory_result)}"
                                ],
                            )

                        if inventory_errors:
                            errors.extend(inventory_errors)
                        general_obj = inventory
                elif key == "production":
                    production = write_production(
                        workspace, user, data_dictionary[key], transfer_history.id
                    )
                    general_obj = production
                elif key == TYPE_OBJECT_BILL:
                    bill = write_bill(
                        workspace, user, data_dictionary[key], transfer_history.id
                    )
                    general_obj = bill
                elif key == TYPE_OBJECT_CASE:
                    case = write_case(
                        workspace, user, data_dictionary[key], transfer_history.id
                    )
                    general_obj = case
                elif key == TYPE_OBJECT_INVENTORY_TRANSACTION:
                    inventory_transaction = write_inventory_transaction(
                        workspace, user, lang, data_dictionary[key], transfer_history.id
                    )
                    general_obj = inventory_transaction
                elif key == TYPE_OBJECT_INVENTORY_WAREHOUSE:
                    location = write_location(
                        workspace, user, lang, data_dictionary[key], transfer_history.id
                    )
                    general_obj = location
                elif key == TYPE_OBJECT_SUBSCRIPTION:
                    subscription = write_subscription(
                        workspace, user, lang, data_dictionary[key], transfer_history.id
                    )
                    general_obj = subscription
                elif (
                    key == TYPE_OBJECT_ESTIMATE
                    or key == TYPE_OBJECT_INVOICE
                    or key == TYPE_OBJECT_DELIVERY_NOTE
                    or key == TYPE_OBJECT_RECEIPT
                    or key == TYPE_OBJECT_SLIP
                ):
                    # Soon, can support multiple billing, like estimate and others
                    bill = write_dynamic_commerce(
                        workspace,
                        user,
                        lang,
                        data_dictionary[key],
                        key,
                        transfer_history.id,
                    )
                    general_obj = bill
                elif key == TYPE_OBJECT_PURCHASE_ORDER:
                    # function purchase order
                    purchase_order = write_purchase_order(
                        workspace,
                        user,
                        data_dictionary[key].copy(),
                        transfer_history.id,
                    )
                    general_obj = purchase_order
                elif key == TYPE_OBJECT_EXPENSE:
                    # function expense
                    expense = write_expense(
                        workspace,
                        user,
                        data_dictionary[key].copy(),
                        transfer_history.id,
                    )
                    general_obj = expense
                elif key == TYPE_OBJECT_JOURNAL:
                    # function journal
                    journal = write_journal(
                        workspace,
                        user,
                        data_dictionary[key].copy(),
                        transfer_history.id,
                    )
                    general_obj = journal
                elif key == TYPE_OBJECT_COMMERCE_METER:
                    # function Commerce Meter
                    commerce_meter, errors = write_commerce_meter(
                        workspace,
                        user,
                        data_dictionary[key].copy(),
                        transfer_history.id,
                    )
                    general_obj = commerce_meter
                elif key == custom_object.slug:
                    obj = write_dynamic_custom_object(
                        workspace,
                        custom_object,
                        user,
                        lang,
                        data_dictionary[key],
                        "custom_object",
                        transfer_history.id,
                    )
                    general_obj = obj

                if general_obj:
                    success_number += 1
                    transfer_history.refresh_from_db()
                    if hasattr(transfer_history, "success_number"):
                        transfer_history.success_number = success_number
                    transfer_history.save()
            except Exception as e:
                traceback.print_exc()
                DiscordNotification().send_message(
                    f"[Import job] [{key}] - Row Failed: {traceback.format_exc()}",
                    mention_owner=True,
                )
                if lang == "ja":
                    errors = [f"{idx + 1}行目: " + str(e)]
                else:
                    errors = [f"Row {idx + 1}: " + str(e)]
                transfer_history.refresh_from_db()
                checkpoint_details = get_checkpoint_details(transfer_history) or {}
                checkpoint_details["status"] = "error"
                set_checkpoint_details(transfer_history, checkpoint_details)
                transfer_history.status = "error"
                transfer_history.save()

            if errors:
                have_error = True
                # Ensure all errors are strings to prevent TypeError during join
                error_strings = [str(error) for error in errors]
                error_val = "\n".join(error_strings)
                logger.error([row[k] for k in row] + [error_val])
                error_writer.writerow([row[k] for k in row] + [error_val])

        # connect
        # if order:
        #     if contact:order.contact=contact
        #     if company:order.company=company
        #     if item:

        #         if not item.currency:
        #             if lang == 'ja':
        #                 item.currency = 'JPY'
        #             else:
        #                 item.currency = 'USD'
        #             item.save()

        #         lang = user.verification.language
        #         if not order.currency:
        #             if not item.currency:
        #                 if lang == 'ja':
        #                     order.currency = 'JPY'
        #                 else:
        #                     order.currency = 'USD'
        #             else:
        #                 order.currency = item.currency

        #             order.save()

        #         items_price = ShopTurboItemsPrice.objects.filter(item=item,default=True).first()
        #         if not items_price:
        #             items_price = ShopTurboItemsPrice.objects.create(item=item,default=True)
        #         items_price.default=True
        #         if not items_price.price: items_price.price=0
        #         items_price.currency = item.currency
        #         items_price.save()

        #         shopturbo_item_order,_ = ShopTurboItemsOrders.objects.get_or_create(
        #             item=item,
        #             order=order
        #         )
        #         if not order.number_item: order.number_item=1

        #         shopturbo_item_order.currency = item.currency
        #         shopturbo_item_order.number_item = order.number_item
        #         shopturbo_item_order.item_price_order = items_price.price
        #         shopturbo_item_order.item_price = items_price

        #         if float(shopturbo_item_order.number_item) > 0.0:
        #             shopturbo_item_order.total_price = float(shopturbo_item_order.item_price_order) * float(shopturbo_item_order.number_item)
        #         else:
        #             shopturbo_item_order.total_price = shopturbo_item_order.item_price_order

        #         shopturbo_item_order.save()

        #         setattr(order, "order_type", "item_order")
        #         order.currency = item.currency
        #         order.save()
        #     else:
        #         if order.shopturboitemsorders_set.all():
        #             setattr(order, "order_type", "item_order")
        #             order.save()
        #         else:
        #             setattr(order, "order_type", "manual_order")
        #             order.save()

    transfer_history.refresh_from_db()
    checkpoint_details = get_checkpoint_details(transfer_history) or {}
    checkpoint_details["status"] = "completed"
    set_checkpoint_details(transfer_history, checkpoint_details)
    transfer_history.save()

    if have_error:
        # utf-8-sig will help excel to recognize that the file is utf-8 encoded.
        error_data = error_csv_buffer.getvalue().encode("utf-8-sig")
        # logger.error(error_data)
        content_file = DjangoContentFile(error_data)

        # Check if the model has error_file field (TransferHistory) or use result_file (ActionTaskHistory)
        if hasattr(transfer_history, "error_file"):
            transfer_history.error_file.save(str(uuid.uuid4()) + ".csv", content_file)
        elif hasattr(transfer_history, "result_file"):
            transfer_history.result_file.save(str(uuid.uuid4()) + ".csv", content_file)

        # Update status to error when errors occur
        checkpoint_details = get_checkpoint_details(transfer_history) or {}
        checkpoint_details["status"] = "error"
        set_checkpoint_details(transfer_history, checkpoint_details)
        transfer_history.save()

        if lang == "ja":
            Notification.objects.create(
                workspace=workspace,
                user=user,
                message="エラー: インポート履歴のcsvファイルエラーを参照してください",
                type="error",
            )
        else:
            Notification.objects.create(
                workspace=workspace,
                user=user,
                message="Error: Please refer to csv file error on import history",
                type="error",
            )

    progress = 100 * (row_pos + 1) / len(row_dicts)
    if hasattr(transfer_history, "progress"):
        if hasattr(transfer_history, "progress"):
            transfer_history.progress = progress
    if hasattr(transfer_history, "success_number"):
        transfer_history.success_number = success_number
    if hasattr(transfer_history, "total_number"):
        transfer_history.total_number = len(row_dicts)
    if hasattr(transfer_history, "end_date"):
        transfer_history.end_date = timezone.now()
    transfer_history.save()

    if transfer_history.status != "canceled":
        transfer_history.status = "completed"
        transfer_history.save()
