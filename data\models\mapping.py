from django.db import models
from django.utils import timezone

from data.models.constant import *
from data.constants.constant import *


class ShopTurboOrdersMappingFields(models.Model):
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    platform = models.CharField(
        choices=PLATFORM, max_length=100, null=True, blank=True)
    channel = models.ForeignKey(
        "Channel", on_delete=models.CASCADE, null=True, blank=True
    )
    input_data = models.JSONField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)

class ShopTurboOrdersAssociationMappingFields(models.Model):
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    platform = models.CharField(
        choices=PLATFORM, max_length=100, null=True, blank=True)
    channel = models.ForeignKey(
        "Channel", on_delete=models.CASCADE, null=True, blank=True
    )
    input_data = models.JSO<PERSON>ield(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)

class ShopTurboOrdersStatusMappingFields(models.Model):
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    platform = models.CharField(
        choices=PLATFORM, max_length=100, null=True, blank=True)
    channel = models.ForeignKey(
        "Channel", on_delete=models.CASCADE, null=True, blank=True
    )
    input_data = models.JSONField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)
    function_type = models.CharField(max_length=100, null=True, blank=True)

class ShopTurboOrdersMappingFieldsConditional(models.Model):
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    platform = models.CharField(
        choices=PLATFORM, max_length=100, null=True, blank=True)
    channel = models.ForeignKey(
        "Channel", on_delete=models.CASCADE, null=True, blank=True
    )
    input_data = models.JSONField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


class DealsMappingFields(models.Model):
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    platform = models.CharField(
        choices=PLATFORM, max_length=100, null=True, blank=True)
    channel = models.ForeignKey(
        "Channel", on_delete=models.CASCADE, null=True, blank=True
    )
    input_data = models.JSONField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)

class DealsMappingOrderFields(models.Model):
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    platform = models.CharField(
        choices=PLATFORM, max_length=100, null=True, blank=True)
    input_data = models.JSONField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)

class DealsAssociationMappingFields(models.Model):
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    platform = models.CharField(
        choices=PLATFORM, max_length=100, null=True, blank=True)
    channel = models.ForeignKey(
        "Channel", on_delete=models.CASCADE, null=True, blank=True
    )
    input_data = models.JSONField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)

class InvoiceMappingFields(models.Model):
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    platform = models.CharField(
        choices=PLATFORM, max_length=100, null=True, blank=True)
    channel = models.ForeignKey(
        "Channel", on_delete=models.CASCADE, null=True, blank=True
    )
    input_data = models.JSONField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)
    custom_object_name = models.CharField(max_length=200, null=True, blank=True)

class InvoiceAssociationMappingFields(models.Model):
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    platform = models.CharField(
        choices=PLATFORM, max_length=100, null=True, blank=True)
    channel = models.ForeignKey(
        "Channel", on_delete=models.CASCADE, null=True, blank=True
    )
    input_data = models.JSONField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)
    custom_object_name = models.CharField(max_length=200, null=True, blank=True)

class SubscriptionMappingFields(models.Model):
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    platform = models.CharField(
        choices=PLATFORM, max_length=100, null=True, blank=True)
    channel = models.ForeignKey(
        "Channel", on_delete=models.CASCADE, null=True, blank=True
    )
    input_data = models.JSONField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)
    custom_object_name = models.CharField(max_length=200, null=True, blank=True)

class SubscriptionAssociationMappingFields(models.Model):
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    platform = models.CharField(
        choices=PLATFORM, max_length=100, null=True, blank=True)
    channel = models.ForeignKey(
        "Channel", on_delete=models.CASCADE, null=True, blank=True
    )
    input_data = models.JSONField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)
    custom_object_name = models.CharField(max_length=200, null=True, blank=True)

class ContactsAssociationMappingFields(models.Model):
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    platform = models.CharField(
        choices=PLATFORM, max_length=100, null=True, blank=True)
    channel = models.ForeignKey(
        "Channel", on_delete=models.CASCADE, null=True, blank=True
    )
    input_data = models.JSONField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)
    
class CompanyAssociationMappingFields(models.Model):
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    platform = models.CharField(
        choices=PLATFORM, max_length=100, null=True, blank=True)
    channel = models.ForeignKey(
        "Channel", on_delete=models.CASCADE, null=True, blank=True
    )
    input_data = models.JSONField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)

class ImportMappingFields(models.Model):
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    object_type = models.CharField(max_length=200)
    import_data_type = models.CharField(max_length=500, null=True, blank=True)
    input_pair = models.JSONField(null=True, blank=True)
    file = models.FileField(upload_to="csv-entry-file", blank=True, null=True)
    entry_method = models.CharField(max_length=50, blank=True, null=True)
    key_field = models.JSONField(blank=True, null=True)
    custom_key_field = models.CharField(max_length=250, blank=True, null=True)
    component_key_field = models.CharField(max_length=250, blank=True, null=True)  # New field for component key
    property_object_relation_key_field = models.CharField(
        max_length=250, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)

class ExportImportConfiguration(models.Model):
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    object_type = models.CharField(max_length=200, blank=True, null=True)
    platform = models.CharField(
        choices=PLATFORM, max_length=100, null=True, blank=True)
    input_data = models.JSONField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)
