import ast
from datetime import timedelta
import traceback

from asgiref.sync import sync_to_async
from hatchet_sdk import Context

from data.models import TransferHistory
from utils.bgjobs.handler import (
    aio_set_bg_job_completed,
    aio_set_bg_job_failed,
    aio_set_bg_job_running,
)
from utils.logger import logger
from utils.makeshop import import_makeshop_orders as makeshop_import_util

from ..models import ImportOrdersMakeshopPayload
from ..workflows import import_makeshop_orders


@import_makeshop_orders.task(
    name="ImportMakeshopOrdersTask",
    execution_timeout=timedelta(hours=2),
    schedule_timeout=timedelta(hours=1),
)
async def import_makeshop_orders_task(
    input: ImportOrdersMakeshopPayload, ctx: Context
) -> dict:
    """
    Child task for importing Makeshop orders
    """
    logger.info("Run Makeshop orders import child task")
    logger.info(f"Input: {input}")

    history_id = input.history_id
    task = None

    try:
        task = await TransferHistory.objects.select_related("workspace", "user").aget(
            id=history_id
        )
        task.keep_alive = True
        await task.asave()
    except TransferHistory.DoesNotExist:
        logger.warning("TransferHistory does not exist")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])

    if input.background_job_id:
        await aio_set_bg_job_running(input.background_job_id)

    try:
        # Process mapping fields
        mapping_custom_fields = (
            input.mapping_custom_fields
            if input.mapping_custom_fields and input.mapping_custom_fields != "None"
            else {}
        )
        mapping_status_custom_fields = (
            input.mapping_status_custom_fields
            if input.mapping_status_custom_fields
            and input.mapping_status_custom_fields != "None"
            else {}
        )

        # Parse string mappings if needed
        if isinstance(mapping_custom_fields, str):
            mapping_custom_fields = ast.literal_eval(mapping_custom_fields)
        if isinstance(mapping_status_custom_fields, str):
            mapping_status_custom_fields = ast.literal_eval(
                mapping_status_custom_fields
            )

        # Call the Makeshop import utility
        await sync_to_async(makeshop_import_util)(
            input.channel_id,
            mapping_custom_fields,
            key_item_field=input.key_item_field,
            how_to_import=input.how_to_import,
            import_order_date=input.import_order_date,
            mapping_status_custom_fields=mapping_status_custom_fields,
            default_customer=input.default_customer,
            import_filter=input.import_filter,
        )

        logger.info("Successfully imported Makeshop orders")
        if task:
            await task.arefresh_from_db()
            task.status = "completed"
            await task.asave(update_fields=["status"])

        if input.background_job_id:
            await aio_set_bg_job_completed(input.background_job_id)

    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error importing Makeshop orders: {str(e)}")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])
