from utils.hubspot.utils_hubspot import get_records_object, refresh_hubspot_token
import ast
import json
import traceback

from dateutil import parser
from hubspot import HubSpot
from hubspot.crm import associations
from hubspot.crm.associations import ApiException as AssociationsApiException
from hubspot.crm.associations import BatchInputPublicObjectId
from hubspot.crm.deals import ApiException as DealsApiException
from hubspot.crm.line_items import ApiException as LineItemsApiException
from hubspot.crm.objects import ApiException
from hubspot.crm.products import ApiException as ProductsApiException
from hubspot.crm.associations import ApiException as AssociationsApiException
from hubspot.crm.associations import BatchInputPublicObjectId
from hubspot.crm.properties import PropertyCreate, PropertyUpdate, ApiException

from hubspot.crm.deals.models import (
    SimplePublicObjectInputForCreate as SimplePublicObjectInputForCreateDeals,
)
from hubspot.crm.contacts import ApiException as ContactsApiException
from hubspot.crm.contacts.models import (
    SimplePublicObjectInput as ContactInput,
    PublicObjectSearchRequest,
    Filter,
    FilterGroup,
)
from hubspot.crm.companies import ApiException as CompaniesApiException
from hubspot.crm.companies.models import SimplePublicObjectInput as CompanyInput

# Import necessary models for V4 batch associations
from hubspot.crm.associations.v4.models import (
    BatchInputPublicAssociationMultiPost,
    PublicDefaultAssociation,
)

from data.models import *
from data.models.backgroundjob import BackgroundJob
from utils.logger import logger
from utils.meter import *
from utils.utility import chunks_dict, chunks_list, get_workspace
from utils.custom_field_utils import safe_update_custom_field_value
from collections import defaultdict

import json
import ast
import traceback
import requests

from data.constants.constant import HUBSPOT_DEAL_STAGE_MAPPER
from data.models import (
    Channel,
    Company,
    CompanyNameCustomField,
    CompanyPlatforms,
    CompanyValueCustomField,
    Contact,
    ContactsNameCustomField,
    ContactsPlatforms,
    ContactsValueCustomField,
    CustomObject,
    CustomObjectPlatforms,
    CustomObjectPropertyName,
    CustomObjectPropertyRow,
    CustomObjectPropertyValue,
    Deals,
    DealsNameCustomField,
    DealsPlatforms,
    DealsValueCustomField,
    DealsItems,
    DealsItemsNameCustomField,
    DealsItemsValueCustomField,
    Notification,
    ShopTurboItems,
    ShopTurboItemsNameCustomField,
    ShopTurboItemsOrders,
    ShopTurboItemsOrdersNameCustomField,
    ShopTurboItemsOrdersValueCustomField,
    ShopTurboItemsPrice,
    ShopTurboItemsValueCustomField,
    ShopTurboItemsPlatforms,
    ShopTurboOrders,
    ShopTurboOrdersNameCustomField,
    ShopTurboOrdersValueCustomField,
    ShopTurboOrdersPlatforms,
    SocialAccountLink,
    User,
    TransferHistory,
)
from django.conf import settings
from utils.meter import has_quota
from data.constants.constant import ORDER_USAGE_CATEGORY
from utils.custom_field_utils import safe_update_custom_field_value
from utils.utility import chunks_list
from asgiref.sync import sync_to_async


def import_hubspot_orders(
    channel_id: str,
    filter_deal_stage=None,
    mapping_custom_fields=None,
    how_to_import=None,
    lang="ja",
    how_to_import_items=None,
    key_item_field=None,
    mapping_status_custom_fields=None,
    last_index=None,
    hubspot_filter_import=None,
    history=None,
):
    channel = Channel.objects.get(pk=channel_id)
    workspace = channel.workspace
    platform = channel.integration.slug
    imported_orders = []
    task = history

    try:
        token_url = "https://api.hubapi.com/oauth/v1/token"
        data = {
            "grant_type": "refresh_token",
            "client_id": settings.HUBSPOT_CLIENT_ID,
            "client_secret": settings.HUBSPOT_CLIENT_SECRET,
            "refresh_token": channel.refresh_token,
        }
        response = requests.post(token_url, data=data)
        token_info = response.json()
        channel.access_token = token_info.get("access_token")
        channel.save()

        access_token = (
            token_info.get("access_token")
            if token_info.get("access_token")
            else channel.access_token
        )
        api_client = HubSpot(access_token=access_token)

        deals_api_response = None
        properties = [
            "dealname",
            "amount",
            "deal_currency_code",
            "dealstage",
            "createdate",
        ]
        contact_properties = ["firstname", "lastname", "email", "phone"]
        company_properties = ["name", "email", "phone", "domain"]
        # Initialize hubspot_import_platform_match with a default value
        hubspot_import_platform_match = None
        if how_to_import in ["update", "match", "skip"]:
            hubspot_import_platform_match = (
                Channel.objects.filter(workspace=workspace)
                .exclude(integration__slug="hubspot")
                .values_list("id", flat=True)
            )

        if mapping_custom_fields:
            if (
                "platform|deal" not in mapping_custom_fields
                or "platform_id|deal" not in mapping_custom_fields
            ):
                hubspot_import_platform_match = None
            for field in mapping_custom_fields:
                if field not in [
                    "deal_name",
                    "deal_stage",
                    "issue_date",
                    "billing_date",
                    "partner_display_name",
                ]:
                    if "|deal" in field:
                        field = field.replace("|deal", "")
                        properties.append(field)
                    if "|contact" in field:
                        field = field.replace("|contact", "")
                        contact_properties.append(field)
                    if "|company" in field:
                        field = field.replace("|company", "")
                        company_properties.append(field)

        for i in range(0, 3):  # Somehow its success in second try
            try:
                deals_api_response = api_client.crm.deals.basic_api.get_page(
                    limit=100,
                    properties=properties,
                    associations=["contacts", "companies"],
                )
                break
            except:
                pass
        if not deals_api_response:
            return False
        deals = deals_api_response.results
        while deals_api_response.paging is not None:
            next_after = deals_api_response.paging.next.after
            deals_api_response = api_client.crm.deals.basic_api.get_page(
                limit=100,
                after=next_after,
                properties=properties,
                associations=["contacts", "companies"],
            )
            deals.extend(deals_api_response.results)

        deal_to_company = fetch_companies_in_deals(
            channel_id, [deal.id for deal in deals]
        )

        deals = sorted(
            deals,
            key=lambda deal: deal.properties.get("createdate", ""),
            reverse=False,  # Set to True for descending order
        )

        counter = 0
        deal_stages = (
            api_client.crm.pipelines.pipelines_api.get_all("deals").results[0].stages
        )
        deal_pipelines_call = api_client.crm.pipelines.pipelines_api.get_all(
            "deals"
        ).results
        deal_pipelines = []
        for pipeline in deal_pipelines_call:
            pipeline_key_val = {}
            pipeline_key_val["id"] = pipeline.id
            pipeline_key_val["label"] = pipeline.label
            deal_pipelines.append(pipeline_key_val)

        updated_deal_stage = False
        updated_deal_pipeline = False

        if hubspot_filter_import:
            try:
                last_order = hubspot_filter_import.get("last_order")

                if last_order:
                    value = last_order.get("value")
                    if value and value.isdigit():
                        last_order_value = int(value)
                        if isinstance(deals, list) and len(deals) > last_order_value:
                            deals = deals[:last_order_value]
            except:
                pass

        if last_index:
            try:
                last_index = int(last_index)
                deals = deals[: last_index + 1]
            except:
                pass

        if task:
            task.total_number = len(deals)
            task.save()

        for deal in deals[::-1]:
            counter += 1
            if counter % 100 == 0 or counter == 1:
                token_url = "https://api.hubapi.com/oauth/v1/token"
                data = {
                    "grant_type": "refresh_token",
                    "client_id": settings.HUBSPOT_CLIENT_ID,
                    "client_secret": settings.HUBSPOT_CLIENT_SECRET,
                    "refresh_token": channel.refresh_token,
                }
                response = requests.post(token_url, data=data)
                token_info = response.json()
                channel.access_token = token_info.get("access_token")
                channel.save()

                access_token = (
                    token_info.get("access_token")
                    if token_info.get("access_token")
                    else channel.access_token
                )
                api_client = HubSpot(access_token=access_token)

            task = (
                TransferHistory.objects.filter(workspace=workspace, type="import_order")
                .order_by("-created_at")
                .first()
            )

            if task.status == "canceled":
                return False

            try:
                if task:
                    bg_job = BackgroundJob.objects.filter(transfer_history=task).first()
                    # Check if bg_job is not None before accessing its payload
                    if bg_job is not None:
                        payload = bg_job.payload
                        if payload:
                            data_args = payload.get("args")
                            # Store the current deal index to avoid repeated O(n) calls
                            current_deal_index = deals.index(deal)
                            # Create a new list excluding any argument starting with "--last_index="
                            new_data_args = [
                                arg
                                for arg in data_args
                                if not arg.startswith("--last_index=")
                            ]
                            # Add the new last_index
                            new_data_args.append(f"--last_index={current_deal_index}")
                            payload["args"] = new_data_args
                            bg_job.payload = payload
                            bg_job.save()
            except:
                pass

            progress = 100 * (len(deals) - deals.index(deal)) / len(deals)
            task.success_number = counter
            task.progress = progress
            task.save()
            deal_company = None

            deal_id = deal.id
            associated_contact = None
            associated_company = None

            # Check if there are associated contacts
            if deal.associations and "contacts" in deal.associations:
                contact_id = deal.associations["contacts"].results[0].id
                contact_response = api_client.crm.contacts.basic_api.get_by_id(
                    contact_id,
                    properties=contact_properties,  # Desired properties
                )
                associated_contact = contact_response.properties

            # Check if there are associated companies
            if deal.associations and "companies" in deal.associations:
                company_id = deal.associations["companies"].results[0].id
                company_response = api_client.crm.companies.basic_api.get_by_id(
                    company_id,
                    properties=company_properties,  # Desired properties
                )
                associated_company = company_response.properties

            # Add to the deal object (modify based on your use case)
            print("contact_company", associated_contact, associated_company)
            if (
                not filter_deal_stage
            ):  # Takes much time to import deals, skip for workflow tmporary
                if deal_to_company.get(str(deal.id), None):
                    deal_company = api_client.crm.companies.basic_api.get_by_id(
                        company_id=deal_to_company[str(deal.id)]
                    )
                # print("Deal==========:", deal)

            dealstage = deal.properties.get("dealstage")
            print("stage:", dealstage, hubspot_filter_import)
            if hubspot_filter_import:
                if hubspot_filter_import.get("order_status"):
                    if hubspot_filter_import.get("order_status")["key"] == "includes":
                        if (
                            dealstage
                            not in hubspot_filter_import.get("order_status")["value"]
                        ):
                            continue
                    elif hubspot_filter_import.get("order_status")["key"] == "excludes":
                        if (
                            dealstage
                            in hubspot_filter_import.get("order_status")["value"]
                        ):
                            continue

            try:
                if filter_deal_stage:
                    if filter_deal_stage in HUBSPOT_DEAL_STAGE_MAPPER:
                        filter_deal_stage = HUBSPOT_DEAL_STAGE_MAPPER[filter_deal_stage]
                    if deal.properties["dealstage"] != filter_deal_stage:
                        continue
                order_id = deal.id
                order_display_name = deal.properties.get("dealname")
                total_price = deal.properties.get("amount")
                currency = deal.properties.get("deal_currency_code", "JPY")
                platform_order_id = order_id

                match_order_platform = None
                match_order_platform_id = None
                if hubspot_import_platform_match:
                    match_order_platform = deal.properties.get("platform")
                    match_order_platform_id = deal.properties.get("platform_id")

                exist_order_platform = None
                if match_order_platform and match_order_platform_id:
                    exist_order_platform = ShopTurboOrdersPlatforms.objects.filter(
                        channel__workspace=workspace,
                        platform_order_id=match_order_platform_id,
                    ).first()

                if exist_order_platform:
                    if exist_order_platform.channel.id in hubspot_import_platform_match:
                        order_platform, _ = (
                            ShopTurboOrdersPlatforms.objects.get_or_create(
                                channel=channel, platform_order_id=platform_order_id
                            )
                        )
                        order_platform.order = exist_order_platform.order
                        order_platform.save()

                        shopturbo_order = exist_order_platform.order

                        if how_to_import == "skip":
                            shopturbo_order.save()
                            continue

                        if channel.ms_refresh_token:
                            if associated_company or associated_contact:
                                if associated_company:
                                    company_id = associated_company.get("hs_object_id")
                                    company_name = associated_company.get("name")
                                    company_email = associated_company.get("email")
                                    company_phone = associated_company.get("phone")
                                else:
                                    company_id = associated_contact.get("hs_object_id")
                                    company_name = associated_contact.get("firstname")
                                    company_email = associated_contact.get("email")
                                    company_phone = associated_contact.get("phone")

                                try:
                                    company, _ = Company.objects.get_or_create(
                                        workspace=workspace,
                                        name=company_name,
                                        email=company_email,
                                    )
                                except:
                                    company = Company.objects.filter(
                                        workspace=workspace,
                                        name=company_name,
                                        email=company_email,
                                    ).first()

                                if company_phone:
                                    company.phone_number = company_phone
                                company.save()
                                if shopturbo_order.contact:
                                    shopturbo_order.contact = None
                                shopturbo_order.company = company

                        else:
                            if associated_contact:
                                contact_id = associated_contact.get("hs_object_id")
                                contact_firstname = associated_contact.get("firstname")
                                contact_lastname = associated_contact.get("lastname")
                                contact_email = associated_contact.get("email")
                                contact_phone = associated_contact.get("phone")
                                contact_platform, _ = (
                                    ContactsPlatforms.objects.get_or_create(
                                        channel=channel,
                                        platform_id=contact_id,
                                    )
                                )

                                if contact_platform.contact:
                                    contact = contact_platform.contact
                                else:
                                    try:
                                        contact, _ = Contact.objects.get_or_create(
                                            workspace=workspace,
                                            email=contact_email,
                                            name=contact_firstname,
                                        )
                                    except:
                                        contact = Contact.objects.filter(
                                            workspace=workspace,
                                            email=contact_email,
                                            name=contact_firstname,
                                        ).first()

                                    contact_platform.contact = contact
                                    contact_platform.save()
                                    if contact_phone:
                                        contact.phone = contact_phone
                                    if contact_lastname:
                                        contact.last_name = contact_lastname
                                    contact.save()

                                if shopturbo_order.company:
                                    shopturbo_order.company = None
                                shopturbo_order.contact = contact

                            elif associated_company:
                                company_id = associated_company.get("hs_object_id")
                                company_name = associated_company.get("name")
                                company_email = associated_company.get("email")
                                company_phone = associated_company.get("phone")

                                try:
                                    company, _ = Company.objects.get_or_create(
                                        workspace=workspace,
                                        name=company_name,
                                        email=company_email,
                                    )
                                except:
                                    company = Company.objects.filter(
                                        workspace=workspace,
                                        name=company_name,
                                        email=company_email,
                                    ).first()

                                if company_phone:
                                    company.phone_number = company_phone
                                company.save()

                                if shopturbo_order.contact:
                                    shopturbo_order.contact = None
                                shopturbo_order.company = company

                        if mapping_status_custom_fields and dealstage:
                            shopturbo_order.delivery_status = (
                                mapping_status_custom_fields.get(dealstage)
                            )

                        shopturbo_order.save()

                        if mapping_custom_fields:
                            print(mapping_custom_fields)
                            for field in mapping_custom_fields:
                                sanka_field = mapping_custom_fields[field]
                                if field in [
                                    "deal_name",
                                    "deal_stage",
                                    "issue_date",
                                    "billing_date",
                                    "partner_display_name",
                                ]:
                                    value = None
                                    if field == "deal_name":
                                        value = deal.properties.get("dealname", None)
                                    if field == "deal_stage":
                                        value = deal.properties.get("dealstage", None)
                                    if field == "issue_date":
                                        value = deal.properties.get("createdate", None)
                                    if field == "billing_date":
                                        value = deal.properties.get("closedate", None)
                                    if field == "partner_display_name":
                                        value = deal_company.properties.get(
                                            "name", None
                                        )

                                    custom_field = (
                                        ShopTurboOrdersNameCustomField.objects.filter(
                                            workspace=workspace, name=sanka_field
                                        ).first()
                                    )
                                    if custom_field:
                                        custom_value, _ = (
                                            ShopTurboOrdersValueCustomField.objects.get_or_create(
                                                field_name=custom_field,
                                                orders=shopturbo_order,
                                            )
                                        )
                                        custom_value.value = value
                                        custom_value.save()
                                else:
                                    value = None
                                    if "|deal" in field:
                                        field = field.replace("|deal", "")
                                        value = deal.properties.get(field, None)
                                    if "|contact" in field:
                                        field = field.replace("|contact", "")
                                        if associated_contact:
                                            value = associated_contact.get(field, None)
                                    if "|company" in field:
                                        field = field.replace("|company", "")
                                        if associated_company:
                                            value = associated_company.get(field, None)

                                    if "|contact" in sanka_field:
                                        sanka_field = sanka_field.replace(
                                            "|contact", ""
                                        )
                                        try:
                                            if sanka_field in [
                                                "name",
                                                "last_name",
                                                "email",
                                                "phone_number",
                                            ]:
                                                if shopturbo_order.contact:
                                                    contact = shopturbo_order.contact
                                                    if sanka_field == "name":
                                                        contact.name = value
                                                    elif sanka_field == "last_name":
                                                        contact.last_name = value
                                                    elif sanka_field == "email":
                                                        contact.email = value
                                                    elif sanka_field == "phone_number":
                                                        contact.phone_number = value
                                                    contact.save()
                                        except:
                                            pass
                                        custom_field = (
                                            ContactsNameCustomField.objects.filter(
                                                workspace=workspace, name=sanka_field
                                            ).first()
                                        )
                                    elif "|company" in sanka_field:
                                        sanka_field = sanka_field.replace(
                                            "|company", ""
                                        )
                                        try:
                                            if sanka_field in [
                                                "name",
                                                "email",
                                                "phone_number",
                                            ]:
                                                if shopturbo_order.company:
                                                    company = shopturbo_order.company
                                                    if sanka_field == "name":
                                                        company.name = value
                                                    elif sanka_field == "email":
                                                        company.email = value
                                                    elif sanka_field == "phone_number":
                                                        company.phone_number = value
                                                    company.save()
                                        except:
                                            pass
                                        custom_field = (
                                            CompanyNameCustomField.objects.filter(
                                                workspace=workspace, name=sanka_field
                                            ).first()
                                        )
                                    else:
                                        custom_field = ShopTurboOrdersNameCustomField.objects.filter(
                                            workspace=workspace, name=sanka_field
                                        ).first()
                                    if custom_field:
                                        if isinstance(
                                            custom_field, ContactsNameCustomField
                                        ):
                                            if shopturbo_order.contact:
                                                custom_value, _ = (
                                                    ContactsValueCustomField.objects.get_or_create(
                                                        field_name=custom_field,
                                                        contact=shopturbo_order.contact,
                                                    )
                                                )
                                                custom_value.value = value
                                                custom_value.save()
                                        elif isinstance(
                                            custom_field, CompanyNameCustomField
                                        ):
                                            if shopturbo_order.company:
                                                custom_value, _ = (
                                                    CompanyValueCustomField.objects.get_or_create(
                                                        field_name=custom_field,
                                                        company=shopturbo_order.company,
                                                    )
                                                )
                                                custom_value.value = value
                                                custom_value.save()
                                        else:
                                            custom_value, _ = (
                                                ShopTurboOrdersValueCustomField.objects.get_or_create(
                                                    field_name=custom_field,
                                                    orders=shopturbo_order,
                                                )
                                            )
                                            custom_value.value = value
                                            custom_value.save()
                        continue

                if hubspot_import_platform_match:
                    if how_to_import == "update":
                        continue

                order_platform, _ = ShopTurboOrdersPlatforms.objects.get_or_create(
                    channel=channel, platform_order_id=platform_order_id
                )

                if order_platform.order:
                    shopturbo_order = order_platform.order
                    if shopturbo_order.status == "archived":
                        shopturbo_order = ShopTurboOrders.objects.create(
                            workspace=workspace,
                            currency=currency,
                            platform=platform,
                            order_type="item_order",
                            status="active",
                        )
                else:
                    if not has_quota(workspace, ORDER_USAGE_CATEGORY):
                        for user in workspace.user.all():
                            if user.verification.language == "ja":
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=user,
                                    message="制限を超えたため、受注レコードを作成できませんでした。サブスクリプション プランをアップグレードして割り当てを増やすか、既存のレコードをアーカイブしてスペースを解放してください。",
                                    type="error",
                                )
                            else:
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=user,
                                    message="Order could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some records to free up space.",
                                    type="error",
                                )
                        continue
                    shopturbo_order = ShopTurboOrders.objects.create(
                        workspace=workspace,
                        currency=currency,
                        platform=platform,
                        order_type="item_order",
                        status="active",
                    )

                order_platform.order = shopturbo_order
                order_platform.order_display_name = order_display_name
                order_platform.save()
                # print("Order Platform:========", order_platform.__dict__)

                if total_price:
                    shopturbo_order.total_price = float(total_price)
                    shopturbo_order.total_price_without_tax = float(total_price)
                    shopturbo_order.item_price_order = float(total_price)

                if currency:
                    shopturbo_order.currency = currency

                shopturbo_order.order_at = deal.properties.get("createdate")
                if mapping_status_custom_fields and dealstage:
                    shopturbo_order.delivery_status = mapping_status_custom_fields.get(
                        dealstage
                    )
                shopturbo_order.save()

                if channel.ms_refresh_token:
                    if associated_company or associated_contact:
                        if associated_company:
                            company_id = associated_company.get("hs_object_id")
                            company_name = associated_company.get("name")
                            company_email = associated_company.get("email")
                            company_phone = associated_company.get("phone")
                        else:
                            company_id = associated_contact.get("hs_object_id")
                            company_name = associated_contact.get("firstname")
                            company_email = associated_contact.get("email")
                            company_phone = associated_contact.get("phone")

                        try:
                            company, _ = Company.objects.get_or_create(
                                workspace=workspace,
                                name=company_name,
                                email=company_email,
                            )
                        except:
                            company = Company.objects.filter(
                                workspace=workspace,
                                name=company_name,
                                email=company_email,
                            ).first()

                        if company_phone:
                            company.phone_number = company_phone
                        company.save()
                        if shopturbo_order.contact:
                            shopturbo_order.contact = None
                        shopturbo_order.company = company

                else:
                    if associated_contact:
                        contact_id = associated_contact.get("hs_object_id")
                        contact_firstname = associated_contact.get("firstname")
                        contact_lastname = associated_contact.get("lastname")
                        contact_email = associated_contact.get("email")
                        contact_phone = associated_contact.get("phone")
                        contact_platform, _ = ContactsPlatforms.objects.get_or_create(
                            channel=channel,
                            platform_id=contact_id,
                        )

                        if contact_platform.contact:
                            contact = contact_platform.contact
                        else:
                            try:
                                contact, _ = Contact.objects.get_or_create(
                                    workspace=workspace,
                                    email=contact_email,
                                    name=contact_firstname,
                                )
                            except:
                                contact = Contact.objects.filter(
                                    workspace=workspace,
                                    email=contact_email,
                                    name=contact_firstname,
                                ).first()

                            contact_platform.contact = contact
                            contact_platform.save()
                            if contact_phone:
                                contact.phone = contact_phone
                            if contact_lastname:
                                contact.last_name = contact_lastname
                            contact.save()

                        if shopturbo_order.company:
                            shopturbo_order.company = None
                        shopturbo_order.contact = contact

                    elif associated_company:
                        company_id = associated_company.get("hs_object_id")
                        company_name = associated_company.get("name")
                        company_email = associated_company.get("email")
                        company_phone = associated_company.get("phone")

                        try:
                            company, _ = Company.objects.get_or_create(
                                workspace=workspace,
                                name=company_name,
                                email=company_email,
                            )
                        except:
                            company = Company.objects.filter(
                                workspace=workspace,
                                name=company_name,
                                email=company_email,
                            ).first()

                        if company_phone:
                            company.phone_number = company_phone
                        company.save()

                        if shopturbo_order.contact:
                            shopturbo_order.contact = None
                        shopturbo_order.company = company

                shopturbo_order.save()
                batch_input_public_object_id = BatchInputPublicObjectId(
                    inputs=[{"id": deal.id}]
                )
                associations_response = api_client.crm.associations.batch_api.read(
                    from_object_type="deal",
                    to_object_type="line_item",
                    batch_input_public_object_id=batch_input_public_object_id,
                )
                line_item_ids = []
                for assoc in associations_response.results:
                    for to_object in assoc.to:
                        line_item_ids.append(to_object.id)

                if line_item_ids:
                    try:
                        ShopTurboItemsOrdersValueCustomField.objects.filter(
                            item_order__order=shopturbo_order
                        ).delete()
                        ShopTurboItemsOrders.objects.filter(
                            order=shopturbo_order
                        ).delete()
                    except:
                        continue

                # Association Label
                association_label = AssociationLabel.objects.filter(
                    workspace=workspace,
                    label="Customer",
                    object_source=TYPE_OBJECT_ORDER,
                    object_target=",".join([TYPE_OBJECT_COMPANY, TYPE_OBJECT_CONTACT]),
                    association_type="many_to_many",
                    created_by_sanka=True,
                    label_ja="取引先",
                ).first()
                if association_label and shopturbo_order:
                    if shopturbo_order.company:
                        try:
                            AssociationLabelImplementation.objects.get(
                                label=association_label,
                                object_source_id=shopturbo_order.id,
                                object_target_id=shopturbo_order.company.id,
                            )
                        except AssociationLabelImplementation.MultipleObjectsReturned:
                            AssociationLabelImplementation.objects.filter(
                                label=association_label,
                                object_source_id=shopturbo_order.id,
                                object_target_id=shopturbo_order.company.id,
                            ).first().delete()
                        except AssociationLabelImplementation.DoesNotExist:
                            AssociationLabelImplementation.objects.create(
                                label=association_label,
                                object_source_id=shopturbo_order.id,
                                object_target_id=shopturbo_order.company.id,
                            )
                    if shopturbo_order.contact:
                        try:
                            AssociationLabelImplementation.objects.get(
                                label=association_label,
                                object_source_id=shopturbo_order.id,
                                object_target_id=shopturbo_order.contact.id,
                            )
                        except AssociationLabelImplementation.MultipleObjectsReturned:
                            AssociationLabelImplementation.objects.filter(
                                label=association_label,
                                object_source_id=shopturbo_order.id,
                                object_target_id=shopturbo_order.contact.id,
                            ).first().delete()
                        except AssociationLabelImplementation.DoesNotExist:
                            AssociationLabelImplementation.objects.create(
                                label=association_label,
                                object_source_id=shopturbo_order.id,
                                object_target_id=shopturbo_order.contact.id,
                            )

                if len(line_item_ids) == 0:
                    print(f"No line items found for deal {deal_id}")
                    shopturbo_order.order_type = "manual_order"
                    shopturbo_order.save()

                for line_item_id in line_item_ids:
                    try:
                        properties = [
                            "name",
                            "amount",
                            "quantity",
                            "hs_product_id",
                            "product_id",
                            "hs_total_discount",
                            "hs_discount_percentage",
                            "hs_sku",
                        ]
                        for field in mapping_custom_fields:
                            if "|line_item" in field and field not in [
                                "name|line_item",
                                "discount|line_item",
                                "product_id|line_item",
                            ]:
                                properties.append(field.replace("|line_item", ""))
                        line_item_response = (
                            api_client.crm.line_items.basic_api.get_by_id(
                                line_item_id, properties=properties
                            )
                        )
                        print("Line Item:", line_item_response)
                        item_price = line_item_response.properties.get("amount")
                        order_item_name = line_item_response.properties.get("name")
                        number_of_item = line_item_response.properties.get("quantity")
                        product_id = line_item_response.properties.get("hs_product_id")
                        product_sku = line_item_response.properties.get("hs_sku")
                        discount = line_item_response.properties.get(
                            "hs_discount_percentage"
                        )
                        discount_amount = line_item_response.properties.get(
                            "hs_total_discount"
                        )

                        # custom product id
                        custom_product_id = line_item_response.properties.get(
                            "product_id"
                        )

                        if product_sku:
                            if product_id:
                                existing_item_order = (
                                    ShopTurboItemsOrders.objects.filter(
                                        platform_item_id=product_id,
                                        order=shopturbo_order,
                                        order_platform=order_platform,
                                    ).first()
                                )
                                if existing_item_order:
                                    existing_item_order.platform_item_id = product_sku
                                    existing_item_order.save()
                                    shopturbo_item_order = existing_item_order
                                else:
                                    shopturbo_item_order, _ = (
                                        ShopTurboItemsOrders.objects.get_or_create(
                                            platform_item_id=product_sku,
                                            order=shopturbo_order,
                                            order_platform=order_platform,
                                        )
                                    )
                            else:
                                shopturbo_item_order, _ = (
                                    ShopTurboItemsOrders.objects.get_or_create(
                                        platform_item_id=product_sku,
                                        order=shopturbo_order,
                                        order_platform=order_platform,
                                    )
                                )
                        elif product_id:
                            shopturbo_item_order, _ = (
                                ShopTurboItemsOrders.objects.get_or_create(
                                    platform_item_id=product_id,
                                    order=shopturbo_order,
                                    order_platform=order_platform,
                                )
                            )
                        else:
                            old_order_name = f"Item {line_item_id}"
                            if ShopTurboItemsOrders.objects.filter(
                                order=shopturbo_order, custom_item_name=old_order_name
                            ).exists():
                                shopturbo_item_order = (
                                    ShopTurboItemsOrders.objects.filter(
                                        order=shopturbo_order,
                                        custom_item_name=old_order_name,
                                    ).first()
                                )
                            else:
                                shopturbo_item_order, _ = (
                                    ShopTurboItemsOrders.objects.get_or_create(
                                        custom_item_name=order_item_name,
                                        order=shopturbo_order,
                                        order_platform=order_platform,
                                    )
                                )

                        if number_of_item:
                            shopturbo_item_order.number_item = float(number_of_item)
                        if item_price:
                            item_price = float(item_price) / float(number_of_item)
                            shopturbo_item_order.item_price_order = float(item_price)
                            shopturbo_item_order.total_price = float(item_price)
                        if currency:
                            shopturbo_item_order.currency = currency

                        if not order_item_name:
                            order_item_name = f"Item {line_item_id}"

                        if not product_id and not product_sku:
                            shopturbo_item_order.custom_item_name = order_item_name

                        shopturbo_item_order.save()

                        if mapping_custom_fields:
                            for field in mapping_custom_fields:
                                if "|line_item" in field:
                                    sanka_field = mapping_custom_fields[field].replace(
                                        "|line_item", ""
                                    )
                                    value = None

                                    if field == "name|line_item":
                                        value = (
                                            order_item_name or f"Item {line_item_id}"
                                        )
                                    elif field == "discount|line_item":
                                        value = discount or discount_amount
                                    elif field == "product_id|line_item":
                                        value = custom_product_id or product_id
                                    else:
                                        value = line_item_response.properties.get(
                                            field.replace("|line_item", ""), None
                                        )

                                    custom_field = ShopTurboItemsOrdersNameCustomField.objects.filter(
                                        workspace=workspace, name=sanka_field
                                    ).first()
                                    if custom_field:
                                        custom_value, _ = (
                                            ShopTurboItemsOrdersValueCustomField.objects.get_or_create(
                                                field_name=custom_field,
                                                item_order=shopturbo_item_order,
                                            )
                                        )
                                        custom_value.value = value
                                        if discount and field == "discount|line_item":
                                            custom_value.value_number_format = "%"
                                        elif discount_amount:
                                            custom_value.value_number_format = "number"
                                        custom_value.save()

                        if product_sku:
                            try:
                                product_response = None

                                try:
                                    item_platform, _ = (
                                        ShopTurboItemsPlatforms.objects.get_or_create(
                                            channel=channel,
                                            platform_id=product_sku,
                                            platform_type="default",
                                        )
                                    )
                                except:
                                    item_platform = (
                                        ShopTurboItemsPlatforms.objects.filter(
                                            channel=channel,
                                            platform_id=product_sku,
                                            platform_type="default",
                                        ).first()
                                    )

                                if product_id:
                                    try:
                                        product_response = (
                                            api_client.crm.products.basic_api.get_by_id(
                                                product_id
                                            )
                                        )
                                        # Convert old platform IDs
                                        existing_item_platform = (
                                            ShopTurboItemsPlatforms.objects.filter(
                                                channel=channel, platform_id=product_id
                                            ).first()
                                        )
                                        if existing_item_platform:
                                            existing_item_platform_item = (
                                                existing_item_platform.item
                                            )
                                            if existing_item_platform_item:
                                                item_platform.item = (
                                                    existing_item_platform_item
                                                )
                                                item_platform.save()
                                            existing_item_platform.delete()
                                    except:
                                        pass

                                update_value = (
                                    False if how_to_import_items == "match" else True
                                )
                                if not key_item_field:
                                    key_item_field = "None"

                                if item_platform.item:
                                    shopturbo_item = item_platform.item
                                    if key_item_field != "None":
                                        custom_field = ShopTurboItemsNameCustomField.objects.filter(
                                            workspace=workspace, name=key_item_field
                                        ).first()
                                        custom_value = ShopTurboItemsValueCustomField.objects.filter(
                                            field_name=custom_field, value=product_sku
                                        ).first()
                                        if custom_value:
                                            shopturbo_item = custom_value.items
                                            shopturbo_item.product_id = product_sku
                                        else:
                                            custom_value, _ = (
                                                ShopTurboItemsValueCustomField.objects.get_or_create(
                                                    field_name=custom_field,
                                                    items=shopturbo_item,
                                                )
                                            )
                                            custom_value.value = product_sku
                                            custom_value.save()
                                else:
                                    if shopturbo_item_order.item:
                                        shopturbo_item = shopturbo_item_order.item
                                        if key_item_field != "None":
                                            custom_field = ShopTurboItemsNameCustomField.objects.filter(
                                                workspace=workspace, name=key_item_field
                                            ).first()
                                            custom_value, _ = (
                                                ShopTurboItemsValueCustomField.objects.get_or_create(
                                                    field_name=custom_field,
                                                    items=shopturbo_item,
                                                )
                                            )
                                            custom_value.value = product_sku
                                            custom_value.save()
                                    else:
                                        if key_item_field != "None":
                                            custom_field = ShopTurboItemsNameCustomField.objects.filter(
                                                workspace=workspace, name=key_item_field
                                            ).first()
                                            custom_value = ShopTurboItemsValueCustomField.objects.filter(
                                                field_name=custom_field,
                                                value=product_sku,
                                            ).first()
                                            if custom_value:
                                                shopturbo_item = custom_value.items
                                                shopturbo_item.product_id = product_sku
                                            else:
                                                update_value = True
                                                shopturbo_item = (
                                                    ShopTurboItems.objects.create(
                                                        workspace=workspace,
                                                        platform=platform,
                                                        product_id=product_sku,
                                                    )
                                                )
                                                custom_value, _ = (
                                                    ShopTurboItemsValueCustomField.objects.get_or_create(
                                                        field_name=custom_field,
                                                        items=shopturbo_item,
                                                    )
                                                )
                                                custom_value.value = product_sku
                                                custom_value.save()
                                        else:
                                            update_value = True
                                            shopturbo_item = (
                                                ShopTurboItems.objects.create(
                                                    workspace=workspace,
                                                    platform=platform,
                                                    product_id=product_sku,
                                                )
                                            )

                                if product_response:
                                    item_name = product_response.properties.get("name")
                                    item_price = product_response.properties.get(
                                        "price"
                                    )
                                else:
                                    item_name = order_item_name

                                if update_value:
                                    if item_name:
                                        shopturbo_item.name = item_name
                                    if item_price:
                                        shopturbo_item.price = float(item_price)
                                    if currency:
                                        shopturbo_item.currency = currency

                                shopturbo_item.save()

                                item_platform.platform_id = product_sku
                                item_platform.item = shopturbo_item
                                item_platform.save()

                                price, _ = ShopTurboItemsPrice.objects.get_or_create(
                                    item=shopturbo_item,
                                    price=float(item_price),
                                    currency=currency,
                                )

                                check_default = ShopTurboItemsPrice.objects.filter(
                                    item=shopturbo_item, default=True
                                )
                                if len(check_default) == 0:
                                    price.default = True
                                elif update_value:
                                    for defaults in check_default:
                                        defaults.default = False
                                        defaults.save()
                                    price.default = True
                                price.name = item_name
                                price.save()

                                shopturbo_item_order.item = shopturbo_item
                                shopturbo_item_order.save()
                            except:
                                traceback.print_exc()
                                pass

                        elif product_id:
                            try:
                                product_response = (
                                    api_client.crm.products.basic_api.get_by_id(
                                        product_id
                                    )
                                )
                                # print("Product:=======", product_response)
                                item_platform, _ = (
                                    ShopTurboItemsPlatforms.objects.get_or_create(
                                        channel=channel,
                                        platform_id=product_id,
                                        platform_type="default",
                                    )
                                )

                                if item_platform.item:
                                    shopturbo_item = item_platform.item
                                else:
                                    if shopturbo_item_order.item:
                                        shopturbo_item = shopturbo_item_order.item
                                    else:
                                        shopturbo_item = ShopTurboItems.objects.create(
                                            workspace=workspace,
                                            platform=platform,
                                            product_id=product_id,
                                            status="active",
                                        )

                                item_name = product_response.properties.get("name")
                                item_price = product_response.properties.get("price")

                                if item_name:
                                    shopturbo_item.name = item_name
                                if item_price:
                                    shopturbo_item.price = float(item_price)
                                if currency:
                                    shopturbo_item.currency = currency

                                shopturbo_item.save()

                                item_platform.platform_id = product_id
                                item_platform.item = shopturbo_item
                                item_platform.save()

                                # print("Item Platform=======:", item_platform.__dict__, shopturbo_item.__dict__)

                                price, _ = ShopTurboItemsPrice.objects.get_or_create(
                                    item=shopturbo_item,
                                    price=float(item_price),
                                    currency=currency,
                                )

                                check_default = ShopTurboItemsPrice.objects.filter(
                                    item=shopturbo_item, default=True
                                )
                                if len(check_default) == 0:
                                    price.default = True
                                price.name = item_name
                                price.save()

                                shopturbo_item_order.item = shopturbo_item
                                shopturbo_item_order.save()

                            except ProductsApiException as e:
                                print(f"... ERROR when FETCH product from hubspot: {e}")
                        else:
                            print("No product ID found for line item:", line_item_id)
                        # print("Item Order=======:", shopturbo_item_order.__dict__)

                    except LineItemsApiException as e:
                        print(f"... ERROR when FETCH line item from hubspot: {e}")

                shopturbo_order.number_item = len(line_item_ids)
                shopturbo_order.save()

                imported_orders.append(shopturbo_order.id)

                # print("Order:", shopturbo_order.__dict__)
                if mapping_custom_fields:
                    print(mapping_custom_fields)
                    for field in mapping_custom_fields:
                        sanka_field = mapping_custom_fields[field]
                        if field in [
                            "deal_name",
                            "deal_stage",
                            "issue_date",
                            "billing_date",
                            "partner_display_name",
                            "pipeline|deal",
                        ]:
                            value = None
                            stage_mapping = {
                                "Appointment Scheduled": "appointmentscheduled",
                                "Qualified To Buy": "qualifiedtobuy",
                                "Presentation Scheduled": "presentationscheduled",
                                "Decision Maker Bought-In": "decisionmakerboughtin",
                                "Contract Sent": "contractsent",
                                "Closed Won": "closedwon",
                                "Closed Lost": "closedlost",
                            }
                            if field == "deal_name":
                                value = deal.properties.get("dealname", None)
                            if field == "deal_stage":
                                value = deal.properties.get("dealstage", None)
                                for stage in deal_stages:
                                    if (
                                        value == stage.id
                                        and stage.label in stage_mapping
                                    ):
                                        value = stage_mapping[stage.label]
                                        break
                            if field == "issue_date":
                                value = deal.properties.get("createdate", None)
                            if field == "billing_date":
                                value = deal.properties.get("closedate", None)
                            if field == "pipeline|deal":
                                field = field.replace("|deal", "")
                                value = deal.properties.get(field, None)
                            if field == "partner_display_name":
                                value = deal_company.properties.get("name", None)

                            custom_field = (
                                ShopTurboOrdersNameCustomField.objects.filter(
                                    workspace=workspace, name=sanka_field
                                ).first()
                            )
                            if custom_field:
                                if field == "deal_stage" and not updated_deal_stage:
                                    if not custom_field.choice_value:
                                        available_choice_values = []
                                    else:
                                        available_choice_values = ast.literal_eval(
                                            custom_field.choice_value
                                        )
                                    for stage in deal_stages:
                                        if stage.label in stage_mapping:
                                            stage.id = stage_mapping[stage.label]

                                        if lang == "ja":
                                            translated_stage = {
                                                "appointmentscheduled": "アポイント設定済み (販売パイプライン)",
                                                "qualifiedtobuy": "購入見込みあり (販売パイプライン)",
                                                "presentationscheduled": "プレゼン予定済み (販売パイプライン)",
                                                "decisionmakerboughtin": "意思決定者の賛同 (販売パイプライン)",
                                                "contractsent": "契約書送付済み (販売パイプライン)",
                                                "closedwon": "成約 (販売パイプライン)",
                                                "closedlost": "クローズした不成立取引 (販売パイプライン)",
                                            }
                                            if stage.id in translated_stage:
                                                stage_dict = {
                                                    "label": translated_stage[stage.id],
                                                    "value": stage.id,
                                                }
                                            else:
                                                stage_dict = {
                                                    "label": stage.label,
                                                    "value": stage.id,
                                                }
                                        else:
                                            stage_dict = {
                                                "label": stage.label,
                                                "value": stage.id,
                                            }

                                        if stage_dict not in available_choice_values:
                                            update_label = False
                                            for choice in available_choice_values:
                                                if (
                                                    choice["value"]
                                                    == stage_dict["value"]
                                                ):
                                                    choice["label"] = stage_dict[
                                                        "label"
                                                    ]
                                                    update_label = True
                                                    break
                                            if not update_label:
                                                available_choice_values.append(
                                                    stage_dict
                                                )

                                    custom_field.choice_value = json.dumps(
                                        available_choice_values
                                    )
                                    custom_field.type = "choice"
                                    custom_field.save()
                                    updated_deal_stage = True
                                elif field == "pipeline" and not updated_deal_pipeline:
                                    if not custom_field.choice_value:
                                        available_choice_values = []
                                    else:
                                        available_choice_values = ast.literal_eval(
                                            custom_field.choice_value
                                        )
                                    for stage in deal_pipelines:
                                        if lang == "ja":
                                            translated_stage = {
                                                "default": "販売パイプライン"
                                            }
                                            if stage["id"] in translated_stage:
                                                stage_dict = {
                                                    "label": translated_stage[
                                                        stage["id"]
                                                    ],
                                                    "value": stage["id"],
                                                }
                                            else:
                                                stage_dict = {
                                                    "label": stage["label"],
                                                    "value": stage["id"],
                                                }
                                        else:
                                            stage_dict = {
                                                "label": stage["label"],
                                                "value": stage["id"],
                                            }

                                        if stage_dict not in available_choice_values:
                                            update_label = False
                                            for choice in available_choice_values:
                                                if (
                                                    choice["value"]
                                                    == stage_dict["value"]
                                                ):
                                                    choice["label"] = stage_dict[
                                                        "label"
                                                    ]
                                                    update_label = True
                                                    break
                                            if not update_label:
                                                available_choice_values.append(
                                                    stage_dict
                                                )

                                    custom_field.choice_value = json.dumps(
                                        available_choice_values
                                    )
                                    custom_field.type = "choice"
                                    custom_field.save()
                                    updated_deal_pipeline = True

                                # Use safe utility function to handle potential duplicates
                                safe_update_custom_field_value(
                                    ShopTurboOrdersValueCustomField,
                                    custom_field,
                                    shopturbo_order,
                                    value,
                                    "orders",
                                )
                        else:
                            value = None
                            if "|deal" in field:
                                field = field.replace("|deal", "")
                                value = deal.properties.get(field, None)
                            if "|contact" in field:
                                field = field.replace("|contact", "")
                                if associated_contact:
                                    value = associated_contact.get(field, None)
                            if "|company" in field:
                                field = field.replace("|company", "")
                                if associated_company:
                                    value = associated_company.get(field, None)

                            if "|contact" in sanka_field:
                                sanka_field = sanka_field.replace("|contact", "")
                                try:
                                    if sanka_field in [
                                        "name",
                                        "last_name",
                                        "email",
                                        "phone_number",
                                    ]:
                                        if shopturbo_order.contact:
                                            contact = shopturbo_order.contact
                                            if sanka_field == "name":
                                                contact.name = value
                                            elif sanka_field == "last_name":
                                                contact.last_name = value
                                            elif sanka_field == "email":
                                                contact.email = value
                                            elif sanka_field == "phone_number":
                                                contact.phone_number = value
                                            contact.save()
                                except:
                                    pass
                                custom_field = ContactsNameCustomField.objects.filter(
                                    workspace=workspace, name=sanka_field
                                ).first()
                            elif "|company" in sanka_field:
                                sanka_field = sanka_field.replace("|company", "")
                                try:
                                    if sanka_field in ["name", "email", "phone_number"]:
                                        if shopturbo_order.company:
                                            company = shopturbo_order.company
                                            if sanka_field == "name":
                                                company.name = value
                                            elif sanka_field == "email":
                                                company.email = value
                                            elif sanka_field == "phone_number":
                                                company.phone_number = value
                                            company.save()
                                except:
                                    pass
                                custom_field = CompanyNameCustomField.objects.filter(
                                    workspace=workspace, name=sanka_field
                                ).first()
                            else:
                                custom_field = (
                                    ShopTurboOrdersNameCustomField.objects.filter(
                                        workspace=workspace, name=sanka_field
                                    ).first()
                                )
                            if custom_field:
                                try:
                                    if isinstance(
                                        custom_field, ContactsNameCustomField
                                    ):
                                        if shopturbo_order.contact:
                                            custom_value, _ = (
                                                ContactsValueCustomField.objects.get_or_create(
                                                    field_name=custom_field,
                                                    contact=shopturbo_order.contact,
                                                )
                                            )
                                            custom_value.value = value
                                            custom_value.save()
                                    elif isinstance(
                                        custom_field, CompanyNameCustomField
                                    ):
                                        if shopturbo_order.company:
                                            custom_value, _ = (
                                                CompanyValueCustomField.objects.get_or_create(
                                                    field_name=custom_field,
                                                    company=shopturbo_order.company,
                                                )
                                            )
                                            custom_value.value = value
                                            custom_value.save()
                                    else:
                                        # Use safe utility function to handle potential duplicates
                                        safe_update_custom_field_value(
                                            ShopTurboOrdersValueCustomField,
                                            custom_field,
                                            shopturbo_order,
                                            value,
                                            "orders",
                                        )
                                except:
                                    pass
            except AssociationsApiException as e:
                print(f"... ERROR when FETCH associations from hubspot: {e}")

    except DealsApiException as e:
        print(f"... ERROR when FETCH list deal from hubspot: {e}")
        return

    return imported_orders


def get_associated_object(
    from_object_type, from_object_id, to_object_type, access_token
):
    url = f"https://api.hubapi.com/crm/v3/objects/{from_object_type}/{from_object_id}/associations/{to_object_type}"
    HEADERS = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }
    response = requests.get(url, headers=HEADERS)
    if response.status_code == 200:
        data = response.json()
        results = data.get("results", [])
        if results:
            return results[0].get(
                "id"
            )  # Return the first associated object (primary by assumption)
    return None


def import_hubspot_deals(
    channel_id: str,
    mapping_custom_fields=None,
    lang="ja",
):
    try:
        channel = Channel.objects.get(id=channel_id)
        access_token = channel.access_token
        user = channel.user.first()  # or pass user_id explicitly

        task = (
            TransferHistory.objects.filter(
                workspace=channel.workspace, type="import_case", channel=channel
            )
            .order_by("-created_at")
            .first()
        )

        try:
            properties = ["subject", "content", "hs_pipeline", "hs_ticket_category"]

            # Add custom fields
            if mapping_custom_fields:
                for field in mapping_custom_fields:
                    if field not in properties:
                        properties.append(field)

            _, ticket_records = get_records_object(access_token, "tickets", properties)
        except ApiException as e:
            print("Exception when calling basic_api->get_page: %s\n" % e)
            return False

        print(ticket_records)
        platform_ids = []

        api_client = HubSpot(access_token=access_token)
        try:
            ticket_categories = api_client.crm.properties.core_api.get_by_name(
                "tickets", "hs_ticket_category"
            ).options
        except:
            ticket_categories = {}

        if "hs_ticket_category" in mapping_custom_fields:
            sanka_property = mapping_custom_fields["hs_ticket_category"]
            try:
                category_custom_field = DealsNameCustomField.objects.get(
                    workspace=channel.workspace, name=sanka_property
                )
                if category_custom_field:
                    # Map HubSpot ticket categories to Sanka categories
                    if not category_custom_field.choice_value:
                        available_choice_values = []
                    else:
                        available_choice_values = ast.literal_eval(
                            category_custom_field.choice_value
                        )
                    for category in ticket_categories:
                        stage_dict = {
                            "label": category.label,
                            "value": category.value,
                        }

                        if stage_dict not in available_choice_values:
                            update_label = False
                            for choice in available_choice_values:
                                if choice["value"] == stage_dict["value"]:
                                    choice["label"] = stage_dict["label"]
                                    update_label = True
                                    break
                            if not update_label:
                                available_choice_values.append(stage_dict)

                    category_custom_field.choice_value = json.dumps(
                        available_choice_values
                    )
                    category_custom_field.type = "choice"
                    category_custom_field.multiple_select = True
                    category_custom_field.save()
            except Exception as e:
                print("Error creating or updating category custom field:", e)
                pass

        if task:
            task.total_number = len(ticket_records)
            task.save()

        for record in ticket_records:
            task = (
                TransferHistory.objects.filter(
                    workspace=channel.workspace, type="import_case", channel=channel
                )
                .order_by("-created_at")
                .first()
            )
            if task and task.status == "canceled":
                return False

            if task:
                progress = round(
                    (ticket_records.index(record) + 1) / len(ticket_records) * 100, 2
                )
                task.progress = progress
                task.success_number = ticket_records.index(record) + 1
                task.save()

            platform_id = record["id"]
            props = record["properties"]
            subject = props.get("subject")
            # content = props.get("content")
            # pipeline = props.get("hs_pipeline")
            category = props.get("hs_ticket_category")

            deal_platform, _ = DealsPlatforms.objects.get_or_create(
                platform_id=platform_id,
                channel=channel,
            )

            try:
                if deal_platform.deal:
                    deal = deal_platform.deal
                else:
                    deal = Deals.objects.create(workspace=channel.workspace)
                    deal_platform.deal = deal
                    deal_platform.save()
            except Exception as e:
                print("[ERROR] importing ticket from HubSpot: ", e)

            deal.name = subject or ""
            deal.status = "active"
            deal.save()

            # Associate contact/company
            associated_contact = None
            associated_company = None

            api_client = HubSpot(access_token=access_token)
            # Check if there are associated contacts
            HUBSPOT_API_BASE = "https://api.hubapi.com"
            HEADERS = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
            }

            # Usage:
            ticket_id = record["id"]

            # Get associated contact
            contact_id = get_associated_object(
                "tickets", ticket_id, "contacts", access_token
            )
            associated_contact = None
            if contact_id:
                contact_url = f"{HUBSPOT_API_BASE}/crm/v3/objects/contacts/{contact_id}?properties=firstname,lastname,email"
                contact_resp = requests.get(contact_url, headers=HEADERS)
                if contact_resp.status_code == 200:
                    associated_contact = contact_resp.json().get("properties")

            # Get associated company
            company_id = get_associated_object(
                "tickets", ticket_id, "companies", access_token
            )
            associated_company = None
            if company_id:
                company_url = f"{HUBSPOT_API_BASE}/crm/v3/objects/companies/{company_id}?properties=name,domain"
                company_resp = requests.get(company_url, headers=HEADERS)
                if company_resp.status_code == 200:
                    associated_company = company_resp.json().get("properties")

            if associated_contact:
                contact_id = associated_contact.get("hs_object_id")
                contact_firstname = associated_contact.get("firstname")
                contact_lastname = associated_contact.get("lastname")
                contact_email = associated_contact.get("email")
                contact_phone = associated_contact.get("phone")
                contact_platform, _ = ContactsPlatforms.objects.get_or_create(
                    channel=channel,
                    platform_id=contact_id,
                )

                if contact_platform.contact:
                    contact = contact_platform.contact
                else:
                    try:
                        contact, _ = Contact.objects.get_or_create(
                            workspace=channel.workspace,
                            email=contact_email,
                            name=contact_firstname,
                        )
                    except:
                        contact = Contact.objects.filter(
                            workspace=channel.workspace,
                            email=contact_email,
                            name=contact_firstname,
                        ).first()

                    contact_platform.contact = contact
                    contact_platform.save()
                    if contact_phone:
                        contact.phone = contact_phone
                    if contact_lastname:
                        contact.last_name = contact_lastname
                    contact.save()

                if deal.company:
                    deal.company.clear()
                deal.contact.add(contact)

            elif associated_company:
                company_id = associated_company.get("hs_object_id")
                company_name = associated_company.get("name")
                company_email = associated_company.get("email")
                company_phone = associated_company.get("phone")

                try:
                    company, _ = Company.objects.get_or_create(
                        workspace=channel.workspace,
                        name=company_name,
                        email=company_email,
                    )
                except:
                    company = Company.objects.filter(
                        workspace=channel.workspace,
                        name=company_name,
                        email=company_email,
                    ).first()

                if company_phone:
                    company.phone_number = company_phone
                company.save()

                if deal.contact:
                    deal.contact.clear()
                deal.company.add(company)

            # Handle custom fields
            if mapping_custom_fields:
                workspace = channel.workspace
                for hubspot_field, sanka_field in mapping_custom_fields.items():
                    value = props.get(hubspot_field)
                    if value is not None:
                        custom_field = DealsNameCustomField.objects.filter(
                            workspace=workspace, name=sanka_field
                        ).first()

                        if custom_field:
                            custom_value, _ = (
                                DealsValueCustomField.objects.get_or_create(
                                    field_name=custom_field,
                                    deals=deal,
                                )
                            )
                            custom_value.value = value
                            custom_value.save()
                        else:
                            if sanka_field == "name":
                                deal.name = value

            platform_ids.append(platform_id)

        # Cleanup
        deal_platforms = DealsPlatforms.objects.filter(channel=channel).exclude(
            platform_id__in=platform_ids
        )
        for deal_platform in deal_platforms:
            deal = deal_platform.deal
            deal_platform.delete()
            print("Ticket platform deleted:", deal_platform)
            if lang == "ja":
                Notification.objects.create(
                    workspace=channel.workspace,
                    user=user,
                    message=f"HubSpotのプラットフォームIDを削除しました。チケットID: {deal.deal_id:04d} はHubSpot上で利用できなくなりました。",
                    type="info",
                )
            else:
                Notification.objects.create(
                    workspace=channel.workspace,
                    user=user,
                    message=f"HubSpot platform ID removed. Ticket ID: {deal.deal_id:04d} is no longer available on HubSpot.",
                    type="info",
                )

        # Success
        message = (
            "HubSpotのチケットが正常にインポートされました。"
            if lang == "ja"
            else "HubSpot tickets successfully imported."
        )
        Notification.objects.create(
            workspace=channel.workspace, user=user, message=message, type="success"
        )
        return True

    except Exception as e:
        print("Error in import_hubspot_deals:", e)
        traceback.print_exc()
        return False


def import_hubspot_orders_as_deals(
    channel_id: str,
    filter_deal_stage=None,
    mapping_custom_fields=None,
    how_to_import=None,
    lang="ja",
):
    channel = Channel.objects.get(pk=channel_id)
    workspace = channel.workspace
    platform = channel.integration.slug
    imported_orders = []
    task = (
        TransferHistory.objects.filter(
            workspace=workspace, type="import_case", channel=channel
        )
        .order_by("-created_at")
        .first()
    )

    try:
        token_url = "https://api.hubapi.com/oauth/v1/token"
        data = {
            "grant_type": "refresh_token",
            "client_id": settings.HUBSPOT_CLIENT_ID,
            "client_secret": settings.HUBSPOT_CLIENT_SECRET,
            "refresh_token": channel.refresh_token,
        }
        response = requests.post(token_url, data=data)
        token_info = response.json()
        channel.access_token = token_info.get("access_token")
        channel.save()

        access_token = (
            token_info.get("access_token")
            if token_info.get("access_token")
            else channel.access_token
        )
        api_client = HubSpot(access_token=access_token)

        deals_api_response = None
        properties = [
            "dealname",
            "amount",
            "deal_currency_code",
            "dealstage",
            "createdate",
        ]
        contact_properties = ["firstname", "lastname", "email", "phone"]
        company_properties = ["name", "email", "phone", "domain"]
        # Initialize hubspot_import_platform_match with a default value
        hubspot_import_platform_match = None
        if how_to_import in ["update", "match", "skip"]:
            hubspot_import_platform_match = (
                Channel.objects.filter(workspace=workspace)
                .exclude(integration__slug="hubspot")
                .values_list("id", flat=True)
            )

        if mapping_custom_fields:
            if (
                "platform|deal" not in mapping_custom_fields
                or "platform_id|deal" not in mapping_custom_fields
            ):
                hubspot_import_platform_match = None
            for field in mapping_custom_fields:
                if field not in [
                    "deal_name",
                    "deal_stage",
                    "issue_date",
                    "billing_date",
                    "partner_display_name",
                ]:
                    if "|deal" in field:
                        field = field.replace("|deal", "")
                        properties.append(field)
                    if "|contact" in field:
                        field = field.replace("|contact", "")
                        contact_properties.append(field)
                    if "|company" in field:
                        field = field.replace("|company", "")
                        company_properties.append(field)

        for i in range(0, 3):  # Somehow its success in second try
            try:
                deals_api_response = api_client.crm.deals.basic_api.get_page(
                    limit=100,
                    properties=properties,
                    associations=["contacts", "companies"],
                )
                break
            except:
                pass
        if not deals_api_response:
            return False
        deals = deals_api_response.results
        while deals_api_response.paging is not None:
            next_after = deals_api_response.paging.next.after
            deals_api_response = api_client.crm.deals.basic_api.get_page(
                limit=100,
                after=next_after,
                properties=properties,
                associations=["contacts", "companies"],
            )
            deals.extend(deals_api_response.results)

        deal_to_company = fetch_companies_in_deals(
            channel_id, [deal.id for deal in deals]
        )

        deals = sorted(
            deals,
            key=lambda deal: deal.properties.get("createdate", ""),
            reverse=False,  # Set to True for descending order
        )

        counter = 0
        deal_stages = (
            api_client.crm.pipelines.pipelines_api.get_all("deals").results[0].stages
        )
        updated_deal_stage = False

        if task:
            task.total_number = len(deals)
            task.save()

        for deal in deals[::-1]:
            counter += 1
            if counter % 250 == 0 or counter == 1:
                token_url = "https://api.hubapi.com/oauth/v1/token"
                data = {
                    "grant_type": "refresh_token",
                    "client_id": settings.HUBSPOT_CLIENT_ID,
                    "client_secret": settings.HUBSPOT_CLIENT_SECRET,
                    "refresh_token": channel.refresh_token,
                }
                response = requests.post(token_url, data=data)
                token_info = response.json()
                channel.access_token = token_info.get("access_token")
                channel.save()

                access_token = (
                    token_info.get("access_token")
                    if token_info.get("access_token")
                    else channel.access_token
                )
                api_client = HubSpot(access_token=access_token)

            task = (
                TransferHistory.objects.filter(
                    workspace=workspace, type="import_case", channel=channel
                )
                .order_by("-created_at")
                .first()
            )
            if task.status == "canceled":
                return False
            progress = 100 * (len(deals) - deals.index(deal)) / len(deals)
            task.success_number = counter
            task.progress = progress
            task.save()
            deal_company = None

            associated_contact = None
            associated_company = None

            # Check if there are associated contacts
            if deal.associations and "contacts" in deal.associations:
                contact_id = deal.associations["contacts"].results[0].id
                contact_response = api_client.crm.contacts.basic_api.get_by_id(
                    contact_id,
                    properties=contact_properties,  # Desired properties
                )
                associated_contact = contact_response.properties

            # Check if there are associated companies
            if deal.associations and "companies" in deal.associations:
                company_id = deal.associations["companies"].results[0].id
                company_response = api_client.crm.companies.basic_api.get_by_id(
                    company_id,
                    properties=company_properties,  # Desired properties
                )
                associated_company = company_response.properties

            # Add to the deal object (modify based on your use case)
            print("contact_company", associated_contact, associated_company)
            if (
                not filter_deal_stage
            ):  # Takes much time to import deals, skip for workflow tmporary
                if deal_to_company.get(str(deal.id), None):
                    deal_company = api_client.crm.companies.basic_api.get_by_id(
                        company_id=deal_to_company[str(deal.id)]
                    )
                # print("Deal==========:", deal)

            try:
                if filter_deal_stage:
                    if filter_deal_stage in HUBSPOT_DEAL_STAGE_MAPPER:
                        filter_deal_stage = HUBSPOT_DEAL_STAGE_MAPPER[filter_deal_stage]
                    if deal.properties["dealstage"] != filter_deal_stage:
                        continue
                order_id = deal.id
                platform_display_name = deal.properties.get("dealname")
                currency = deal.properties.get("deal_currency_code", "JPY")
                platform_id = order_id

                match_order_platform = None
                match_order_platform_id = None
                if hubspot_import_platform_match:
                    match_order_platform = deal.properties.get("platform")
                    match_order_platform_id = deal.properties.get("platform_id")

                exist_order_platform = None
                if match_order_platform and match_order_platform_id:
                    exist_order_platform = DealsPlatforms.objects.filter(
                        channel__workspace=workspace,
                        platform_id=match_order_platform_id,
                    ).first()

                if exist_order_platform:
                    if exist_order_platform.channel.id in hubspot_import_platform_match:
                        order_platform, _ = DealsPlatforms.objects.get_or_create(
                            channel=channel, platform_id=platform_id
                        )
                        order_platform.deal = exist_order_platform.deal
                        order_platform.save()

                        shopturbo_order = exist_order_platform.deal

                        if how_to_import == "skip":
                            shopturbo_order.save()
                            continue

                        if channel.ms_refresh_token:
                            if associated_company or associated_contact:
                                if associated_company:
                                    company_id = associated_company.get("hs_object_id")
                                    company_name = associated_company.get("name")
                                    company_email = associated_company.get("email")
                                    company_phone = associated_company.get("phone")
                                else:
                                    company_id = associated_contact.get("hs_object_id")
                                    company_name = associated_contact.get("firstname")
                                    company_email = associated_contact.get("email")
                                    company_phone = associated_contact.get("phone")

                                try:
                                    company, _ = Company.objects.get_or_create(
                                        workspace=workspace,
                                        name=company_name,
                                        email=company_email,
                                    )
                                except:
                                    company = Company.objects.filter(
                                        workspace=workspace,
                                        name=company_name,
                                        email=company_email,
                                    ).first()

                                if company_phone:
                                    company.phone_number = company_phone
                                company.save()
                                if shopturbo_order.contact:
                                    shopturbo_order.contact.clear()
                                shopturbo_order.company.add(company)

                        else:
                            if associated_contact:
                                contact_id = associated_contact.get("hs_object_id")
                                contact_firstname = associated_contact.get("firstname")
                                contact_lastname = associated_contact.get("lastname")
                                contact_email = associated_contact.get("email")
                                contact_phone = associated_contact.get("phone")
                                contact_platform, _ = (
                                    ContactsPlatforms.objects.get_or_create(
                                        channel=channel,
                                        platform_id=contact_id,
                                    )
                                )

                                if contact_platform.contact:
                                    contact = contact_platform.contact
                                else:
                                    try:
                                        contact, _ = Contact.objects.get_or_create(
                                            workspace=workspace,
                                            email=contact_email,
                                            name=contact_firstname,
                                        )
                                    except:
                                        contact = Contact.objects.filter(
                                            workspace=workspace,
                                            email=contact_email,
                                            name=contact_firstname,
                                        ).first()

                                    contact_platform.contact = contact
                                    contact_platform.save()
                                    if contact_phone:
                                        contact.phone = contact_phone
                                    if contact_lastname:
                                        contact.last_name = contact_lastname
                                    contact.save()

                                if shopturbo_order.company:
                                    shopturbo_order.company.clear()
                                shopturbo_order.contact.add(contact)

                            elif associated_company:
                                company_id = associated_company.get("hs_object_id")
                                company_name = associated_company.get("name")
                                company_email = associated_company.get("email")
                                company_phone = associated_company.get("phone")

                                try:
                                    company, _ = Company.objects.get_or_create(
                                        workspace=workspace,
                                        name=company_name,
                                        email=company_email,
                                    )
                                except:
                                    company = Company.objects.filter(
                                        workspace=workspace,
                                        name=company_name,
                                        email=company_email,
                                    ).first()

                                if company_phone:
                                    company.phone_number = company_phone
                                company.save()

                                if shopturbo_order.contact:
                                    shopturbo_order.contact.clear()
                                shopturbo_order.company.add(company)

                        shopturbo_order.name = platform_display_name
                        shopturbo_order.save()
                        if mapping_custom_fields:
                            print(mapping_custom_fields)
                            for field in mapping_custom_fields:
                                sanka_field = mapping_custom_fields[field]
                                if field in [
                                    "deal_name",
                                    "deal_stage",
                                    "issue_date",
                                    "billing_date",
                                    "partner_display_name",
                                ]:
                                    value = None
                                    if field == "deal_name":
                                        value = deal.properties.get("dealname", None)
                                    if field == "deal_stage":
                                        value = deal.properties.get("dealstage", None)
                                    if field == "issue_date":
                                        value = deal.properties.get("createdate", None)
                                    if field == "billing_date":
                                        value = deal.properties.get("closedate", None)
                                    if field == "partner_display_name":
                                        value = deal_company.properties.get(
                                            "name", None
                                        )

                                    custom_field = DealsNameCustomField.objects.filter(
                                        workspace=workspace, name=sanka_field
                                    ).first()
                                    if custom_field:
                                        custom_value, _ = (
                                            DealsValueCustomField.objects.get_or_create(
                                                field_name=custom_field,
                                                deals=shopturbo_order,
                                            )
                                        )
                                        custom_value.value = value
                                        custom_value.save()
                                else:
                                    value = None
                                    if "|deal" in field:
                                        field = field.replace("|deal", "")
                                        value = deal.properties.get(field, None)
                                    if "|contact" in field:
                                        field = field.replace("|contact", "")
                                        if associated_contact:
                                            value = associated_contact.get(field, None)
                                    if "|company" in field:
                                        field = field.replace("|company", "")
                                        if associated_company:
                                            value = associated_company.get(field, None)

                                    if "|contact" in sanka_field:
                                        sanka_field = sanka_field.replace(
                                            "|contact", ""
                                        )
                                        try:
                                            if sanka_field in [
                                                "name",
                                                "last_name",
                                                "email",
                                                "phone_number",
                                            ]:
                                                if shopturbo_order.contact:
                                                    contact = (
                                                        shopturbo_order.contact.first()
                                                    )
                                                    if sanka_field == "name":
                                                        contact.name = value
                                                    elif sanka_field == "last_name":
                                                        contact.last_name = value
                                                    elif sanka_field == "email":
                                                        contact.email = value
                                                    elif sanka_field == "phone_number":
                                                        contact.phone_number = value
                                                    contact.save()
                                        except:
                                            pass
                                        custom_field = (
                                            ContactsNameCustomField.objects.filter(
                                                workspace=workspace, name=sanka_field
                                            ).first()
                                        )
                                    elif "|company" in sanka_field:
                                        sanka_field = sanka_field.replace(
                                            "|company", ""
                                        )
                                        try:
                                            if sanka_field in [
                                                "name",
                                                "email",
                                                "phone_number",
                                            ]:
                                                if shopturbo_order.company:
                                                    company = (
                                                        shopturbo_order.company.first()
                                                    )
                                                    if sanka_field == "name":
                                                        company.name = value
                                                    elif sanka_field == "email":
                                                        company.email = value
                                                    elif sanka_field == "phone_number":
                                                        company.phone_number = value
                                                    company.save()
                                        except:
                                            pass
                                        custom_field = (
                                            CompanyNameCustomField.objects.filter(
                                                workspace=workspace, name=sanka_field
                                            ).first()
                                        )
                                    else:
                                        custom_field = (
                                            DealsNameCustomField.objects.filter(
                                                workspace=workspace, name=sanka_field
                                            ).first()
                                        )
                                    if custom_field:
                                        if isinstance(
                                            custom_field, ContactsNameCustomField
                                        ):
                                            if shopturbo_order.contact:
                                                custom_value, _ = (
                                                    ContactsValueCustomField.objects.get_or_create(
                                                        field_name=custom_field,
                                                        contact=shopturbo_order.contact.first(),
                                                    )
                                                )
                                                custom_value.value = value
                                                custom_value.save()
                                        elif isinstance(
                                            custom_field, CompanyNameCustomField
                                        ):
                                            if shopturbo_order.company:
                                                custom_value, _ = (
                                                    CompanyValueCustomField.objects.get_or_create(
                                                        field_name=custom_field,
                                                        company=shopturbo_order.company.first(),
                                                    )
                                                )
                                                custom_value.value = value
                                                custom_value.save()
                                        else:
                                            custom_value, _ = (
                                                DealsValueCustomField.objects.get_or_create(
                                                    field_name=custom_field,
                                                    deals=shopturbo_order,
                                                )
                                            )
                                            custom_value.value = value
                                            custom_value.save()
                        continue

                if hubspot_import_platform_match:
                    if how_to_import == "update":
                        continue

                order_platform, _ = DealsPlatforms.objects.get_or_create(
                    channel=channel, platform_id=platform_id
                )
                order_platform.object_type = "deal"

                if order_platform.deal:
                    shopturbo_order = order_platform.deal
                    if shopturbo_order.status == "archived":
                        shopturbo_order = Deals.objects.create(
                            workspace=workspace, status="active"
                        )
                else:
                    shopturbo_order = Deals.objects.create(
                        workspace=workspace, status="active"
                    )

                order_platform.deal = shopturbo_order
                order_platform.platform_display_name = platform_display_name
                order_platform.save()
                # print("Order Platform:========", order_platform.__dict__)

                shopturbo_order.order_at = deal.properties.get("createdate")
                shopturbo_order.name = platform_display_name
                shopturbo_order.save()

                if channel.ms_refresh_token:
                    if associated_company or associated_contact:
                        if associated_company:
                            company_id = associated_company.get("hs_object_id")
                            company_name = associated_company.get("name")
                            company_email = associated_company.get("email")
                            company_phone = associated_company.get("phone")
                        else:
                            company_id = associated_contact.get("hs_object_id")
                            company_name = associated_contact.get("firstname")
                            company_email = associated_contact.get("email")
                            company_phone = associated_contact.get("phone")

                        try:
                            company, _ = Company.objects.get_or_create(
                                workspace=workspace,
                                name=company_name,
                                email=company_email,
                            )
                        except:
                            company = Company.objects.filter(
                                workspace=workspace,
                                name=company_name,
                                email=company_email,
                            ).first()

                        if company_phone:
                            company.phone_number = company_phone
                        company.save()
                        if shopturbo_order.contact:
                            shopturbo_order.contact.clear()
                        shopturbo_order.company.add(company)

                else:
                    if associated_contact:
                        contact_id = associated_contact.get("hs_object_id")
                        contact_firstname = associated_contact.get("firstname")
                        contact_lastname = associated_contact.get("lastname")
                        contact_email = associated_contact.get("email")
                        contact_phone = associated_contact.get("phone")
                        contact_platform, _ = ContactsPlatforms.objects.get_or_create(
                            channel=channel,
                            platform_id=contact_id,
                        )

                        if contact_platform.contact:
                            contact = contact_platform.contact
                        else:
                            try:
                                contact, _ = Contact.objects.get_or_create(
                                    workspace=workspace,
                                    email=contact_email,
                                    name=contact_firstname,
                                )
                            except:
                                contact = Contact.objects.filter(
                                    workspace=workspace,
                                    email=contact_email,
                                    name=contact_firstname,
                                ).first()

                            contact_platform.contact = contact
                            contact_platform.save()
                            if contact_phone:
                                contact.phone = contact_phone
                            if contact_lastname:
                                contact.last_name = contact_lastname
                            contact.save()

                        if shopturbo_order.company:
                            shopturbo_order.company.clear()
                        shopturbo_order.contact.add(contact)

                    elif associated_company:
                        company_id = associated_company.get("hs_object_id")
                        company_name = associated_company.get("name")
                        company_email = associated_company.get("email")
                        company_phone = associated_company.get("phone")

                        try:
                            company, _ = Company.objects.get_or_create(
                                workspace=workspace,
                                name=company_name,
                                email=company_email,
                            )
                        except:
                            company = Company.objects.filter(
                                workspace=workspace,
                                name=company_name,
                                email=company_email,
                            ).first()

                        if company_phone:
                            company.phone_number = company_phone
                        company.save()

                        if shopturbo_order.contact:
                            shopturbo_order.contact.clear()
                        shopturbo_order.company.add(company)

                batch_input_public_object_id = BatchInputPublicObjectId(
                    inputs=[{"id": deal.id}]
                )
                associations_response = api_client.crm.associations.batch_api.read(
                    from_object_type="deal",
                    to_object_type="line_item",
                    batch_input_public_object_id=batch_input_public_object_id,
                )
                line_item_ids = []
                for assoc in associations_response.results:
                    for to_object in assoc.to:
                        line_item_ids.append(to_object.id)

                for line_item_id in line_item_ids:
                    try:
                        properties = [
                            "name",
                            "amount",
                            "quantity",
                            "hs_product_id",
                            "product_id",
                            "hs_total_discount",
                            "hs_discount_percentage",
                            "hs_sku",
                        ]
                        for field in mapping_custom_fields:
                            if "|line_item" in field and field not in [
                                "name|line_item",
                                "discount|line_item",
                                "product_id|line_item",
                            ]:
                                properties.append(field.replace("|line_item", ""))
                        line_item_response = (
                            api_client.crm.line_items.basic_api.get_by_id(
                                line_item_id, properties=properties
                            )
                        )
                        print("Line Item:", line_item_response)
                        item_price = line_item_response.properties.get("amount")
                        order_item_name = line_item_response.properties.get("name")
                        number_of_item = line_item_response.properties.get("quantity")
                        product_id = line_item_response.properties.get("hs_product_id")
                        product_sku = line_item_response.properties.get("hs_sku")
                        discount = line_item_response.properties.get(
                            "hs_discount_percentage"
                        )
                        discount_amount = line_item_response.properties.get(
                            "hs_total_discount"
                        )

                        # custom product id
                        custom_product_id = line_item_response.properties.get(
                            "product_id"
                        )

                        if product_sku:
                            if product_id:
                                existing_item_order = DealsItems.objects.filter(
                                    platform_item_id=product_id,
                                    deal=shopturbo_order,
                                    deal_platform=order_platform,
                                ).first()
                                if existing_item_order:
                                    existing_item_order.platform_item_id = product_sku
                                    existing_item_order.save()
                                    shopturbo_item_order = existing_item_order
                                else:
                                    shopturbo_item_order, _ = (
                                        DealsItems.objects.get_or_create(
                                            platform_item_id=product_sku,
                                            deal=shopturbo_order,
                                            deal_platform=order_platform,
                                        )
                                    )
                            else:
                                shopturbo_item_order, _ = (
                                    DealsItems.objects.get_or_create(
                                        platform_item_id=product_sku,
                                        deal=shopturbo_order,
                                        deal_platform=order_platform,
                                    )
                                )
                        elif product_id:
                            shopturbo_item_order, _ = DealsItems.objects.get_or_create(
                                platform_item_id=product_id,
                                deal=shopturbo_order,
                                deal_platform=order_platform,
                            )
                        else:
                            old_order_name = f"Item {line_item_id}"
                            if DealsItems.objects.filter(
                                deal=shopturbo_order, custom_item_name=old_order_name
                            ).exists():
                                shopturbo_item_order = DealsItems.objects.filter(
                                    deal=shopturbo_order,
                                    custom_item_name=old_order_name,
                                ).first()
                            else:
                                shopturbo_item_order, _ = (
                                    DealsItems.objects.get_or_create(
                                        custom_item_name=order_item_name,
                                        deal=shopturbo_order,
                                        deal_platform=order_platform,
                                    )
                                )

                        if number_of_item:
                            shopturbo_item_order.number_item = float(number_of_item)
                        if item_price:
                            item_price = float(item_price) / float(number_of_item)
                            shopturbo_item_order.item_price_deal = float(item_price)
                            shopturbo_item_order.total_price = float(item_price)
                        if currency:
                            shopturbo_item_order.currency = currency

                        if not order_item_name:
                            order_item_name = f"Item {line_item_id}"

                        if not product_id and not product_sku:
                            shopturbo_item_order.custom_item_name = order_item_name

                        shopturbo_item_order.save()

                        if mapping_custom_fields:
                            for field in mapping_custom_fields:
                                if "|line_item" in field:
                                    sanka_field = mapping_custom_fields[field].replace(
                                        "|line_item", ""
                                    )
                                    value = None

                                    if field == "name|line_item":
                                        value = (
                                            order_item_name or f"Item {line_item_id}"
                                        )
                                    elif field == "discount|line_item":
                                        value = discount or discount_amount
                                    elif field == "product_id|line_item":
                                        value = custom_product_id or product_id
                                    else:
                                        value = line_item_response.properties.get(
                                            field.replace("|line_item", ""), None
                                        )

                                    # custom_field = DealsItemsOrdersNameCustomField.objects.filter(
                                    #     workspace=workspace, name=sanka_field
                                    # ).first()
                                    # if custom_field:
                                    #     custom_value, _ = (
                                    #         DealsItemsOrdersValueCustomField.objects.get_or_create(
                                    #             field_name=custom_field,
                                    #             item_order=shopturbo_item_order,
                                    #         )
                                    #     )
                                    #     custom_value.value = value
                                    #     if discount and field == "discount|line_item":
                                    #         custom_value.value_number_format = "%"
                                    #     elif discount_amount:
                                    #         custom_value.value_number_format = "number"
                                    #     custom_value.save()

                        if product_sku:
                            try:
                                product_response = None

                                try:
                                    item_platform, _ = (
                                        ShopTurboItemsPlatforms.objects.get_or_create(
                                            channel=channel,
                                            platform_id=product_sku,
                                            platform_type="default",
                                        )
                                    )
                                except:
                                    item_platform = (
                                        ShopTurboItemsPlatforms.objects.filter(
                                            channel=channel,
                                            platform_id=product_sku,
                                            platform_type="default",
                                        ).first()
                                    )

                                if product_id:
                                    try:
                                        product_response = (
                                            api_client.crm.products.basic_api.get_by_id(
                                                product_id
                                            )
                                        )
                                        # Convert old platform IDs
                                        existing_item_platform = (
                                            ShopTurboItemsPlatforms.objects.filter(
                                                channel=channel, platform_id=product_id
                                            ).first()
                                        )
                                        if existing_item_platform:
                                            existing_item_platform_item = (
                                                existing_item_platform.item
                                            )
                                            if existing_item_platform_item:
                                                item_platform.item = (
                                                    existing_item_platform_item
                                                )
                                                item_platform.save()
                                            existing_item_platform.delete()
                                    except:
                                        pass

                                update_value = True
                                key_item_field = "None"

                                if item_platform.item:
                                    shopturbo_item = item_platform.item
                                    if key_item_field != "None":
                                        custom_field = (
                                            DealsItemsNameCustomField.objects.filter(
                                                workspace=workspace, name=key_item_field
                                            ).first()
                                        )
                                        custom_value = (
                                            DealsItemsValueCustomField.objects.filter(
                                                field_name=custom_field,
                                                value=product_sku,
                                            ).first()
                                        )
                                        if custom_value:
                                            shopturbo_item = custom_value.items
                                            shopturbo_item.product_id = product_sku
                                        else:
                                            custom_value, _ = (
                                                ShopTurboItemsValueCustomField.objects.get_or_create(
                                                    field_name=custom_field,
                                                    items=shopturbo_item,
                                                )
                                            )
                                            custom_value.value = product_sku
                                            custom_value.save()
                                else:
                                    if shopturbo_item_order.item:
                                        shopturbo_item = shopturbo_item_order.item
                                        if key_item_field != "None":
                                            custom_field = ShopTurboItemsNameCustomField.objects.filter(
                                                workspace=workspace, name=key_item_field
                                            ).first()
                                            custom_value, _ = (
                                                ShopTurboItemsValueCustomField.objects.get_or_create(
                                                    field_name=custom_field,
                                                    items=shopturbo_item,
                                                )
                                            )
                                            custom_value.value = product_sku
                                            custom_value.save()
                                    else:
                                        if key_item_field != "None":
                                            custom_field = ShopTurboItemsNameCustomField.objects.filter(
                                                workspace=workspace, name=key_item_field
                                            ).first()
                                            custom_value = ShopTurboItemsValueCustomField.objects.filter(
                                                field_name=custom_field,
                                                value=product_sku,
                                            ).first()
                                            if custom_value:
                                                shopturbo_item = custom_value.items
                                                shopturbo_item.product_id = product_sku
                                            else:
                                                update_value = True
                                                shopturbo_item = (
                                                    ShopTurboItems.objects.create(
                                                        workspace=workspace,
                                                        platform=platform,
                                                        product_id=product_sku,
                                                    )
                                                )
                                                custom_value, _ = (
                                                    ShopTurboItemsValueCustomField.objects.get_or_create(
                                                        field_name=custom_field,
                                                        items=shopturbo_item,
                                                    )
                                                )
                                                custom_value.value = product_sku
                                                custom_value.save()
                                        else:
                                            update_value = True
                                            shopturbo_item = (
                                                ShopTurboItems.objects.create(
                                                    workspace=workspace,
                                                    platform=platform,
                                                    product_id=product_sku,
                                                )
                                            )

                                if product_response:
                                    item_name = product_response.properties.get("name")
                                    item_price = product_response.properties.get(
                                        "price"
                                    )
                                else:
                                    item_name = order_item_name

                                if update_value:
                                    if item_name:
                                        shopturbo_item.name = item_name
                                    if item_price:
                                        shopturbo_item.price = float(item_price)
                                    if currency:
                                        shopturbo_item.currency = currency

                                shopturbo_item.save()

                                item_platform.platform_id = product_sku
                                item_platform.item = shopturbo_item
                                item_platform.save()

                                price, _ = ShopTurboItemsPrice.objects.get_or_create(
                                    item=shopturbo_item,
                                    price=float(item_price),
                                    currency=currency,
                                )

                                check_default = ShopTurboItemsPrice.objects.filter(
                                    item=shopturbo_item, default=True
                                )
                                if len(check_default) == 0:
                                    price.default = True
                                elif update_value:
                                    for defaults in check_default:
                                        defaults.default = False
                                        defaults.save()
                                    price.default = True
                                price.name = item_name
                                price.save()

                                shopturbo_item_order.item = shopturbo_item
                                shopturbo_item_order.save()
                            except:
                                traceback.print_exc()
                                pass

                        elif product_id:
                            try:
                                product_response = (
                                    api_client.crm.products.basic_api.get_by_id(
                                        product_id
                                    )
                                )
                                # print("Product:=======", product_response)
                                item_platform, _ = (
                                    ShopTurboItemsPlatforms.objects.get_or_create(
                                        channel=channel,
                                        platform_id=product_id,
                                        platform_type="default",
                                    )
                                )

                                if item_platform.item:
                                    shopturbo_item = item_platform.item
                                else:
                                    if shopturbo_item_order.item:
                                        shopturbo_item = shopturbo_item_order.item
                                    else:
                                        shopturbo_item = ShopTurboItems.objects.create(
                                            workspace=workspace,
                                            platform=platform,
                                            product_id=product_id,
                                            status="active",
                                        )

                                item_name = product_response.properties.get("name")
                                item_price = product_response.properties.get("price")

                                if item_name:
                                    shopturbo_item.name = item_name
                                if item_price:
                                    shopturbo_item.price = float(item_price)
                                if currency:
                                    shopturbo_item.currency = currency

                                shopturbo_item.save()

                                item_platform.platform_id = product_id
                                item_platform.item = shopturbo_item
                                item_platform.save()

                                # print("Item Platform=======:", item_platform.__dict__, shopturbo_item.__dict__)

                                price, _ = ShopTurboItemsPrice.objects.get_or_create(
                                    item=shopturbo_item,
                                    price=float(item_price),
                                    currency=currency,
                                )

                                check_default = ShopTurboItemsPrice.objects.filter(
                                    item=shopturbo_item, default=True
                                )
                                if len(check_default) == 0:
                                    price.default = True
                                price.name = item_name
                                price.save()

                                shopturbo_item_order.item = shopturbo_item
                                shopturbo_item_order.save()

                            except ProductsApiException as e:
                                print(f"... ERROR when FETCH product from hubspot: {e}")
                        else:
                            print("No product ID found for line item:", line_item_id)
                        # print("Item Order=======:", shopturbo_item_order.__dict__)

                    except LineItemsApiException as e:
                        print(f"... ERROR when FETCH line item from hubspot: {e}")

                shopturbo_order.save()

                imported_orders.append(shopturbo_order.id)

                # print("Order:", shopturbo_order.__dict__)
                if mapping_custom_fields:
                    print(mapping_custom_fields)
                    for field in mapping_custom_fields:
                        sanka_field = mapping_custom_fields[field]
                        if field in [
                            "deal_name",
                            "deal_stage",
                            "issue_date",
                            "billing_date",
                            "partner_display_name",
                        ]:
                            value = None
                            if field == "deal_name":
                                value = deal.properties.get("dealname", None)
                            if field == "deal_stage":
                                value = deal.properties.get("dealstage", None)
                            if field == "issue_date":
                                value = deal.properties.get("createdate", None)
                            if field == "billing_date":
                                value = deal.properties.get("closedate", None)
                            if field == "partner_display_name":
                                value = deal_company.properties.get("name", None)

                            custom_field = DealsNameCustomField.objects.filter(
                                workspace=workspace, name=sanka_field
                            ).first()
                            if custom_field:
                                if field == "deal_stage" and not updated_deal_stage:
                                    if not custom_field.choice_value:
                                        available_choice_values = []
                                    else:
                                        available_choice_values = ast.literal_eval(
                                            custom_field.choice_value
                                        )
                                    for stage in deal_stages:
                                        if lang == "ja":
                                            translated_stage = {
                                                "appointmentscheduled": "アポイント設定済み (販売パイプライン)",
                                                "qualifiedtobuy": "購入見込みあり (販売パイプライン)",
                                                "presentationscheduled": "プレゼン予定済み (販売パイプライン)",
                                                "decisionmakerboughtin": "意思決定者の賛同 (販売パイプライン)",
                                                "contractsent": "契約書送付済み (販売パイプライン)",
                                                "closedwon": "成約 (販売パイプライン)",
                                                "closedlost": "クローズした不成立取引 (販売パイプライン)",
                                            }
                                            if stage.id in translated_stage:
                                                stage_dict = {
                                                    "label": translated_stage[stage.id],
                                                    "value": stage.id,
                                                }
                                            else:
                                                stage_dict = {
                                                    "label": stage.label,
                                                    "value": stage.id,
                                                }
                                        else:
                                            stage_dict = {
                                                "label": stage.label,
                                                "value": stage.id,
                                            }

                                        if stage_dict not in available_choice_values:
                                            update_label = False
                                            for choice in available_choice_values:
                                                if (
                                                    choice["value"]
                                                    == stage_dict["value"]
                                                ):
                                                    choice["label"] = stage_dict[
                                                        "label"
                                                    ]
                                                    update_label = True
                                                    break
                                            if not update_label:
                                                available_choice_values.append(
                                                    stage_dict
                                                )

                                    custom_field.choice_value = json.dumps(
                                        available_choice_values
                                    )
                                    custom_field.type = "choice"
                                    custom_field.save()
                                    updated_deal_stage = True

                                custom_value, _ = (
                                    DealsValueCustomField.objects.get_or_create(
                                        field_name=custom_field, deals=shopturbo_order
                                    )
                                )
                                custom_value.value = value
                                custom_value.save()
                        else:
                            value = None
                            if "|deal" in field:
                                field = field.replace("|deal", "")
                                value = deal.properties.get(field, None)
                            if "|contact" in field:
                                field = field.replace("|contact", "")
                                if associated_contact:
                                    value = associated_contact.get(field, None)
                            if "|company" in field:
                                field = field.replace("|company", "")
                                if associated_company:
                                    value = associated_company.get(field, None)

                            if "|contact" in sanka_field:
                                sanka_field = sanka_field.replace("|contact", "")
                                try:
                                    if sanka_field in [
                                        "name",
                                        "last_name",
                                        "email",
                                        "phone_number",
                                    ]:
                                        if shopturbo_order.contact:
                                            contact = shopturbo_order.contact.first()
                                            if sanka_field == "name":
                                                contact.name = value
                                            elif sanka_field == "last_name":
                                                contact.last_name = value
                                            elif sanka_field == "email":
                                                contact.email = value
                                            elif sanka_field == "phone_number":
                                                contact.phone_number = value
                                            contact.save()
                                except:
                                    pass
                                custom_field = ContactsNameCustomField.objects.filter(
                                    workspace=workspace, name=sanka_field
                                ).first()
                            elif "|company" in sanka_field:
                                sanka_field = sanka_field.replace("|company", "")
                                try:
                                    if sanka_field in ["name", "email", "phone_number"]:
                                        if shopturbo_order.company:
                                            company = shopturbo_order.company.first()
                                            if sanka_field == "name":
                                                company.name = value
                                            elif sanka_field == "email":
                                                company.email = value
                                            elif sanka_field == "phone_number":
                                                company.phone_number = value
                                            company.save()
                                except:
                                    pass
                                custom_field = CompanyNameCustomField.objects.filter(
                                    workspace=workspace, name=sanka_field
                                ).first()
                            else:
                                custom_field = DealsNameCustomField.objects.filter(
                                    workspace=workspace, name=sanka_field
                                ).first()
                            if custom_field:
                                if isinstance(custom_field, ContactsNameCustomField):
                                    if shopturbo_order.contact:
                                        custom_value, _ = (
                                            ContactsValueCustomField.objects.get_or_create(
                                                field_name=custom_field,
                                                contact=shopturbo_order.contact.first(),
                                            )
                                        )
                                        custom_value.value = value
                                        custom_value.save()
                                elif isinstance(custom_field, CompanyNameCustomField):
                                    if shopturbo_order.company:
                                        custom_value, _ = (
                                            CompanyValueCustomField.objects.get_or_create(
                                                field_name=custom_field,
                                                company=shopturbo_order.company.first(),
                                            )
                                        )
                                        custom_value.value = value
                                        custom_value.save()
                                else:
                                    custom_value, _ = (
                                        DealsValueCustomField.objects.get_or_create(
                                            field_name=custom_field,
                                            deals=shopturbo_order,
                                        )
                                    )
                                    custom_value.value = value
                                    custom_value.save()
            except Exception as e:
                traceback.print_exc()
                print(f"... ERROR when FETCH associations from hubspot: {e}")

    except Exception as e:
        print(f"... ERROR when FETCH list deal from hubspot: {e}")
        task = (
            TransferHistory.objects.filter(workspace=workspace, type="import_order")
            .order_by("-created_at")
            .first()
        )
        if task:
            task.error_message = e
            task.save()
        return

    return imported_orders


def fetch_companies_in_deals(channel_id: str, deal_object_ids: list):
    channel = Channel.objects.get(pk=channel_id)
    access_token = channel.access_token
    client = HubSpot(access_token=access_token)

    inputs = [{"id": id} for id in deal_object_ids]

    results = {}
    for chunk in chunks_list(inputs, 100):
        try:
            batch_input_public_fetch_associations_batch_request = (
                associations.BatchInputPublicObjectId(inputs=chunk)
            )
            api_response = client.crm.associations.v4.batch_api.get_page(
                from_object_type="deal",
                to_object_type="company",
                batch_input_public_fetch_associations_batch_request=batch_input_public_fetch_associations_batch_request,
            )
            if isinstance(
                api_response, associations.BatchResponsePublicAssociationWithErrors
            ) or isinstance(
                api_response, associations.BatchResponsePublicAssociationMultiWithErrors
            ):
                if api_response.errors[0].errors:
                    print(
                        f"... ERROR === hubspot.py -- 749: {api_response.errors[0].message}"
                    )
            elif len(api_response.results) > 0:
                for _result in api_response.results:
                    results[_result._from.id] = _result.to[0].to_object_id

        except Exception as e:
            print(f"... ERROR === hubspot.py -- 754: {e}")

    return results


async def sync_contacts(channel_id, lang, user_id, mapping_custom_fields=None):
    try:
        channel = await Channel.objects.select_related("workspace", "integration").aget(
            id=channel_id
        )
        access_token = channel.access_token
        user = await sync_to_async(User.objects.get)(id=user_id)

        task = await sync_to_async(
            lambda: TransferHistory.objects.filter(
                workspace=channel.workspace,
                user=user,
                type="import_contact",
                channel=channel,
            )
            .order_by("-created_at")
            .first()
        )()

        properties = ["firstname", "lastname", "email", "phone", "website"]
        if mapping_custom_fields:
            for field in mapping_custom_fields:
                if field not in properties:
                    properties.append(field)

        try:
            # Assuming get_records_object is synchronous — wrap in sync_to_async
            _, contact_records = await sync_to_async(get_records_object)(
                access_token, "contacts", properties
            )
        except ApiException as e:
            print(f"Exception when calling get_records_object for contacts: {e}\n")
            contact_records = []

        print(contact_records)
        platform_ids = []

        if task:
            task.total_number = len(contact_records)
            task.success_number = 0
            await sync_to_async(task.save)()

        for idx, record in enumerate(contact_records):
            task = await sync_to_async(
                lambda: TransferHistory.objects.filter(
                    workspace=channel.workspace, type="import_contact", channel=channel
                )
                .order_by("-created_at")
                .first()
            )()

            if task:
                if task.status == "canceled":
                    return False
                progress = round((idx + 1) / len(contact_records) * 100, 2)
                task.progress = progress
                await sync_to_async(task.save)()

            platform_id = record.get("id")
            props = record.get("properties", {})
            name = props.get("firstname", "")
            lastname = props.get("lastname", "")
            email = props.get("email")
            phone_number = props.get("phone")
            website = props.get("website")
            print(
                f"Processing contact: {name}, Lastname: {lastname}, Email: {email}, Phone: {phone_number}, Website: {website}"
            )

            try:
                contactplatform, _ = await sync_to_async(
                    ContactsPlatforms.objects.select_related("contact").get_or_create
                )(platform_id=platform_id, channel=channel)
            except:
                contactplatform = await sync_to_async(
                    lambda: ContactsPlatforms.objects.select_related("contact")
                    .filter(platform_id=platform_id, channel=channel)
                    .first()
                )()

            try:
                if contactplatform.contact:
                    contact = contactplatform.contact
                else:
                    contact = await sync_to_async(Contact.objects.create)(
                        workspace=channel.workspace
                    )
                    contactplatform.contact = contact
                    await sync_to_async(contactplatform.save)()

                if task:
                    task.success_number += 1
                    await sync_to_async(task.save)()
            except Exception as e:
                print("Error: ", e)

            if name == "None":
                name = None
            if lastname == "None":
                lastname = None
            if not name and not lastname:
                name = email
            contact.name = name
            contact.last_name = lastname
            contact.phone_number = phone_number
            contact.email = email
            contact.status = "active"

            if mapping_custom_fields:
                workspace = channel.workspace
                for hubspot_field, sanka_field in mapping_custom_fields.items():
                    value = props.get(hubspot_field)
                    if value is not None:
                        custom_field = await sync_to_async(
                            lambda: ContactsNameCustomField.objects.filter(
                                workspace=workspace, name=sanka_field
                            ).first()
                        )()
                        if custom_field:
                            custom_value, _ = await sync_to_async(
                                ContactsValueCustomField.objects.get_or_create
                            )(field_name=custom_field, contact=contact)
                            custom_value.value = value
                            await sync_to_async(custom_value.save)()
                        else:
                            try:
                                if sanka_field == "full_name":
                                    contact.name = value
                                else:
                                    setattr(contact, sanka_field, value)
                            except:
                                print("Error setting contact field:", sanka_field)
                                pass

            await sync_to_async(contact.save)()

            if website:
                await sync_to_async(SocialAccountLink.objects.get_or_create)(
                    contact=contact, platform_url=website
                )

            print("Contact saved:", contact)
            platform_ids.append(platform_id)

        contact_platforms = await sync_to_async(
            lambda: list(
                ContactsPlatforms.objects.select_related("contact")
                .filter(channel=channel)
                .exclude(platform_id__in=platform_ids)
            )
        )()
        for contact_platform in contact_platforms:
            await sync_to_async(contact_platform.delete)()
            print("Contact platform deleted:", contact_platform)
            contact = contact_platform.contact
            if lang == "ja":
                await sync_to_async(Notification.objects.create)(
                    workspace=channel.workspace,
                    user=user,
                    message=f"HubSpotのプラットフォームIDを削除しました。連絡先ID: {contact.contact_id:04d} はHubSpot上で利用できなくなりました。",
                    type="info",
                )
            else:
                await sync_to_async(Notification.objects.create)(
                    workspace=channel.workspace,
                    user=user,
                    message=f"HubSpot platform ID removed. Contact ID: {contact.contact_id:04d} is no longer available on HubSpot.",
                    type="info",
                )

        if lang == "ja":
            await sync_to_async(Notification.objects.create)(
                workspace=channel.workspace,
                user=user,
                message="HubSpotの連絡先が正常にインポートされました。",
                type="success",
            )
        else:
            await sync_to_async(Notification.objects.create)(
                workspace=channel.workspace,
                user=user,
                message="HubSpot contact successfully imported.",
                type="success",
            )

        return True

    except Exception as e:
        print("Error in sync_contacts:", e)
        traceback.print_exc()
        return False


async def sync_companies(channel_id, lang, user_id, mapping_custom_fields=None):
    try:
        channel = await Channel.objects.select_related("workspace", "integration").aget(
            id=channel_id
        )
        access_token = channel.access_token

        user = await sync_to_async(User.objects.get)(id=user_id)

        task = await sync_to_async(
            lambda: TransferHistory.objects.filter(
                workspace=channel.workspace,
                user=user,
                type="import_company",
                channel=channel,
            )
            .order_by("-created_at")
            .first()
        )()

        try:
            properties = ["name", "address", "phone", "website"]

            if mapping_custom_fields:
                for field in mapping_custom_fields:
                    if field not in properties:
                        properties.append(field)

            # Wrap external API call with sync_to_async
            _, company_records = await sync_to_async(get_records_object)(
                access_token, "companies", properties
            )
        except ApiException as e:
            print(f"Exception when calling basic_api->get_page: {e}")

        print(company_records)
        platform_ids = []

        if task:
            task.total_number = len(company_records)
            await sync_to_async(task.save)()

        for record in company_records:
            task = await sync_to_async(
                lambda: TransferHistory.objects.filter(
                    workspace=channel.workspace, type="import_company", channel=channel
                )
                .order_by("-created_at")
                .first()
            )()

            if task:
                if task.status == "canceled":
                    return False

                progress = round(
                    (company_records.index(record) + 1) / len(company_records) * 100, 2
                )
                task.progress = progress
                task.success_number = company_records.index(record) + 1
                await sync_to_async(task.save)()

            platform_id = record["id"]
            properties = record["properties"]
            name = properties.get("name")
            website = properties.get("website")
            address = properties.get("address")
            phone = properties.get("phone")

            try:
                companyplatform, _ = await sync_to_async(
                    CompanyPlatforms.objects.select_related("company").get_or_create
                )(
                    platform_id=platform_id,
                    channel=channel,
                )
            except:
                companyplatform = await sync_to_async(
                    lambda: CompanyPlatforms.objects.select_related("company")
                    .filter(
                        platform_id=platform_id,
                        channel=channel,
                    )
                    .first()
                )()

            try:
                if companyplatform.company:
                    company = companyplatform.company
                else:
                    company = await sync_to_async(Company.objects.create)(
                        workspace=channel.workspace
                    )
                    companyplatform.company = company
                    await sync_to_async(companyplatform.save)()
            except Exception as e:
                print("[ERROR] import company from hubspot ", e)

            company.name = name
            company.address = address
            company.url = website
            company.phone_number = phone
            company.status = "active"

            await sync_to_async(company.save)()

            if mapping_custom_fields:
                workspace = channel.workspace
                for hubspot_field, sanka_field in mapping_custom_fields.items():
                    value = properties.get(hubspot_field)
                    if value is not None:
                        custom_field = await sync_to_async(
                            lambda: CompanyNameCustomField.objects.filter(
                                workspace=workspace, name=sanka_field
                            ).first()
                        )()

                        if custom_field:
                            custom_value, _ = await sync_to_async(
                                CompanyValueCustomField.objects.get_or_create
                            )(
                                field_name=custom_field,
                                company=company,
                            )
                            custom_value.value = value
                            await sync_to_async(custom_value.save)()
                        else:
                            try:
                                setattr(company, sanka_field, value)
                            except:
                                print("Error setting company field:", sanka_field)
                                pass

            platform_ids.append(platform_id)

        company_platforms = await sync_to_async(
            lambda: list(
                CompanyPlatforms.objects.select_related("company")
                .filter(channel=channel)
                .exclude(platform_id__in=platform_ids)
            )
        )()

        for company_platform in company_platforms:
            await sync_to_async(company_platform.delete)()
            print("Company platform deleted:", company_platform)
            if lang == "ja":
                await sync_to_async(Notification.objects.create)(
                    workspace=channel.workspace,
                    user=user,
                    message=f"HubSpotのプラットフォームIDを削除しました。連絡先ID: {company.company_id:04d} はHubSpot上で利用できなくなりました。",
                    type="info",
                )
            else:
                await sync_to_async(Notification.objects.create)(
                    workspace=channel.workspace,
                    user=user,
                    message=f"HubSpot platform ID removed. Contact ID: {company.company_id:04d} is no longer available on HubSpot.",
                    type="info",
                )

        if lang == "ja":
            await sync_to_async(Notification.objects.create)(
                workspace=channel.workspace,
                user=user,
                message="HubSpotの連絡先が正常にインポートされました。",
                type="success",
            )
        else:
            await sync_to_async(Notification.objects.create)(
                workspace=channel.workspace,
                user=user,
                message="HubSpot contact successfully imported.",
                type="success",
            )

        return True

    except Exception as e:
        print("Error in sync_contacts:", e)
        traceback.print_exc()
        return False


def import_hubspot_custom_object(
    channel_id: str,
    custom_object_id: str,
    platform_object_type_id: str,
    mapping_custom_fields={},
    lang="ja",
    user_id="",
):
    channel = Channel.objects.get(pk=channel_id)
    workspace = channel.workspace
    platform = channel.integration.slug
    user = User.objects.get(id=user_id)

    custom_object = CustomObject.objects.filter(id=custom_object_id).first()

    task = (
        TransferHistory.objects.filter(
            workspace=workspace, type=f"import_{custom_object.slug}"
        )
        .order_by("-created_at")
        .first()
    )

    if lang == "ja":
        Notification.objects.create(
            workspace=workspace,
            user=user,
            message=f"{custom_object.name}をインポートしております。少々お待ちください...",
            type="success",
        )
    else:
        Notification.objects.create(
            workspace=workspace,
            user=user,
            message=f"{custom_object.name} being imported. Please give it a few moments...",
            type="success",
        )

    try:
        access_token = refresh_hubspot_token(channel.id)
        hubspot_properties = list(mapping_custom_fields.keys()) + ["hs_object_id"]
        _, hubspot_records = get_records_object(
            access_token,
            platform_object_type_id,
            [
                custom_field.split("|")[0]
                for custom_field in hubspot_properties
                if "|" in custom_field
            ],
        )

        EXCLUDED_FIELDS = []
        count_processed_records = 0
        hubspot_records_length = len(hubspot_records)
        for record in hubspot_records:
            properties = record["properties"]

            customobjectplatform = CustomObjectPlatforms.objects.filter(
                channel=channel,
                custom_object=custom_object,
                platform_id=properties.get("hs_object_id", ""),
            ).first()
            custom_object_row = None
            if customobjectplatform:
                custom_object_row = customobjectplatform.object
            else:
                custom_object_row = CustomObjectPropertyRow.objects.create(
                    workspace=workspace,
                    custom_object=custom_object,
                    usage_status="active",
                )

                customobjectplatform = CustomObjectPlatforms.objects.create(
                    channel=channel,
                    custom_object=custom_object,
                    object=custom_object_row,
                    platform=platform,
                    platform_object_type=platform_object_type_id,
                    platform_id=properties.get("hs_object_id", ""),
                )

            for hubspot_property, sanka_property in mapping_custom_fields.items():
                if "|" not in hubspot_property:
                    continue
                hubspot_property_list = hubspot_property.split("|")
                hubspot_property_name = hubspot_property_list[0]
                hubspot_property_type = hubspot_property_list[1]

                CustomFieldName = CustomObjectPropertyName.objects.filter(
                    workspace=workspace,
                    custom_object=custom_object,
                    name=sanka_property,
                ).first()

                if sanka_property not in EXCLUDED_FIELDS:
                    if CustomFieldName:
                        CustomFieldValue, _ = (
                            CustomObjectPropertyValue.objects.get_or_create(
                                **{
                                    "field_name": CustomFieldName,
                                    "object": custom_object_row,
                                }
                            )
                        )
                        if hubspot_property_type in ["date", "datetime", "date_time"]:
                            date_string = properties[hubspot_property_name]
                            date_value = (
                                parser.parse(date_string) if date_string else ""
                            )

                            if date_value:
                                CustomFieldValue.value_time = date_value
                            CustomFieldValue.value = str(date_value)
                        elif hubspot_property_type in "number":
                            hubspot_value = properties[hubspot_property_name]
                            CustomFieldValue.value_number = (
                                float(hubspot_value)
                                if hubspot_value is not None
                                else 0.0
                            )
                            CustomFieldValue.value = (
                                hubspot_value if hubspot_value is not None else ""
                            )
                        else:
                            hubspot_value = properties[hubspot_property_name]
                            if hubspot_value is not None:
                                CustomFieldValue.value = hubspot_value
                            else:
                                CustomFieldValue.value = ""

                CustomFieldValue.save()

            count_processed_records += 1
            task.status = "running"
            task.progress = round(
                float(count_processed_records / hubspot_records_length) * 100, 2
            )
            task.save()

        if lang == "ja":
            Notification.objects.create(
                workspace=workspace,
                user=user,
                message=f"{custom_object.name}が Hubspot に正常にインポートされました",
                type="success",
            )
        else:
            Notification.objects.create(
                workspace=workspace,
                user=user,
                message=f"{custom_object.name} successfully imported to Hubspot",
                type="success",
            )

        task.status = "completed"
        task.progress = 100
        task.save()

    except Exception as e:
        task.status = "Failed"
        task.save()
        traceback.print_exc()
        print(f"[ERROR] Error import hubspot custom object {custom_object.name}, ", e)

        if lang == "ja":
            Notification.objects.create(
                workspace=workspace,
                user=user,
                message=f"Hubspot {custom_object.name} のインポートに失敗しました",
                type="error",
            )
        else:
            Notification.objects.create(
                workspace=workspace,
                user=user,
                message=f"Hubspot {custom_object.name} import failed",
                type="error",
            )


def import_hubspot_subscriptions_custom_object(
    channel_id: str,
    platform_object_type_id: str,
    mapping_custom_fields={},
    lang="ja",
    user_id="",
):
    channel = Channel.objects.get(pk=channel_id)
    workspace = channel.workspace
    platform = channel.integration.slug
    user = User.objects.get(id=user_id)

    task = (
        TransferHistory.objects.filter(
            workspace=workspace, type=f"import_subscriptions"
        )
        .order_by("-created_at")
        .first()
    )

    if lang == "ja":
        Notification.objects.create(
            workspace=workspace,
            user=user,
            message=f"サブスクリプションをインポートしております。少々お待ちください...",
            type="success",
        )
    else:
        Notification.objects.create(
            workspace=workspace,
            user=user,
            message=f"Subscriptions being imported. Please give it a few moments...",
            type="success",
        )

    try:
        access_token = refresh_hubspot_token(channel.id)
        hubspot_properties = list(mapping_custom_fields.keys()) + ["hs_object_id"]
        _, hubspot_records = get_records_object(
            access_token,
            platform_object_type_id,
            [
                custom_field.split("|")[0]
                for custom_field in hubspot_properties
                if "|" in custom_field
            ],
        )

        EXCLUDED_FIELDS = []
        count_processed_records = 0
        hubspot_records_length = len(hubspot_records)
        for record in hubspot_records:
            print("log record: ", record)
            properties = record["properties"]

            subscriptionobjectplatform = ShopTurboSubscriptionPlatforms.objects.filter(
                channel=channel,
                platform_id=properties.get("hs_object_id", ""),
            ).first()
            subscription = None
            if subscriptionobjectplatform:
                subscription = subscriptionobjectplatform.source_subscription
            else:
                subscription = ShopTurboSubscriptions.objects.create(
                    workspace=workspace,
                )

                subscriptionobjectplatform = (
                    ShopTurboSubscriptionPlatforms.objects.create(
                        channel=channel,
                        source_subscription=subscription,
                        platform_id=properties.get("hs_object_id", ""),
                    )
                )
            subscription.status = "active"
            subscription.save()

            for hubspot_property, sanka_property in mapping_custom_fields.items():
                if "|" not in hubspot_property:
                    continue
                hubspot_property_list = hubspot_property.split("|")
                hubspot_property_name = hubspot_property_list[0]
                hubspot_property_type = hubspot_property_list[1]

                CustomFieldName = ShopTurboSubscriptionsNameCustomField.objects.filter(
                    workspace=workspace,
                    name=sanka_property,
                ).first()

                if sanka_property not in EXCLUDED_FIELDS:
                    try:
                        if CustomFieldName:
                            CustomFieldValue, _ = (
                                ShopTurboSubscriptionsValueCustomField.objects.get_or_create(
                                    field_name=CustomFieldName,
                                    subscriptions=subscription,
                                )
                            )
                            if hubspot_property_type in [
                                "date",
                                "datetime",
                                "date_time",
                            ]:
                                date_string = properties[hubspot_property_name]
                                date_value = (
                                    parser.parse(date_string) if date_string else ""
                                )

                                CustomFieldValue.value_time = date_value
                                CustomFieldValue.value = str(date_value)
                            elif hubspot_property_type in "number":
                                hubspot_value = properties[hubspot_property_name]
                                CustomFieldValue.value_number = (
                                    float(hubspot_value)
                                    if hubspot_value is not None
                                    else 0.0
                                )
                                CustomFieldValue.value = (
                                    hubspot_value if hubspot_value is not None else ""
                                )
                            else:
                                hubspot_value = properties[hubspot_property_name]
                                if hubspot_value is not None:
                                    CustomFieldValue.value = hubspot_value
                                else:
                                    CustomFieldValue.value = ""

                            CustomFieldValue.save()
                        else:
                            hubspot_value = properties[hubspot_property_name]
                            if hubspot_value:
                                if hubspot_property_type in [
                                    "date",
                                    "datetime",
                                    "date_time",
                                ]:
                                    date_string = properties[hubspot_property_name]
                                    date_value = (
                                        parser.parse(date_string).strftime("%Y-%m-%d")
                                        if date_string
                                        else ""
                                    )
                                    setattr(
                                        subscription, sanka_property, str(date_value)
                                    )
                                elif hubspot_property_type in "number":
                                    hubspot_value = properties[hubspot_property_name]
                                    setattr(
                                        subscription,
                                        sanka_property,
                                        float(hubspot_value)
                                        if hubspot_value is not None
                                        else 0.0,
                                    )
                                else:
                                    hubspot_value = properties[hubspot_property_name]
                                    if hubspot_value is not None:
                                        setattr(
                                            subscription, sanka_property, hubspot_value
                                        )
                            subscription.save()
                    except Exception as e:
                        logger.info(
                            f"[ERROR] Error setting field {sanka_property} for subscription {subscription.id}: {e}"
                        )

            count_processed_records += 1
            task.status = "running"
            task.progress = round(
                float(count_processed_records / hubspot_records_length) * 100, 2
            )
            task.save()

        if lang == "ja":
            Notification.objects.create(
                workspace=workspace,
                user=user,
                message="サブスクリプションが Hubspot に正常にインポートされました",
                type="success",
            )
        else:
            Notification.objects.create(
                workspace=workspace,
                user=user,
                message="Subscription successfully imported to Hubspot",
                type="success",
            )

        task.status = "completed"
        task.progress = 100
        task.save()
        return True

    except Exception as e:
        task.status = "Failed"
        task.save()
        print("[ERROR] Error import hubspot custom object subscription, ", e)

        if lang == "ja":
            Notification.objects.create(
                workspace=workspace,
                user=user,
                message="Hubspot サブスクリプション のインポートに失敗しました",
                type="error",
            )
        else:
            Notification.objects.create(
                workspace=workspace,
                user=user,
                message="Hubspot Subscription import failed",
                type="error",
            )
        return False
