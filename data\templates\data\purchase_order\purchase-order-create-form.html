{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% if source_integration %}
    {% apply_language_from_hubspot request.LANGUAGE_CODE as LANGUAGE_CODE %}
{% else %}
    {% get_current_language as LANGUAGE_CODE %}
{% endif %}

{% if section == 'single-entry' %}
    <div id="object-overviews" class="tab-switcher-content">
        {% if purchase.id %}
            <div class="mb-5">
                <label class="{% include 'data/utility/form-label.html' %}">
                    <span>
                        ID
                    </span>
                </label>
                <span class="mt-5 fw-bolder fs-4">
                    {{purchase.id_po|stringformat:"04d"}}
                </span>
            </div>
        {% endif %}
        {% if purchase.usage_status == 'archived' %}
            <div class="card-body mt-0 mb-4 px-10">
                <div class="fv-rowd-flex flex-column">
                    <h3>
                        {% if LANGUAGE_CODE == 'ja'%}
                        このレコードはアーカイブされました。このレコードを確認して使用するためにアクティブにしてください
                        {% else %}
                        This record was archived. Please activate to see and use this record.
                        {% endif %}
                    </h3>
                    <div class="border-0">
                        {% if permission|check_permission:'archive' %}
                        <input hidden id="submit_action" name="submit_action" value="">
                        <input type="hidden" name="view_id" {% if view_id %} value="{{view_id}}" {% endif %} /> 
                        <form method="POST" {% if purchase.id %} action="{% host_url 'purchase_set' id=purchase.id  host 'app' %}" {% else %} action="{% host_url 'purchase_new' host 'app' %}" {% endif %} enctype="multipart/form-data">
                            {% csrf_token %}
                            
                            <input type="hidden" name="module" value="{{module}}">
                            <input type="hidden" name="object_type" value="{{object_type}}">
                            <button name="post" type="submit" value="toggle" class="flex-column-fluid btn btn-success">
                                {% if LANGUAGE_CODE == 'ja'%}有効化{% else %}Activate{% endif %}
                            </button>
                        </form>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="pe-none user-select-none" hx-get="{% url 'load_drawer_orders' %}" hx-vals='{"drawer_type":"orders", "section":"blurry" }' hx-trigger="load" hx-swap="innerHTML"></div>
        {% else %}

            <form class="mb-20" id="purchase-form" onsubmit="validate_form(event)" method="POST" {% if purchase.id %} action="{% host_url 'purchase_set' id=purchase.id  host 'app' %}" {% else %} action="{% host_url 'purchase_new' host 'app' %}" {% endif %} enctype="multipart/form-data">
                {% csrf_token %}
                <input type="hidden" name="module" value="{{module}}">
                <input type="hidden" name="object_type" value="{{object_type}}">
                <input hidden name="view_id" {% if view_id %} value="{{view_id}}" {% endif %} /> 
                <input hidden name="set_id" {% if set_id %} value="{{set_id}}" {% endif %} /> 
                {% if type_association %}<input hidden name="type_association" value="{{type_association}}">{% endif %}
                {% if source %}<input hidden name="source" value="{{source}}">{% endif %}
                {% if object_id %}<input hidden name="object_id" value="{{object_id}}">{% endif %}
                {% if selected_order_id and selected_order_id != 'None' %}<input hidden name="selected_order_id" value="{{selected_order_id}}">{% endif %}

                {% if associate_id %}
                    <input hidden name="associate_id" value="{{associate_id}}" />
                    <input hidden name="object_id" value="{{object_id}}" />
                    <input hidden name="source" value="{{source}}" />
                    <input hidden name="page" value="{{page}}" />
                    <input hidden name="associate_view_id" value="{{view_id}}" />
                {% endif %}
                
                {% for property in properties.list_all %}
                    {% if property == 'supplier' %}
                        <div class="mt-5">
                            <div class="mb-2">
                                <div class="fv-rowd-flex flex-column mb-8">
                                    <label class="{% include 'data/utility/form-label.html' %}">
                                        <span class="">
                                            {% if LANGUAGE_CODE == 'ja' %}
                                            仕入先
                                            {% else %}
                                            Supplier
                                            {% endif %}
                                        </span>
                                        {% render_contact_company_link "purchase-order" source_hx_target %}
                                    </label>
                                    {% include 'data/common/dynamic-customer-select.html' with form="purchase-form" contact_preselected=contact_preselected company_preselected=company_preselected contact_selected=purchase.contact company_selected=purchase.company associate_id=associate_id %}
                                </div>  
                            </div> 
                        </div>
                    {% elif property == 'owner' %}
                        <div class="owner-form-{{object_type}} mb-5">
                            <label class="{% include 'data/utility/form-label.html' %}">
                                <span class="min-w-100px">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    所有者
                                    {% else %}
                                    Owner
                                    {% endif %}
                                </span>
                            </label>
                            <select data-allow-clear='true' id="owner-input-{{object_type}}" class="h-40px form-select form-select-solid border bg-white select2-this" data-control="select2" name="owner" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}所有者{% else %}Owner{% endif %}">
                                <option></option>
                                {% if purchase.id and purchase.owner and purchase.owner.user %}
                                    <option value="{{purchase.owner.user.username}}" selected>
                                        {{purchase.owner.user.first_name}} - {{purchase.owner.user.email}}
                                    </option>
                                {% endif %}
                                {% for member in permission|get_scope_from_permission:'edit'|get_users_by_scope:user %}
                                    {% if not purchase.owner or member != purchase.owner.user %}
                                    <option value="{{member.username}}">
                                        {{member.first_name}} - {{member.email}}
                                    </option>
                                    {% endif %}
                                {% endfor %}
                            </select>
    
                            <script>
                                $('#owner-input-{{object_type}}-{{form_id}}').select2()
                            </script>
                        </div>
                    {% elif property == 'line_item' %}
                        <div class="mt-5">
                            <div class="w-100">
                                <label class="{% include 'data/utility/form-label.html' %}">
                                    <span class="required">
                                        {% translate_lang "Currency" LANGUAGE_CODE %}
                                    </span>
                                </label>

                                <div class="mb-2">
                                    <select 
                                        id="currency"
                                        hx-trigger="htmx-change"
                                        hx-get="{% host_url 'po_available_items' host 'app' %}"
                                        hx-swap="innerHTML"
                                        hx-target="#add-order-item-section"
                                        hx-vals='js:{"available-items":document.querySelectorAll(".item-lists").length}'
                                        class="form-control h-40px select2-this currency" name="currency" {% if purchase.id %} data-placeholder="{{purchase.currency}}" {% endif %} >
                                        
                                        {% if workspace.currencies %}
                                        <optgroup
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            label="デフォルト通貨"
                                            {% else %}
                                            label="Default Currency"
                                            {% endif %}
                                        >
                                            {% for currency in workspace.currencies|string_list_to_list %}
                                                {% if forloop.counter == 1 %}
                                                    <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>                         
                                                {% elif purchase.currency == currency %}
                                                    <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>   
                                                {% else %}
                                                    <option value="{{currency}}">{{currency|get_currencies_text_symbol}}</option>                         
                                                {% endif %}
                                            {% endfor %}

                                        </optgroup>
                                        {% endif %}

                                        <optgroup
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            label="すべての通貨"
                                            {% else %}
                                            label="All Currency"
                                            {% endif %}
                                        >
                                        {% include 'data/partials/money-currency.html' with obj=purchase.currency %}    
                                        </optgroup>           
                                    
                                    </select>
                                </div>
                                <script>
                                    
                                    $('.currency').on('select2:select', function (e) {
                                        var selectedOption = e.params.data.id;
                                        // Clear the previous selection if no matching option is found
                                        var selectElement = $(this).closest('select').get(0);
                                        selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
                                    });
                                    $('.currency').on('select2:open', function() {
                                        setTimeout(function() {
                                            // Find the first optgroup in the dropdown
                                            
                                            var defaultGroup = $('.select2-results__group').first();
                            
                                            var selectedOption = $('.select2-results__option[aria-selected=true]');
                                            
                                            var dropdown = $('.select2-results__options');
                                            
                                            if (defaultGroup.length) {
                                                // Scroll to the top where the default group starts
                                                dropdown.scrollTop(0);
                                                
                                                // Highlight the group
                                                defaultGroup.trigger('mouseenter');
                                            }
                                        }, 10);
                            
                                    });
                                    {% if associate_id %}
                                        $('#select-contact_and_company_{{associate_id}}').on('select2:unselect', function (e) {
                                            var selectElement = $(this).closest('select').get(0);
                                            selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
                                        });
                                    {% else %}
                                        $('#select-contact_and_company').on('select2:unselect', function (e) {
                                            var selectElement = $(this).closest('select').get(0);
                                            selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
                                        });
                                    {% endif %}
                                </script>
                            </div>
                        </div>
                        <div id="add-order-item-section">
                            {% include 'data/purchase_order/purchase-order-partial-add-items.html' with obj=purchase type_data='purchase' drawer_type='purchase order' currency=purchase.currency %}
                        </div>
                    {% elif property == 'status' %}
                        <div class="mt-5">
                            <div class="mb-5">
                                <label class="{% include 'data/utility/form-label.html' %}">
                                    <span class="required">
                                        {% if LANGUAGE_CODE == 'ja' %}
                                        ステータス
                                        {% else %}
                                        Status
                                        {% endif %}
                                    </span>
                                </label>
                            
                                <select class="form-select select2-this h-40px" name="status" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}ステータスを選択します{% else %}Select Status{% endif %}">
                                    {% if 'status'|get_custom_property_object:purchase %}
                                        {% with value_map_label='status'|get_custom_property_object:purchase|get_attr:'value'|string_list_to_list %}
                                            {% for value, label in value_map_label.items %}
                                                <option value="{{value}}" {% if purchase.status == value %}selected{% endif %}>
                                                    {{label}}
                                                </option>
                                            {% endfor %}
                                        {% endwith %}
                                    {% else %}
                                        {% with status=purchase|get_default_status_display:property %}
                                            {% for key, value in  status.items %}
                                                <option value="{{key}}" {% if purchase.status ==  key %} selected {% endif %}>
                                                    {% if LANGUAGE_CODE == 'ja' %}
                                                        {{value.ja}}
                                                    {% else %}
                                                        {{value.en}}
                                                    {% endif %}
                                                </option>
                                            {% endfor %}
                                        {% endwith %}
                                    {% endif %}
                                </select>
                            </div>
                        </div>
                    {% elif property == 'date' %}
                        <div class="mt-5">
                            <div class="mb-5">
                                <label class="{% include 'data/utility/form-label.html' %}">
                                    <span>
                                        {% if LANGUAGE_CODE == 'ja' %}
                                        日付
                                        {% else %}
                                        Date 
                                        {% endif %}
                                    </span>
                                </label>

                                <div class="mb-0">
                                    <input autocomplete="off" 
                                           id="id_date" 
                                           name="date" 
                                           {% if purchase.id and purchase.date %} value="{{purchase.date}}" {% endif %}
                                           class="form-control dateinput form-control" 
                                           {% if LANGUAGE_CODE == 'ja' %}
                                           placeholder="日付を選択" 
                                           {% else %}
                                           placeholder="Select a date" 
                                           {% endif %}>
                                </div>
                            </div>
                        </div>
                    {% elif property == 'send_from' %}
                        <div class="mt-5">

                            <div class="mb-5">
                                <label class="{% include 'data/utility/form-label.html' %}">
                                    <span>
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        送信元
                                        {% else %}
                                        Send From
                                        {% endif %}
                                    </span>
                                </label>

                                <textarea 
                                placeholder="{% translate_lang "Sanka, Inc." LANGUAGE_CODE %}"

                                name="send_from" 
                                
                                class="rounded form-control w-100 autosize-this" 
                                style="resize: none; overflow: hidden;">{% if purchase.id %}{% if purchase.send_from  %}{{purchase.send_from}}{% endif %}{% elif value_app_setting_send_from %}{{value_app_setting_send_from}}{% endif %}</textarea>
                            </div>
                        </div>
                        {% elif property == 'notes' %}
                        <div class="mt-5">
                            <div class="mb-5">
                                <label class="{% include 'data/utility/form-label.html' %}">
                                    <span>
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        備考欄
                                        {% else %}
                                        Notes
                                        {% endif %}
                                    </span>
                                </label>

                                <div class="emoji-picker-container">
                                    <textarea  
                                    wrap="off" 
                                    
                                    placeholder="{% translate_lang "Bank account, etc." LANGUAGE_CODE %}"

                                    class="rounded form-control w-100 autosize-this" 
                                    style="resize: none; overflow: hidden;"
                                    name="notes" class="form-control" >{% if purchase.id %}{% if purchase.notes %}{{purchase.notes}}{% endif %}{% elif value_app_setting_note %}{{value_app_setting_note}}{% endif %}</textarea>
                                </div>
                            </div>
                        </div>

                    {% elif property|lower in association_label_list and property != 'supplier'%}  {% comment %} supplier already handled by above code {% endcomment %}
                        {% comment %} htmx {% endcomment %}
                        <div hx-get="{% url 'load_association_label_form' %}" 
                            hx-vals='{"obj_id":"{{purchase.id}}", "property":"{{property}}", "page_group_type":"purchaseorder" }' 
                            hx-trigger="load" hx-swap="outerHTML">
                        </div>
                        
                    {% else %}
                        <div class="mt-5">
                            {% with CustomFieldName=PurchaseOrdersCustomFieldMap|get_attr:property %}  
                                {% if CustomFieldName %}
                                    <div class="fv-rowd-flex flex-column mb-8">
                                        <div class="mb-4">
                                            
                                            <span class="{% include 'data/utility/form-label.html' %} {% if CustomFieldName.required_field %} required {% endif%}">
                                                {{CustomFieldName.name }}
                                            </span>
                                            
                                            {% include 'data/common/custom_field/custom-property.html' with CustomFieldName=CustomFieldName obj=purchase object_type="purchaseorder" %}

                                        </div>
                                    </div>
                                {% endif %}
                            {% endwith %}                        
                        </div>
                    {% endif %}

            {% endfor %}
                <div class="mt-5">
                    {% if purchase.id %}
                        <div class="mb-5 d-flex flex-row h-40px">
                            {% if permission|check_permission:'edit' %}
                            <button name="post" id="submit_btn" type="submit" value="update_po" class="flex-column-fluid me-2 btn btn-dark">
                                {% if LANGUAGE_CODE == 'ja'%}更新{% else %}Update{% endif %}
                            </button>
                            {% endif %}
                            
                            <div class="ms-2 me-2">
                                <div class="btn-group w-100" role="group">
                                    <a target="_blank" name="downloadPDF" href="{% host_url 'purchase_order_pdf_download' id=purchase.id host 'app' %}" class="submitbtn flex-column-fluid btn btn-primary py-2">
                                        {% translate_lang "Download" LANGUAGE_CODE %} (PDF)
                                    </a>
                                    
                                    <button id="btnGroupDrop1" type="button" class="btn btn-light-primary dropdown-toggle p-2" data-bs-toggle="dropdown" aria-expanded="false">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234CAF50" width="18px" height="18px"><path d="M0 0h24v24H0z" fill="none"/><path d="M6.3 9.3L12 15l5.7-5.7c.4-.4 1-.4 1.4 0s.4 1 0 1.4l-6 6c-.2.2-.5.3-.7.3s-.5-.1-.7-.3l-6-6c-.4-.4-.4-1 0-1.4s1-.4 1.4 0z"/></svg>
                                    </button>
                                    
                                    <ul class="dropdown-menu" aria-labelledby="btnGroupDrop1">
                                        {% for template in list_customize_template %}
                                                <li>
                                                    <a type='button' 
                                                        class="dropdown-item tw-text-ellipsis tw-overflow-hidden text-click-white" 
                                                        target="_blank" name="downloadPDF" href="{% host_url 'purchase_order_pdf_download' id=purchase.id host 'app' %}?template_select={{template.id|to_str}}" 
                                                        >
                                                        {% if template.name %}
                                                            {{template.name}}
                                                        {% else %}
                                                            {% if LANGUAGE_CODE == 'ja' %}
                                                            {{template.master_pdf.name_ja}}
                                                            {% else %}
                                                            {{template.master_pdf.name_en}}
                                                            {% endif %}
                                                        {% endif %}
                                                    </a>
                                                </li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    {% else %}

                        <div class="my-5 border-gray-300 border-bottom-dashed">
                        </div>
            
                        <button name="post" type="submit" value="new_purchase_order" class="btn btn-dark w-100">
                            {% if LANGUAGE_CODE == 'ja'%}発注レコードの作成{% else %}Create Purchase Order Record{% endif %}
                        </button>
                    {% endif %}
                    
                </div>
            </form>

        {% endif %}
    </div>
    
{% else %}
    <form method="POST" action="{% host_url 'csv_upload_submit' host 'app' %}" id="bulk-add" enctype="multipart/form-data">
        {% csrf_token %}
        <input type="text" name="submit_csv_upload" value='1' hidden/>
        <div class="card-body mb-2">

            <div class="mb-2 "
                hx-get="{% host_url 'csv_upload' host 'app' %}"
                hx-trigger="load"
                hx-swap="#csv-import"
                hx-vals='{"page_group_type":"purchaseorder"}'
            >
                <div id="csv-import"></div>
            </div>
            
            <div class="my-5 border-gray-300 border-bottom-dashed"></div>

            <button id="submit_csv_upload" name="submit_csv_upload" onclick="handleSubmit(this)"  type="submit" class="btn btn-dark w-100" >
                <span class="svg-icon svg-icon-2 svg-icon-white">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-cloud-download" viewBox="0 0 16 16">
                        <path d="M4.406 1.342A5.53 5.53 0 0 1 8 0c2.69 0 4.923 2 5.166 4.579C14.758 4.804 16 6.137 16 7.773 16 9.569 14.502 11 12.687 11H10a.5.5 0 0 1 0-1h2.688C13.979 10 15 8.988 15 7.773c0-1.216-1.02-2.228-2.313-2.228h-.5v-.5C12.188 2.825 10.328 1 8 1a4.53 4.53 0 0 0-2.941 1.1c-.757.652-1.153 1.438-1.153 2.055v.448l-.445.049C2.064 4.805 1 5.952 1 7.318 1 8.785 2.23 10 3.781 10H6a.5.5 0 0 1 0 1H3.781C1.708 11 0 9.366 0 7.318c0-1.763 1.266-3.223 2.942-3.593.143-.863.698-1.723 1.464-2.383"/>
                        <path d="M7.646 15.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 14.293V5.5a.5.5 0 0 0-1 0v8.793l-2.146-2.147a.5.5 0 0 0-.708.708z"/>
                    </svg>
                </span>
                {% if LANGUAGE_CODE == 'ja'%}
                インポート
                {% else %}
                Import
                {% endif %}
            </button>
        </div>

    </form>

    <script>
        function showFilename(elm){
            if (elm.files.length === 0) return;

            path = elm.value.replace(/^.*[\\\/]/, '')
            elm.parentElement.parentElement.querySelector('.filename').innerHTML = path
            
            if (elm.id === 'csv-input') {
                htmx.trigger('#csv-input', 'csvFileChanged')
            }
        } 
        function handleSubmit(button) {
            
            // Store original text
            var originalText = button.innerHTML;
            
            // Disable button
            button.disabled = true;
            
            // Optional: Change text to show loading
            {% if LANGUAGE_CODE == 'ja'%}
            button.innerHTML = '処理中...';
            {% else %}
            button.innerHTML = 'Processing...';
            {% endif %}
            
            // Optional: Reset button state after 30 seconds (failsafe)
            setTimeout(() => {
                button.disabled = false;
                button.innerHTML = originalText;
            }, 30000);
            
            // Get the form and submit it
            var form = button.closest('form');
            form.submit();
        }
    </script>




    {% if transfer_history.status == 'running' %}
        <script>
            let button = document.getElementById("submit_csv_upload")
            handleSubmit(button,submit=false)
        </script>
    {% endif %}
{% endif %}

{% block js %}
    {% include 'data/javascript/expenseJS.html' with obj=purchase %}
    {% include 'data/property/property-validate-form-script.html' %}
    <script>
    $(document).ready(function() {
        $('.dateinput').daterangepicker({
            singleDatePicker: true,
            showDropdowns: true,
            autoApply: true,
            locale: {
                format: 'YYYY-MM-DD',
                {% if LANGUAGE_CODE == 'ja' %}
                applyLabel: '確定',
                cancelLabel: 'キャンセル',
                daysOfWeek: ['日', '月', '火', '水', '木', '金', '土'],
                monthNames: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                {% endif %}
            }
        });
    });
    </script>
{% endblock %}