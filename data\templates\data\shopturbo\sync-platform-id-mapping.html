{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}


<div class="border-0 card shadow-none rounded-0 w-100 h-100 bg-white">

    <div>
        <span class="fs-5 fw-bolder text-active-primary ms-0 mb-3 py-0 border-0">
            {% if LANGUAGE_CODE == 'ja' %}
            プラットフォームIDマッピング
            {% else %}
            Platform ID Mapping
            {% endif %}
        </span>

        <div>

            <table class="{% include "data/utility/table.html" %} px-5">
                <thead class="{% include "data/utility/table-header.html" %}">
                    <tr>
                        <th class="min-w-50px">
                            <span>
                                {% if LANGUAGE_CODE == 'ja' %}
                                プラットフォームIDヘッダー
                                {% else %}
                                Platform ID Header
                                {% endif %}
                            </span>
                        </th>
                        {% if object_type != 'subscription' %}
                        <th class="min-w-50px">
                            <span>
                                {% if LANGUAGE_CODE == 'ja' %}
                                ソースプロパティ
                                {% else %}
                                Source Property
                                {% endif %}
                            </span>
                        </th>
                        {% endif %}
                        <th class="min-w-50px">
                            <span>
                                {% if LANGUAGE_CODE == 'ja' %}
                                Sankaプロパティ
                                {% else %}
                                Sanka Property
                                {% endif %}
                            </span>
                        </th>
                    </tr>
                </thead>
                <tbody id="body-section" class="fs-6">
                    {% for header in header_list %}
                    <tr>
                        <td>
                            <input name="platform-file-column{% if postfix %}-{{postfix}}{% endif %}" type="hidden" value="{{header|parse_header_item:'value'}}"/>
                            {% if LANGUAGE_CODE == 'ja' %}
                                <input name="platform-file-column-name{% if postfix %}-{{postfix}}{% endif %}" type="hidden" value="{{header|parse_header_item:'name_ja'}}"/>
                                <span style="display: inline-block; max-width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                                {% if header|parse_header_item:'hubspot_default' == True %}{% translate_lang header|parse_header_item:'name_ja' LANGUAGE_CODE %}{% else %}{{header|parse_header_item:'name_ja'}}{% endif %}
                                </span>
                            {% else %}
                                <input name="platform-file-column-name{% if postfix %}-{{postfix}}{% endif %}" type="hidden" value="{{header|parse_header_item:'name'}}"/>
                                <span style="display: inline-block; max-width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                                {{header|parse_header_item:'name'}}
                                </span>
                            {% endif %}
                        </td>
                        
                        {% if object_type != 'subscription' %}
                        <td>
                            <div>
                                <select name="platform-source-sanka-properties{% if postfix %}-{{postfix}}{% endif %}" class="bg-white form-select form-select-solid border {% if is_multi_object %}w-300px{% endif %} h-40px select2-this select-sanka-properties-{{header|parse_header_item}}"
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    data-placeholder="ヘッダーの選択"
                                    {% else %}
                                    data-placeholder="Select Header"
                                    {% endif %}
                                    >
                                    <option></option>
                                    {% for column in source_platform_columns %}
                                        <option {% if column == header|parse_header_item:'default'%} selected {%endif%} value='{{column}}'>
                                            {{column|display_column_subscriptions:request}}
                                        </option>  
                                    {% endfor %} 
                                    
                                </select>
                            </div>
                        </td>
                        {% endif %}
                        <td>
                            <div>
                                <select name="platform-sanka-properties-{{header|parse_header_item:'value'}}{% if postfix %}-{{postfix}}{% endif %}" class="bg-white form-select form-select-solid border {% if is_multi_object %}w-300px{% endif %} h-40px select2-this select-sanka-properties-{{header|parse_header_item}}" multiple
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    data-placeholder="ヘッダーの選択"
                                    {% else %}
                                    data-placeholder="Select Header"
                                    {% endif %}
                                    >
                                    <option></option>
                                    {% if object_type == 'subscription' %}
                                        {% for column in platform_columns %}
                                            <option {% if column == header|parse_header_item:'default'%} selected {%endif%} value='{{column}}'>
                                                {{column|display_column_subscriptions:request}}
                                            </option>  
                                        {% endfor %} 
                                    {% else %}
                                        {% for column in platform_columns %}
                                            <option {% if column == header|parse_header_item:'default'%} selected {%endif%} value='{{column}}'>
                                                {{column|display_column_invoice:request}}
                                            </option>  
                                        {% endfor %} 
                                    {% endif %}
                                </select>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <div id="loading-spinner-header" class="text-center mb-3 d-none">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div>
                {% if LANGUAGE_CODE == 'ja' %}
                さらにプロパティを読み込んでいます...
                {% else %}
                Loading More Properties...
                {% endif %}
            </div>
        </div>

        <script>
            function disableButtons() {
                const importBtn = document.getElementById('import-btn');
                const exportBtn = document.getElementById('export-btn');
                if (importBtn) importBtn.disabled = true;
                if (exportBtn) exportBtn.disabled = true;
            }

            function enableButtons() {
                const importBtn = document.getElementById('import-btn');
                const exportBtn = document.getElementById('export-btn');
                if (importBtn) importBtn.disabled = false;
                if (exportBtn) exportBtn.disabled = false;
            }
        </script>

        <div id="mappingLoadingIndicator" class="d-none">
            {% if LANGUAGE_CODE == 'ja' %}保存中...{% else %}Saving...{% endif %}
        </div>

        <script>
            function statusCheckerChannelChanged() {
                setTimeout(() => {
                    if (document.getElementById('status-checker')) {
                        htmx.trigger('#status-checker', 'channelChanged');
                    }
                }, 4000);
            }

            function showSaveMessage() {
                const saveMessage = document.getElementById('saveMappingMessage');
                if (saveMessage) {
                    saveMessage.classList.remove('d-none'); // Show the message
                    setTimeout(() => {
                        saveMessage.classList.add('d-none'); // Hide it after 5 seconds
                    }, 5000);
                }
            }
        </script>
    
    </div>

    <script>
        $(document).ready(function() {
            // Hide content and show spinner at the very beginning
            if (document.getElementById('platformMappingContainer')) {
                document.getElementById('platformMappingContainer').classList.add('d-none');
                document.getElementById('loading-spinner-status').classList.remove('d-none');
            }
            
            // Step 1: Use setTimeout to allow browser to render pagination
            // before moving to the next heavy operation
            setTimeout(function() {
                // Initialize Select2 controls in smaller batches
                initializeSelect2Controls();
                
                // Step 2: After Select2 initialization is complete, set up events
                setTimeout(function() {
                    setupSelect2Events();
                    
                    // Show content and hide spinner when everything is done
                    if (document.getElementById('platformMappingContainer')) {
                        document.getElementById('loading-spinner-status').classList.add('d-none');
                        document.getElementById('platformMappingContainer').classList.remove('d-none');
                    }
                }, 50);
            }, 100);
        });

        // Helper functions to break up the work
        function initializeSelect2Controls() {
            // Initialize Select2 in batches to prevent browser freeze
            const selects = $('.select2-this').toArray();
            const batchSize = 50;
            let currentBatch = 0;
            
            function initializeBatch() {
                // Process current batch
                const startIndex = currentBatch * batchSize;
                const endIndex = Math.min(startIndex + batchSize, selects.length);
                
                if (startIndex < selects.length) {
                    // Process this batch
                    const batch = selects.slice(startIndex, endIndex);
                    $(batch).select2();
                    
                    // Update progress indicator if needed
                    if (document.getElementById('loading-spinner-progress')) {
                        const progress = Math.round((endIndex / selects.length) * 100);
                        document.getElementById('loading-spinner-progress').style.width = progress + '%';
                        document.getElementById('loading-spinner-text').textContent = 
                            `Initializing... ${progress}%`;
                    }
                    
                    // Schedule next batch
                    currentBatch++;
                    setTimeout(initializeBatch, 10);
                } else {
                    // All batches processed
                    // Handle item_id selects
                    $('.select2-this-order').each(function() {
                        let selectElement = $(this).closest('select').get(0);
                        if (selectElement && selectElement.value === 'item_id') {
                            selectElement.dispatchEvent(new Event('htmx-change'));
                        }
                    });
                }
            }
            
            // Start the batch processing
            initializeBatch();
        }

        function setupSelect2Events() {
            // Track previous values more efficiently
            var previousValues = {};
            $(`.select2-this-order`).on('select2:opening', function(e) {
                // Store the current value before change
                var selectElement = $(this).closest('select').get(0);
                if (selectElement) {
                    previousValues[selectElement.id || $(this).index()] = selectElement.value;
                }
            }).on('select2:select', function(e) {
                var selectElement = $(this).closest('select').get(0);
                if (!selectElement) return;
                
                let id = selectElement.id || $(this).index();
                let oldValue = previousValues[id] || '';
                let currentValue = selectElement.value;

                if (['order', 'inventory'].includes(import_as)) {
                    if ((currentValue === 'item_id') || (oldValue === 'item_id')) {
                        selectElement.dispatchEvent(new Event('htmx-change'));
                    }
                } else {
                    selectElement.dispatchEvent(new Event('htmx-change'));
                }

                // Update the previous value after change
                previousValues[id] = currentValue;
            });
        }
    </script>
    
</div>
