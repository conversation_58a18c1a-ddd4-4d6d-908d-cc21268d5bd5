import ast
from datetime import timedelta
import traceback

from hatchet_sdk import Context

from data.models import Notification, TransferHistory, User, Workspace
from utils.bgjobs.handler import (
    aio_set_bg_job_completed,
    aio_set_bg_job_failed,
    aio_set_bg_job_running,
)
from utils.ecforce_bg_jobs.ecforce import import_ecforce_orders as ecforce_import_util
from utils.logger import logger

from ..models import ImportOrdersEcforcePayload
from ..workflows import import_ecforce_orders


@import_ecforce_orders.task(
    name="ImportEcforceOrdersTask",
    execution_timeout=timedelta(hours=2),
    schedule_timeout=timedelta(hours=1),
)
async def import_ecforce_orders_task(
    input: ImportOrdersEcforcePayload, ctx: Context
) -> dict:
    """
    Child task for importing Ecforce orders
    """
    logger.info("Run Ecforce orders import child task")
    logger.info(f"Input: {input}")

    history_id = input.history_id
    task = None

    try:
        task = await TransferHistory.objects.select_related("workspace", "user").aget(
            id=history_id
        )
        task.keep_alive = True
        await task.asave()
    except TransferHistory.DoesNotExist:
        logger.warning("TransferHistory does not exist")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])

    if input.background_job_id:
        await aio_set_bg_job_running(input.background_job_id)

    try:
        # Get the workspace and user objects
        workspace = await Workspace.objects.aget(id=input.workspace_id)
        # Handle both user_id and user attributes for backward compatibility
        user_id = getattr(input, "user_id", None) or getattr(input, "user", None)
        if not user_id:
            raise ValueError("No user ID provided in input payload")
        user = await User.objects.aget(id=user_id)

        # Process mapping fields
        mapping_custom_fields = (
            input.mapping_custom_fields
            if input.mapping_custom_fields and input.mapping_custom_fields != "None"
            else {}
        )

        # Parse string mappings if needed
        if isinstance(mapping_custom_fields, str):
            mapping_custom_fields = ast.literal_eval(mapping_custom_fields)

        # Call the Ecforce import utility
        await ecforce_import_util(
            input.channel_id,
            mapping_custom_fields=mapping_custom_fields,
            lang=input.lang,
            history_id=history_id,
        )

        logger.info("Successfully completed import for platform: ecforce")
        if task:
            await task.arefresh_from_db()
            task.status = "completed"
            await task.asave(update_fields=["status"])

        if input.background_job_id:
            await aio_set_bg_job_completed(input.background_job_id)

        # Create success notification
        if input.lang == "ja":
            message = "ECフォース注文のインポートは正常に完了しました。"
        else:
            message = "EC-Force Order import completed successfully."

        await Notification.objects.acreate(
            workspace=workspace, user=user, message=message, type="success"
        )

    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error importing Ecforce orders: {str(e)}")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])
        # Create failed notification
        if input.lang == "ja":
            message = "ECフォース注文のインポートに失敗しました。"
        else:
            message = "EC-Force Order import failed."

        workspace = await Workspace.objects.aget(id=input.workspace_id)
        # Handle both user_id and user attributes for backward compatibility
        user_id = getattr(input, "user_id", None) or getattr(input, "user", None)
        if user_id:
            user = await User.objects.aget(id=user_id)
        else:
            user = None

        if user:
            await Notification.objects.acreate(
                workspace=workspace, user=user, message=message, type="error"
            )
