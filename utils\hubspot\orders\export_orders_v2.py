"""
Enhanced HubSpot orders (deals) export using CSV bulk imports.
This version handles large volumes of orders efficiently using streaming and chunking.
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional

from data.models import (
    Channel,
    Notification,
    TransferHistory,
)
from utils.error_logger.import_export_logger import ImportExportLogger
from utils.hubspot.utils_hubspot import refresh_hubspot_token
from utils.logger import logger

# Import our order modules
from .csv_export import StreamingOrderCSVExporter
from .csv_import import HubSpotOrderCSVImporter
from .import_monitor import HubSpotOrderImportMonitor
from .property_manager import OrderPropertyManager
from .models import ExportResult, AssociationMapping, OrderExportConfig
from .batch_operations import (
    get_order_statistics,
    get_all_owners_async,
)


def parse_order_association_mappings(
    mapping_custom_fields_association: Dict[str, str],
) -> List[AssociationMapping]:
    """Convert association mapping dictionary to AssociationMapping objects."""
    association_mappings = []
    for (
        hubspot_association,
        sanka_association,
    ) in mapping_custom_fields_association.items():
        parts = hubspot_association.split("|")
        if len(parts) == 4:
            association_mapping = AssociationMapping(
                sanka_association_id=sanka_association,
                hubspot_association_name=parts[0],
                hubspot_association_id=parts[1],
                hubspot_association_from_object_type_id=parts[2],
                hubspot_association_to_object_type_id=parts[3],
            )
            association_mappings.append(association_mapping)
    return association_mappings


async def export_hubspot_orders_v2(
    channel_id: str,
    order_ids: Optional[List[str]] = None,  # List of ShopTurboOrders.id UUIDs
    mapping_custom_fields: Dict[str, str] = None,
    mapping_custom_fields_association: Dict[str, str] = None,
    mapping_status_custom_fields: Dict[str, str] = None,
    hubspot_group_name: str = "sankaorderinformation",
    lang: str = "ja",
    user_id: str = "",
    task: Optional[TransferHistory] = None,
    use_csv_import: bool = True,  # Feature flag to enable CSV import approach
) -> ExportResult:
    """
    Enhanced order export function using CSV bulk imports for better performance and scalability.

    Args:
        channel_id: Channel ID for HubSpot integration
        order_ids: Optional list of specific ShopTurboOrders.id UUIDs to export
        mapping_custom_fields: Field mappings between Sanka and HubSpot
        mapping_custom_fields_association: Association mappings
        hubspot_group_name: Property group name in HubSpot
        lang: Language for notifications
        user_id: User ID for notifications
        task: Optional TransferHistory task for progress tracking
        use_csv_import: Whether to use new CSV import approach (default: True)

    Returns:
        ExportResult with comprehensive export metrics
    """
    # Initialize default values
    if mapping_custom_fields is None:
        mapping_custom_fields = {}
    if order_ids is None:
        order_ids = []
    if mapping_custom_fields_association is None:
        mapping_custom_fields_association = {}

    # Initialize error logger
    error_logger = ImportExportLogger(lang=lang)

    # Initialize result
    result = ExportResult()

    async with error_logger.afunction_context("export_hubspot_orders_v2"):
        channel = await Channel.objects.aget(pk=channel_id)
        workspace_id = channel.workspace_id

        # Update task status
        if task:
            task.status = "running"
            task.progress = 5
            await task.asave(update_fields=["status", "progress"])

        try:
            # Step 1: Refresh token
            error_logger.log_info(
                "export_hubspot_orders_v2", "Refreshing HubSpot token"
            )
            await asyncio.to_thread(refresh_hubspot_token, channel_id)
            channel = await Channel.objects.aget(pk=channel_id)  # Reload with new token

            # Step 2: Get order statistics
            error_logger.log_info(
                "export_hubspot_orders_v2", "Getting order statistics"
            )
            stats = await get_order_statistics(workspace_id, order_ids)
            result.total_orders = stats["total_orders"]
            result.total_line_items = stats["total_line_items"]

            logger.info(
                f"Exporting {result.total_orders} orders with {result.total_line_items} line items"
            )

            if task:
                task.total_number = result.total_orders
                task.progress = 10
                await task.asave(update_fields=["total_number", "progress"])

            # Step 3: Setup property manager and ensure custom properties exist
            error_logger.log_info(
                "export_hubspot_orders_v2", "Setting up property manager"
            )
            async with OrderPropertyManager(
                channel_id=channel_id,
                access_token=channel.access_token,
                workspace_id=workspace_id,
            ) as property_manager:
                # Ensure property group exists
                await property_manager.ensure_property_group(hubspot_group_name)

                # Sync custom properties
                if mapping_custom_fields:
                    property_mapping = await property_manager.sync_custom_properties(
                        hubspot_group_name
                    )
                    logger.info(f"Synced {len(property_mapping)} custom properties")

                # Validate required properties
                missing_props = await property_manager.validate_required_properties()
                if missing_props:
                    error_logger.log_warning(
                        "export_hubspot_orders_v2",
                        f"Missing required properties: {missing_props}",
                    )

            if task:
                task.progress = 20
                await task.asave(update_fields=["progress"])

            # Step 4: Get owners and dealstage options for mapping
            error_logger.log_info(
                "export_hubspot_orders_v2",
                "Fetching HubSpot owners and property options",
            )

            # Get HubSpot owners for mapping
            headers = {
                "Authorization": f"Bearer {channel.access_token}",
                "Content-Type": "application/json",
            }
            owners = await get_all_owners_async(headers)
            logger.info(f"Fetched {len(owners)} HubSpot owners")

            # Get dealstage options if dealstage is mapped
            dealstage_options = {}
            if "dealstage" in mapping_custom_fields:
                async with OrderPropertyManager(
                    channel_id=channel_id,
                    access_token=channel.access_token,
                    workspace_id=workspace_id,
                ) as pm:
                    dealstage_options = await pm.get_dealstage_options()
                    logger.info(f"Loaded {len(dealstage_options)} dealstage options")

            if task:
                task.progress = 25
                await task.asave(update_fields=["progress"])

            # Step 5: Export orders to CSV
            error_logger.log_info("export_hubspot_orders_v2", "Exporting orders to CSV")

            config = OrderExportConfig(
                chunk_size=5000,
                include_custom_fields=True,
                include_line_items=False,
                include_associations=False,
            )

            csv_exporter = StreamingOrderCSVExporter(
                workspace_id=workspace_id,
                config=config,
                mapping_custom_fields=mapping_custom_fields,
                mapping_status_custom_fields=mapping_status_custom_fields,
                owners=owners,
                dealstage_options=dealstage_options,
                task_id=str(task.id) if task else None,
                channel_id=channel_id if channel_id else None,
            )

            # Normal export
            order_csv_chunks = await csv_exporter.export_orders_to_csv(order_ids)
            logger.info(f"Generated {len(order_csv_chunks)} order CSV files")

            # # Export line items if configured
            line_item_csv_chunks = []
            # # NOTE: Temporary - Disable export_line_items_to_csv first
            # if config.include_line_items:
            #     line_item_csv_chunks = await csv_exporter.export_line_items_to_csv(
            #         order_ids
            #     )
            #     logger.info(
            #         f"Generated {len(line_item_csv_chunks)} line item CSV files"
            #     )

            contact_csv_chunks = []
            # # Export contacts if there are contact field mappings
            # contact_csv_chunks = await csv_exporter.export_contacts_to_csv(order_ids)
            # if contact_csv_chunks:
            #     logger.info(f"Generated {len(contact_csv_chunks)} contact CSV files")

            company_csv_chunks = []
            # # Export companies if there are company field mappings
            # company_csv_chunks = await csv_exporter.export_companies_to_csv(order_ids)
            # if company_csv_chunks:
            #     logger.info(f"Generated {len(company_csv_chunks)} company CSV files")

            result.csv_files_created = [
                chunk.file_path
                for chunk in order_csv_chunks
                + line_item_csv_chunks
                + contact_csv_chunks
                + company_csv_chunks
            ]

            if task:
                task.progress = 40
                await task.asave(update_fields=["progress"])

            # Step 6: Import CSV files to HubSpot
            error_logger.log_info(
                "export_hubspot_orders_v2", "Importing CSV files to HubSpot"
            )

            import_ids = []

            async with HubSpotOrderCSVImporter(
                channel_id=channel_id,
                access_token=channel.access_token,
                import_name=f"Sanka Orders Export - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            ) as importer:
                # Import order CSV files
                for chunk in order_csv_chunks:
                    import_result = await importer.import_orders_csv(
                        chunk,
                        mapping_config=mapping_custom_fields,
                    )

                    if import_result.import_id:
                        import_ids.append(import_result.import_id)
                        result.import_ids.append(import_result.import_id)
                        result.import_results.append(import_result)
                    else:
                        error_logger.log_error(
                            "export_hubspot_orders_v2",
                            f"Failed to start import for chunk: {chunk.file_path}",
                            message="Failed to start import for chunk",
                        )

                # Import line item CSV files
                # NOTE: Temporary - Disable import_line_items_csv first
                for chunk in line_item_csv_chunks:
                    import_result = await importer.import_line_items_csv(
                        chunk,
                        mapping_config=mapping_custom_fields,
                    )

                    if import_result.import_id:
                        import_ids.append(import_result.import_id)
                        result.import_ids.append(import_result.import_id)
                        result.import_results.append(import_result)

                # Import contact CSV files
                for chunk in contact_csv_chunks:
                    import_result = await importer.import_contacts_csv(
                        chunk,
                        mapping_config=mapping_custom_fields,
                    )

                    if import_result.import_id:
                        import_ids.append(import_result.import_id)
                        result.import_ids.append(import_result.import_id)
                        result.import_results.append(import_result)
                    else:
                        error_logger.log_error(
                            "export_hubspot_orders_v2",
                            f"Failed to start import for contact chunk: {chunk.file_path}",
                            message="Failed to start contact import",
                        )

                # Import company CSV files
                for chunk in company_csv_chunks:
                    import_result = await importer.import_companies_csv(
                        chunk,
                        mapping_config=mapping_custom_fields,
                    )

                    if import_result.import_id:
                        import_ids.append(import_result.import_id)
                        result.import_ids.append(import_result.import_id)
                        result.import_results.append(import_result)
                    else:
                        error_logger.log_error(
                            "export_hubspot_orders_v2",
                            f"Failed to start import for company chunk: {chunk.file_path}",
                            message="Failed to start company import",
                        )

            logger.info(f"Started {len(import_ids)} import operations")

            if task:
                task.progress = 50
                await task.asave(update_fields=["progress"])

            # Step 7: Monitor imports until completion
            if import_ids:
                error_logger.log_info(
                    "export_hubspot_orders_v2", "Monitoring import progress"
                )

                monitor = HubSpotOrderImportMonitor(
                    channel_id=channel_id,
                    access_token=channel.access_token,
                    task=task,
                )

                # Monitor all imports
                final_results = await monitor.monitor_imports(
                    import_ids,
                    update_progress=True,
                )

                # Update result with final import results
                result.import_results = final_results

                # Calculate success metrics
                for import_result in final_results:
                    result.successful_exports += import_result.successful_rows
                    result.failed_exports += import_result.failed_rows

                # Send completion notification
                if user_id:
                    await monitor.send_completion_notification(
                        final_results,
                        user_id=user_id,
                        workspace_id=workspace_id,
                        lang=lang,
                    )

            if task:
                task.progress = 80
                await task.asave(update_fields=["progress"])


            # Step 9: Cleanup temporary files
            # csv_exporter.cleanup_temp_files()

            # Update final task status
            if task:
                task.status = "completed"
                task.progress = 100
                task.keep_alive = False
                await task.asave(update_fields=["status", "progress", "keep_alive"])

            # Set completion time
            result.completed_at = datetime.now()

            logger.info(
                f"Order export completed: {result.successful_exports}/{result.total_orders} "
                f"orders exported successfully"
            )

        except Exception as e:
            import traceback
            error_logger.log_api_error("export_hubspot_orders_v2", f"Error: {traceback.format_exc()}", e)
            result.errors.append(
                {
                    "error": str(e),
                    "timestamp": datetime.now().isoformat(),
                }
            )

            if task:
                task.status = "failed"
                task.error_message = str(e)
                await task.asave(update_fields=["status", "error_message"])

            # Send error notification
            if user_id:
                await Notification.objects.acreate(
                    workspace_id=workspace_id,
                    user_id=user_id,
                    message=f"Order export failed: {str(e)}",
                    type="error",
                )

        finally:
            # Save error log to transfer history
            if task:
                error_logger.save_to_transfer_history(task)

    return result
