from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect, render
from django_hosts.resolvers import reverse
from django.db.models import Q
from django.contrib.contenttypes.models import ContentType

from data.constants.properties_constant import (DEFAULT_ASSOCIATION_OBJECT_MEMBER,DEAFULT_ASSOCIATION_OBJECT_TARGET,OBJECT_TYPE_TO_SLUG,
                                                TYPE_OBJECT_ORDER, TYPE_OBJECT_CUSTOM_OBJECT, TYPE_OBJECT_TASK,
                                                TYPE_OBJECT_ITEM, TYPE_OBJECT_SUBSCRIPTION, TYPE_OBJECT_DELIVERY_NOTE, TYPE_OBJECT_INVOICE, TYPE_OBJECT_RECEIPT, TYPE_OBJECT_PURCHASE_ORDER,
                                                TYPE_OBJECT_COMPANY,TYPE_OBJECT_INVENTORY_WAREHOUSE,TYPE_OBJECT_INVENTORY, TYPE_OBJECT_CASE, TYPE_OBJECT_CONTACT,TYPE_OBJECT_ESTIMATE,TYPE_OBJECT_INVENTORY_TRANSACTION,TYPE_OBJECT_BILL,TYPE_OBJECT_EXPENSE,TYPE_OBJECT_JOURNAL)
from data.models import (AssociationLabel, AssociationLabelImplementation,AssociationLabelObject,Company,Contact,Estimate,Invoice,InventoryTransaction,PurchaseOrders,ShopTurboSubscriptions,
                         Module, Workspace, ShopTurboOrders,CustomObject,CustomObjectPropertyRow, Projects)
from django.core.paginator import Paginator
from data.models.workspace import ObjectManager
from utils.decorator import login_or_hubspot_required
from utils.utility import build_redirect_url, get_workspace
from django.utils import timezone
from utils.properties.properties import get_properties_with_details
from utils.utility import is_valid_uuid
from utils.properties.properties import get_page_object,property_display, get_object_display_based_columns


@login_or_hubspot_required
def association_labels_settings(request):
    workspace = get_workspace(request.user)
    object_source = request.GET.get('page_group_type')

    #auto populate label
    # delete -> developer mode
    # AssociationLabel.objects.filter(workspace=workspace, object_source=object_source, created_by_sanka=True).delete()
    auto_create_necessary_association_label_setting(workspace, object_source)

    association_labels = AssociationLabel.objects.filter(
        workspace=workspace, object_source=object_source).order_by('created_at')
    
    related_association_labels = AssociationLabel.objects.filter(workspace=workspace, object_target__icontains=object_source, created_by_sanka=False).exclude(
        object_source=object_source).order_by("created_at")

    context = {
        'object_source': object_source,
        'association_labels': association_labels,
        'related_association_labels': related_association_labels,
    }
    return render(request, 'data/association/association-labels/association-label-settings.html', context)


@login_or_hubspot_required
def manage_association_label(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    if request.method == 'GET':
        object_source = request.GET.get('object_source')
        association_label_id = request.GET.get('association_label_id')
        association_label = None

        ASSOCIATION_OBJECT_TARGETS=[]
        if object_source in DEFAULT_ASSOCIATION_OBJECT_MEMBER:
            ASSOCIATION_OBJECT_TARGETS = DEFAULT_ASSOCIATION_OBJECT_MEMBER[object_source].copy()

            if 'customer' in ASSOCIATION_OBJECT_TARGETS:ASSOCIATION_OBJECT_TARGETS.remove('customer')
            if 'supplier' in ASSOCIATION_OBJECT_TARGETS:ASSOCIATION_OBJECT_TARGETS.remove('supplier')
            if 'partner' in ASSOCIATION_OBJECT_TARGETS:ASSOCIATION_OBJECT_TARGETS.remove('partner')

            # self association
            if not object_source in ASSOCIATION_OBJECT_TARGETS:
                ASSOCIATION_OBJECT_TARGETS.append(object_source)

            # Add custom objects if they can associate with this object type
            # Check if custom objects can associate with the current object_source
            if object_source in DEFAULT_ASSOCIATION_OBJECT_MEMBER.get(TYPE_OBJECT_CUSTOM_OBJECT, []):
                custom_objects = CustomObject.objects.filter(workspace=workspace)
                for custom_object in custom_objects:
                    if custom_object.id not in ASSOCIATION_OBJECT_TARGETS:
                        ASSOCIATION_OBJECT_TARGETS.append(custom_object.id)

        elif is_valid_uuid(object_source):
            ASSOCIATION_OBJECT_TARGETS = DEFAULT_ASSOCIATION_OBJECT_MEMBER[TYPE_OBJECT_CUSTOM_OBJECT].copy()
            custom_objects = CustomObject.objects.filter(
                workspace=workspace)
            for custom_object in custom_objects:
                if str(custom_object.id) != object_source and custom_object.id not in ASSOCIATION_OBJECT_TARGETS:
                    ASSOCIATION_OBJECT_TARGETS.append(custom_object.id)
  
        if association_label_id:
            association_label = AssociationLabel.objects.get(
                id=association_label_id, workspace=workspace, object_source=object_source)
        
        # For custom objects, use TYPE_OBJECT_CUSTOM_OBJECT for get_page_object
        page_group_for_properties = TYPE_OBJECT_CUSTOM_OBJECT if is_valid_uuid(object_source) else object_source
        page_obj = get_page_object(page_group_for_properties, lang)
        custom_model = page_obj['custom_model']
        try:
            # For custom objects, pass the custom object instance to get_properties_with_details
            if is_valid_uuid(object_source):
                custom_object = CustomObject.objects.filter(id=object_source).first()
                properties = get_properties_with_details(page_group_for_properties, workspace, lang, custom_object=custom_object)
            else:
                properties = get_properties_with_details(object_source, workspace, lang)
            default_properties = [property_display(f"{p['id']}|{object_source}", lang, workspace)['name'] for p in properties if not is_valid_uuid(p['id'])]
            if is_valid_uuid(object_source):
                names = []  # Custom objects don't have a 'name' field in the same way
            else:
                names = custom_model.objects.filter(workspace=workspace).values_list('name', flat=True)
        except Exception as e:
            print(f"Error getting properties for {object_source}: {e}")
            default_properties = []
            names = []
        
        # Get existing target labels from label_pair column
        existing_target_labels_data = []
        if association_label and association_label.object_target:
            target_objects = association_label.object_target.split(',')
            label_pairs = association_label.label_pair or {}
            
            temp_label_pairs={}
            for target_obj in target_objects:
                target_obj = target_obj.strip()
                
                # Prepare data for template
                try:
                    if is_valid_uuid(target_obj):
                        custom_object = CustomObject.objects.filter(id=target_obj).first()
                        object_name = custom_object.name if custom_object else 'Custom Object'
                    else:
                        # Use target_obj directly as template filter will handle display
                        object_name = target_obj
                except:
                    object_name = target_obj
                
                existing_label = label_pairs.get(target_obj, '')
                existing_target_labels_data.append({
                    'target_value': target_obj,
                    'target_name': object_name,
                    'existing_label': existing_label if existing_label else association_label.label
                })

                if not label_pairs:
                    temp_label_pairs[target_obj]=association_label.label

            if not label_pairs:
                association_label.label_pair = temp_label_pairs


        print("== existing_target_labels_data: ",existing_target_labels_data)
        
        # Get association count efficiently
        association_count = 0
        if association_label:
            association_count = association_label.get_association_count()
        
        context = {
            'object_source': object_source,
            'association_label': association_label,
            'ASSOCIATION_OBJECT_TARGETS': ASSOCIATION_OBJECT_TARGETS,
            'existing_custom_property':default_properties + list(names),
            'existing_target_labels_data': existing_target_labels_data,
            'association_count': association_count
        }
        return render(request, 'data/association/association-labels/manage-association-label-drawer.html', context)

    # POST
    association_label_id = request.POST.get('association_label_id')
    label = request.POST.get('label')

    object_source = request.POST.get('object_source')
    object_targets = request.POST.getlist('object_target')
    project_targets = request.POST.get('project_target', None)
    one_to_one_association_type = request.POST.get(
        'one_to_one_association_type')
    
    # Collect target labels from dynamic inputs - only store non-empty labels
    target_labels = {}
    for key in request.POST.keys():
        if key.startswith('target_label_'):
            target_type = key.replace('target_label_', '')
            target_value = request.POST.get(key, '').strip()
            if target_value:  # Only store non-empty labels
                target_labels[target_type] = target_value
    

    if 'delete-association-label' in request.POST and association_label_id:
        if association_label_id:
            AssociationLabel.objects.get(
                id=association_label_id, workspace=workspace).delete()
            
        if is_valid_uuid(object_source):
            try:
                custom_object = CustomObject.objects.get(
                    id=object_source)
                return redirect(reverse('workspace_setting', host='app')+f'?custom_object={custom_object.slug}')
            except CustomObject.DoesNotExist:
                print('Custom object does not exist')
                return redirect(reverse('workspace_setting', host='app'))
        return redirect(reverse('workspace_setting', host='app')+f'?setting_type={object_source}')

    if association_label_id:
        try:
            
            association_label = AssociationLabel.objects.get(
                id=association_label_id, workspace=workspace, object_source=object_source)
            
        except AssociationLabel.DoesNotExist:
            print('Association label does not exist')
            if is_valid_uuid(object_source):
                try:
                    custom_object = CustomObject.objects.get(
                        id=object_source)
                    return redirect(reverse('workspace_setting', host='app')+f'?custom_object={custom_object.slug}')
                except CustomObject.DoesNotExist:
                    print('Custom object does not exist')
                    return redirect(reverse('workspace_setting', host='app'))
            return redirect(reverse('workspace_setting', host='app')+f'?setting_type={object_source}')
        
        
        association_label.label = label
        association_label.label_ja = label  # Set label_ja to the same value for user-created custom objects
        association_label.project_target = project_targets if project_targets else None
        association_label.object_source = object_source
        association_label.object_target = ','.join(object_targets)

        association_label.association_type = 'one_to_one' if one_to_one_association_type else 'many_to_many'
        
        # Store target labels in label_pair column (only non-empty target labels)
        association_label.label_pair = target_labels if target_labels else None
        
        association_label.save()
    
    else:
        association_label_data = {
            'workspace': workspace,
            'label': label,
            'label_ja': label,  # Set label_ja to the same value for user-created custom objects
            'object_source': object_source,
            'project_target': project_targets if project_targets else None,
            'object_target': ','.join(object_targets),
            'association_type': 'one_to_one' if one_to_one_association_type else 'many_to_many',
            'label_pair': target_labels if target_labels else None  # Store only target labels
        }
            
        association_label = AssociationLabel.objects.create(**association_label_data)

    if is_valid_uuid(object_source):
        try:
            custom_object = CustomObject.objects.get(
                id=object_source)
            return redirect(reverse('workspace_setting', host='app')+f'?custom_object={custom_object.slug}')
        except CustomObject.DoesNotExist:
            print('Custom object does not exist')
            return redirect(reverse('workspace_setting', host='app'))
    return redirect(reverse('workspace_setting', host='app')+f'?setting_type={object_source}')

@login_or_hubspot_required
def manage_related_association_label(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    if request.method == 'GET':
        object_source = request.GET.get('object_source')
        redirect_object_source = request.GET.get('redirect_object_source')
        association_label_id = request.GET.get('association_label_id')
        association_label = None

        ASSOCIATION_OBJECT_TARGETS=[]
        if object_source in DEFAULT_ASSOCIATION_OBJECT_MEMBER:
            ASSOCIATION_OBJECT_TARGETS = DEFAULT_ASSOCIATION_OBJECT_MEMBER[object_source].copy()
            if 'customer' in ASSOCIATION_OBJECT_TARGETS:ASSOCIATION_OBJECT_TARGETS.remove('customer')
            if 'supplier' in ASSOCIATION_OBJECT_TARGETS:ASSOCIATION_OBJECT_TARGETS.remove('supplier')
            if 'partner' in ASSOCIATION_OBJECT_TARGETS:ASSOCIATION_OBJECT_TARGETS.remove('partner')
            

            # self association
            if not object_source in ASSOCIATION_OBJECT_TARGETS:
                ASSOCIATION_OBJECT_TARGETS.append(object_source)

            # Add custom objects if they can associate with this object type
            # Check if custom objects can associate with the current object_source
            if object_source in DEFAULT_ASSOCIATION_OBJECT_MEMBER.get(TYPE_OBJECT_CUSTOM_OBJECT, []):
                custom_objects = CustomObject.objects.filter(workspace=workspace)
                for custom_object in custom_objects:
                    if custom_object.id not in ASSOCIATION_OBJECT_TARGETS:
                        ASSOCIATION_OBJECT_TARGETS.append(custom_object.id)

        elif is_valid_uuid(object_source):
            ASSOCIATION_OBJECT_TARGETS = DEFAULT_ASSOCIATION_OBJECT_MEMBER[TYPE_OBJECT_CUSTOM_OBJECT].copy()
        
            custom_objects = CustomObject.objects.filter(
                workspace=workspace)
            for custom_object in custom_objects:
                if str(custom_object.id) != object_source and custom_object.id not in ASSOCIATION_OBJECT_TARGETS:
                    ASSOCIATION_OBJECT_TARGETS.append(custom_object.id)
  
        if association_label_id:
            association_label = AssociationLabel.objects.get(
                id=association_label_id, workspace=workspace, object_source=object_source)
        
        # For custom objects, use TYPE_OBJECT_CUSTOM_OBJECT for get_page_object
        page_group_for_properties = TYPE_OBJECT_CUSTOM_OBJECT if is_valid_uuid(object_source) else object_source
        page_obj = get_page_object(page_group_for_properties, lang)
        custom_model = page_obj['custom_model']
        try:
            # For custom objects, pass the custom object instance to get_properties_with_details
            if is_valid_uuid(object_source):
                custom_object = CustomObject.objects.filter(id=object_source).first()
                properties = get_properties_with_details(page_group_for_properties, workspace, lang, custom_object=custom_object)
            else:
                properties = get_properties_with_details(object_source, workspace, lang)
            default_properties = [property_display(f"{p['id']}|{object_source}", lang, workspace)['name'] for p in properties if not is_valid_uuid(p['id'])]
            if is_valid_uuid(object_source):
                names = []  # Custom objects don't have a 'name' field in the same way
            else:
                names = custom_model.objects.filter(workspace=workspace).values_list('name', flat=True)
        except Exception as e:
            print(f"Error getting properties for {object_source}: {e}")
            default_properties = []
            names = []

        existing_target_labels_data = []
        if association_label and association_label.object_target:
            target_objects = association_label.object_target.split(',')
            label_pairs = association_label.label_pair or {}
            
            temp_label_pairs={}
            for target_obj in target_objects:
                target_obj = target_obj.strip()
                
                # Prepare data for template
                try:
                    if is_valid_uuid(target_obj):
                        custom_object = CustomObject.objects.filter(id=target_obj).first()
                        object_name = custom_object.name if custom_object else 'Custom Object'
                    else:
                        # Use target_obj directly as template filter will handle display
                        object_name = target_obj
                except:
                    object_name = target_obj
                
                existing_label = label_pairs.get(target_obj, '')
                existing_target_labels_data.append({
                    'target_value': target_obj,
                    'target_name': object_name,
                    'existing_label': existing_label if existing_label else association_label.label
                })

                if not label_pairs:
                    temp_label_pairs[target_obj]=association_label.label

            if not label_pairs:
                association_label.label_pair = temp_label_pairs
        
        # Get association count efficiently
        association_count = 0
        if association_label:
            association_count = association_label.get_association_count()
        
        context = {
            'object_source': object_source,
            'redirect_object_source': redirect_object_source,
            'association_label': association_label,
            'ASSOCIATION_OBJECT_TARGETS': ASSOCIATION_OBJECT_TARGETS,
            'existing_custom_property':default_properties + list(names),
            'association_count': association_count
        }
        return render(request, 'data/association/association-labels/manage-related-association-label-drawer.html', context)

    # POST
    association_label_id = request.POST.get('association_label_id')
    label = request.POST.get('label')
    object_source = request.POST.get('object_source')
    redirect_object_source = request.POST.get('redirect_object_source')
    object_targets = request.POST.getlist('object_target')
    project_targets = request.POST.get('project_target', None)
    one_to_one_association_type = request.POST.get(
        'one_to_one_association_type')
    
    # Collect target labels from dynamic inputs - only store non-empty labels
    target_labels = {}
    for key in request.POST.keys():
        if key.startswith('target_label_'):
            target_type = key.replace('target_label_', '')
            target_value = request.POST.get(key, '').strip()
            if target_value:  # Only store non-empty labels
                target_labels[target_type] = target_value

    if 'delete-association-label' in request.POST and association_label_id:
        if association_label_id:
            AssociationLabel.objects.get(
                id=association_label_id, workspace=workspace).delete()
            
        if is_valid_uuid(redirect_object_source):
            try:
                custom_object = CustomObject.objects.get(
                    id=redirect_object_source)
                return redirect(reverse('workspace_setting', host='app')+f'?custom_object={custom_object.slug}')
            except CustomObject.DoesNotExist:
                print('Custom object does not exist')
                return redirect(reverse('workspace_setting', host='app'))
        return redirect(reverse('workspace_setting', host='app')+f'?setting_type={redirect_object_source}')

    
    if association_label_id:
        try:
            association_label = AssociationLabel.objects.get(
                id=association_label_id, workspace=workspace, object_source=object_source)
        except AssociationLabel.DoesNotExist:
            print('Association label does not exist')
            if is_valid_uuid(redirect_object_source):
                try:
                    custom_object = CustomObject.objects.get(
                        id=redirect_object_source)
                    return redirect(reverse('workspace_setting', host='app')+f'?custom_object={custom_object.slug}')
                except CustomObject.DoesNotExist:
                    print('Custom object does not exist')
                    return redirect(reverse('workspace_setting', host='app'))
            return redirect(reverse('workspace_setting', host='app')+f'?setting_type={redirect_object_source}')
        
        
        association_label.label = label
        association_label.project_target = project_targets if project_targets else None
        association_label.object_source = object_source
    
        association_label.association_type = 'one_to_one' if one_to_one_association_type else 'many_to_many'

        association_label.label_pair = target_labels if target_labels else None

        association_label.save()
    
    else:
        association_label_data = {
            'workspace': workspace,
            'label': label,
            'label_ja': label,  # Set label_ja to the same value for user-created custom objects
            'object_source': object_source,
            'project_target': project_targets if project_targets else None,
            'object_target': ','.join(object_targets),
            'association_type': 'one_to_one' if one_to_one_association_type else 'many_to_many',
            'label_pair': target_labels if target_labels else None  # Store only target labels
        }
        association_label = AssociationLabel.objects.create(**association_label_data)

    if is_valid_uuid(redirect_object_source):
        try:
            custom_object = CustomObject.objects.get(
                id=redirect_object_source)
            return redirect(reverse('workspace_setting', host='app')+f'?custom_object={custom_object.slug}')
        except CustomObject.DoesNotExist:
            print('Custom object does not exist')
            return redirect(reverse('workspace_setting', host='app'))
    return redirect(reverse('workspace_setting', host='app')+f'?setting_type={redirect_object_source}')



def auto_create_necessary_association_label_setting(workspace:Workspace,page_group_type):

    if page_group_type in [TYPE_OBJECT_ORDER,TYPE_OBJECT_CASE,TYPE_OBJECT_CONTACT,TYPE_OBJECT_COMPANY,TYPE_OBJECT_INVENTORY_TRANSACTION,TYPE_OBJECT_INVENTORY,TYPE_OBJECT_SUBSCRIPTION,TYPE_OBJECT_ESTIMATE,TYPE_OBJECT_INVOICE,TYPE_OBJECT_RECEIPT,TYPE_OBJECT_ITEM,TYPE_OBJECT_PURCHASE_ORDER,TYPE_OBJECT_BILL,TYPE_OBJECT_DELIVERY_NOTE]:

        default_properties = get_properties_with_details(page_group_type, workspace, 'en')
        default_association = [item for item in default_properties if item['type'] == 'association']
        for association in default_association:
            
            if association['id'] in DEAFULT_ASSOCIATION_OBJECT_TARGET:
                object_targets = DEAFULT_ASSOCIATION_OBJECT_TARGET[association['id']]
            else:
                object_targets = [association['id']]

            association_label,created = AssociationLabel.objects.get_or_create(workspace=workspace, label=association['id'], object_source=page_group_type, object_target=",".join(object_targets), created_by_sanka=True)
            if not created:
                association_label.updated_at=timezone.now()


            # It's better to move this list to a constant file, e.g., properties_constant.py
            ONE_TO_ONE_ASSOCIATION_LABELS = ['customer']
            
            # Context-specific one-to-one associations: (object_source, label) pairs
            CONTEXT_SPECIFIC_ONE_TO_ONE = [
                (TYPE_OBJECT_PURCHASE_ORDER, 'supplier'),
            ]
            
            # Get target objects as a list, handling comma-separated values
            target_objects = [obj.strip() for obj in association_label.object_target.split(',')] if association_label.object_target else []
            
            related_association_label=None
            if target_objects:
                related_association_label = AssociationLabel.objects.filter(label__in=ONE_TO_ONE_ASSOCIATION_LABELS, object_source__in=target_objects, object_target__icontains=association_label.object_source).exists()

            # Check if this is a context-specific one-to-one association
            is_context_specific_one_to_one = (association_label.object_source, association_label.label) in CONTEXT_SPECIFIC_ONE_TO_ONE

            if (association_label.label in ONE_TO_ONE_ASSOCIATION_LABELS) or related_association_label or is_context_specific_one_to_one:
                association_label.association_type = 'one_to_one'
            else:
                association_label.association_type = 'many_to_many'

            association_label.save()

    return True

def load_association_label_form(request):
    workspace = get_workspace(request.user)
    obj_id = request.GET.get('obj_id')
    property = request.GET.get('property')
    page_group_type = request.GET.get('page_group_type')

    created_by_sanka = False
    if is_valid_uuid(property):
        association_label = AssociationLabel.objects.filter(workspace=workspace,id=property).first()
    else:
        association_label = AssociationLabel.objects.filter(workspace=workspace,label__iexact=property,object_source=page_group_type).first()
        if association_label.created_by_sanka:
            created_by_sanka = True
    # Determine selected associations first (used only to render preselected options)
    selected_associations = None
    obj_instance = None
    if obj_id:
        try:
            page_obj_source = get_page_object(page_group_type, 'en')
            obj_instance = page_obj_source['base_model'].objects.filter(id=obj_id).first()
        except Exception:
            obj_instance = CustomObjectPropertyRow.objects.filter(id=obj_id).first()
        if obj_instance:
            selected_associations = AssociationLabelObject.get_for_object(obj_instance, workspace, association_label).values_list('source_object_id', 'target_object_id')
            selected_associations = list(set(str(item) for tuple_item in selected_associations for item in tuple_item))
            if obj_id in selected_associations:
                selected_associations.remove(obj_id)

    if not selected_associations and created_by_sanka and obj_id and obj_instance:
        reverse_association_label = AssociationLabel.objects.filter(workspace=workspace, created_by_sanka=True, object_source__icontains=association_label.object_target, object_target__icontains=page_group_type).first()
        if reverse_association_label:
            selected_associations = AssociationLabelObject.get_for_target(obj_instance, workspace, reverse_association_label).values_list('source_object_id', 'target_object_id')
            selected_associations = list(set(str(item) for tuple_item in selected_associations for item in tuple_item))
            if obj_id in selected_associations:
                selected_associations.remove(obj_id)

    # Initialize containers for preselected objects per type
    companies = []
    contacts = []
    subscriptions = []
    estimates = []
    purchaseorders = []
    invoices = []
    inventory_transactions = []
    cases = []
    delivery_notes = []
    orders = []
    custom_objects = []
    tasks = []
    receipts = []
    bills = []
    expenses = []
    inventory = []
    items = []
    inventory_warehouses = []
    journal_entries=[]

    print('Loading association label form for:', property, 'on page group type:', page_group_type)
    object_targets = association_label.object_target.split(',')
    print("======== object_targets: ", object_targets)

    # Only fetch the preselected objects; all searching is done via AJAX
    if selected_associations:
        for object_target in object_targets:
            page_obj = get_page_object(object_target, 'en')
            base_model = page_obj.get('base_model')
            try:
                if object_target == TYPE_OBJECT_COMPANY:
                    companies = base_model.objects.filter(workspace=workspace, id__in=selected_associations)
                elif object_target == TYPE_OBJECT_CONTACT:
                    contacts = base_model.objects.filter(workspace=workspace, id__in=selected_associations)
                elif object_target == TYPE_OBJECT_SUBSCRIPTION:
                    subscriptions = base_model.objects.filter(workspace=workspace, id__in=selected_associations)
                elif object_target == TYPE_OBJECT_ESTIMATE:
                    estimates = base_model.objects.filter(workspace=workspace, id__in=selected_associations)
                elif object_target == TYPE_OBJECT_PURCHASE_ORDER:
                    purchaseorders = base_model.objects.filter(workspace=workspace, id__in=selected_associations)
                elif object_target == TYPE_OBJECT_INVOICE:
                    invoices = base_model.objects.filter(workspace=workspace, id__in=selected_associations)
                elif object_target == TYPE_OBJECT_INVENTORY_TRANSACTION:
                    inventory_transactions = base_model.objects.filter(workspace=workspace, id__in=selected_associations)
                elif object_target == TYPE_OBJECT_CASE:
                    cases = base_model.objects.filter(workspace=workspace, id__in=selected_associations)
                elif object_target == TYPE_OBJECT_DELIVERY_NOTE:
                    delivery_notes = base_model.objects.filter(workspace=workspace, id__in=selected_associations)
                elif object_target == TYPE_OBJECT_ORDER:
                    orders = base_model.objects.filter(workspace=workspace, id__in=selected_associations)
                elif object_target == TYPE_OBJECT_RECEIPT:
                    receipts = base_model.objects.filter(workspace=workspace, id__in=selected_associations)
                elif object_target == TYPE_OBJECT_BILL:
                    bills = base_model.objects.filter(workspace=workspace, id__in=selected_associations)
                elif object_target == TYPE_OBJECT_EXPENSE:
                    expenses = base_model.objects.filter(workspace=workspace, id__in=selected_associations)
                elif object_target == TYPE_OBJECT_INVENTORY:
                    inventory = base_model.objects.filter(workspace=workspace, id__in=selected_associations)
                elif object_target == TYPE_OBJECT_ITEM:
                    items = base_model.objects.filter(workspace=workspace, id__in=selected_associations)
                elif object_target == TYPE_OBJECT_INVENTORY_WAREHOUSE:
                    inventory_warehouses = base_model.objects.filter(workspace=workspace, id__in=selected_associations)
                elif object_target == TYPE_OBJECT_JOURNAL:
                    journal_entries = base_model.objects.filter(workspace=workspace, id__in=selected_associations)
                elif is_valid_uuid(object_target):
                    custom_objects = CustomObjectPropertyRow.objects.filter(workspace=workspace, custom_object__id=object_target, id__in=selected_associations)
                elif object_target == TYPE_OBJECT_TASK:
                    # For preselected options, filter only by IDs in current workspace
                    tasks = base_model.objects.filter(workspace=workspace, id__in=selected_associations)
            except Exception as e:
                print(f"Error preloading selected options for {object_target}: {e}")

    filter_by_object=None
    if page_group_type in [TYPE_OBJECT_ITEM,TYPE_OBJECT_INVENTORY] and association_label.object_source in [TYPE_OBJECT_INVENTORY,TYPE_OBJECT_ITEM]:
        filter_by_object = obj_id
    
    context = {
        'obj_id': obj_id,
        'property': property,
        'page_group_type': page_group_type,
        'association_label': association_label,

        'companies': companies,
        'contacts': contacts,
        'subscriptions': subscriptions,
        'estimates': estimates,
        'purchaseorders': purchaseorders,
        'invoices': invoices,
        'inventory_transactions': inventory_transactions,
        'inventory': inventory,
        'cases':cases,
        'delivery_notes':delivery_notes,
        'orders':orders,
        'custom_objects': custom_objects,
        'tasks': tasks,
        'receipts': receipts,
        'bills': bills,
        'expenses': expenses,
        'items': items,
        'inventory_warehouses': inventory_warehouses,
        'journal_entries':journal_entries,

        'selected_associations': selected_associations,
        'filter_by_object': filter_by_object
    }
    
    side_menu= request.GET.get('side_menu', False) == 'True'
    if side_menu:
        print('logging side menu', request.GET)

        return render(request, 'data/partials/manage-drawer/associated-label-form-side-menu.html', context)

    return render(request, 'data/partials/manage-drawer/associated-label-form.html', context)


def save_association_label(request, obj, page_group_type=None):
    workspace = get_workspace(request.user)

    # In your view handling the POST request
    for key in request.POST.keys():
        if key.startswith('association_label#'):
            # Extract the ID from the key
            association_label_id = key.split('#')[-1]
            association_label = AssociationLabel.objects.filter(id=association_label_id).first()
     
            if association_label:
                # Reset associations for this label first
                AssociationLabelObject.reset_associations_for_object(
                    obj, 
                    workspace, 
                    association_label
                )
                
                # Get ALL values for this key (this is the fix!)
                values = request.POST.getlist(key)
                # Process each value
                for value in values:
                    if value:  # Make sure value is not empty
                        # Process the value
                        value_parts = value.split('#')
                        if len(value_parts) == 2:
                            object_id = value_parts[0]
                            object_type = value_parts[1]
                            try:
                                page_obj = get_page_object(object_type, 'en')
                                target_object = page_obj['base_model'].objects.filter(id=object_id).first()
                            except:
                                target_object = CustomObjectPropertyRow.objects.filter(id=object_id).first()                    

                            if target_object:
                                AssociationLabelObject.create_association(obj, target_object, workspace, association_label)

                                #keep old one -> commented out because it's not working
                                # old_save_association_label(obj,target_object,object_type, page_group_type)
    
    # used for create association from other page
    if request.POST.get("type_association", "") == "create-association" or request.POST.get("type", "") == "create-association":       
        
        source = request.POST.get("source")
        
        try:
            object_id = request.POST.get("source_object_id", request.POST.get("object_id"))
            
            try:
                page_obj = get_page_object(source, 'en')
                base_model = page_obj['base_model']
                source_obj = base_model.objects.filter(id=object_id).first()
            except:
                source_obj = CustomObjectPropertyRow.objects.filter(id=object_id).first()                    
            
            if not source_obj:
                return False
            
            association_label = AssociationLabel.objects.filter(workspace=workspace,object_source=source, label__iexact=page_group_type).first()
            
            if source_obj and association_label:
                AssociationLabelObject.create_association(source_obj, obj, workspace, association_label)
        except Exception as e:
            print("Error association: ", e)

    object_type = request.POST.get('object_type', request.POST.get('page_group_type', ''))
    if f'update_{object_type}' in request.POST:
        source = request.POST.get('source', None)
        
        association_label = AssociationLabel.objects.filter(workspace=workspace,object_source=source, label__iexact=object_type).first()
        
        if not association_label:
            return False
        
        # target object
        target_objs = []
        selected_objects = []
        if object_type == TYPE_OBJECT_SUBSCRIPTION:
            selected_objects = request.POST.getlist('subscriptions', [])
        elif object_type == TYPE_OBJECT_ORDER:
            selected_objects = request.POST.getlist('orders', [])
        # ----add other object_type----
        
        try:
            page_obj = get_page_object(object_type, 'en')
            target_obj = page_obj['base_model']
            target_objs = target_obj.objects.filter(id__in=selected_objects)
        except:
            target_obj = CustomObject.objects.filter(id=object_type).first()                
            target_objs = CustomObjectPropertyRow.objects.filter(id__in=selected_objects, custom_object=target_obj)
        if not target_obj:
            return False
        
        if obj and any(target_objs):
            
            AssociationLabelObject.reset_associations_for_object(
                obj, 
                workspace, 
                association_label
            )
            for target_obj in target_objs:
                AssociationLabelObject.create_association(obj, target_obj, workspace, association_label)

        else:
            AssociationLabelObject.reset_associations_for_object(
                obj, 
                workspace, 
                association_label
            )
        
    return True           
       

def save_association_label_side_menu(request):
    workspace = get_workspace(request.user)
    obj_id = request.POST.get('obj_id')
    page_group_type = request.POST.get('page_group_type')

    obj = None
    custom_object = None
    if page_group_type:
        try:
            page_obj = get_page_object(page_group_type, 'en')
            obj = page_obj['base_model'].objects.filter(id=obj_id).first()
        except Exception:
            obj = CustomObjectPropertyRow.objects.filter(id=obj_id).first()
            if obj:
                custom_object = obj.custom_object

    if not obj:
        return redirect(reverse("main", host="app"))

    save_association_label(request, obj, page_group_type)

    module = Module.objects.filter(workspace=workspace, object_values__contains=str(
        page_group_type)).order_by('order', 'created_at').first()

    if module:
        module_slug = module.slug
        if custom_object:
            module_object_slug = custom_object.slug
        else:
            module_object_slug = OBJECT_TYPE_TO_SLUG.get(page_group_type, page_group_type)

        url = reverse(
            "load_object_page",
            host="app",
            kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
        )
        url = url + f"?id={obj_id}&target={page_group_type}"
        return redirect(url)
    return redirect(reverse("main", host="app"))

#======= not being used

def old_save_association_label(obj:ShopTurboOrders,target_object,target_object_type, page_group_type=None):

    if page_group_type == TYPE_OBJECT_ORDER and obj:
        if target_object_type == TYPE_OBJECT_COMPANY:
            obj.company = target_object
            obj.contact = None
        elif target_object_type == TYPE_OBJECT_CONTACT:
            obj.company = None
            obj.contact = target_object
        elif target_object_type == TYPE_OBJECT_ESTIMATE:
            obj.estimate.add(target_object)
        elif target_object_type == TYPE_OBJECT_INVOICE:
            obj.invoice.add(target_object)
        elif target_object_type == TYPE_OBJECT_INVENTORY_TRANSACTION:
            obj.inventory_transactions.add(target_object)
        elif target_object_type == TYPE_OBJECT_PURCHASE_ORDER:
            obj.purchase_orders.add(target_object)
        elif target_object_type == TYPE_OBJECT_CASE:
            obj.cases.add(target_object)
        elif target_object_type == TYPE_OBJECT_DELIVERY_NOTE:
            obj.deliveryslip.add(target_object)
        obj.save()


@login_or_hubspot_required
def apply_association_label(request):
    workspace = get_workspace(request.user)
    if request.method == 'GET':
        object_source = request.GET.get('object_source')
        object_source_id = request.GET.get('object_source_id')
        object_target = request.GET.get('object_target')
        object_target_id = request.GET.get('object_target_id')
        association_label_implementation_id = request.GET.get(
            'association_label_implementation_id')
        
        association_label_implementation = None
        if association_label_implementation_id:
            try:
                association_label_implementation = AssociationLabelImplementation.objects.get(
                    id=association_label_implementation_id, label__workspace=workspace, object_source_id=object_source_id)
            except AssociationLabelImplementation.DoesNotExist:
                print('Association label implementation does not exist')
                return HttpResponse('Association label implementation does not exist', status=404)
        association_labels = AssociationLabel.objects.filter(
            workspace=workspace, object_source=object_source, object_target__contains=object_target)
        
        context = {
            'association_label_implementation': association_label_implementation,
            'object_source': object_source,
            'object_source_id': object_source_id,
            'object_target': object_target,
            'object_target_id': object_target_id,
            'association_labels': association_labels,
            'view': request.GET.get('view'),
        }
        return render(request, 'data/association/association-labels/apply-association-label-drawer.html', context)

    # POST
    association_label_implementation_id = request.POST.get(
        'association_label_implementation_id')
    association_label_id = request.POST.get('association_label_id')
    object_source = request.POST.get('object_source')
    object_source_id = request.POST.get('object_source_id')
    object_target = request.POST.get('object_target')
    object_target_id = request.POST.get('object_target_id')
    view = request.POST.get('view')
    page = request.POST.get('page')

    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_ORDER]
    module_slug = request.POST.get('module')
    module = Module.objects.filter(
        workspace=workspace, object_values__contains=TYPE_OBJECT_ORDER).order_by('order', 'created_at')
    if module_slug:
        module = module.filter(slug=module_slug).first()
    else:
        module = module.first()
        module_slug = module.slug
    redirect_url = reverse('main', host='app')
    if module:
        redirect_url = build_redirect_url(reverse('load_object_page', host='app', kwargs={
                                          'module_slug': module_slug, 'object_slug': module_object_slug}), view_id=view, page=page, id=object_source_id, target=TYPE_OBJECT_ORDER)
    if not (association_label_id and object_source_id and object_target_id and object_source and object_target):
        print(
            f'Invalid request {association_label_id} {object_source_id} {object_target_id}')
        return redirect(redirect_url)

    try:
        association_label = AssociationLabel.objects.get(
            id=association_label_id, workspace=workspace)
    except AssociationLabel.DoesNotExist:
        print('Association label does not exist')
        return redirect(redirect_url)

    if association_label_implementation_id:
        try:
            association_label_implementation = AssociationLabelImplementation.objects.get(
                id=association_label_implementation_id, label__workspace=workspace, object_source_id=object_source_id, object_target_id=object_target_id)
            association_label_implementation.label = association_label
            association_label_implementation.save()
        except AssociationLabel.DoesNotExist:
            print('Association label does not exist')
            return redirect(redirect_url)
    else:
        association_label_implementation = AssociationLabelImplementation.objects.create(
            label=association_label, object_source_id=object_source_id, object_target_id=object_target_id)
        
    if association_label.association_type == 'one_to_one':
        try:
            AssociationLabelImplementation.objects.filter(
                label=association_label, object_source_id=object_source_id).exclude(id=association_label_implementation.id).delete()
        except:
            pass

    return redirect(redirect_url)


@login_or_hubspot_required
def unapply_association_label(request, id):
    workspace = get_workspace(request.user)
    try:
        AssociationLabelImplementation.objects.get(
            id=id, label__workspace=workspace).delete()
    except AssociationLabel.DoesNotExist:
        print('Association label does not exist')
        return HttpResponse('Association label does not exist', status=404)

    return HttpResponse()


def get_object_display_text(obj, object_target, workspace, language_code='en'):
    """
    Get display text for an object using workspace display settings
    """
    try:
        # Get display settings for this object type
        object_manager, _ = ObjectManager.objects.get_or_create(
            workspace=workspace, 
            page_group_type=object_target
        )
        
        if object_manager.column_display:
            columns = [col.strip() for col in object_manager.column_display.split(',') if col.strip()]
            if columns:
                # Use display settings to format the text
                display_text = get_object_display_based_columns(
                    object_target, obj, columns, 'UTC', language_code
                ).strip()
                if display_text:
                    return display_text
        
        # Fallback to default display logic if no display settings or empty result
        page_obj = get_page_object(object_target, 'en')
        id_field = page_obj['id_field']
        
        # Get object type label
        type_labels = {
            TYPE_OBJECT_COMPANY: {'ja': '企業', 'en': 'Company'},
            TYPE_OBJECT_CONTACT: {'ja': '連絡先', 'en': 'Contact'},
            TYPE_OBJECT_ITEM: {'ja': '商品', 'en': 'Item'},
            TYPE_OBJECT_INVENTORY: {'ja': '在庫', 'en': 'Inventory'},
            TYPE_OBJECT_INVENTORY_WAREHOUSE: {'ja': '倉庫', 'en': 'Warehouse'},
            TYPE_OBJECT_ORDER: {'ja': '注文書', 'en': 'Order'},
            TYPE_OBJECT_ESTIMATE: {'ja': '見積もり', 'en': 'Estimate'},
            TYPE_OBJECT_INVOICE: {'ja': '請求書', 'en': 'Invoice'},
            TYPE_OBJECT_PURCHASE_ORDER: {'ja': '注文書', 'en': 'Purchase Order'},
            TYPE_OBJECT_CASE: {'ja': '案件', 'en': 'Case'},
            TYPE_OBJECT_TASK: {'ja': 'タスク', 'en': 'Task'},
            TYPE_OBJECT_SUBSCRIPTION: {'ja': 'サブスクリプション', 'en': 'Subscription'},
            TYPE_OBJECT_DELIVERY_NOTE: {'ja': '納品書', 'en': 'Delivery Note'},
            TYPE_OBJECT_INVENTORY_TRANSACTION: {'ja': '在庫取引', 'en': 'Inventory Transaction'},
            TYPE_OBJECT_RECEIPT: {'ja': '支払', 'en': 'Payment'},
            
            TYPE_OBJECT_BILL: {'ja': '支払請求', 'en': 'Bill'},
            TYPE_OBJECT_EXPENSE: {'ja': '経費', 'en': 'Expense'},
            TYPE_OBJECT_JOURNAL: {'ja': '仕訳', 'en': 'Journal Entry'},
        }
        
        type_label = type_labels.get(object_target, {}).get(language_code, 'Object')
        
        print("============== type_label: ", type_label)
        
        # Build fallback display text
        if object_target == TYPE_OBJECT_COMPANY:
            obj_id = getattr(obj, 'company_id', 0)
            name = getattr(obj, 'name', '')
            return f"#{obj_id:04d} | {name} ({type_label})"
        elif object_target == TYPE_OBJECT_CONTACT:
            obj_id = getattr(obj, 'contact_id', 0)
            first_name = getattr(obj, 'name', '')
            last_name = getattr(obj, 'last_name', '')
            
            if language_code == 'ja':
                if last_name:
                    return f"#{obj_id:04d} | {last_name} {first_name} ({type_label})"
                else:
                    return f"#{obj_id:04d} | {first_name} ({type_label})"
            else:
                if last_name:
                    return f"#{obj_id:04d} | {first_name} {last_name} ({type_label})"
                else:
                    return f"#{obj_id:04d} | {first_name} ({type_label})"

            
        
        elif object_target == TYPE_OBJECT_ITEM:
            obj_id = getattr(obj, 'item_id', 0)
            name = getattr(obj, 'name', '')
            return f"#{obj_id:04d} | {name} ({type_label})"
        elif object_target == TYPE_OBJECT_INVENTORY_WAREHOUSE:
            obj_id = getattr(obj, 'id_iw', 0)
            name = getattr(obj, 'warehouse', '') or getattr(obj, 'warehouse_name', '')
            return f"#{obj_id:04d} | {name} ({type_label})"
        elif object_target == TYPE_OBJECT_TASK:
            obj_id = getattr(obj, 'task_id', 0)
            title = getattr(obj, 'title', '')
            project_title = getattr(getattr(obj, 'project_target', None), 'title', '')
            return f"{project_title} | #{obj_id:04d} - {title} | ({type_label})"
        elif object_target == TYPE_OBJECT_JOURNAL:
            obj_id = getattr(obj, 'id_journal', 0)
            category = getattr(obj, 'category', '')
            amount = getattr(obj, 'amount', 0) or 0
            transaction_date = getattr(obj, 'transaction_date', None)
            date_str = transaction_date.strftime('%Y-%m-%d') if transaction_date else ''
            if category and amount and date_str:
                return f"#{obj_id:04d} | {category} | {amount:,.2f} | {date_str} ({type_label})"
            elif category and date_str:
                return f"#{obj_id:04d} | {category} | {date_str} ({type_label})"
            elif category:
                return f"#{obj_id:04d} | {category} ({type_label})"
            else:
                return f"#{obj_id:04d} | ({type_label})"
        elif object_target == TYPE_OBJECT_INVOICE:
            obj_id = getattr(obj, 'id_inv', 0)
            
            # Try to get company or contact information
            company = getattr(obj, 'company', None)
            contact = getattr(obj, 'contact', None)
            due_date = getattr(obj, 'due_date', None)
            
            # Build display text with available information
            parts = [f"#{obj_id:04d}"]
            
            if company:
                company_name = getattr(company, 'name', '')
                if company_name:
                    parts.append(company_name)
            elif contact:
                # Handle contact name properly
                first_name = getattr(contact, 'name', '')
                last_name = getattr(contact, 'last_name', '')
                if language_code == 'ja':
                    if last_name:
                        contact_name = f"{last_name} {first_name}"
                    else:
                        contact_name = first_name
                else:
                    if last_name:
                        contact_name = f"{first_name} {last_name}"
                    else:
                        contact_name = first_name
                if contact_name:
                    parts.append(contact_name)
            
            # Add due date if available
            if due_date:
                date_str = due_date.strftime('%Y-%m-%d')
                parts.append(f"Due: {date_str}")
            
            # Join parts and add type label
            display_text = " | ".join(parts)
            return f"{display_text} ({type_label})"
        else:
            # Generic fallback for other object types
            obj_id = getattr(obj, id_field, 0)
            return f"#{obj_id:04d} | ({type_label})"
            
    except Exception as e:
        print(f"Error getting display text for {object_target}: {e}")
        # Final fallback
        return f"#{getattr(obj, 'id', 0):04d} | ({object_target})"


@login_or_hubspot_required
def load_target_labels(request):
    """
    HTMX endpoint to load target labels dynamically based on selected object targets
    """
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    
    # Get parameters from request
    object_source = request.GET.get('object_source')
    association_label_id = request.GET.get('association_label_id')
    object_targets = request.GET.getlist('object_target')

    print(f"DEBUG: load_target_labels called with {object_targets}")
    print(f"  object_source={object_source}")
    print(f"  association_label_id={association_label_id}")
    
    # Debug logging for HTMX dynamic changes only
    # print(f"DEBUG: load_target_labels called with {object_targets}")
    
    if not object_targets:
        # Return empty response if no targets selected
        return render(request, 'data/association/association-labels/target-labels-partial.html', {
            'target_labels': []
        })
    
    # Get existing target labels from label_pair if editing
    existing_target_labels = {}
    
    if association_label_id:
        try:
            association_label = AssociationLabel.objects.get(
                id=association_label_id, workspace=workspace, object_source=object_source)
            
            # Get existing target labels from label_pair column
            existing_target_labels = association_label.label_pair or {}
        except AssociationLabel.DoesNotExist:
            pass
    
    # Prepare target labels data
    target_labels = []
    for target_obj in object_targets:
        target_obj = target_obj.strip()
        if not target_obj:
            continue
            
        # Get target object display name
        try:
            if is_valid_uuid(target_obj):
                # Handle custom objects
                custom_object = CustomObject.objects.filter(id=target_obj).first()
                object_name = custom_object.name if custom_object else 'Custom Object'
            else:
                # Handle standard objects - use the target_obj directly as the object name
                # since page_group_to_object_singular template filter will handle the display
                object_name = target_obj
        except:
            object_name = target_obj
        
        # Get existing label if available
        existing_label = existing_target_labels.get(target_obj, '')
        
        target_labels.append({
            'target_value': target_obj,
            'target_name': object_name,
            'existing_label': existing_label
        })
    
    # Prepared target_labels for HTMX response
    print("== target_labels: ",target_labels)
    context = {
        'target_labels': target_labels,
        'LANGUAGE_CODE': lang
    }
    
    return render(request, 'data/association/association-labels/target-labels-partial.html', context)


@login_or_hubspot_required
def association_label_options(request):
    """
    AJAX endpoint for Select2 lazy loading of association label options
    Supports multiple object types from association_label.object_target
    """
    workspace = get_workspace(request.user)
    
    # Get parameters
    search_term = request.GET.get('q', '').strip()
    page = int(request.GET.get('page', 1))
    property_id = request.GET.get('property')
    page_group_type = request.GET.get('page_group_type')
    obj_id = request.GET.get('obj_id')
    filter_by_object = request.GET.get('filter_by_object')
    
    print("[OPTIONS] filter_by_object: ",filter_by_object)

    # Pagination settings
    per_page = 50
    offset = (page - 1) * per_page
    
    # Verify required parameters
    if not (property_id and page_group_type):
        return JsonResponse({'results': [], 'pagination': {'more': False}})
    
    # Get association label
    association_label = None
    if is_valid_uuid(property_id):
        association_label = AssociationLabel.objects.filter(workspace=workspace, id=property_id).first()
    else:
        association_label = AssociationLabel.objects.filter(
            workspace=workspace, label__iexact=property_id, object_source=page_group_type).first()
    
    if not association_label:
        return JsonResponse({'results': [], 'pagination': {'more': False}})
    
    # Get object targets from comma-separated string
    object_targets = [target.strip() for target in association_label.object_target.split(',') if target.strip()]

    # Helper function to get results for a specific object type
    def get_objects_for_type(object_target):
        results = []
        try:
            # Handle custom objects separately since they use UUIDs instead of object types
            if is_valid_uuid(object_target):
                # Handle custom objects
                queryset = CustomObjectPropertyRow.objects.filter(
                    workspace=workspace, custom_object__id=object_target)

                if search_term:
                    queryset = queryset.filter(
                        Q(property_values__icontains=search_term) | Q(row_id__icontains=search_term)
                    )

                if obj_id:
                    queryset = queryset.exclude(id=obj_id)
                object_type_key = object_target

                for obj in queryset:
                    # For custom objects, fall back to simple display since object_target is a UUID
                    try:
                        display_text = f"#{obj.row_id:04d} | ({obj.custom_object.name})"
                    except:
                        display_text = f"#{getattr(obj, 'row_id', 0):04d} | (Custom Object)"
                    results.append({
                        'id': f"{obj.id}#{object_type_key}",
                        'text': display_text,
                        'sort_key': getattr(obj, 'row_id', 0)
                    })
                print("results: ",results)
                return results

            # For standard object types, get page object configuration
            page_obj = get_page_object(object_target, 'en')
            base_model = page_obj['base_model']
            id_field = page_obj['id_field']

            # Build base query based on object type
            queryset = None
            object_type_key = ""
            
            if object_target == TYPE_OBJECT_COMPANY:
                queryset = base_model.objects.filter(workspace=workspace, status='active')
                if search_term:
                    queryset = queryset.filter(
                        Q(name__icontains=search_term) | Q(**{f'{id_field}': search_term})
                    )
                if obj_id:
                    queryset = queryset.exclude(id=obj_id)
                object_type_key = TYPE_OBJECT_COMPANY
                for obj in queryset:
                    display_text = get_object_display_text(obj, object_target, workspace, request.LANGUAGE_CODE)
                    results.append({
                        'id': f"{obj.id}#{object_type_key}",
                        'text': display_text,
                        'sort_key': getattr(obj, id_field, 0)
                    })
                    
            elif object_target == TYPE_OBJECT_CONTACT:
                queryset = base_model.objects.filter(workspace=workspace, status='active')
                if search_term:
                    search_parts = search_term.strip().split()
                    
                    # Base conditions for name fields
                    conditions = Q(name__icontains=search_term) | Q(last_name__icontains=search_term)
                    
                    # Add ID field search only if search_term can be converted to integer
                    try:
                        int(search_term)
                        conditions |= Q(**{f'{id_field}': search_term})
                    except ValueError:
                        # search_term is not a valid integer, skip ID search
                        pass
                    
                    # If multiple words, also search cross-field combinations
                    if len(search_parts) > 1:
                        first_word = search_parts[0]
                        last_word = search_parts[-1]
                        
                        conditions |= (Q(name__icontains=first_word) & Q(last_name__icontains=last_word))
                        conditions |= (Q(name__icontains=last_word) & Q(last_name__icontains=first_word))
                    
                    queryset = queryset.filter(conditions)
                        
                if obj_id:
                    queryset = queryset.exclude(id=obj_id)
                object_type_key = TYPE_OBJECT_CONTACT
                
                for obj in queryset:
                    display_text = get_object_display_text(obj, object_target, workspace, request.LANGUAGE_CODE)
                    results.append({
                        'id': f"{obj.id}#{object_type_key}",
                        'text': display_text,
                        'sort_key': getattr(obj, id_field, 0)
                    })
                    
            elif object_target == TYPE_OBJECT_SUBSCRIPTION:
                queryset = base_model.objects.filter(workspace=workspace, status='active')
                if search_term:
                    queryset = queryset.filter(
                     Q(**{f'{id_field}': search_term})
                    )
                if obj_id:
                    queryset = queryset.exclude(id=obj_id)
                object_type_key = TYPE_OBJECT_SUBSCRIPTION
                
                for obj in queryset:
                    display_text = get_object_display_text(obj, object_target, workspace, request.LANGUAGE_CODE)
                    results.append({
                        'id': f"{obj.id}#{object_type_key}",
                        'text': display_text,
                        'sort_key': getattr(obj, id_field, 0)
                    })
                    
            elif object_target == TYPE_OBJECT_ITEM:
                queryset = base_model.objects.filter(workspace=workspace, status='active')
                if search_term:
                    queryset = queryset.filter(
                        Q(name__icontains=search_term) | Q(**{f'{id_field}': search_term})
                    )
                if obj_id:
                    queryset = queryset.exclude(id=obj_id)
                object_type_key = TYPE_OBJECT_ITEM
                
                for obj in queryset:
                    display_text = get_object_display_text(obj, object_target, workspace, request.LANGUAGE_CODE)
                    results.append({
                        'id': f"{obj.id}#{object_type_key}",
                        'text': display_text,
                        'sort_key': getattr(obj, id_field, 0)
                    })
                    
            elif object_target == TYPE_OBJECT_INVENTORY:
                queryset = base_model.objects.filter(workspace=workspace, status='active')
                
                # Filter by specific item if filter_by_item parameter is provided
                if filter_by_object:
                    queryset = queryset.filter(item__id=filter_by_object)
                
                if search_term:
                    queryset = queryset.filter(
                        Q(**{f'{id_field}': search_term})
                    )
                if obj_id:
                    queryset = queryset.exclude(id=obj_id)
                object_type_key = TYPE_OBJECT_INVENTORY
                
                for obj in queryset:
                    display_text = get_object_display_text(obj, object_target, workspace, request.LANGUAGE_CODE)
                    results.append({
                        'id': f"{obj.id}#{object_type_key}",
                        'text': display_text,
                        'sort_key': getattr(obj, id_field, 0)
                    })
                    
            elif object_target == TYPE_OBJECT_INVENTORY_WAREHOUSE:
                queryset = base_model.objects.filter(workspace=workspace, usage_status='active')
                if search_term:
                    queryset = queryset.filter(
                        Q(**{f'{id_field}': search_term})
                    )
                if obj_id:
                    queryset = queryset.exclude(id=obj_id)
                object_type_key = TYPE_OBJECT_INVENTORY_WAREHOUSE
                for obj in queryset:
                    display_text = get_object_display_text(obj, object_target, workspace, request.LANGUAGE_CODE)
                    results.append({
                        'id': f"{obj.id}#{object_type_key}",
                        'text': display_text,
                        'sort_key': getattr(obj, id_field, 0)
                    })
                    
            elif object_target == TYPE_OBJECT_ORDER:
                queryset = base_model.objects.filter(workspace=workspace, status='active')
                if search_term:
                    queryset = queryset.filter(
                        Q(**{f'{id_field}': search_term})
                    )
                if obj_id:
                    queryset = queryset.exclude(id=obj_id)
                object_type_key = TYPE_OBJECT_ORDER
                
                for obj in queryset:
                    display_text = get_object_display_text(obj, object_target, workspace, request.LANGUAGE_CODE)
                    results.append({
                        'id': f"{obj.id}#{object_type_key}",
                        'text': display_text,
                        'sort_key': getattr(obj, id_field, 0)
                    })
                    
            elif object_target == TYPE_OBJECT_ESTIMATE:
                queryset = base_model.objects.filter(workspace=workspace)
                if search_term:
                    queryset = queryset.filter(
                       Q(**{f'{id_field}': search_term})
                    )
                if obj_id:
                    queryset = queryset.exclude(id=obj_id)
                object_type_key = TYPE_OBJECT_ESTIMATE
                
                for obj in queryset:
                    display_text = get_object_display_text(obj, object_target, workspace, request.LANGUAGE_CODE)
                    results.append({
                        'id': f"{obj.id}#{object_type_key}",
                        'text': display_text,
                        'sort_key': getattr(obj, id_field, 0)
                    })
                    
            elif object_target == TYPE_OBJECT_INVOICE:
                queryset = base_model.objects.filter(workspace=workspace, usage_status='active')
                if search_term:
                    queryset = queryset.filter(
                        Q(**{f'{id_field}': search_term})
                    )
                if obj_id:
                    queryset = queryset.exclude(id=obj_id)
                object_type_key = TYPE_OBJECT_INVOICE
                
                for obj in queryset:
                    display_text = get_object_display_text(obj, object_target, workspace, request.LANGUAGE_CODE)
                    results.append({
                        'id': f"{obj.id}#{object_type_key}",
                        'text': display_text,
                        'sort_key': getattr(obj, id_field, 0)
                    })
                    
            elif object_target == TYPE_OBJECT_PURCHASE_ORDER:
                queryset = base_model.objects.filter(workspace=workspace, usage_status='active')
                if search_term:
                    queryset = queryset.filter(
                        Q(**{f'{id_field}': search_term})
                    )
                if obj_id:
                    queryset = queryset.exclude(id=obj_id)
                object_type_key = TYPE_OBJECT_PURCHASE_ORDER
                
                for obj in queryset:
                    display_text = get_object_display_text(obj, object_target, workspace, request.LANGUAGE_CODE)
                    results.append({
                        'id': f"{obj.id}#{object_type_key}",
                        'text': display_text,
                        'sort_key': getattr(obj, id_field, 0)
                    })
                    
            elif object_target == TYPE_OBJECT_CASE:
                queryset = base_model.objects.filter(workspace=workspace, status='active')
                if search_term:
                    queryset = queryset.filter(
                        Q(**{f'{id_field}': search_term})
                    )
                if obj_id:
                    queryset = queryset.exclude(id=obj_id)
                object_type_key = TYPE_OBJECT_CASE
                
                for obj in queryset:
                    display_text = get_object_display_text(obj, object_target, workspace, request.LANGUAGE_CODE)
                    results.append({
                        'id': f"{obj.id}#{object_type_key}",
                        'text': display_text,
                        'sort_key': getattr(obj, id_field, 0)
                    })
                    
            elif object_target == TYPE_OBJECT_TASK:
                project_target = association_label.project_target
                if not project_target:
                    project_target = Projects.objects.filter(workspace=workspace, default=True).first()
                    if project_target:
                        project_target = str(project_target.id)
                        
                queryset = base_model.objects.filter(
                    project_target=project_target, workspace=workspace, usage_status='active')
                if search_term:
                    queryset = queryset.filter(
                        Q(**{f'{id_field}': search_term})
                    )
                if obj_id:
                    queryset = queryset.exclude(id=obj_id)
                object_type_key = TYPE_OBJECT_TASK
                
                for obj in queryset:
                    display_text = get_object_display_text(obj, object_target, workspace, request.LANGUAGE_CODE)
                    results.append({
                        'id': f"{obj.id}#{object_type_key}",
                        'text': display_text,
                        'sort_key': getattr(obj, id_field, 0)
                    })
                    
            elif object_target == TYPE_OBJECT_DELIVERY_NOTE:
                queryset = base_model.objects.filter(workspace=workspace, usage_status='active')
                if search_term:
                    queryset = queryset.filter(
                        Q(**{f'{id_field}': search_term})
                    )
                if obj_id:
                    queryset = queryset.exclude(id=obj_id)
                object_type_key = TYPE_OBJECT_DELIVERY_NOTE
                
                for obj in queryset:
                    display_text = get_object_display_text(obj, object_target, workspace, request.LANGUAGE_CODE)
                    results.append({
                        'id': f"{obj.id}#{object_type_key}",
                        'text': display_text,
                        'sort_key': getattr(obj, id_field, 0)
                    })
                    
            elif object_target == TYPE_OBJECT_INVENTORY_TRANSACTION:
                queryset = base_model.objects.filter(workspace=workspace, usage_status='active')
                
                # Filter by specific inventory if filter_by_inventory parameter is provided
                if filter_by_object:
                    queryset = queryset.filter(inventory__id=filter_by_object)
                
                if search_term:
                    queryset = queryset.filter(
                        Q(**{f'{id_field}': search_term})
                    )
                if obj_id:
                    queryset = queryset.exclude(id=obj_id)
                object_type_key = TYPE_OBJECT_INVENTORY_TRANSACTION
                for obj in queryset:
                    display_text = get_object_display_text(obj, object_target, workspace, request.LANGUAGE_CODE)
                    results.append({
                        'id': f"{obj.id}#{object_type_key}",
                        'text': display_text,
                        'sort_key': getattr(obj, id_field, 0)
                    })
                    
            elif object_target == TYPE_OBJECT_RECEIPT:
                queryset = base_model.objects.filter(workspace=workspace, usage_status='active')
                if search_term:
                    queryset = queryset.filter(
                        Q(**{f'{id_field}': search_term})
                    )
                if obj_id:
                    queryset = queryset.exclude(id=obj_id)
                object_type_key = TYPE_OBJECT_RECEIPT
                
                for obj in queryset:
                    display_text = get_object_display_text(obj, object_target, workspace, request.LANGUAGE_CODE)
                    results.append({
                        'id': f"{obj.id}#{object_type_key}",
                        'text': display_text,
                        'sort_key': getattr(obj, id_field, 0)
                    })
                    
            elif object_target == TYPE_OBJECT_BILL:
                queryset = base_model.objects.filter(workspace=workspace, usage_status='active')
                if search_term:
                    queryset = queryset.filter(
                        Q(**{f'{id_field}': search_term})
                    )
                if obj_id:
                    queryset = queryset.exclude(id=obj_id)
                object_type_key = TYPE_OBJECT_BILL
                
                for obj in queryset:
                    display_text = get_object_display_text(obj, object_target, workspace, request.LANGUAGE_CODE)
                    results.append({
                        'id': f"{obj.id}#{object_type_key}",
                        'text': display_text,
                        'sort_key': getattr(obj, id_field, 0)
                    })
                    
            elif object_target == TYPE_OBJECT_EXPENSE:
                queryset = base_model.objects.filter(workspace=workspace, usage_status='active')
                if search_term:
                    queryset = queryset.filter(
                        Q(**{f'{id_field}': search_term})
                    )
                if obj_id:
                    queryset = queryset.exclude(id=obj_id)
                object_type_key = TYPE_OBJECT_EXPENSE
                
                for obj in queryset:
                    display_text = get_object_display_text(obj, object_target, workspace, request.LANGUAGE_CODE)
                    results.append({
                        'id': f"{obj.id}#{object_type_key}",
                        'text': display_text,
                        'sort_key': getattr(obj, id_field, 0)
                    })
            elif object_target == TYPE_OBJECT_JOURNAL:
                queryset = base_model.objects.filter(workspace=workspace, usage_status='active')
                if search_term:
                    queryset = queryset.filter(
                        Q(**{f'{id_field}': search_term})
                    )
                if obj_id:
                    queryset = queryset.exclude(id=obj_id)
                object_type_key = TYPE_OBJECT_JOURNAL
                
                for obj in queryset:
                    display_text = get_object_display_text(obj, object_target, workspace, request.LANGUAGE_CODE)
                    results.append({
                        'id': f"{obj.id}#{object_type_key}",
                        'text': display_text,
                        'sort_key': getattr(obj, id_field, 0)
                    })
                    
            # obj_id exclusion is now handled individually for each object type
                
        except Exception as e:
            print(f"Error processing object_target {object_target}: {e}")
            
        return results
        
    # Collect all results from all object types
    all_results = []
    for object_target in object_targets:
        all_results.extend(get_objects_for_type(object_target))
    
    # Sort all results by sort_key (descending - newest first)
    all_results.sort(key=lambda x: x.get('sort_key', 0), reverse=True)
    
    # Apply pagination
    paginated_results = all_results[offset:offset + per_page]
    
    # Remove sort_key from final results
    final_results = []
    for result in paginated_results:
        final_results.append({
            'id': result['id'],
            'text': result['text']
        })
    
    # Check if there are more results
    has_more = len(all_results) > offset + per_page
    
    return JsonResponse({
        'results': final_results,
        'pagination': {'more': has_more}
    })


@login_or_hubspot_required
def get_association_label_members(request):
    """
    HTMX endpoint to load association label members with pagination
    Similar to get_related_objects_by_property_value but for association labels
    """
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    
    # Get parameters
    association_label_id = request.GET.get("association_label_id")
    object_source = request.GET.get("object_source")
    per_page = int(request.GET.get("per_page", 10))
    page = int(request.GET.get("page", 1))
    
    if not association_label_id or not object_source:
        return HttpResponse("Missing association_label_id or object_source", status=400)
    
    try:
        # Get the association label
        association_label = AssociationLabel.objects.get(
            id=association_label_id, workspace=workspace, object_source=object_source
        )
        
        # Get all association implementations for this label using the same approach as get_association_count
        association_implementations = AssociationLabelObject.objects.filter(
            workspace=workspace,
            label=association_label
        ).select_related('source_content_type', 'target_content_type')
        
        # Convert to simpler format - each association implementation is one relationship
        associations = []
        for impl in association_implementations:
            associations.append({
                'source_object': impl.source_object,
                'target_object': impl.target_object,
                'source_content_type': impl.source_content_type,
                'target_content_type': impl.target_content_type,
            })
        
        unique_members = associations
        
        # Pagination
        paginator = Paginator(unique_members, per_page)
        page_objects = paginator.get_page(page)
        
        # Calculate pagination info
        paginator_item_begin = (per_page * page) - (per_page - 1) if paginator.count > 0 else 0
        paginator_item_end = min(per_page * page, paginator.count)
        start_page = max(1, page - 2)
        end_page = min(paginator.num_pages, page + 2)
        
        # Add missing context variables that the template expects
        context = {
            "association_label": association_label,
            "page_objects": page_objects,
            "pagination": paginator,
            "paginator_item_begin": paginator_item_begin,
            "paginator_item_end": paginator_item_end,
            "current_page": page,
            "per_page": per_page,
            "object_source": object_source,
            "start_page": start_page,
            "end_page": end_page,
            # Add missing variables that template expects
            "view_id": request.GET.get("view_id", ""),
            "menu_key": request.GET.get("menu_key", ""),
            "module": request.GET.get("module", ""),
            "set_id": request.GET.get("set_id", ""),
            "from": request.GET.get("from", ""),
            "p_id": request.GET.get("p_id", ""),
        }
        
        return render(
            request, "data/association/association-labels/association-label-members.html", context
        )
        
    except AssociationLabel.DoesNotExist:
        return HttpResponse("Association label not found", status=404)
    except Exception as e:
        print(f"Error getting association label members: {e}")
        return HttpResponse("Internal server error", status=500)


def get_association_label_list(workspace, object_type):
    """
    Helper function to collect association labels for a given workspace and object type.
    Returns a list containing both direct association labels and related association labels.
    
    Args:
        workspace: The workspace to filter by
        object_type: The object type to get association labels for
        
    Returns:
        list: Combined list of association labels (string labels for Sanka-created, 
              string IDs for non-Sanka-created labels)
    """
    try:
        # Get association labels created by Sanka (use label string)
        association_labels_sanka_true = AssociationLabel.objects.filter(
            workspace=workspace, object_source=object_type, created_by_sanka=True
        )
        association_label_list_sanka_true = [
            association_label.label for association_label in association_labels_sanka_true
        ]
        
        # Get association labels not created by Sanka (use ID string)
        association_labels_sanka_false = AssociationLabel.objects.filter(
            workspace=workspace, object_source=object_type, created_by_sanka=False
        )
        association_label_list_sanka_false = [
            str(association_label.id)
            for association_label in association_labels_sanka_false
        ]
        
        # Combine both lists
        association_label_list = (
            association_label_list_sanka_true + association_label_list_sanka_false
        )
        
        # Get related association labels (where object_type appears in object_target)
        related_association_labels = AssociationLabel.objects.filter(
            created_by_sanka=False,
            workspace=workspace,
            object_target__icontains=object_type,
        ).order_by("created_at")
        
        # Add related association label IDs to the list
        for related_association_label in related_association_labels:
            association_label_list.append(str(related_association_label.id))

        return association_label_list
    except Exception as e:
        print(f"Error getting association labels for {object_type}: {e}")
        return []