{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}


<div class="">

    <div class="" id="main-content" style="padding: 0;">

        <div id="change-stamp-section" class="mb-10">
            <div class="">
                <form 
                    action="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}"
                    method="POST"
                    >
                    {% csrf_token %}

                    <div class="task_wizard">
                        <div class="mb-5">
                            <label class="{% include 'data/utility/form-label.html' %}">
                                <span>
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    連携サービス
                                    {% else %}
                                    Integrations
                                    {% endif %}
    
                                </span>
                            </label>
                            <select required class="{% include 'data/utility/select-form.html' %} select2-this" data-control="select2" data-hide-search="false" 
                                id="channel-select"
                                name="select_integration_ids" placeholder="{% if LANGUAGE_CODE == 'ja' %}担当者{% else %}Assignee{% endif %}">
                                {% for channel in channels %}
                                    <option value="{{ channel.id }}" data-checkbox="{% if channel.following %}{{ channel.following }}{% else %}{{ channel.ms_refresh_token }}{% endif %}" data-platform="{{ channel.integration.slug }}">
                                        {{ channel.name|cut:" Power Order" }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="mb-8">
                        {% for order_id in order_ids %}
                            <input type="hidden" name="order_ids" value="{{order_id}}">
                        {% endfor %}
                    </div>
                    
                    <div id="default-customer-toggle-container" class="mb-6">
                        <input id="default-customer-toggle" class="form-check-input mb-2" type="checkbox"
                        onchange="onClickToggleDefaultCustomer(this);">
                        <label class="form-check-label fw-semibold text-gray-700 ms-3" for="default-customer-toggle">
                            {% if LANGUAGE_CODE == 'ja' %}
                            デフォルトの顧客を使用
                            {% else %}
                            Default Customer
                            {% endif %}
                        </label>
                        <div id="default-customer-toggle-content" class="d-none">
                            <select id='select-contact_and_company' name="default-customer" class="bg-white form-select form-select-solid border h-40px" 
                                {% if LANGUAGE_CODE == 'ja'%}
                                data-placeholder="顧客を選択"
                                {% else %}
                                data-placeholder="Select Customer"
                                {% endif %}
                                data-allow-clear="true"
                                >
                                {% if contact_preselected %}
                                    <option value="{{contact_preselected.id}}" selected>{{contact_preselected|display_contact_name:LANGUAGE_CODE}}</option>
                                {% endif %}

                                {% if company_preselected %}
                                    <option value="{{company_preselected.id}}" selected>{{company_preselected.name}}</option>
                                {% endif %}
                            </select>
                        </div>

                        <script>
                            function onClickToggleDefaultCustomer(checkbox) {
                                const selectElement = document.getElementById('default-customer-toggle-content');
                                if (checkbox.checked) {
                                    selectElement.classList.remove('d-none');
                                } else {
                                    selectElement.classList.add('d-none');
                                }
                            }
                            
                            $(document).ready(function () {
                                var lazyLoadSelectorMapUrl = {
                                    '#select-contact_and_company': '{% host_url "get_customer_options" host "app" %}'
                                }

                                Object.keys(lazyLoadSelectorMapUrl).forEach(selector => {
                                    $(`${selector}`).select2({
                                        ajax: {
                                            delay: 250, // wait 250 milliseconds before triggering the request
                                            dataType: 'json',
                                            url: lazyLoadSelectorMapUrl[selector],
                                            data: function (params) {
                                                    var query = {
                                                        q: params.term,
                                                        page: params.page || 1,
                                                        json_response: true
                                                    }
                                                    return query;
                                                },
                                            minimumInputLength: 2,
                                        }
                                    }).on('select2:select', function (e){
                                        var innerTextList = [];
                                        // Loop through each list item to get the innerText and add it to the array
                                        $(`#select2-${selector.replace('#', '').replace('.', '')}-container li`).each(function() {
                                            var innerText = $(this).text().trim(); // Use trim() to remove any extra spaces
                                            innerTextList.push(innerText);
                                        });
                            
                                        if(e.params.data.text.endsWith('(Company)')|| e.params.data.text.endsWith('(企業)')){
                                            var currentValues = $(this).val();
                                            var currentObj = $(this).text().trim();
                                            innerTextList.forEach(function(innerText, index) {
                                                if (innerText.includes('(Company)') || innerText.includes('(企業)')) {
                                                    currentValues.splice(index, 1);
                                                } 
                                            });
                            
                                            currentValues.push(e.params.data.id);
                                            newValues = currentValues 
                            
                                            
                                            $(this).val(newValues).trigger('change');
                                            
                                        } else if(e.params.data.text.endsWith('(Contact)')|| e.params.data.text.endsWith('(連絡先)')){
                                            var currentValues = $(this).val();
                                            var currentObj = $(this).text().trim();
                                            innerTextList.forEach(function(innerText, index) {
                                                if (innerText.includes('(Contact)') || innerText.includes('(連絡先)')) {
                                                    currentValues.splice(index, 1);
                                                } 
                                            });
                            
                                            currentValues.push(e.params.data.id);
                                            newValues = currentValues 
                            
                                            $(this).val(newValues).trigger('change');
                                            
                                        } 
                                        
                            
                                    })
                                })
                            });
                        </script>
                    </div>

                    <div id="saveMappingMessage" class="text-success d-none">
                        {% if LANGUAGE_CODE == 'ja' %}マッピング情報が正常に保存されました。{% else %}Mapping information has been saved successfully.{% endif %}
                    </div>

                    <div id="mapping-contact-field" class="d-none">
                        {% csrf_token %}
                        <div class="mb-6 form-check form-switch form-check-custom form-check-solid d-none ">
                            <input id="contact-checker" name="item-checker" class="form-check-input" type="checkbox"
                                
                                hx-post="{% host_url 'sync_order_header_extractor' host 'app' %}"
                                hx-target="#csvMappingContainer"
                                hx-include="#channel-select"
                                hx-trigger="load, channelChanged"
                                hx-vals='{"function_type":"{{import_export_type}}"}'
                                hx-encoding="multipart/form-data"
                                onchange="contactMappingHandler(this)"

                                hx-on::before-send="
                                    document.getElementById('csvMappingContainer').classList.add('d-none');
                                    document.getElementById('csvMappingContainer').innerHTML = '';
                                    document.getElementById('loading-spinner').classList.remove('d-none');"
                                
                            >
                        </div>

                        <div> 
                            <a type='button' class="d-none cursor-pointer fs-8 create-property-table-mapping-wizard create-property-elm-order"
                            
                                hx-get="{% host_url 'new_property' host 'app' %}" 
                                hx-target="#create-property-table-mapping-content"
                                hx-vals='js:{"page_group_type":"commerce_orders","from-table-mapping":"True"}'
                                hx-trigger='click'
                            
                            ></a>
                        </div>

                        <div id="loading-spinner" class="text-center d-none">
                            <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                            </div>
                            <div>
                            {% if LANGUAGE_CODE == 'ja' %}
                            プロパティをロード中...
                            {% else %}
                            Loading Properties...
                            {% endif %}
                            </div>
                        </div>
                        <div id="csvMappingContainer" class="mb-3 mb-10 mt-8"></div>
                    </div> 

                    <div id="mapping-association-field" class="d-none">
                        {% csrf_token %}
                        <div class="mb-6 form-check form-switch form-check-custom form-check-solid d-none ">
                            <input id="assoc-checker" name="item-checker" class="form-check-input" type="checkbox"
                                
                                hx-post="{% host_url 'sync_order_association_header_extractor' host 'app' %}"
                                hx-target="#associationMappingContainer"
                                hx-include="#channel-select"
                                hx-trigger="load, channelChanged"
                                hx-vals='{"function_type":"{{import_export_type}}"}'
                                hx-encoding="multipart/form-data"
                                onchange="contactMappingHandler(this)"

                                hx-on::before-send="
                                    document.getElementById('associationMappingContainer').classList.add('d-none');
                                    document.getElementById('associationMappingContainer').innerHTML = '';
                                    document.getElementById('loading-spinner-2').classList.remove('d-none');"
                                
                            >
                        </div>
                        <div id="loading-spinner-2" class="text-center d-none mt-3">
                            <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                            </div>
                            <div>
                            {% if LANGUAGE_CODE == 'ja' %}
                            受注関連プロパティをロード中...
                            {% else %}
                            Loading Association Properties...
                            {% endif %}
                            </div>
                        </div>
                        <div id="associationMappingContainer" class="mb-3 mb-10 mt-8"></div>
                    </div> 

                    <div id="mapping-order-status-field" class="d-none">
                        {% csrf_token %}
                        <div class="mb-6 form-check form-switch form-check-custom form-check-solid d-none ">
                            <input id="status-checker" name="item-checker" class="form-check-input" type="checkbox"
                                
                                hx-post="{% host_url 'sync_order_status_header_extractor' host 'app' %}"
                                hx-target="#statusMappingContainer"
                                hx-include="#channel-select"
                                hx-trigger="load, channelChanged"
                                hx-vals='{"function_type":"{{import_export_type}}"}'
                                hx-encoding="multipart/form-data"
                                onchange="contactMappingHandler(this)"

                                hx-on::before-send="
                                    document.getElementById('statusMappingContainer').classList.add('d-none');
                                    document.getElementById('loading-spinner-status').classList.remove('d-none');"
                                
                            >
                        </div>

                        <div id="loading-spinner-status" class="text-center d-none mt-8">
                            <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                            </div>
                            <div>
                            {% if LANGUAGE_CODE == 'ja' %}
                            受注ステータスプロパティをロード中...
                            {% else %}
                            Loading Order Status Properties...
                            {% endif %}
                            </div>
                        </div>

                        <div id="statusMappingContainer" class="mb-3 mb-10 mt-8"></div>
                    </div> 
                    
                    <div id="import-filters-field" class="d-none mb-5">
                        <div class="fs-4 fw-bolder mt-5">
                            {% if LANGUAGE_CODE == 'ja' %}フィルター{% else %}Filters{% endif %}
                        </div>
                        <select id="import-data-filter" class="bg-white border min-h-40px form-select form-select-solid w-100 select2-this select2-this-filter"
                            hx-get="{% host_url 'sync_order_import_filter_selector' host 'app' %}"
                            hx-include="#channel-select"
                            hx-target="#import-data-selector-content"
                            hx-trigger="htmx-change"
                            name="data_filter"
                            hx-swap="beforeend" 
                            {% if LANGUAGE_CODE == 'ja'%}
                            data-placeholder="フィルターを選択"
                            {% else %}
                            data-placeholder="Select Filter"
                            {% endif %}
                            onchange="onchangeFilterSelect(this)"
                            >
                            <option></option>
                            <option value='created_at'>{% if LANGUAGE_CODE == 'ja' %}作成日時{% else %}Created At{% endif %}</option>
                            <option value='updated_at'>{% if LANGUAGE_CODE == 'ja' %}更新日時{% else %}Updated At{% endif %}</option>
                        </select>

                        <div class="w-100">
                            <div id="import-data-selector-content" class="mt-3">
                            </div>
                        </div> 

                        <script>
                            function onchangeFilterSelect(element) {
                                htmx.trigger(element, 'htmx-change');
                            }
                        </script>
                    </div>

                    <div id="hubspot-import-platform-field" class="d-none mb-5">
                        <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                            <span>
                                {% if LANGUAGE_CODE == 'ja'%}受注レコードのインポート方法を選択{% else %}Choose How to Import Order Records{% endif %}
                            </span>
                        </label>

                        <select id="how-to-import-hubspot" name="how-to-import-hubspot" class="how-to-import bg-white form-select form-select-solid border h-40px select2-this" 
                            {% if LANGUAGE_CODE == 'ja'%}
                            data-placeholder="インポート方法の選択"
                            {% else %}
                            data-placeholder="Select Import Method"
                            {% endif %}
                            data-allow-clear="true">
                            <option value="create">
                                {% if LANGUAGE_CODE == 'ja'%}
                                作成のみ
                                {% else %}
                                Create Only
                                {% endif %}
                            </option>
                            <option value="update">
                                {% if LANGUAGE_CODE == 'ja'%}
                                更新のみ
                                {% else %}
                                Update Only
                                {% endif %}
                            </option>
                            <option value="match">
                                {% if LANGUAGE_CODE == 'ja'%}
                                作成と更新
                                {% else %}
                                Create and Update
                                {% endif %}
                            </option>
                            <option value="skip">
                                {% if LANGUAGE_CODE == 'ja'%}
                                スキップ
                                {% else %}
                                Skip
                                {% endif %}
                            </option>
                        </select>
                    </div>

                    <div id="sync-filters-field" class="mb-10 mt-8 d-none">
                        <div class="mb-3">
                            <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                                <span>
                                    {% if LANGUAGE_CODE == 'ja'%}
                                        フィルター
                                    {% else %}
                                        Filters
                                    {% endif %}
                                </span>
                            </label>
                            <select id="filter-select" name="filter" class="{% include 'data/utility/select-form.html' %} select2-this" data-control="select2" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}フィルターを選択{% else %}Select Filters{% endif %}" data-allow-clear="true">
                                <option value=""></option>
                                {% for filter in filters %}
                                    <option value="{{filter.id}}">
                                        {% if filter.name %}
                                            {{filter.name}}
                                        {% else %}
                                            {{filter.id}}
                                        {% endif %}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div id="filter-choice-field" class="mb-3 d-none">
                            <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                                <span>
                                    {% if LANGUAGE_CODE == 'ja'%}
                                        フィルターの選択肢
                                    {% else %}
                                        Filter Choice
                                    {% endif %}
                                </span>
                            </label>
                            <select id="filter-choice" name="filter-choice" class="{% include 'data/utility/select-form.html' %} select2-this" data-control="select2" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}フィルターの選択肢を選択{% else %}Select Filter Choice{% endif %}" data-hide-search="true" data-allow-clear="true">
                            </select>
                        </div>
                    </div>

                    <span {% comment %}  # Filter Property {% endcomment %}
                    id="integration-filter"
                        hx-get="{% host_url 'shopturbo_load_drawer' host 'app' %}"
                        hx-target="#integration-filter-section"
                        hx-trigger="channelChanged" 
                        hx-vals='{"drawer_type":"shopturbo-integration-settings","type":"integration-filter","integration_filter":"get_filter","import_export_type":"{{import_export_type}}"}'>
                    </span>
                    <div id="integration-filter-section" class="mb-10 mt-8 d-none">
                    </div>

                    <div id="nextengine-hubspot-sync-filters-field" class="mb-5 mt-8 d-none">
                        <div class="mb-3">
                            <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                                <span>
                                    {% if LANGUAGE_CODE == 'ja'%}
                                        Hubspot を使用して Nextengine フィルターを実行する
                                    {% else %}
                                        Hubspot to Nextengine Filter
                                    {% endif %}
                                </span>
                            </label>
                            <select id="nextengine-hubspot-filter-select" name="nextengine-hubspot-filter" class="{% include 'data/utility/select-form.html' %} select2-this" data-control="select2" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}フィルターを選択{% else %}Select Filters{% endif %}" data-allow-clear="true">
                                <option value="false">{% if LANGUAGE_CODE == 'ja'%}いいえ{% else %}No{% endif %}</option>
                                <option value="true">{% if LANGUAGE_CODE == 'ja'%}はい{% else %}Yes{% endif %}</option>
                            </select>
                        </div>
                    </div>

                    <div id="additional-action-field" class="mb-5 d-none">
                        <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                            <span>
                                {% if LANGUAGE_CODE == 'ja'%}
                                    追加のエクスポートアクション
                                {% else %}
                                    Additional Export Action
                                {% endif %}
                            </span>
                        </label>
                        <select id="additional-action-channel-choice" name="additional-action-channel-choice" class="{% include 'data/utility/select-form.html' %} select2-this" data-control="select2" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}統合の選択{% else %}Select Integration{% endif %}" data-hide-search="true" data-allow-clear="true">
                            {% for channel in additional_action_channels %} 
                                <option value="{{ channel.id }}">{{ channel.name }}</option>
                            {% endfor %}
                        </select>

                        <input 
                            type="text"
                            id="filter-output" 
                            name="filter-output" 
                            class="form-control mt-3" 
                            placeholder="{% if LANGUAGE_CODE == 'ja' %}変換される取引ステージ{% else %}Deal Stage to be Converted to{% endif %}" data-hide-search="true" data-allow-clear="true">

                    </div>

                    <div id="how-to-import-container" class="fv-row d-flex flex-column mb-6 d-none">    
                        <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                            <span class="">
                                {% if LANGUAGE_CODE == 'ja'%}商品のインポート方法を選択{% else %}Choose How to Import Item{% endif %}
                            </span>
                        </label>
                        
                        <select id="how-to-import" name="how-to-import" class="how-to-import bg-white form-select form-select-solid border h-40px select2-this" 
                            {% if LANGUAGE_CODE == 'ja'%}
                            data-placeholder="インポートデータの種類を選択"
                            {% else %}
                            data-placeholder="Select Import Data Type"
                            {% endif %}
                            data-allow-clear="true">
                            {# Select optuon was moved to import_item_option_constant.py #}
                        </select>
                    </div>
                    
                    <div id="update-options" class="fv-row d-flex flex-column mb-7 d-none">
                        <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                            <span class="required">
                                {% if LANGUAGE_CODE == 'ja'%}マッチする商品プロパティを選択 (ユニークプロパティ){% else %}Choose Item Property to Match (Unique Property){% endif %}
                            </span>
                        </label>
                    
                        <select id="update-key-select" name="update-options" class="update-options bg-white form-select form-select-solid border h-40px select2-this" 
                            {% if LANGUAGE_CODE == 'ja'%}
                            data-placeholder="キープロパティを選択"
                            {% else %}
                            data-placeholder="Select Key Property"
                            {% endif %}
                            data-allow-clear="true"
                            >
                            <option value="" selected disabled>
                                {% if LANGUAGE_CODE == 'ja' %}
                                    キープロパティを選択
                                {% else %}
                                    Select Key Property
                                {% endif %}
                            </option>
                            {% for column in columns %}
                                <option value='{{column}}'>
                                    {% if not column|is_uuid %}
                                        {% with args=column|add:'|'|add:'commerce_items' %} 
                                            {% with column_display=args|get_column_display:request %}
                                                {{column_display.name}}
                                            {% endwith %}
                                        {% endwith %}
                                    {% else %}
                                        {% with custom_column=column|search_custom_field_object_items:request %}
                                            {{custom_column.name}}
                                        {% endwith %}
                                    {% endif %}
                                </option>   
                            {% endfor %}  
                        </select>
                    </div>

                    <div id="sync-method-field" class="d-none mb-5">
                        <div class="mb-6">
                            <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                                <span>
                                    {% if LANGUAGE_CODE == 'ja'%}
                                        商品の同期方法
                                    {% else %}
                                        Item Sync Method
                                    {% endif %}
                                </span>
                            </label>
                            <select name="sync-method" class="{% include 'data/utility/select-form.html' %} select2-this" data-control="select2" data-hide-search="true">
                                <option value="sku" selected>
                                    {% if LANGUAGE_CODE == 'ja' %}
                                    在庫SKU
                                    {% else %}
                                    Inventory SKU
                                    {% endif %}
                                </option>
                                <option value="platform">
                                    {% if LANGUAGE_CODE == 'ja' %}
                                    プラットフォームID
                                    {% else %}
                                    Platform ID 
                                    {% endif %}
                                </option>
                            </select>
                        </div>
                    </div>

                    <div id="how-to-import-customer-container" class="fv-row d-flex flex-column mb-6 d-none">    
                        <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                            <span class="">
                                {% if LANGUAGE_CODE == 'ja'%}顧客のインポート方法を選択{% else %}Choose How to Import Customer{% endif %}
                            </span>
                        </label>
                        
                        <select id="how-to-import-customer" name="how-to-import-customer" class="how-to-import-customer bg-white form-select form-select-solid border h-40px select2-this" 
                            {% if LANGUAGE_CODE == 'ja'%}
                            data-placeholder="インポートデータの種類を選択"
                            {% else %}
                            data-placeholder="Select Import Data Type"
                            {% endif %}
                            data-allow-clear="true">
                            <option value="create" id="create-customer-option">
                                {% if LANGUAGE_CODE == 'ja'%}
                                顧客の更新または作成
                                {% else %}
                                Update or Create Customers
                                {% endif %}
                            </option>
                            <option value="update">
                                {% if LANGUAGE_CODE == 'ja'%}
                                顧客の更新または作成 (カスタムプロパティ)
                                {% else %}
                                Update or Create Customers (Custom Property)
                                {% endif %}
                            </option>
                            <option value="match">
                                {% if LANGUAGE_CODE == 'ja'%}
                                顧客の一致または作成 (カスタムプロパティ)
                                {% else %}
                                Match or Create (Custom Property)
                                {% endif %}
                            </option>
                        </select>
                    </div>
                    
                    <div id="update-options-customer" class="fv-row d-flex flex-column mb-7 d-none">
                        <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                            <span class="required">
                                {% if LANGUAGE_CODE == 'ja'%}マッチする顧客プロパティを選択{% else %}Choose Customer Property to Match{% endif %}
                            </span>
                        </label>
                    
                        <select id="update-customer-key-select" name="update-options-customer" class="update-options-customer bg-white form-select form-select-solid border h-40px select2-this" 
                            {% if LANGUAGE_CODE == 'ja'%}
                            data-placeholder="キープロパティを選択"
                            {% else %}
                            data-placeholder="Select Key Property"
                            {% endif %}
                            data-allow-clear="true"
                            >
                            <option value="" selected disabled>
                                {% if LANGUAGE_CODE == 'ja' %}
                                    キープロパティを選択
                                {% else %}
                                    Select Key Property
                                {% endif %}
                            </option>
                            {% for column in customer_columns %}
                                <option value='{{column}}'>
                                    {% if not column|is_uuid %}
                                        {% with args=column|add:'|'|add:'company' %} 
                                            {% with column_display=args|get_column_display:request %}
                                                {{column_display.name}}
                                            {% endwith %}
                                        {% endwith %}
                                    {% else %}
                                        {% with custom_column=column|search_custom_field_object_company:request %}
                                            {{custom_column.name}}
                                        {% endwith %}
                                    {% endif %}
                                </option>   
                            {% endfor %}  
                        </select>
                    </div>
                    
                    <script>
                        $('#how-to-import').on('change', function() {
                            const updateOptions = document.getElementById('update-options');
                            const keySelect = document.getElementById('update-key-select');
                            if (this.value === 'update' || this.value === 'match' || this.value === 'update|sku' || this.value === 'match|sku') {
                                updateOptions.classList.remove('d-none');
                                keySelect.required = true;
                            } else {
                                updateOptions.classList.add('d-none');
                                keySelect.required = false;
                            }
                        });
                        $('#how-to-import-customer').on('change', function() {
                            const updateOptionsCustomer = document.getElementById('update-options-customer');
                            const keySelectCustomer = document.getElementById('update-customer-key-select');
                            if (this.value === 'update' || this.value === 'match') {
                                updateOptionsCustomer.classList.remove('d-none');
                                keySelectCustomer.required = true;
                                if (!keySelectCustomer.value) {
                                    keySelectCustomer.selectedIndex = 0; // Automatically select the first option
                                }
                            } else {
                                updateOptionsCustomer.classList.add('d-none');
                                keySelectCustomer.required = false;
                            }
                        });
                    </script>                    

                    <div id="set-contact-as-company-field" class="form-check mb-5 d-none">
                        <input class="form-check-input" type="checkbox" id="set-contact-as-company" name="set-contact-as-company" value=1>
                        <label class="form-check-label ms-1 fs-5 text-gray-700" for="set-contact-as-company" >
                            {% if LANGUAGE_CODE == 'ja' %}会社を優先的に顧客として登録{% else %}Prioritize Company for Customer{% endif %}
                        </label>
                    </div>

                    <div id="sync-progress-message" class="mb-3 text-danger d-none">
                        {% if LANGUAGE_CODE == 'ja'%}
                            同期中です。しばらくお待ちください。
                        {% else %}
                            Sync in progress. Please wait a moment.
                        {% endif %}
                    </div>

                    <div class="d-flex ">
                        <button id="import-btn" type="submit" class="btn btn-primary d-none" name="import_orders">
                            <span class="svg-icon svg-icon-2 svg-icon-white">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-cloud-download" viewBox="0 0 16 16">
                                    <path d="M4.406 1.342A5.53 5.53 0 0 1 8 0c2.69 0 4.923 2 5.166 4.579C14.758 4.804 16 6.137 16 7.773 16 9.569 14.502 11 12.687 11H10a.5.5 0 0 1 0-1h2.688C13.979 10 15 8.988 15 7.773c0-1.216-1.02-2.228-2.313-2.228h-.5v-.5C12.188 2.825 10.328 1 8 1a4.53 4.53 0 0 0-2.941 1.1c-.757.652-1.153 1.438-1.153 2.055v.448l-.445.049C2.064 4.805 1 5.952 1 7.318 1 8.785 2.23 10 3.781 10H6a.5.5 0 0 1 0 1H3.781C1.708 11 0 9.366 0 7.318c0-1.763 1.266-3.223 2.942-3.593.143-.863.698-1.723 1.464-2.383"/>
                                    <path d="M7.646 15.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 14.293V5.5a.5.5 0 0 0-1 0v8.793l-2.146-2.147a.5.5 0 0 0-.708.708z"/>
                                </svg>
                            </span>
                            {% if LANGUAGE_CODE == 'ja'%}
                            受注レコードをインポート
                            {% else %}
                            Import Orders
                            {% endif %}
                        </button>

                        <button id="export-btn" type="submit" class="btn btn-primary d-none" name="export_orders">
                            <span class="svg-icon svg-icon-2 svg-icon-white">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-cloud-upload" viewBox="0 0 16 16">
                                    <path fill-rule="evenodd" d="M4.406 1.342A5.53 5.53 0 0 1 8 0c2.69 0 4.923 2 5.166 4.579C14.758 4.804 16 6.137 16 7.773 16 9.569 14.502 11 12.687 11H10a.5.5 0 0 1 0-1h2.688C13.979 10 15 8.988 15 7.773c0-1.216-1.02-2.228-2.313-2.228h-.5v-.5C12.188 2.825 10.328 1 8 1a4.53 4.53 0 0 0-2.941 1.1c-.757.652-1.153 1.438-1.153 2.055v.448l-.445.049C2.064 4.805 1 5.952 1 7.318 1 8.785 2.23 10 3.781 10H6a.5.5 0 0 1 0 1H3.781C1.708 11 0 9.366 0 7.318c0-1.763 1.266-3.223 2.942-3.593.143-.863.698-1.723 1.464-2.383"/>
                                    <path fill-rule="evenodd" d="M7.646 4.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 5.707V14.5a.5.5 0 0 1-1 0V5.707L5.354 7.854a.5.5 0 1 1-.708-.708z"/>
                                </svg>
                            </span>
                            {% if LANGUAGE_CODE == 'ja'%}
                            受注レコードをエクスポート
                            {% else %}
                            Export Orders
                            {% endif %}
                        </button>

                        <button type="button" class="btn btn-light-primary ms-2" name="save_mapping"
                                id="saveMappingButton"
                                onclick="contactCheckerChannelChanged(); showSaveMessage()">
                            {% if LANGUAGE_CODE == 'ja'%}
                            マッピングの保存
                            {% else %}
                            Save Mapping
                            {% endif %}
                        </button>
                        <div id="returnResultSaveMapping"></div>
                        <button type="button" class="btn btn-light-primary ms-2 d-none"
                            hx-indicator="#mappingLoadingIndicator"
                            hx-trigger="htmx-change"
                            hx-target="#returnResultSaveMapping"
                            hx-post="{% host_url 'shopturbo' host 'app' %}"
                            hx-vals='{"save_mapping":"", "object_type": "{{object_type}}", "header_list":"{{header_list}}", "channel_id":"{{channel_id}}"}'
                            hx-on="htmx:afterRequest:contactCheckerChannelChanged();htmx:afterRequest:showSaveMessage()"
                            id="saveImportAndDefaultCustomerButton"
                        >
                        </button>

                        <div id="mappingLoadingIndicator" class="d-none">
                            {% if LANGUAGE_CODE == 'ja' %}保存中...{% else %}Saving...{% endif %}
                        </div>

                        <script>
                            $(document).ready(function() {
                                var saveMappingBtn = document.getElementById('saveMappingButton');
                                if (saveMappingBtn) {
                                    saveMappingBtn.addEventListener('click', function() {
                                        // container
                                        var saveImportAndDefaultCustomerButton = document.getElementById('saveImportAndDefaultCustomerButton');
                                        var howToImportContainer = document.getElementById('how-to-import-container');
                                        var howToImportCustomerContainer = document.getElementById('how-to-import-customer-container');
                                        var hubspotImportPlatformField = document.getElementById('hubspot-import-platform-field');
                                        var updateOptionsContainer = document.getElementById('update-options');
                                        var updateOptionsCustomerContainer = document.getElementById('update-options-customer');
                                        var defaultCustomerToggleContainer = document.getElementById('default-customer-toggle-container');
                                        var syncMethodField = document.getElementById('sync-method-field');

                                        // save import method and default customer
                                        var importMethod = document.getElementById('how-to-import').value;
                                        var importCustomerMethod = document.getElementById('how-to-import-customer').value;
                                        var importHubspotPlatform = document.getElementById('how-to-import-hubspot').value;
                                        var updateOptionsCustomerKey = document.getElementById('update-customer-key-select').value;
                                        var updateOptions = document.getElementById('update-key-select').value;
                                        var syncMethod = document.querySelector('[name="sync-method"]').value;

                                        var currentHxVals = saveImportAndDefaultCustomerButton.getAttribute('hx-vals');
                                        var currentHxValsObject = JSON.parse(currentHxVals);
                                        
                                        var defaultCustomerToggle = document.getElementById('default-customer-toggle');
                                        if (defaultCustomerToggle.checked && !defaultCustomerToggleContainer.classList.contains('d-none')) {
                                            var default_customer_selector = document.querySelector("[name='default-customer']");
                                            var selectedOption = default_customer_selector.options[default_customer_selector.selectedIndex];
                                            var default_customer_text = selectedOption ? selectedOption.text : null;
                                            var default_customer_value = selectedOption ? selectedOption.value : null;
                                            currentHxValsObject['default-customer-id'] = default_customer_value;
                                            currentHxValsObject['default-customer-text'] = default_customer_text;
                                        }
                                        if (!howToImportContainer.classList.contains('d-none')) {
                                            currentHxValsObject['how-to-import'] = importMethod
                                            if (!updateOptionsContainer.classList.contains('d-none')) {
                                                currentHxValsObject['update-options'] = updateOptions
                                            }
                                        }
                                        if (!howToImportCustomerContainer.classList.contains('d-none')) {
                                            currentHxValsObject['how-to-import-customer'] = importCustomerMethod
                                            if(!updateOptionsCustomerContainer.classList.contains('d-none')) {
                                                currentHxValsObject['update-options-customer'] = updateOptionsCustomerKey
                                            }
                                        }
                                        if (!hubspotImportPlatformField.classList.contains('d-none')) {
                                            currentHxValsObject['how-to-import-hubspot'] = importHubspotPlatform
                                        }
                                        var channelSelect = document.getElementById('channel-select');
                                        if (channelSelect) {
                                            currentHxValsObject['channel-select-platform'] = channelSelect.options[channelSelect.selectedIndex].getAttribute('data-platform')
                                        }
                                        saveImportAndDefaultCustomerButton.setAttribute('hx-vals', JSON.stringify(currentHxValsObject));
                                        htmx.trigger(saveImportAndDefaultCustomerButton, 'htmx-change');
                                    })
                                }

                            });
                        </script>
                    </div>

                    <style>
                        #task-field-indicator{
                            display:none;
                        }
                        .htmx-request#task-field-indicator{
                            display:inline-block;
                        }
                    </style>
                    <span class="spinner-border spinner-border-sm text-secondary task-field-indicator" id="task-field-indicator" style="position:relative; right:-6px" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </span>
                    
                </form>
            </div>

        </div>
    </div>
    <div class="card-body d-none" id="history-content">
        {% for item in history %}
        <div class="card mb-3 p-0">
            <div class="card-body">
                <h6 class="card-title mb-5 fw-bold text-black-1200">{{item.name}}</h6>

                {% if LANGUAGE_CODE == 'ja'%}
                    <h6 class="{% include "data/utility/card-header.html" %}">ステータス:</h6>
                {% else %}
                    <h6 class="{% include "data/utility/card-header.html" %}">Status:</h6>
                {% endif %}
                <p class="card-text">{{ task_status|get_item:item.status }}</p>
                
                {% if LANGUAGE_CODE == 'ja'%}
                    <h6 class="{% include "data/utility/card-header.html" %}">エラー:</h6>
                {% else %}
                    <h6 class="{% include "data/utility/card-header.html" %}">Created at:</h6>
                {% endif %}
                <p class="card-text">{{ item.created_at }}</p>

                {% if LANGUAGE_CODE == 'ja'%}
                    <h6 class="{% include "data/utility/card-header.html" %}">開始:</h6>
                {% else %}
                    <h6 class="{% include "data/utility/card-header.html" %}">Progress</h6>
                {% endif %}
                <p class="card-text">{{ item.progress }}%</p>

                {% if forloop.first %}
                <form method="post" class="d-inline">
                    {% csrf_token %}
                    <input type="hidden" name="task_id" value="{{ item.id }}">
                    <input type="hidden" name="task_status" value="canceled">
                    
                    {% if item.status == 'running' %}
                        <button type="submit" class="btn btn-danger btn-sm" name="cancel_task">
                            {% if LANGUAGE_CODE == 'ja' %}
                            キャンセル
                            {% else %}
                            Cancel
                            {% endif %}
                        </button>
                    {% endif %}
                </form>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<script>
    $('.select2-this').select2();
    function showFilename(elm){
        path = elm.value.replace(/^.*[\\\/]/, '')
        elm.parentElement.parentElement.querySelector('.filename').innerHTML = path
    }
    $(document).ready(function() {
        toggleInputSendTo();

        var selectElement = $('#channel-select')
        var platform = selectElement.find('option:selected').data('platform')
        var object_type = "{{object_type}}"
        getImportOrderConfig(platform, object_type)

        // radio = document.querySelector('input[name="switch"]:checked');
        // if (radio) {
        //     document.getElementById('export-btn').classList.toggle('d-none', radio.id !== 'kt_switch_option_3')
        //     document.getElementById('import-btn').classList.toggle('d-none', radio.id !== 'kt_switch_option_1')
        // }

        const import_export_type = '{{import_export_type}}'
        if (import_export_type === 'import') {
            document.getElementById('import-btn').classList.remove('d-none')
        } else {
            document.getElementById('export-btn').classList.remove('d-none')
        }
    });

    $('#channel-select').on('select2:select', function (e) {
        toggleInputSendTo();
        var selectElement = $(this)
        var platform = selectElement.find('option:selected').data('platform')
        var object_type = "{{object_type}}"
        getImportOrderConfig(platform, object_type)
    });

    function getImportOrderConfig(platform, object_type){
        const import_export_type = '{{import_export_type}}'

        var selected_platform = platform
        var default_customer = ""
        var how_to_import = ""
        var how_to_import_hubspot = ""
        var how_to_import_customer = ""
        var key_item_field = ""
        var key_customer_field = ""
        var import_order_date = ""
        var sync_method = ""
        $.ajax({
            url: '{% url "get_import_order_configuration" %}',
            type: 'GET',
            data: {
                platform: platform,
                object_type: object_type,
            },
            success: function(response) {
                default_customer_id = response.default_customer_id
                default_customer_text = response.default_customer_text
                how_to_import = response.how_to_import
                how_to_import_hubspot = response.how_to_import_hubspot
                how_to_import_customer = response.how_to_import_customer
                key_item_field = response.key_item_field
                key_customer_field = response.key_customer_field
                import_order_date = response.import_order_date
                sync_method = response.sync_method
                
                // container
                var howToImportContainer = document.getElementById('how-to-import-container');
                var howToImportCustomerContainer = document.getElementById('how-to-import-customer-container');
                var hubspotImportPlatformField = document.getElementById('hubspot-import-platform-field');
                var updateOptionsContainer = document.getElementById('update-options');
                var updateOptionsCustomerContainer = document.getElementById('update-options-customer');
                var syncMethodField = document.getElementById('sync-method-field');


                // get import method and default customer element
                var importMethod = document.getElementById('how-to-import');
                var importCustomerMethod = document.getElementById('how-to-import-customer');
                var importHubspotPlatform = document.getElementById('how-to-import-hubspot');
                var updateOptionsCustomerKey = document.getElementById('update-customer-key-select');
                var updateOptions = document.getElementById('update-key-select');
                var syncMethod = document.querySelector('[name="sync-method"]');

                // default state
                importMethod.value = 'create';
                importCustomerMethod.value = 'create';
                importHubspotPlatform.value = 'create';
                updateOptionsCustomerKey.value = '';
                updateOptions.value = '';
                syncMethod.value = 'platform';
                
                // set the value of the element dynamically
                if (default_customer_id && default_customer_text && (platform === 'amazon' || platform === 'shopify' || platform === 'rakuten' || platform == 'makeshop')) {
                    var defaultCustomerToggle = document.getElementById('default-customer-toggle');
                    if (!defaultCustomerToggle.checked) {
                        defaultCustomerToggle.click();
                    }  
                    var $defaultCustomerSelector = $("#select-contact_and_company");
                    var newOption = new Option(default_customer_text, default_customer_id, true, true);
                    $defaultCustomerSelector.append(newOption).trigger('change');
                }
                if (how_to_import && import_export_type === 'import'){
                    howToImportContainer.classList.remove('d-none');
                    importMethod.value = how_to_import;
                    importMethod.dispatchEvent(new Event('change'));
                    if (key_item_field && how_to_import !== 'create'){
                        updateOptionsContainer.classList.remove('d-none');
                        updateOptions.value = key_item_field;
                        updateOptions.dispatchEvent(new Event('change'));
                    }
                }
                if (how_to_import_customer && platform === 'b-cart'){
                    howToImportCustomerContainer.classList.remove('d-none');
                    importCustomerMethod.value = how_to_import_customer;
                    importCustomerMethod.dispatchEvent(new Event('change'));
                    if (key_customer_field && how_to_import_customer !== 'create'){
                        updateOptionsCustomerContainer.classList.remove('d-none');
                        updateOptionsCustomerKey.value = key_customer_field;
                        updateOptionsCustomerKey.dispatchEvent(new Event('change'));
                    }
                }
                if (how_to_import_hubspot && platform === 'hubspot' && import_export_type === 'import'){
                    hubspotImportPlatformField.classList.remove('d-none');
                    importHubspotPlatform.value = how_to_import_hubspot;
                    importHubspotPlatform.dispatchEvent(new Event('change'));
                }
                if (sync_method && platform === 'shopify' && import_export_type === 'import'){
                    syncMethodField.classList.remove('d-none');
                    syncMethod.value = sync_method;
                    syncMethod.dispatchEvent(new Event('change'));
                }
            },
            error: function(xhr, status, error) {
                console.log(error)
            }
        })   
    }

    function toggleInputSendTo(){
        let platform = $('#channel-select option:selected').data('platform');
        let checkbox_status = $('#channel-select option:selected').data('checkbox');
        var contact_mapping = document.getElementById("mapping-contact-field");
        var association_mapping = document.getElementById("mapping-association-field");
        var status_mapping = document.getElementById("mapping-order-status-field");
        var syncMethodField = document.getElementById("sync-method-field");
        
        var importFilters = document.getElementById("import-filters-field");
        var syncFilters = document.getElementById("sync-filters-field");
        var contact_as_company = document.getElementById("set-contact-as-company-field");
        var contact_as_company_input = document.getElementById("set-contact-as-company");
        const import_export_type = '{{import_export_type}}'
        var nextengineHubspotFilters = document.getElementById("nextengine-hubspot-sync-filters-field");
        var additionalActionField = document.getElementById("additional-action-field");
        var hubspotImportPlatform = document.getElementById("hubspot-import-platform-field");
        const has_hubspot = '{{has_hubspot}}'
        
        var integrationFilter = document.getElementById("integration-filter-section");
        var integration_filter_handler = document.getElementById('integration-filter')
        var defaultCustomerToggle = document.getElementById("default-customer-toggle-container");

        var HxVals = integration_filter_handler.getAttribute('hx-vals')
        var HxValsJson = JSON.parse(HxVals)
        HxValsJson.platform = platform
        integration_filter_handler.setAttribute( "hx-vals", JSON.stringify(HxValsJson) ); 
        htmx.trigger(`#integration-filter`, "channelChanged")

        if (platform === 'hubspot' && import_export_type === 'import') {
            hubspotImportPlatform.classList.remove('d-none');
        } else {
            hubspotImportPlatform.classList.add('d-none');
        }

        if (platform === 'hubspot' | platform === 'b-cart' | platform === 'makeshop' | platform === 'amazon' | platform === 'shopify' | platform === 'rakuten') {
            contact_as_company.classList.remove('d-none')
        } else {
            contact_as_company.classList.add('d-none')
        }
        
        if (platform === 'nextengine' ) {
            integrationFilter.classList.remove('d-none');
            document.getElementById('update-options').classList.add('d-none');
        }
        else {
            integrationFilter.classList.add('d-none')
        }

        if (platform === 'freee' | platform === 'hubspot' && import_export_type === 'export') {
            syncFilters.classList.remove('d-none');
        }
        else {
            syncFilters.classList.add('d-none')
        }
        
        if (checkbox_status == 1){
            contact_as_company_input.checked = true
        } else {
            contact_as_company_input.checked = false
        }

        if ((platform === 'makeshop' | platform === 'hubspot' | platform === 'salesforce' && import_export_type === 'import') | (platform === 'hubspot' && import_export_type === 'export')) {
            status_mapping.classList.remove('d-none')
        } else {
            status_mapping.classList.add('d-none')
        }

        if (platform === 'rakuten' | platform === 'makeshop' | platform === 'amazon' | platform === 'yahoo-shopping' | platform === 'b-cart' && import_export_type === 'import') {
            importFilters.classList.remove('d-none')
        } else {
            importFilters.classList.add('d-none')
        }

        if (platform === 'nextengine' && import_export_type === 'export' && has_hubspot === 'True') {
            nextengineHubspotFilters.classList.remove('d-none')
        } else {
            nextengineHubspotFilters.classList.add('d-none')
            additionalActionField.classList.add('d-none')
        }
        
        if(platform === 'ecforce' | platform === 'nextengine' | platform === 'rakuten' | platform === 'makeshop' | platform === 'yahoo-shopping' | platform === 'hubspot' | platform === 'wordpress'| platform === 'amazon' | platform === 'b-cart' | platform === 'salesforce'| platform === 'shopify'){
            contact_mapping.classList.remove('d-none')
            contact_mapping.querySelector('input[name="item-checker"]').setAttribute('hx-vals', `{"mapping_type":"order","function_type":"${import_export_type}"}`)
        } else if(platform === 'freee'){
            contact_mapping.classList.remove('d-none')
            contact_mapping.querySelector('input[name="item-checker"]').setAttribute('hx-vals', `{"mapping_type":"contact","function_type":"${import_export_type}"}`)
        } else {
            contact_mapping.classList.add('d-none')
        }

        if((platform === 'hubspot' && import_export_type === 'export') || (platform === 'salesforce' && import_export_type === 'import')){
            association_mapping.classList.remove('d-none')
            association_mapping.querySelector('input[name="item-checker"]').setAttribute('hx-vals', `{"mapping_type":"order","function_type":"${import_export_type}"}`)
        } else {
            association_mapping.classList.add('d-none')
        }

        if ((platform === 'amazon' || platform === 'rakuten' || platform === 'makeshop' || platform === 'yahoo-shopping' || platform === 'hubspot' || platform === 'ebay' || platform === 'shopify' || platform === 'b-cart') && import_export_type === 'import') {
            document.getElementById('how-to-import-container').classList.remove('d-none')
        } else {
            document.getElementById('how-to-import-container').classList.add('d-none')
        }

        if (platform === 'shopify') {
            syncMethodField.classList.remove('d-none');
        } else {
            syncMethodField.classList.add('d-none');
        }

        if (platform === 'amazon' | platform === 'shopify' | platform === 'rakuten' | platform == 'makeshop') {
            defaultCustomerToggle.classList.remove('d-none');
        } else {
            defaultCustomerToggle.classList.add('d-none');
        }

        if (platform === 'b-cart') {
            document.getElementById('how-to-import-customer-container').classList.remove('d-none');
            document.getElementById('update-options-customer').classList.remove('d-none');
            document.getElementById('update-customer-key-select').required = true;
        } else {
            document.getElementById('how-to-import-customer-container').classList.add('d-none');
            document.getElementById('update-options-customer').classList.add('d-none'); 
            document.getElementById('update-customer-key-select').required = false;
        }
 
        // Reload Property Mapping
        Array.from(document.querySelectorAll("input[name='item-checker']")).forEach(element => {
            if (!Array.from(element.parentElement.parentElement.classList).includes('d-none')) {
                htmx.trigger(`#${element.id}`, "channelChanged")
            }
        });
    }
    
</script>

<script>
    //if filter selected
    $('#filter-select').on('change', function() {
        var filterId = this.value;
        if (filterId) {
            var filterChoice = document.getElementById("filter-choice");
            var filterChoiceField = document.getElementById("filter-choice-field");
            filterChoiceField.classList.remove('d-none');
            filterChoice.innerHTML = '';
            
            {% for filter in filters %}
                if (filterId === '{{filter.id}}') {
                    var choiceValueStr = `{{filter.choice_value|safe}}`
                    //Replace ' with "
                    choiceValueStr = choiceValueStr.replace(/'/g, '"');
                    var choiceValue = JSON.parse(choiceValueStr);
                    filterChoice.innerHTML += `<option value=""></option>`;
                    for (var i = 0; i < choiceValue.length; i++) {
                        filterChoice.innerHTML += `<option value="${choiceValue[i].value}">${choiceValue[i].label}</option>`;
                    }
                }
            {% endfor %}
        }
    })
    
    $('#nextengine-hubspot-filter-select').on('change', function() {
        var filterId = this.value;
        var additionalActionField = document.getElementById("additional-action-field");
        if (filterId === 'true') {
            additionalActionField.classList.remove('d-none');
        } else {
            additionalActionField.classList.add('d-none');
        }
    })
</script>

<script>
    function contactCheckerChannelChanged() {
        setTimeout(() => {
            if (document.getElementById('order-checker')) {
                htmx.trigger('#order-checker', 'channelChanged');
            } else if (document.getElementById('contact-checker')) {
                htmx.trigger('#contact-checker', 'channelChanged');
            } else {
                htmx.trigger('#assoc-checker', 'channelChanged');
            }
        }, 4000);
    }

    function showSaveMessage() {
        const saveMessage = document.getElementById('saveMappingMessage');
        if (saveMessage) {
            saveMessage.classList.remove('d-none'); // Show the message
            setTimeout(() => {
                saveMessage.classList.add('d-none'); // Hide it after 5 seconds
            }, 5000);
        }
    }
</script>

<script>
    var channel_ids= {{ channel_ids|safe }};
    console.log(channel_ids);

    // Function to update sync status based on selected channel
    function updateSyncStatus(selectedChannelId) {
        var isRunning = channel_ids.includes(selectedChannelId);
        var syncMessage = document.getElementById('sync-progress-message');
        var importBtn = document.getElementById('import-btn');
        var exportBtn = document.getElementById('export-btn');
        
        // Show/hide sync message
        syncMessage.classList.toggle('d-none', !isRunning);
        
        // Enable/disable buttons
        importBtn.disabled = isRunning;
        exportBtn.disabled = isRunning;
        
        if (isRunning) {
            console.log('Channel is running, sync in progress:', selectedChannelId);
        }
    }

    // Get selected channel ID from channel-select
    var selectedChannelId = document.getElementById('channel-select').value;
    updateSyncStatus(selectedChannelId);

    // Listen for channel-select changes
    $('#channel-select').on('select2:select', function (e) {
        var selectElement = $(this)
        selectedChannelId = selectElement.val();
        updateSyncStatus(selectedChannelId);
    });
</script>