import ast
import csv
from datetime import datetime
import json
import traceback
import uuid
import os

from dateutil import parser
from django.core.paginator import EmptyPage, PageNotAnInteger, Paginator
from django.db import models
from django.db.models import ManyToManyField, Prefetch, Q
from django.http import HttpResponse, HttpResponseRedirect
from django.http.response import JsonR<PERSON>ponse
from django.shortcuts import redirect, render
from django.template.loader import render_to_string
from django.utils import timezone
from django.views.decorators.http import require_GET
from django_hosts.resolvers import reverse
import pytz
import requests
import stripe

from utils.logger import logger as sentry_logger

from data.accounts.association_labels import (
    auto_create_necessary_association_label_setting,
    get_association_label_list,
)
from data.constants.constant import (
    APP_TARGET_SLUG,
    DEFAULT_COLUMNS_SUBSCRIPTIONS,
    DEFAULT_PERMISSION,
    SUBSCRIPTIONS_COLUMNS_DISPLAY,
    SUBSCRIPTION_USAGE_CATEGORY,
)
from data.constants.date_constant import DEFAULT_DATE_FORMAT
from data.constants.properties_constant import (
    DEFAULT_OBJECT_DISPLAY,
    OBJECT_TYPE_TO_SLUG,
    TYPE_OBJECT_COMPANY,
    TYPE_OBJECT_CONTACT,
    TYPE_OBJECT_INVOICE,
    TYPE_OBJECT_JOURNAL,
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_SUBSCRIPTION,
)
from data.home import hubspot_subscription_callback
from data.models import (
    AdvanceSearchFilter,
    AppSetting,
    Channel,
    Company,
    Contact,
    CustomProperty,
    DateFormat,
    HSMapField,
    HSOwner,
    Invoice,
    JournalEntry,
    Module,
    Notification,
    ObjectManager,
    PropertySet,
    ShopTurboItems,
    ShopTurboItemsDiscount,
    ShopTurboItemsPrice,
    ShopTurboItemsSubscriptions,
    ShopTurboOrders,
    ShopTurboShippingCost,
    ShopTurboSubscriptionPlatforms,
    ShopTurboSubscriptions,
    ShopTurboSubscriptionsNameCustomField,
    ShopTurboSubscriptionsValueCustomField,
    SubscriptionMappingFields,
    SubscriptionAssociationMappingFields,
    TransferHistory,
    View,
    ViewFilter,
    AssociationLabel,
    AssociationLabelObject
)
from data.shopturbo import get_export_import_object_config
from data.subscriptions.background.export_hubspot_subscriptions import (
    ExportHubspotSubscriptionsCustomObjectPayload,
    export_hubspot_subscriptions_custom_object_task,
)
from sanka.settings import LOCAL
from utils.bgjobs.handler import add_hatchet_run_id, create_bg_job
from utils.bgjobs.runner import trigger_bg_job
from utils.contact import display_contact_name
from utils.date import format_date, format_date_to_str, get_last_datetime_next_month
from utils.decorator import login_or_hubspot_required
from utils.discord import DiscordNotification
from utils.filter import build_view_filter
from utils.hubspot import (
    check_validation_token_hubspot,
    get_records_object,
    get_schema_detail_by_object_type_id,
    get_schema_properties,
    push_hubspot_object,
    refresh_hubspot_token,
    update_batch_records,
)
from utils.list_action_trigger_event import trigger_subscriptions_draft_to_active
from utils.logger import logger
from utils.meter import get_workspace_available_storage, has_quota, sync_usage
from utils.project import get_ordered_views
from utils.properties.properties import (
    get_object_display_based_columns,
    get_page_object,
)
from utils.salesforce.subscriptions import get_subscription_mapping_fields
from utils.seal_subscription import export_seal_subscriptions
from utils.stripe.stripe_subscription import create_stripe_payment_link_for_subscription
from utils.stripe.subscription_payment import update_subscription_payment_status
from utils.utility import (
    get_permission_filter,
    get_workspace,
    is_valid_number,
    is_valid_uuid,
    modular_view_filter,
    remove_unsupported_characters,
    save_custom_property,
)
from utils.view.subscription_view import apply_subscriptions_search_setting
from utils.view.subscription_view_filter import apply_shopturbosubscriptions_view_filter
from utils.view.view_utils import get_view_objects
from utils.workspace import get_permission
from data.constants.integration_constant import ECFORCE_SUBSCRIPTION_HEADER_LIST

SHOPTURBO_APP_TARGET = "shopturbo"
SHOPTURBO_APP_SLUG = APP_TARGET_SLUG[SHOPTURBO_APP_TARGET]


def export_subscription_to_hubspot(request):
    user = request.user
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(user)
    access_token = request.POST.get("hubspot_access_token")
    id = request.POST.get("channel_id", None)
    hub_domain = request.POST.get("hub_domain", None)
    object_type = request.POST.get("object_type", None)
    export_customers = request.POST.get("export_customers", None)
    hubspot_object_name = request.POST.get("hubspot_object_name", None)
    subscription_ids = request.POST.getlist("subscription_ids[]", None)

    try:
        channel = Channel.objects.filter(id=id, name=hub_domain).first()
        hsuser = HSOwner.objects.filter(
            hub_id=channel.account_id, hub_domain=channel.name
        ).first()
        object_fields = HSMapField.objects.filter(hsuser=hsuser, data_name=object_type)
        mapFields = []
        hubspot_fields = []

        for field in object_fields:
            mapFields.append(
                {
                    "hubspot_name": field.hubspot_name,
                    "hubspot_type": field.hubspot_type,
                    "sanka_name": field.sanka_name,
                    "sanka_type": field.sanka_type,
                }
            )
            hubspot_fields.append(field.hubspot_name)

        config = get_export_import_object_config(object_type)
        default_columns = config["default_columns"]
        DataTable = config["DataTable"]
        DataTableValueCustomField = config["DataTableValueCustomField"]
        DataTableNameCustomField = config["DataTableNameCustomField"]

        task_name = (
            f"Export {channel.integration.slug} - {channel.name} subscriptions"
            if lang != "ja"
            else f"{channel.integration.slug} - {channel.name} 見積書をエクスポート"
        )
        history = TransferHistory.objects.create(
            workspace=workspace,
            user=request.user,
            status="running",
            type="export_subscription",
            name=task_name,
            channel=channel or None,
        )

        try:
            valid, message = check_validation_token_hubspot(access_token)
            if valid:
                print("token hubspot is valid")
            elif not valid and message == "Invalid access token":
                access_token = refresh_hubspot_token(channel.id)
            else:
                raise Exception("Error when check validation token", message)
        except Exception as e:
            traceback.print_exc()
            print("error ", str(e))
            if lang == "ja":
                return render(
                    request,
                    "data/shopturbo/hubspot-fields-table-mapping.html",
                    {
                        "get_object_response": False,
                        "message": "HubSpotとの操作中にエラーが発生しました",
                    },
                )
            else:
                return render(
                    request,
                    "data/shopturbo/hubspot-fields-table-mapping.html",
                    {
                        "get_object_response": False,
                        "message": "Failed when refresh token. Please reauthenticate",
                    },
                )

        object_property_ids = []
        ok, response = get_records_object(access_token, hubspot_object_name, ["id"])
        if ok:
            object_property_ids.extend([data["properties"]["id"] for data in response])
        else:
            raise Exception(response)
        if len(subscription_ids) > 0:
            record_ids = subscription_ids
        else:
            record_ids = list(
                DataTable.objects.filter(
                    workspace=workspace, status="active"
                ).values_list("id", flat=True)
            )
        inputs = []
        inputs_batch_update = []
        inputs_associations = []
        EXCLUDED_FIELDS = ["customer", "item_name", "durations"]
        related_fields = []
        for field_name in default_columns:
            field = ""
            related_field = ""

            if field_name not in EXCLUDED_FIELDS:
                field = DataTable._meta.get_field(field_name)
                related_field = field_name
            if isinstance(field, ManyToManyField):
                related_fields.append(related_field)

        for record_id in record_ids:
            shopturbo_object = None
            record_name = ""
            shopturbo_object = DataTable.objects.get(id=record_id)

            properties = {}
            associations = {}
            if shopturbo_object:
                for field in mapFields:
                    sanka_field = field["sanka_name"]
                    if sanka_field[:2] == "c_":
                        sanka_field = " ".join(sanka_field[2:].split("____"))
                    # custom field
                    customField = DataTableNameCustomField.objects.filter(
                        workspace=workspace, name__iexact=sanka_field
                    ).first()
                    if customField:
                        custom_value = None
                        custom_value, _ = (
                            DataTableValueCustomField.objects.get_or_create(
                                field_name=customField, subscriptions=shopturbo_object
                            )
                        )

                        if custom_value:
                            if custom_value.value and customField.type == "tag":
                                tag_values = ast.literal_eval(custom_value.value)
                                properties[field["hubspot_name"]] = [
                                    tag["value"] for tag in tag_values
                                ]
                                properties[field["hubspot_name"]] = ",".join(
                                    properties[field["hubspot_name"]]
                                )
                            elif custom_value.value_number:
                                properties[field["hubspot_name"]] = (
                                    custom_value.value_number
                                )
                            elif custom_value.value_time:
                                properties[field["hubspot_name"]] = (
                                    custom_value.value_time.isoformat()
                                )
                            elif custom_value.value:
                                properties[field["hubspot_name"]] = custom_value.value
                            else:
                                properties[field["hubspot_name"]] = None

                    if shopturbo_object.company:
                        if export_customers is not None:
                            _company = {
                                "sanka_id": f"{shopturbo_object.company.company_id}",
                                "name": shopturbo_object.company.name,
                                "email": shopturbo_object.company.email,
                                "phone": shopturbo_object.company.phone_number,
                            }
                            associations["company"] = _company
                    if shopturbo_object.contact:
                        if export_customers is not None:
                            _contact = {
                                "sanka_id": f"{shopturbo_object.contact.contact_id}",
                                "name": display_contact_name(shopturbo_object.contact),
                                "email": shopturbo_object.contact.email,
                                "phone": shopturbo_object.contact.phone_number,
                            }
                            associations["contact"] = _contact
                    if field["sanka_name"] == "id":
                        associations["id"] = str(shopturbo_object.id)
                        properties[field["hubspot_name"]] = str(shopturbo_object.id)
                    elif field["sanka_name"] == "durations":
                        start_date = getattr(shopturbo_object, "start_date", None)
                        end_date = getattr(shopturbo_object, "end_date", None)
                        if end_date:
                            properties[field["hubspot_name"]] = (
                                f"{start_date.isoformat()} - {end_date.isoformat()}"
                            )
                        else:
                            if lang == "ja":
                                properties[field["hubspot_name"]] = (
                                    f"{start_date.isoformat()} - 無期限"
                                )
                            else:
                                properties[field["hubspot_name"]] = (
                                    f"{start_date.isoformat()} - Indefinitely"
                                )

                    elif field["hubspot_type"] in ["date", "datetime"]:
                        date_value = getattr(shopturbo_object, sanka_field, None)
                        if date_value:
                            properties[field["hubspot_name"]] = date_value.isoformat()
                    elif field["sanka_name"] == "item_name":
                        itemsInfo = []
                        _items = []
                        _amount = 0
                        for (
                            item_sub
                        ) in shopturbo_object.shopturboitemssubscriptions_set.all():
                            if item_sub.item is not None:
                                itemsInfo.append(
                                    f"#{item_sub.item.item_id} {item_sub.item.name} x {item_sub.number_item}"
                                )
                                _item_platform = item_sub.item.item.filter(
                                    channel=channel
                                ).first()
                            else:
                                itemsInfo.append(
                                    f"#{item_sub.item.item_id} {item_sub.item.name} x {item_sub.number_item}"
                                )
                                _item_platform = None
                            if _item_platform and _item_platform.platform_id:
                                _items.append(
                                    {
                                        "name": item_sub.item.name,
                                        "description": item_sub.item.description,
                                        "hs_sku": _item_platform.platform_id,
                                        "price": item_sub.item.price,
                                        "quantity": item_sub.number_item,
                                    }
                                )
                                try:
                                    _amount += (
                                        item_sub.number_item * item_sub.item.price
                                    )
                                except:
                                    _amount += 0
                            else:
                                _items.append(
                                    {
                                        "name": item_sub.item.name,
                                        "description": item_sub.item.description,
                                        "hs_sku": None,
                                        "price": item_sub.item.price,
                                        "quantity": item_sub.number_item,
                                    }
                                )
                                try:
                                    _amount += (
                                        item_sub.number_item * item_sub.item.price
                                    )
                                except:
                                    _amount += 0
                        if _amount == 0:
                            _amount = shopturbo_object.total_price
                        associations["items"] = _items
                        itemsInfo = " | ".join(itemsInfo)
                        properties[field["hubspot_name"]] = itemsInfo
                    elif field["sanka_name"] == "name":
                        customer_name = ""
                        start_date = getattr(shopturbo_object, "start_date", None)
                        if start_date:
                            start_date = start_date.isoformat()
                        if shopturbo_object.company:
                            customer_name = f"{shopturbo_object.company.name}"
                        if shopturbo_object.contact:
                            customer_name = f"{shopturbo_object.contact.name}"
                        total_price = getattr(shopturbo_object, "total_price", None)
                        currency = getattr(shopturbo_object, "currency", None)
                        id_object_value = getattr(
                            shopturbo_object, "subscriptions_id", None
                        )
                        record_name = f"{id_object_value} {start_date} {customer_name} {currency}{total_price}"
                        properties[field["hubspot_name"]] = record_name
                    else:
                        attr = getattr(shopturbo_object, sanka_field, None)
                        properties[field["hubspot_name"]] = attr
                if export_customers is not None:
                    inputs_associations.append(associations)

                if str(record_id) not in object_property_ids:
                    inputs.append({"properties": properties})
                else:
                    inputs_batch_update.append(
                        {
                            "idProperty": "id",
                            "id": str(record_id),
                            "properties": properties,
                        }
                    )

        if inputs_batch_update:
            ok, response = update_batch_records(
                access_token, hubspot_object_name, inputs_batch_update
            )
            if ok:
                print(response)
            else:
                raise Exception(response)

        ok, response = push_hubspot_object(
            access_token, hubspot_object_name, inputs, inputs_associations
        )
        if ok:
            history.status = "completed"
            history.progress = 100
            history.save()

            if lang == "ja":
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="データのエクスポートに成功しました",
                    type="success",
                )
            else:
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="Data exported successfully",
                    type="success",
                )
            return HttpResponse(200)
        else:
            raise Exception(response)

    except Exception as e:
        traceback.print_exc()
        print(f"Unexpected error: {e}")
        DiscordNotification().send_message(f"Export data object error: {str(e)}")

        if lang == "ja":
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="HubSpotへのエクスポートに失敗しました",
                type="error",
            )
        else:
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="Failed export to hubspot",
                type="error",
            )

        return HttpResponse(500)


@login_or_hubspot_required
def shopturbo_subscriptions(request, id=None):
    lang = request.LANGUAGE_CODE
    isopen = None

    page_obj = get_page_object(TYPE_OBJECT_SUBSCRIPTION, lang)
    base_model = page_obj["base_model"]
    item_model = page_obj["item_model"]
    id_field = page_obj["id_field"]
    custom_value_model = page_obj["custom_value_model"]
    custom_value_relation = page_obj["custom_value_relation"]
    custom_value_file_model = page_obj["custom_value_file_model"]
    custom_value_file_relation = page_obj["custom_value_file_relation"]
    additional_filter_fields = page_obj["additional_filter_fields"]

    if id:
        isopen = id
    if lang == "ja":
        page_title = "サブスクリプション"
    else:
        page_title = "Subscriptions"

    workspace = get_workspace(request.user)
    if not workspace:
        return redirect(reverse("start", host="app"))

    if request.method == "GET":
        subscription_id = request.GET.get("subscription_id", None)

        config_view = None
        archive = None
        view_id = request.GET.get("view_id", None)

        workspace = get_workspace(request.user)
        app_setting, _ = AppSetting.objects.get_or_create(
            workspace=workspace, app_target=SHOPTURBO_APP_TARGET
        )

        # Set Default Search Setting if first time open Subscriptions
        if not app_setting.search_setting_subscriptions:
            app_setting.search_setting_subscriptions = "customer"
            app_setting.save()

        # Auto populate association labels
        auto_create_necessary_association_label_setting(
            workspace, TYPE_OBJECT_SUBSCRIPTION
        )

        permission = get_permission(
            object_type=TYPE_OBJECT_SUBSCRIPTION, user=request.user
        )
        if not permission:
            permission = DEFAULT_PERMISSION

        if permission == "hide":
            context = {
                "permission": permission,
                "page_title": page_title,
                "target": TYPE_OBJECT_SUBSCRIPTION,
            }
            return render(request, "data/subscriptions/subscriptions.html", context)

        views = get_ordered_views(
            workspace, TYPE_OBJECT_SUBSCRIPTION, user=request.user
        )
        view_filter = modular_view_filter(
            workspace,
            TYPE_OBJECT_SUBSCRIPTION,
            view_id=view_id,
            column_view=DEFAULT_COLUMNS_SUBSCRIPTIONS.copy(),
            user=request.user,
        )

        config_view = view_filter.view_type
        archive = view_filter.archive
        shopturbo_Subscriptions_columns = view_filter.column

        pagination_number = view_filter.pagination
        if not pagination_number:
            pagination_number = 20
        POSTS_PER_PAGE = pagination_number
        POSTS_PER_PAGE_BEGIN = POSTS_PER_PAGE - 1

        status = request.GET.get("status")
        search_q = request.GET.get("q")
        advance_search = None
        advance_search_filter = {}

        import_id = request.GET.get("imported")
        import_history = TransferHistory.objects.filter(
            id=import_id, workspace=workspace
        ).first()
        imported_ids = None
        if import_history:
            imported_ids = import_history.checkpoint_details["imported"]
        else:
            advance_search = AdvanceSearchFilter.objects.filter(
                workspace=workspace,
                object_type=TYPE_OBJECT_SUBSCRIPTION,
                type="default",
            ).first()
            if not advance_search:
                advance_search = None

            if advance_search:
                if advance_search.search_settings:
                    app_setting.search_setting_subscriptions = (
                        advance_search.search_settings
                    )
                    app_setting.save()
            print("search setting:", app_setting.search_setting_subscriptions)

            advance_search_filter = None
            if advance_search:
                if advance_search.is_active:
                    advance_search_filter = advance_search.search_filter
                    advance_search_filter = {
                        k: v
                        for k, v in advance_search_filter.items()
                        if v["value"] != ""
                    }
                    try:
                        if (
                            advance_search_filter.get("usage_status", {}).get("value")
                            == "all"
                        ):
                            del advance_search_filter["usage_status"]
                    except KeyError:
                        pass

                    view_filter.filter_value = advance_search_filter

        view_object_dict = get_view_objects(
            object_type=TYPE_OBJECT_SUBSCRIPTION,
            workspace=workspace,
            user=request.user,
            permission=permission,
            app_setting=app_setting,
            view_filter=view_filter,
            status=status,
            search_q=search_q,
            filter_object_ids=imported_ids,
        )
        paginator = view_object_dict["paginator"]
        shopturbo_subscriptions = view_object_dict["object_qs"]
        if id:
            isopen = id
            try:
                selected_shopturbo_subscription = (
                    ShopTurboSubscriptions.objects.select_related("contact", "company")
                    .prefetch_related(
                        "shopturboitemssubscriptions_set",
                        "shopturboitemssubscriptions_set__item",
                        Prefetch(
                            "shopturbo_subscriptions_custom_field_relations",
                            queryset=ShopTurboSubscriptionsValueCustomField.objects.select_related(
                                "field_name"
                            ),
                        ),
                    )
                    .get(id=id)
                )

                if selected_shopturbo_subscription not in shopturbo_subscriptions:
                    # Create a list of selected subscription + filtered subscriptions
                    shopturbo_subscriptions = list(shopturbo_subscriptions)
                    # Insert the selected subscription at the beginning of the list
                    shopturbo_subscriptions.insert(0, selected_shopturbo_subscription)
            except ShopTurboSubscriptions.DoesNotExist:
                pass

        # Apply pagination after fetching the optimized queryset
        paginator = Paginator(shopturbo_subscriptions, pagination_number)
        page = request.GET.get("page", 1)
        try:
            page = int(page)
            page_content = paginator.page(page)
            shopturbo_subscriptions = page_content.object_list
            paginator_item_begin = (POSTS_PER_PAGE * page) - POSTS_PER_PAGE_BEGIN
            paginator_item_end = POSTS_PER_PAGE * page
        except (ValueError, PageNotAnInteger):
            page = 1
            page_content = paginator.page(page)
            paginator_item_begin = POSTS_PER_PAGE * page - POSTS_PER_PAGE_BEGIN
            paginator_item_end = POSTS_PER_PAGE * page
        except EmptyPage:
            page = paginator.num_pages
            page_content = paginator.page(page)
            paginator_item_begin = (POSTS_PER_PAGE * page) - POSTS_PER_PAGE_BEGIN
            paginator_item_end = POSTS_PER_PAGE * page

        else:
            page_content = paginator.page(1)
            paginator_item_begin = POSTS_PER_PAGE * 1 - POSTS_PER_PAGE_BEGIN
            paginator_item_end = POSTS_PER_PAGE * 1

        # Cache the custom_properties_map to avoid repeated lookups
        custom_properties_map = {}
        custom_properties = CustomProperty.objects.filter(
            workspace=workspace, model=ShopTurboSubscriptions._meta.db_table
        )
        for property in custom_properties:
            if property.value:
                custom_properties_map[property.name] = ast.literal_eval(property.value)

        # Property Set
        property_sets = PropertySet.objects.filter(
            workspace=workspace, target=TYPE_OBJECT_SUBSCRIPTION
        ).order_by("created_at")

        set_id = request.GET.get("set_id")
        if view_filter.view.form:
            set_id = view_filter.view.form.id
        else:
            if not set_id:
                property_set_default = property_sets.filter(as_default=True).first()
                if property_set_default:
                    set_id = property_set_default.id

        members = []
        groups = request.user.group_set.all()
        for group in groups:
            ids = group.user.all().values_list("id", flat=True)
            members.extend(ids)

        members = list(set(members))
        members = ",".join(str(id) for id in members)
        
        
        # association label
        association_label_list = get_association_label_list(workspace, TYPE_OBJECT_SUBSCRIPTION)
        

        context = {
            "page_title": page_title,
            "search_q": search_q,
            # NOTE: Use object_type
            "app_slug": SHOPTURBO_APP_SLUG,
            "shopturbo_subscriptions": shopturbo_subscriptions,
            "shopturbo_subscriptions_columns": shopturbo_Subscriptions_columns,
            "config_view": config_view,
            "view_filter": view_filter,
            "view_id": view_id if view_id else view_filter.view.id,
            "target": TYPE_OBJECT_SUBSCRIPTION,
            "views": views,
            "isopen": isopen,
            "paginator_item_begin": paginator_item_begin,
            "paginator_item_end": paginator_item_end,
            "page_content": page_content,
            "paginator": paginator,
            "custom_properties_map": custom_properties_map,
            "subscription_id": subscription_id,
            "permission": permission,
            "open_drawer": request.GET.get("open_drawer"),
            "status": request.GET.get("status"),
            "property_sets": property_sets,
            "set_id": set_id,
            "advance_search": advance_search,
            "advance_search_filter": advance_search_filter,
            "group_members": members,
            "association_label_list":association_label_list
        }

        # Debug: log template path and mtime to ensure correct file is being compiled
        try:
            from django.template import engines

            django_engine = engines["django"]
            tmpl = django_engine.get_template("data/subscriptions/subscriptions.html")
            origin = getattr(tmpl, "origin", None)
            tmpl_path = origin.name if origin and hasattr(origin, "name") else "unknown"
            mtime = (
                os.path.getmtime(tmpl_path)
                if tmpl_path != "unknown" and os.path.exists(tmpl_path)
                else None
            )
            sentry_logger.info(
                f"[subscriptions] template_path={tmpl_path} mtime={mtime} workspace_id={workspace.id} user_id={request.user.id}"
            )
        except Exception as e:
            sentry_logger.error(f"[subscriptions] template debug failed: {e}")

        return render(request, "data/subscriptions/subscriptions.html", context)
    else:
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_SUBSCRIPTION]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_SUBSCRIPTION
            )
            .order_by("order", "created_at")
            .first()
        )
        if module:
            module_slug = module.slug

        if "bulk_delete_subscriptions" in request.POST:
            if "flag_all" in request.POST:
                view_id = request.POST.get("view_id")
                view = View.objects.filter(id=view_id).first()
                if not view:
                    view = View.objects.filter(
                        title=None, target=TYPE_OBJECT_SUBSCRIPTION, workspace=workspace
                    ).first()
                view_filter = view.viewfilter_set.first()
                filter_conditions = Q(workspace=workspace)
                filter_conditions = build_view_filter(
                    filter_conditions,
                    view_filter,
                    TYPE_OBJECT_SUBSCRIPTION,
                    force_filter_list=additional_filter_fields,
                )
                ShopTurboSubscriptions.objects.filter(filter_conditions).update(
                    status="archived"
                )
            else:
                list_objects = request.POST.getlist("checkbox")
                ShopTurboSubscriptions.objects.filter(id__in=list_objects).update(
                    status="archived"
                )
            sync_usage(workspace, SUBSCRIPTION_USAGE_CATEGORY)

        elif "bulk_restore_subscriptions" in request.POST:
            sync_usage(workspace, SUBSCRIPTION_USAGE_CATEGORY)
            if has_quota(workspace, SUBSCRIPTION_USAGE_CATEGORY):
                workspace_available_storage = get_workspace_available_storage(
                    workspace, SUBSCRIPTION_USAGE_CATEGORY
                )
                if "flag_all" in request.POST:
                    view_id = request.POST.get("view_id")
                    view = View.objects.filter(id=view_id).first()
                    if not view:
                        view = View.objects.filter(
                            title=None,
                            target=TYPE_OBJECT_SUBSCRIPTION,
                            workspace=workspace,
                        ).first()
                    view_filter = view.viewfilter_set.first()
                    filter_conditions = Q(workspace=workspace)
                    filter_conditions = build_view_filter(
                        filter_conditions,
                        view_filter,
                        TYPE_OBJECT_SUBSCRIPTION,
                        force_filter_list=additional_filter_fields,
                    )

                    objects = ShopTurboSubscriptions.objects.filter(filter_conditions)
                else:
                    list_objects = request.POST.getlist("checkbox")
                    objects = ShopTurboSubscriptions.objects.filter(id__in=list_objects)
                if (
                    workspace_available_storage is None
                    or workspace_available_storage > 0
                ):
                    objects.update(status="active")

                # if flag_all:
                #     target_objects = ShopTurboSubscriptions.objects.filter(workspace=workspace, status='archived').order_by('-created_at').values('id')[:workspace_available_storage]
                #     ShopTurboSubscriptions.objects.filter(workspace=workspace, id__in=target_objects).update(status='active')
                # else:
                #     target_objects = ShopTurboSubscriptions.objects.filter(workspace=workspace, id__in=account_ids, status='archived').order_by('-created_at').values('id')[:workspace_available_storage]
                #     ShopTurboSubscriptions.objects.filter(workspace=workspace, id__in=target_objects).update(status='active')
                sync_usage(workspace, SUBSCRIPTION_USAGE_CATEGORY)

        elif "update-view-button" in request.POST:
            view_id = request.POST.get("view_id", None)
            view_name = request.POST.get("view_name", None)
            view_table = request.POST.get("view", None)
            archive = request.POST.get("archive", None)
            column = request.POST.get("column", None)
            status_selector = request.POST.get("status-selector", None)
            property_set_is_default_input = request.POST.get(
                "property_set_default", None
            )
            order_by = request.POST.get("order-by", None)
            sort_method = request.POST.get("sort-method", None)

            # update
            if view_id:
                view = View.objects.filter(id=view_id).first()
                if view.is_private and (view.user == request.user):
                    view.is_private = bool(request.POST.get("set-private-view", False))
                    view.user = request.user if view.is_private else None
                    view.save()
            # create
            else:
                is_private = bool(request.POST.get("set-private-view", False))
                user = request.user if is_private else None

                view = View.objects.create(
                    workspace=workspace,
                    title=view_name,
                    target=TYPE_OBJECT_SUBSCRIPTION,
                    is_private=is_private,
                    user=user,
                )

            if property_set_is_default_input:
                default_set = PropertySet.objects.get(id=property_set_is_default_input)
                view.form = default_set
                default_set.as_default = True
                default_set.save()
                # check default
                PropertySet.objects.filter(
                    workspace=default_set.workspace, target=default_set.target
                ).exclude(id=default_set.id).update(as_default=False)

            view_filter, _ = ViewFilter.objects.get_or_create(view=view)

            if view_name:
                view.title = view_name

            if view_table:
                view_filter.view_type = view_table

            view_filter.archive = archive

            if order_by:
                view_filter.sort_order_by = order_by
            else:
                view_filter.sort_order_by = None

            if sort_method:
                view_filter.sort_order_method = sort_method
            else:
                if order_by:
                    view_filter.sort_order_method = "asc"
                else:
                    view_filter.sort_order_method = None

            pagination = request.POST.get("pagination", None)
            if pagination:
                view_filter.pagination = pagination
            else:
                view_filter.pagination = 20

            # convert to normal List
            column = column.split(",")
            if view and column[0]:
                view_filter.column = column
            else:
                view_filter.column = None

            if view_table and status_selector:
                if view_table == "kanban":
                    view_filter.choice_customfield = status_selector
                else:
                    view_filter.choice_customfield = None

            filter_dictionary = {}
            filter_types = request.POST.getlist("filter_type", None)
            filter_options = request.POST.getlist("filter_options", None)
            filter_values = request.POST.getlist("filter_value", None)
            # Handle "between" operation (have two filter values)
            filter_values2 = request.POST.getlist("filter_value-2", None)
            idx_filter_values2 = 0
            for idx, filter_type in enumerate(filter_types):
                value = filter_values[idx]
                if filter_options[idx] == "between":
                    try:
                        value = (
                            f"{filter_values[idx]}-{filter_values2[idx_filter_values2]}"
                        )
                    except:
                        value = ""
                    idx_filter_values2 += 1
                filter_dictionary[str(filter_type)] = {
                    "key": filter_options[idx],
                    "value": value,
                }
            view_filter.filter_value = filter_dictionary
            view_filter.save()
            view.save()

            if module:
                if view.title:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                        + "?view_id="
                        + str(view_filter.view.id)
                    )
                else:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                    )
            return redirect(reverse("main", host="app"))

        elif "update-view-button-delete" in request.POST:
            view_id = request.POST.get("view_id", None)
            if view_id:
                View.objects.get(id=view_id).delete()
            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        elif "bulk_duplicate" in request.POST:
            print(request.POST)
            if "flag_all" in request.POST:
                view_id = request.POST.get("view_id")
                view = View.objects.filter(id=view_id).first()
                if not view:
                    view = View.objects.filter(
                        title=None, target=TYPE_OBJECT_SUBSCRIPTION, workspace=workspace
                    ).first()
                view_filter = view.viewfilter_set.first()
                filter_conditions = Q(workspace=workspace)
                filter_conditions = build_view_filter(
                    filter_conditions,
                    view_filter,
                    TYPE_OBJECT_SUBSCRIPTION,
                    force_filter_list=additional_filter_fields,
                )
                objects = base_model.objects.filter(filter_conditions)
            else:
                list_objects = request.POST.getlist("checkbox")
                objects = base_model.objects.filter(id__in=list_objects)

            # objects = base_model.objects.filter(id__in=item_ids)
            for obj in objects:
                obj_item = item_model.objects.filter(**{"subscriptions": obj}).order_by(
                    "created_at"
                )
                customFields = custom_value_model.objects.filter(
                    **{custom_value_relation: obj}
                )

                obj.id = None
                setattr(obj, id_field, None)
                obj.created_at = timezone.now()
                obj.save()

                for field in customFields:
                    old_field_id = field.id
                    field.id = None
                    setattr(field, custom_value_relation, obj)
                    field.save()

                    # duplicate file or image
                    customFieldsValueFile = custom_value_file_model.objects.filter(
                        **{f"{custom_value_file_relation}__id": old_field_id}
                    )
                    if customFieldsValueFile:
                        for value_file in customFieldsValueFile:
                            value_file.id = None
                            value_file.created_at = timezone.now()
                            setattr(value_file, custom_value_file_relation, field)
                            value_file.save()

                if obj_item:
                    for item_ in obj_item:
                        item_.id = None
                        item_.created_at = timezone.now()
                        setattr(item_, "subscriptions", obj)
                        item_.save()

        elif "save_mapping" in request.POST:
            module_slug = module.slug if module else "shopturbo"
            object_slug = "shopturbo-subscriptions"
            print("Saving Subscription Mapping")

            select_integration_ids = request.POST.get("select_integration_ids", None)
            if not select_integration_ids:
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="連携サービスを選択してください。",
                        type="error",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Please select an integration service.",
                        type="error",
                    )
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": object_slug,
                        },
                    )
                )

            try:
                channel = Channel.objects.get(
                    id=select_integration_ids, workspace=workspace
                )
            except Channel.DoesNotExist:
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="選択された連携サービスが見つかりません。",
                        type="error",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Selected integration service not found.",
                        type="error",
                    )
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": object_slug,
                        },
                    )
                )

            platform = channel.integration.slug

            # Get field mapping data
            subscription_mapping = {}
            file_columns = request.POST.getlist("custom-object-file-column", [])
            file_columns_name = request.POST.getlist(
                "custom-object-file-column-name", []
            )
            sanka_properties = request.POST.getlist(
                "custom-object-sanka-properties", []
            )
            ignores = request.POST.getlist("custom-object-ignore", [])

            subscription_mapping_association = {}
            file_columns_association = request.POST.getlist(
                "custom-object-association-file-column", []
            )
            # file_columns_association_name = request.POST.getlist(
            #     "custom-object-association-file-column-name", []
            # )
            sanka_properties_association = request.POST.getlist(
                "custom-object-association-sanka-properties", []
            )
            ignores_association = request.POST.getlist(
                "custom-object-association-ignore", []
            )

            for idx, file_column in enumerate(file_columns):
                if idx < len(sanka_properties) and idx < len(ignores):
                    subscription_mapping[file_column] = sanka_properties[idx]

            for idx, file_column in enumerate(file_columns_association):
                subscription_mapping_association[file_column] = (
                    sanka_properties_association[idx]
                )

            if subscription_mapping:
                selected_custom_object = request.POST.get("custom_object_options", None)
                try:
                    last_mapping = SubscriptionMappingFields.objects.filter(
                        workspace=workspace,
                        platform=channel.integration.slug,
                        custom_object_name__isnull=True,
                        channel=channel
                    ).first()
                    if last_mapping:
                        mapping_obj = last_mapping
                        mapping_obj.custom_object_name = selected_custom_object
                        mapping_obj.save()
                    else:
                        mapping_obj, _ = (
                            SubscriptionMappingFields.objects.get_or_create(
                                workspace=workspace,
                                platform=channel.integration.slug,
                                custom_object_name=selected_custom_object,
                                channel=channel
                            )
                        )
                except:
                    mapping_obj, _ = SubscriptionMappingFields.objects.get_or_create(
                        workspace=workspace,
                        platform=channel.integration.slug,
                        custom_object_name=selected_custom_object,
                        channel=channel
                    )
                data = {}
                for i, field in enumerate(file_columns):
                    if field in subscription_mapping:
                        if (
                            subscription_mapping[field] == "create_new"
                            and ignores[i] != "True"
                        ):
                            new_field, _ = (
                                ShopTurboSubscriptionsNameCustomField.objects.get_or_create(
                                    workspace=workspace,
                                    name=file_columns_name[i],
                                    type="text",
                                )
                            )
                            field_data = {
                                "value": file_columns_name[i],
                                "skip": ignores[i],
                            }
                            sanka_properties[i] = file_columns_name[i]
                        else:
                            field_data = {
                                "value": sanka_properties[i],
                                "skip": ignores[i],
                            }
                        data[field] = field_data
                mapping_obj.input_data = data
                mapping_obj.save()

            if subscription_mapping_association:
                selected_custom_object = request.POST.get("custom_object_options", None)
                try:
                    last_mapping = SubscriptionAssociationMappingFields.objects.filter(
                        workspace=workspace,
                        platform=channel.integration.slug,
                        custom_object_name__isnull=True,
                    ).first()
                    if last_mapping:
                        mapping_obj_association = last_mapping
                        mapping_obj_association.custom_object_name = (
                            selected_custom_object
                        )
                        mapping_obj_association.save()
                    else:
                        mapping_obj_association, _ = (
                            SubscriptionAssociationMappingFields.objects.get_or_create(
                                workspace=workspace,
                                platform=channel.integration.slug,
                                custom_object_name=selected_custom_object,
                            )
                        )
                except:
                    mapping_obj_association, _ = (
                        SubscriptionAssociationMappingFields.objects.get_or_create(
                            workspace=workspace,
                            platform=channel.integration.slug,
                            custom_object_name=selected_custom_object,
                        )
                    )

                data = {}
                for i, field in enumerate(file_columns_association):
                    if field in subscription_mapping_association:
                        field_data = {
                            "value": sanka_properties_association[i],
                            "skip": ignores_association[i],
                        }
                        data[field] = field_data
                mapping_obj_association.input_data = data
                mapping_obj_association.save()

        elif "import_subscriptions" in request.POST:
            # Define required variables
            module_slug = module.slug if module else "shopturbo"
            object_slug = "shopturbo-subscriptions"
            import_export_type = "import"

            select_integration_ids = request.POST.get("select_integration_ids", None)
            if not select_integration_ids:
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="連携サービスを選択してください。",
                        type="error",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Please select an integration service.",
                        type="error",
                    )
                return redirect(
                    reverse(
                        "load_object_page",
                        kwargs={"module_slug": module_slug, "object_slug": object_slug},
                    )
                )

            try:
                channel = Channel.objects.get(
                    id=select_integration_ids, workspace=workspace
                )
            except Channel.DoesNotExist:
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="選択された連携サービスが見つかりません。",
                        type="error",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Selected integration service not found.",
                        type="error",
                    )
                return redirect(
                    reverse(
                        "load_object_page",
                        kwargs={"module_slug": module_slug, "object_slug": object_slug},
                    )
                )

            platform = channel.integration.slug

            # Get field mapping data
            subscription_mapping = {}
            file_columns = request.POST.getlist("custom-object-file-column", [])
            file_columns_name = request.POST.getlist(
                "custom-object-file-column-name", []
            )
            sanka_properties = request.POST.getlist(
                "custom-object-sanka-properties", []
            )
            ignores = request.POST.getlist("custom-object-ignore", [])

            subscription_mapping_association = {}
            file_columns_association = request.POST.getlist(
                "custom-object-association-file-column", []
            )
            # file_columns_association_name = request.POST.getlist(
            #     "custom-object-association-file-column-name", []
            # )
            sanka_properties_association = request.POST.getlist(
                "custom-object-association-sanka-properties", []
            )
            ignores_association = request.POST.getlist(
                "custom-object-association-ignore", []
            )

            for idx, file_column in enumerate(file_columns):
                if idx < len(sanka_properties) and idx < len(ignores):
                    subscription_mapping[file_column] = sanka_properties[idx]

            for idx, file_column in enumerate(file_columns_association):
                subscription_mapping_association[file_column] = (
                    sanka_properties_association[idx]
                )

            if subscription_mapping:
                selected_custom_object = request.POST.get("custom_object_options", None)
                try:
                    last_mapping = SubscriptionMappingFields.objects.filter(
                        workspace=workspace,
                        platform=channel.integration.slug,
                        custom_object_name__isnull=True,
                        channel=channel
                    ).first()
                    if last_mapping:
                        mapping_obj = last_mapping
                        mapping_obj.custom_object_name = selected_custom_object
                        mapping_obj.save()
                    else:
                        mapping_obj, _ = (
                            SubscriptionMappingFields.objects.get_or_create(
                                workspace=workspace,
                                platform=channel.integration.slug,
                                custom_object_name=selected_custom_object,
                                channel=channel
                            )
                        )
                except:
                    mapping_obj, _ = SubscriptionMappingFields.objects.get_or_create(
                        workspace=workspace,
                        platform=channel.integration.slug,
                        custom_object_name=selected_custom_object,
                        channel=channel
                    )
                data = {}
                for i, field in enumerate(file_columns):
                    if field in subscription_mapping:
                        if (
                            subscription_mapping[field] == "create_new"
                            and ignores[i] != "True"
                        ):
                            new_field, _ = (
                                ShopTurboSubscriptionsNameCustomField.objects.get_or_create(
                                    workspace=workspace,
                                    name=file_columns_name[i],
                                    type="text",
                                )
                            )
                            field_data = {
                                "value": file_columns_name[i],
                                "skip": ignores[i],
                            }
                            sanka_properties[i] = file_columns_name[i]
                        else:
                            field_data = {
                                "value": sanka_properties[i],
                                "skip": ignores[i],
                            }
                        data[field] = field_data
                mapping_obj.input_data = data
                mapping_obj.save()

            if subscription_mapping_association:
                selected_custom_object = request.POST.get("custom_object_options", None)
                try:
                    last_mapping = SubscriptionAssociationMappingFields.objects.filter(
                        workspace=workspace,
                        platform=channel.integration.slug,
                        custom_object_name__isnull=True,
                    ).first()
                    if last_mapping:
                        mapping_obj_association = last_mapping
                        mapping_obj_association.custom_object_name = (
                            selected_custom_object
                        )
                        mapping_obj_association.save()
                    else:
                        mapping_obj_association, _ = (
                            SubscriptionAssociationMappingFields.objects.get_or_create(
                                workspace=workspace,
                                platform=channel.integration.slug,
                                custom_object_name=selected_custom_object,
                            )
                        )
                except:
                    mapping_obj_association, _ = (
                        SubscriptionAssociationMappingFields.objects.get_or_create(
                            workspace=workspace,
                            platform=channel.integration.slug,
                            custom_object_name=selected_custom_object,
                        )
                    )

                data = {}
                for i, field in enumerate(file_columns_association):
                    if field in subscription_mapping_association:
                        field_data = {
                            "value": sanka_properties_association[i],
                            "skip": ignores_association[i],
                        }
                        data[field] = field_data
                mapping_obj_association.input_data = data
                mapping_obj_association.save()

            if platform == "salesforce":
                if import_export_type == "import":
                    from data.subscriptions.background.import_salesforce_subscriptions import (
                        ImportSalesforceSubscriptionsPayload,
                        import_salesforce_subscriptions_task,
                    )

                    try:
                        # Get custom object selection
                        selected_custom_object = request.POST.get(
                            "custom_object_options", None
                        )
                        if not selected_custom_object:
                            if lang == "ja":
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=request.user,
                                    message="カスタムオブジェクトを選択してください。",
                                    type="error",
                                )
                            else:
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=request.user,
                                    message="Please select a custom object.",
                                    type="error",
                                )
                            return redirect(
                                reverse(
                                    "load_object_page",
                                    kwargs={
                                        "module_slug": module_slug,
                                        "object_slug": object_slug,
                                    },
                                )
                            )

                        # Create transfer history
                        if lang == "ja":
                            task_name = f"Salesforce カスタムオブジェクト ({selected_custom_object}) からサブスクリプションをインポート"
                        else:
                            task_name = f"Import subscriptions from Salesforce custom object ({selected_custom_object})"

                        try:
                            input_pair = {}
                            for sanka_prop, file_column, ignore in zip(
                                sanka_properties, file_columns, ignores
                            ):
                                if file_column and "|" in file_column:
                                    input_pair[file_column.split("|")[0]] = [
                                        file_column,
                                        sanka_prop,
                                        ignore,
                                    ]
                                else:
                                    input_pair[file_column] = [
                                        file_column,
                                        sanka_prop,
                                        ignore,
                                    ]
                        except Exception as e:
                            logger.error(f"Error creating input pair: {e}")
                            input_pair = None

                        history = TransferHistory.objects.create(
                            workspace=workspace,
                            user=request.user,
                            status="running",
                            type="import_subscriptions",
                            name=task_name,
                            channel=channel,
                            input_pair=input_pair,
                        )

                        # Get field mapping
                        subscription_mapping = {}

                        for idx, file_column in enumerate(file_columns):
                            if idx < len(sanka_properties) and idx < len(ignores):
                                if ignores[idx] != "True":
                                    subscription_mapping[file_column] = (
                                        sanka_properties[idx]
                                    )

                        subscription_mapping_association = {}
                        for idx, file_column in enumerate(file_columns_association):
                            if idx < len(sanka_properties_association) and idx < len(
                                ignores_association
                            ):
                                if ignores_association[idx] != "True":
                                    subscription_mapping_association[file_column] = (
                                        sanka_properties_association[idx]
                                    )

                        subscription_mapping_str = json.dumps(subscription_mapping)
                        subscription_mapping_association_str = json.dumps(
                            subscription_mapping_association
                        )

                        # Get filter settings
                        enable_salesforce_filter = request.POST.get(
                            "enable_salesforce_filter", False
                        )
                        salesforce_field = request.POST.get("salesforce_field", None)
                        salesforce_filter = request.POST.get("salesforce_filter", None)

                        # Create payload for background job
                        payload = ImportSalesforceSubscriptionsPayload(
                            user_id=str(request.user.id),
                            workspace_id=str(workspace.id),
                            channel_id=str(channel.id),
                            custom_object_name=selected_custom_object,
                            history_id=str(history.id),
                            subscription_mapping=subscription_mapping_str,
                            subscription_mapping_association=subscription_mapping_association_str,
                            language=lang,
                            enable_salesforce_filter=enable_salesforce_filter,
                            salesforce_field=salesforce_field,
                            salesforce_filter=salesforce_filter,
                            salesforce_checkpoint=0,
                        )

                        # Create background job
                        job_id = create_bg_job(
                            workspace=workspace,
                            user=request.user,
                            function_name="import_salesforce_subscriptions",
                            transfer_history=history,
                            payload=payload.model_dump(mode="json"),
                        )
                        payload.background_job_id = job_id

                        # Log import job parameters for debugging
                        logger.info(
                            f"IMPORT_SALESFORCE_SUBSCRIPTIONS: Starting subscription import for user {request.user.email} in workspace {workspace.id}"
                        )
                        logger.info(
                            f"IMPORT_SALESFORCE_SUBSCRIPTIONS: Import parameters - function: import_salesforce_subscriptions, job_id: {job_id}"
                        )
                        logger.info(
                            f"IMPORT_SALESFORCE_SUBSCRIPTIONS: Channel ID: {channel.id}, History ID: {history.id}, Language: {lang}"
                        )
                        logger.info(
                            f"IMPORT_SALESFORCE_SUBSCRIPTIONS: Custom Object: {selected_custom_object}"
                        )
                        logger.info(
                            f"IMPORT_SALESFORCE_SUBSCRIPTIONS: Mapping: {subscription_mapping_str}"
                        )
                        logger.info(
                            f"IMPORT_SALESFORCE_SUBSCRIPTIONS: Filter - enabled: {enable_salesforce_filter}, field: {salesforce_field}, value: {salesforce_filter}"
                        )

                        try:
                            ref = import_salesforce_subscriptions_task.run_no_wait(
                                input=payload
                            )
                        except Exception as e:
                            logger.error(
                                f"IMPORT_SALESFORCE_SUBSCRIPTIONS: Exception occurred during import_salesforce_subscriptions_task: {str(e)}",
                                exc_info=True,
                            )
                            ref = None

                        is_running = None
                        if ref:
                            logger.info(
                                f"IMPORT_SALESFORCE_SUBSCRIPTIONS: Background job submitted successfully for user {request.user.email}"
                            )
                            add_hatchet_run_id(job_id, ref)
                            is_running = True
                        else:
                            logger.error(
                                f"IMPORT_SALESFORCE_SUBSCRIPTIONS: Failed to submit background job for user {request.user.email}"
                            )
                            is_running = False

                        if is_running:
                            logger.info(
                                f"IMPORT_SALESFORCE_SUBSCRIPTIONS: Successfully submitted import job for user {request.user.email}"
                            )
                            if lang == "ja":
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=request.user,
                                    message=f"Salesforce カスタムオブジェクト ({selected_custom_object}) からサブスクリプションをインポートしています。",
                                    type="success",
                                )
                            else:
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=request.user,
                                    message=f"Importing subscriptions from Salesforce custom object ({selected_custom_object}).",
                                    type="success",
                                )
                        else:
                            logger.error(
                                f"IMPORT_SALESFORCE_SUBSCRIPTIONS: Background job submission failed for user {request.user.email}"
                            )
                            if lang == "ja":
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=request.user,
                                    message="サブスクリプションのインポートジョブの開始に失敗しました。",
                                    type="error",
                                )
                            else:
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=request.user,
                                    message="Failed to start subscription import job.",
                                    type="error",
                                )

                    except Exception as e:
                        logger.error(
                            f"IMPORT_SALESFORCE_SUBSCRIPTIONS: General exception in Salesforce subscription import: {str(e)}",
                            exc_info=True,
                        )
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message=f"サブスクリプションのインポート中にエラーが発生しました: {str(e)}",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message=f"Error occurred during subscription import: {str(e)}",
                                type="error",
                            )
                else:
                    # Handle export if needed (currently not implemented)
                    if lang == "ja":
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="Salesforce へのサブスクリプションエクスポートは現在サポートされていません。",
                            type="info",
                        )
                    else:
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="Subscription export to Salesforce is not currently supported.",
                            type="info",
                        )

            elif platform == "hubspot":
                if import_export_type == "import":
                    from data.subscriptions.background.import_hubspot_subscriptions import (
                        ImportHubspotSubscriptionsPayload,
                        import_hubspot_subscriptions_task,
                    )

                    try:
                        # Get custom object selection
                        selected_custom_object = request.POST.get(
                            "custom_object_options", None
                        )
                        if not selected_custom_object:
                            if lang == "ja":
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=request.user,
                                    message="カスタムオブジェクトを選択してください。",
                                    type="error",
                                )
                            else:
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=request.user,
                                    message="Please select a custom object.",
                                    type="error",
                                )
                            return redirect(
                                reverse(
                                    "load_object_page",
                                    kwargs={
                                        "module_slug": module_slug,
                                        "object_slug": object_slug,
                                    },
                                )
                            )

                        # Create transfer history
                        if lang == "ja":
                            task_name = f"HubSpot カスタムオブジェクト ({selected_custom_object}) からサブスクリプションをインポート"
                        else:
                            task_name = f"Import subscriptions from HubSpot custom object ({selected_custom_object})"

                        try:
                            input_pair = {}
                            for sanka_prop, file_column, ignore in zip(
                                sanka_properties, file_columns, ignores
                            ):
                                if file_column and "|" in file_column:
                                    input_pair[file_column.split("|")[0]] = [
                                        file_column,
                                        sanka_prop,
                                        ignore,
                                    ]
                                else:
                                    input_pair[file_column] = [
                                        file_column,
                                        sanka_prop,
                                        ignore,
                                    ]
                        except Exception as e:
                            logger.error(f"Error creating input pair: {e}")
                            input_pair = None

                        history = TransferHistory.objects.create(
                            workspace=workspace,
                            user=request.user,
                            status="running",
                            type="import_subscriptions",
                            name=task_name,
                            channel=channel,
                            input_pair=input_pair,
                        )

                        # Get field mapping
                        subscription_mapping = {}

                        for idx, file_column in enumerate(file_columns):
                            if idx < len(sanka_properties) and idx < len(ignores):
                                if ignores[idx] != "True":
                                    subscription_mapping[file_column] = (
                                        sanka_properties[idx]
                                    )

                        subscription_mapping_str = json.dumps(subscription_mapping)

                        # Create payload for background job
                        payload = ImportHubspotSubscriptionsPayload(
                            user_id=str(request.user.id),
                            workspace_id=str(workspace.id),
                            channel_id=str(channel.id),
                            custom_object_name=selected_custom_object,
                            history_id=str(history.id),
                            subscription_mapping=subscription_mapping_str,
                            language=lang,
                        )

                        # Create background job
                        job_id = create_bg_job(
                            workspace=workspace,
                            user=request.user,
                            function_name="import_hubspot_subscriptions",
                            transfer_history=history,
                            payload=payload.model_dump(mode="json"),
                        )
                        payload.background_job_id = job_id

                        # Log import job parameters for debugging
                        logger.info(
                            f"IMPORT_HUBSPOT_SUBSCRIPTIONS: Starting subscription import for user {request.user.email} in workspace {workspace.id}"
                        )
                        logger.info(
                            f"IMPORT_HUBSPOT_SUBSCRIPTIONS: Import parameters - function: import_hubspot_subscriptions, job_id: {job_id}"
                        )
                        logger.info(
                            f"IMPORT_HUBSPOT_SUBSCRIPTIONS: Channel ID: {channel.id}, History ID: {history.id}, Language: {lang}"
                        )
                        logger.info(
                            f"IMPORT_HUBSPOT_SUBSCRIPTIONS: Custom Object: {selected_custom_object}"
                        )
                        logger.info(
                            f"IMPORT_HUBSPOT_SUBSCRIPTIONS: Mapping: {subscription_mapping_str}"
                        )

                        try:
                            ref = import_hubspot_subscriptions_task.run_no_wait(
                                input=payload
                            )
                        except Exception as e:
                            logger.error(
                                f"IMPORT_HUBSPOT_SUBSCRIPTIONS: Exception occurred during import_hubspot_subscriptions_task: {str(e)}",
                                exc_info=True,
                            )
                            ref = None

                        is_running = None
                        if ref:
                            logger.info(
                                f"IMPORT_HUBSPOT_SUBSCRIPTIONS: Background job submitted successfully for user {request.user.email}"
                            )
                            add_hatchet_run_id(job_id, ref)
                            is_running = True
                        else:
                            logger.error(
                                f"IMPORT_HUBSPOT_SUBSCRIPTIONS: Failed to submit background job for user {request.user.email}"
                            )
                            is_running = False

                        if is_running:
                            logger.info(
                                f"IMPORT_HUBSPOT_SUBSCRIPTIONS: Successfully submitted import job for user {request.user.email}"
                            )
                            if lang == "ja":
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=request.user,
                                    message=f"HubSpot カスタムオブジェクト ({selected_custom_object}) からサブスクリプションをインポートしています。",
                                    type="success",
                                )
                            else:
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=request.user,
                                    message=f"Importing subscriptions from HubSpot custom object ({selected_custom_object}).",
                                    type="success",
                                )
                        else:
                            logger.error(
                                f"IMPORT_HUBSPOT_SUBSCRIPTIONS: Background job submission failed for user {request.user.email}"
                            )
                            if lang == "ja":
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=request.user,
                                    message="サブスクリプションのインポートジョブの開始に失敗しました。",
                                    type="error",
                                )
                            else:
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=request.user,
                                    message="Failed to start subscription import job.",
                                    type="error",
                                )

                    except Exception as e:
                        logger.error(
                            f"IMPORT_HUBSPOT_SUBSCRIPTIONS: General exception in HubSpot subscription import: {str(e)}",
                            exc_info=True,
                        )
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message=f"サブスクリプションのインポート中にエラーが発生しました: {str(e)}",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message=f"Error occurred during subscription import: {str(e)}",
                                type="error",
                            )
            elif platform == "ecforce":
                if import_export_type == "import":
                    from data.subscriptions.background.import_ecforce_subscriptions import (
                        ImportEcforceSubscriptionsPayload,
                        import_ecforce_subscriptions_task,
                    )

                    try:
                        # Create transfer history
                        if lang == "ja":
                            task_name = f"EC-Force からサブスクリプションをインポート"
                        else:
                            task_name = f"Import subscriptions from EC-Force"

                        try:
                            input_pair = {}
                            for sanka_prop, file_column, ignore in zip(
                                sanka_properties, file_columns, ignores
                            ):
                                if file_column and "|" in file_column:
                                    input_pair[file_column.split("|")[0]] = [
                                        file_column,
                                        sanka_prop,
                                        ignore,
                                    ]
                                else:
                                    input_pair[file_column] = [
                                        file_column,
                                        sanka_prop,
                                        ignore,
                                    ]
                        except Exception as e:
                            logger.error(f"Error creating input pair: {e}")
                            input_pair = None

                        history = TransferHistory.objects.create(
                            workspace=workspace,
                            user=request.user,
                            status="running",
                            type="import_subscriptions",
                            name=task_name,
                            channel=channel,
                            input_pair=input_pair,
                        )

                        # Get field mapping
                        subscription_mapping = {}

                        for idx, file_column in enumerate(file_columns):
                            if idx < len(sanka_properties) and idx < len(ignores):
                                if ignores[idx] != "True":
                                    subscription_mapping[file_column] = (
                                        sanka_properties[idx]
                                    )

                        subscription_mapping_str = json.dumps(subscription_mapping)

                        # Get import date
                        import_date = request.POST.get("import_date", None)

                        # Create payload for background job
                        payload = ImportEcforceSubscriptionsPayload(
                            user_id=str(request.user.id),
                            workspace_id=str(workspace.id),
                            channel_id=str(channel.id),
                            history_id=str(history.id),
                            subscription_mapping=subscription_mapping_str,
                            language=lang,
                            import_date=import_date,
                        )

                        # Create background job
                        job_id = create_bg_job(
                            workspace=workspace,
                            user=request.user,
                            function_name="import_ecforce_subscriptions",
                            transfer_history=history,
                            payload=payload.model_dump(mode="json"),
                        )
                        payload.background_job_id = job_id

                        # Log import job parameters for debugging
                        logger.info(
                            f"IMPORT_ECFORCE_SUBSCRIPTIONS: Starting subscription import for user {request.user.email} in workspace {workspace.id}"
                        )
                        logger.info(
                            f"IMPORT_ECFORCE_SUBSCRIPTIONS: Import parameters - function: import_ecforce_subscriptions, job_id: {job_id}"
                        )
                        logger.info(
                            f"IMPORT_ECFORCE_SUBSCRIPTIONS: Channel ID: {channel.id}, History ID: {history.id}, Language: {lang}"
                        )
                        logger.info(
                            f"IMPORT_ECFORCE_SUBSCRIPTIONS: Mapping: {subscription_mapping_str}"
                        )
                        logger.info(
                            f"IMPORT_ECFORCE_SUBSCRIPTIONS: Import Date: {import_date}"
                        )

                        try:
                            ref = import_ecforce_subscriptions_task.run_no_wait(
                                input=payload
                            )
                        except Exception as e:
                            logger.error(
                                f"IMPORT_ECFORCE_SUBSCRIPTIONS: Exception occurred during import_ecforce_subscriptions_task: {str(e)}",
                                exc_info=True,
                            )
                            ref = None

                        is_running = None
                        if ref:
                            logger.info(
                                f"IMPORT_ECFORCE_SUBSCRIPTIONS: Background job submitted successfully for user {request.user.email}"
                            )
                            add_hatchet_run_id(job_id, ref)
                            is_running = True
                        else:
                            logger.error(
                                f"IMPORT_ECFORCE_SUBSCRIPTIONS: Failed to submit background job for user {request.user.email}"
                            )
                            is_running = False

                        if is_running:
                            logger.info(
                                f"IMPORT_ECFORCE_SUBSCRIPTIONS: Successfully submitted import job for user {request.user.email}"
                            )
                            if lang == "ja":
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=request.user,
                                    message="EC-Force からサブスクリプションをインポートしています。",
                                    type="success",
                                )
                            else:
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=request.user,
                                    message="Importing subscriptions from EC-Force.",
                                    type="success",
                                )
                        else:
                            logger.error(
                                f"IMPORT_ECFORCE_SUBSCRIPTIONS: Background job submission failed for user {request.user.email}"
                            )
                            if lang == "ja":
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=request.user,
                                    message="サブスクリプションのインポートジョブの開始に失敗しました。",
                                    type="error",
                                )
                            else:
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=request.user,
                                    message="Failed to start subscription import job.",
                                    type="error",
                                )

                    except Exception as e:
                        logger.error(
                            f"IMPORT_ECFORCE_SUBSCRIPTIONS: General exception in EC-Force subscription import: {str(e)}",
                            exc_info=True,
                        )
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message=f"サブスクリプションのインポート中にエラーが発生しました: {str(e)}",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message=f"Error occurred during subscription import: {str(e)}",
                                type="error",
                            )
                else:
                    # Handle export if needed (currently not implemented)
                    if lang == "ja":
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="EC-Force へのサブスクリプションエクスポートは現在サポートされていません。",
                            type="info",
                        )
                    else:
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="Subscription export to EC-Force is not currently supported.",
                            type="info",
                        )

            else:
                # Handle other platforms if needed
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message=f"{platform} からのサブスクリプションインポートはまだサポートされていません。",
                        type="info",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message=f"Subscription import from {platform} is not yet supported.",
                        type="info",
                    )

        elif "export_subscriptions" in request.POST:
            select_integration_ids = request.POST.getlist("select_integration_ids", [])
            subscription_ids = request.POST.getlist("subscription_ids", [])
            channels = Channel.objects.filter(
                workspace=workspace, id__in=select_integration_ids
            )
            for channel in channels:
                if channel.integration.slug == "seal-subscription":
                    export_seal_subscriptions(
                        request.user, channel.id, subscription_ids
                    )
                elif channel.integration.slug == "hubspot":
                    param = {
                        "function": "hubspot_push_subscriptions",
                        "job_id": str(uuid.uuid4()),
                        "workspace_id": str(workspace.id),
                        "user_id": str(request.user.id),
                        "args": [
                            f"--channel_id={channel.id}",
                            f"--subscription_ids={str(subscription_ids)}",
                            f"--lang={lang}",
                            f"--user={request.user.id}",
                        ],
                    }
                    is_running = trigger_bg_job(param)
                    if is_running:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Hubspotのサブスクリプションをエクスポートしました",
                                type="success",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Export Hubspot subscriptions success",
                                type="success",
                            )
                    else:
                        hubspot_subscription_callback(
                            request, sub_ids=subscription_ids, channel_id=channel.id
                        )

            # return HttpResponse(status=200)
            if module:
                return HttpResponseRedirect(
                    request.META.get(
                        "HTTP_REFERER",
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        ),
                    )
                )

        elif "export_subscriptions_custom_object" in request.POST:
            select_integration_ids = request.POST.getlist("select_integration_ids", [])
            subscription_ids = request.POST.getlist("subscription_ids", [])
            selected_custom_object = request.POST.get("custom_object_options", None)

            if not selected_custom_object:
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="カスタムオブジェクトを選択してください",
                        type="error",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Please select a custom object",
                        type="error",
                    )
                return HttpResponseRedirect(request.META.get("HTTP_REFERER"))

            # Get field mapping data
            subscription_mapping = {}
            file_columns = request.POST.getlist("custom-object-file-column", [])
            sanka_properties = request.POST.getlist(
                "custom-object-sanka-properties", []
            )
            ignores = request.POST.getlist("custom-object-ignore", [])

            subscription_mapping_association = {}
            subscription_mapping_association = {}
            file_columns_association = request.POST.getlist(
                "custom-object-association-file-column", []
            )
            sanka_properties_association = request.POST.getlist(
                "custom-object-association-sanka-properties", []
            )
            ignores_association = request.POST.getlist(
                "custom-object-association-ignore", []
            )

            for idx, file_column in enumerate(file_columns):
                if idx < len(sanka_properties) and idx < len(ignores):
                    subscription_mapping[file_column] = sanka_properties[idx]

            for idx, file_column in enumerate(file_columns_association):
                subscription_mapping_association[file_column] = (
                    sanka_properties_association[idx]
                )

            channels = Channel.objects.filter(
                workspace=workspace, id__in=select_integration_ids
            )
            for channel in channels:
                if channel.integration.slug == "hubspot":
                    # Save mapping to database for future use
                    if subscription_mapping:
                        try:
                            last_mapping = SubscriptionMappingFields.objects.filter(
                                workspace=workspace,
                                platform=channel.integration.slug,
                                custom_object_name__isnull=True,
                            ).first()
                            if last_mapping:
                                mapping_obj = last_mapping
                                mapping_obj.custom_object_name = selected_custom_object
                                mapping_obj.save()
                            else:
                                mapping_obj, _ = (
                                    SubscriptionMappingFields.objects.get_or_create(
                                        workspace=workspace,
                                        platform=channel.integration.slug,
                                        custom_object_name=selected_custom_object,
                                    )
                                )
                        except:
                            mapping_obj, _ = (
                                SubscriptionMappingFields.objects.get_or_create(
                                    workspace=workspace,
                                    platform=channel.integration.slug,
                                    custom_object_name=selected_custom_object,
                                )
                            )
                        previous_data = (
                            mapping_obj.input_data if mapping_obj.input_data else {}
                        )
                        for i, field in enumerate(file_columns):
                            if i < len(sanka_properties) and i < len(ignores):
                                field_data = {
                                    "value": sanka_properties[i],
                                    "skip": ignores[i],
                                }
                                previous_data[field] = field_data
                        mapping_obj.input_data = previous_data
                        mapping_obj.save()

                    if subscription_mapping_association:
                        try:
                            last_mapping = (
                                SubscriptionAssociationMappingFields.objects.filter(
                                    workspace=workspace,
                                    platform=channel.integration.slug,
                                    custom_object_name__isnull=True,
                                ).first()
                            )
                            if last_mapping:
                                mapping_obj_association = last_mapping
                                mapping_obj_association.custom_object_name = (
                                    selected_custom_object
                                )
                                mapping_obj_association.save()
                            else:
                                mapping_obj_association, _ = (
                                    SubscriptionAssociationMappingFields.objects.get_or_create(
                                        workspace=workspace,
                                        platform=channel.integration.slug,
                                        custom_object_name=selected_custom_object,
                                    )
                                )
                        except:
                            mapping_obj_association, _ = (
                                SubscriptionAssociationMappingFields.objects.get_or_create(
                                    workspace=workspace,
                                    platform=channel.integration.slug,
                                    custom_object_name=selected_custom_object,
                                )
                            )

                        data = {}
                        for i, field in enumerate(file_columns_association):
                            if field in subscription_mapping_association:
                                field_data = {
                                    "value": sanka_properties_association[i],
                                    "skip": ignores_association[i],
                                }
                                data[field] = field_data
                        mapping_obj_association.input_data = data
                        mapping_obj_association.save()

                    subscription_mapping = {}
                    for idx, file_column in enumerate(file_columns):
                        if idx < len(sanka_properties) and idx < len(ignores):
                            if ignores[idx] != "True":
                                subscription_mapping[file_column] = sanka_properties[
                                    idx
                                ]

                    subscription_mapping_association = {}
                    for idx, file_column in enumerate(file_columns_association):
                        if idx < len(sanka_properties_association) and idx < len(
                            ignores_association
                        ):
                            if ignores_association[idx] != "True":
                                subscription_mapping_association[file_column] = (
                                    sanka_properties_association[idx]
                                )

                    # Convert to JSON strings for payload
                    subscription_mapping_str = json.dumps(subscription_mapping)
                    subscription_mapping_association_str = json.dumps(
                        subscription_mapping_association
                    )
                    file_columns_str = json.dumps(file_columns)
                    sanka_properties_str = json.dumps(sanka_properties)
                    ignores_str = json.dumps(ignores)

                    payload = ExportHubspotSubscriptionsCustomObjectPayload(
                        user_id=str(request.user.id),
                        workspace_id=str(workspace.id),
                        channel_id=str(channel.id),
                        custom_object_id=selected_custom_object,
                        subscription_ids=str(subscription_ids),
                        language=lang,
                        background_job_id=str(uuid.uuid4()),
                        subscription_mapping=subscription_mapping_str,
                        subscription_mapping_association=subscription_mapping_association_str,
                        file_columns=file_columns_str,
                        sanka_properties=sanka_properties_str,
                        ignores=ignores_str,
                    )

                    try:
                        ref = (
                            export_hubspot_subscriptions_custom_object_task.run_no_wait(
                                input=payload
                            )
                        )
                        is_running = True
                    except Exception as e:
                        print(f"Error triggering HubSpot subscription export: {e}")
                        is_running = False
                    if is_running:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Hubspotのカスタムオブジェクトにサブスクリプションをエクスポートしました",
                                type="success",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Export subscriptions to HubSpot custom object success",
                                type="success",
                            )
                    else:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="エクスポートに失敗しました",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Export failed",
                                type="error",
                            )

            # return HttpResponse(status=200)
            if module:
                return HttpResponseRedirect(
                    request.META.get(
                        "HTTP_REFERER",
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        ),
                    )
                )

        elif "csv_download" in request.POST:
            from .background.export_csv_subscription import (
                export_csv_subscriptions,
                ExportCSVSubscriptionsPayload,
            )

            logger.info(f"req POST: {request.POST}")
            if lang == "ja":
                history_name = "サブスクリプションレコードをエクスポートします"
            else:
                history_name = "Export Subscription Records"

            history = TransferHistory.objects.create(
                workspace=workspace,
                user=request.user,
                status="running",
                type=f"export_{TYPE_OBJECT_SUBSCRIPTION}",
                name=history_name,
            )

            view_id = request.POST.get("view_id", None)
            columns = request.POST.get("column", None)
            encoded_format = request.POST.get("encoded_format", "utf-8")

            filter_dictionary = {}
            filter_types = request.POST.getlist("filter_type", None)
            filter_options = request.POST.getlist("filter_options", None)
            filter_values = request.POST.getlist("filter_value", None)
            for idx, filter_type in enumerate(filter_types):
                if filter_type == "inventory_transactions":
                    filter_value = request.POST.getlist(
                        "filter_value_" + filter_type, None
                    )
                    if filter_value:
                        filter_value = ",".join(filter_value)
                else:
                    filter_value = filter_values[idx]

                if "id" in filter_type:
                    try:
                        value = str(filter_value).split(",")[0]
                        _ = uuid.UUID(str(value))
                        filter_type = "id"
                    except (ValueError, TypeError):
                        pass

                filter_dictionary[str(filter_type)] = {
                    "key": filter_options[idx],
                    "value": filter_value,
                }

            record_ids = None
            if request.POST.get("subscriptions_id"):
                record_ids = ",".join(request.POST.getlist("subscriptions_id"))
                filter_dictionary["id"] = {"key": "include", "value": record_ids}

            filter_dictionary = json.dumps(filter_dictionary)

            payload = ExportCSVSubscriptionsPayload(
                user_id=str(request.user.id),
                workspace_id=str(workspace.id),
                view_id=str(view_id),
                history_id=str(history.id),
                columns=columns,
                filter_dictionary=filter_dictionary,
                encoded_format=encoded_format,
                language=lang,
                record_ids=record_ids,
            )

            job_id = create_bg_job(
                workspace,
                request.user,
                "export_csv_subscriptions",
                transfer_history=history,
                payload=payload.model_dump(mode="json"),
            )
            payload.background_job_id = job_id

            # Log export job parameters for debugging
            logger.info(
                f"EXPORT_JOB: Starting subscriptions export for user {request.user.email} in workspace {workspace.id}"
            )
            logger.info(f"EXPORT_JOB: Export parameters - job_id: {job_id}")
            logger.info(
                f"EXPORT_JOB: View ID: {view_id}, History ID: {history.id}, Language: {lang}"
            )
            logger.info(f"EXPORT_JOB: Columns: {columns}")
            logger.info(f"EXPORT_JOB: Filter dictionary: {filter_dictionary}")

            ref = None
            try:
                ref = export_csv_subscriptions.run_no_wait(input=payload)
            except Exception as e:
                logger.error(
                    f"EXPORT_JOB: Exception occurred during export_csv_subscriptions: {str(e)}",
                    exc_info=True,
                )
                logger.info(
                    "EXPORT_JOB: Falling back to synchronous processing due to Hatchet unavailability"
                )
                logger.info(str(e))
            is_running = None
            if ref:
                if ref == "sync_fallback":
                    logger.info(
                        f"EXPORT_JOB: Synchronous export completed successfully for user {request.user.email}"
                    )
                else:
                    logger.info(
                        f"EXPORT_JOB: Background job submitted successfully for user {request.user.email}"
                    )
                is_running = True
            else:
                logger.error(
                    f"EXPORT_JOB: Failed to submit background job for user {request.user.email}"
                )
                is_running = False

            if is_running:
                logger.info(
                    f"EXPORT_JOB: Successfully submitted subscriptions export job for user {request.user.email}"
                )
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="エクスポートジョブが正常に送信されました。CSVはメールで送信されます。",
                        type="success",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Export job submitted successfully, csv will be sent to your email.",
                        type="success",
                    )
            else:
                logger.error(
                    f"EXPORT_JOB: Failed to submit subscriptions export job for user {request.user.email}"
                )
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message=f"{history_name}ジョブの送信中にエラーが発生しました。サポートにお問い合わせください。",
                        type="error",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message=f"There is an error while submitting the {history_name} job. Please contact support.",
                        type="error",
                    )
                history.delete()

            object_type = TYPE_OBJECT_SUBSCRIPTION
            module_object_slug = OBJECT_TYPE_TO_SLUG[object_type]
            module = Module.objects.filter(
                workspace=workspace, object_values__contains=object_type
            ).order_by("order", "created_at")

        if module:
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
            )

        return redirect(reverse("main", host="app"))


def shopify_manage_subscriptions(request):
    subscription_id = request.GET.get("subscription_id")
    access_token = request.GET.get("access_token")

    if not subscription_id or not access_token:
        content = render_to_string(
            "data/shopturbo/shopify_manage_subscriptions.html",
            {"error": "Missing subscription ID or access token."},
        )
        return HttpResponse(content, content_type="application/liquid")

    # Fetch subscriptions from API
    if LOCAL:
        url = "http://127.0.0.1:8000/api/v1/subscriptions"
    else:
        url = "https://sanka.com/api/v1/subscriptions"
    headers = {"Authorization": f"Bearer {access_token}"}

    response = requests.get(url, headers=headers)
    if response.status_code != 200:
        content = render_to_string(
            "data/shopturbo/shopify_manage_subscriptions.html",
            {"error": "Failed to fetch subscriptions from API."},
        )
        return HttpResponse(content, content_type="application/liquid")

    subscriptions = response.json()
    subscription = next(
        (sub for sub in subscriptions if sub["id"] == subscription_id), None
    )

    sub_object = ShopTurboSubscriptions.objects.filter(id=subscription_id).first()
    if not subscription:
        content = render_to_string(
            "data/shopturbo/shopify_manage_subscriptions.html",
            {"error": "Subscription not found."},
        )
        return HttpResponse(content, content_type="application/liquid")

    # Fetch items and contacts related to the workspace
    workspace = sub_object.workspace
    items = ShopTurboItems.objects.filter(workspace=workspace).all()
    contacts = Contact.objects.filter(workspace=workspace).all()
    companies = Company.objects.filter(workspace=workspace).all()

    contacts = list(contacts) + list(companies)

    # Render the template and return with the correct content type
    content = render_to_string(
        "data/shopturbo/shopify_manage_subscriptions.liquid",
        {
            "subscription": subscription,
            "shop_items": items,
            "contacts": contacts,
            "access_token": access_token,
        },
    )
    return HttpResponse(content, content_type="application/liquid")


def update_subscriptions(request, id):
    channel_id = request.POST.get("channel_id")
    hs_language = request.POST.get("hs_language")

    channel = Channel.objects.filter(id=channel_id).first()
    if channel:
        workspace = channel.workspace
    else:
        return
    shopturbo_subscriptions = ShopTurboSubscriptions.objects.get(id=id)

    if shopturbo_subscriptions.subscription_status == "draft":
        was_draft = True
    else:
        was_draft = False
    print(request.POST)
    if "update-subscriptions" in request.POST:
        contact_and_company = request.POST.getlist("contact_and_company", None)
        start_date = request.POST.get("start_date", None)
        end_date = request.POST.get("end_date", None)
        freq = request.POST.get("freq", None)
        freq_time = request.POST.get("freq_time", None)
        prior_to = request.POST.get("prior_to", None)
        prior_time = request.POST.get("prior_time", None)
        billing_timing = request.POST.get("billing_timing", None)
        tax_rate = request.POST.get("tax_rate", None)
        last_ordered_date = request.POST.get("last_ordered_date", None)

        items = request.POST.getlist("item", None)
        susbcriptions_items = request.POST.getlist("susbcriptions_items", None)
        number_of_items = request.POST.getlist("number_of_items", None)
        items_price_ids = request.POST.getlist("item_price_id", None)

        status = request.POST.get("status", None)
        subscription_status = request.POST.get("subscription_status", None)

        shipping_checkbox = request.POST.get("shipping_checkbox", None)
        shipping_id = request.POST.get("subscription_shipping", None)
        subscription_shipping_tax_status = request.POST.get(
            "subscription_shipping_tax_status", None
        )
        subscription_currency = request.POST.get("currency", "USD")

        discount_checkbox = request.POST.get("discount_checkbox", None)
        discount_id = request.POST.get("order_discount", None)

        discount = None
        if discount_id:
            if is_valid_uuid(discount_id):
                discount = ShopTurboItemsDiscount.objects.filter(id=discount_id).first()
            else:
                if is_valid_number(discount_id):
                    discount_number_format = request.POST.get(
                        "discount_number_format", None
                    )
                    discount, _ = ShopTurboItemsDiscount.objects.get_or_create(
                        value=discount_id,
                        discount_type="free_writing_discounts",
                        number_format=discount_number_format,
                    )

        shipping = None
        if shipping_id:
            shipping = ShopTurboShippingCost.objects.filter(id=shipping_id).first()

        shopturbo_subscriptions.contact = None
        shopturbo_subscriptions.company = None
        for contact_and_company_id in contact_and_company:
            if contact_and_company_id == "":
                continue
            if Contact.objects.filter(id=contact_and_company_id):
                shopturbo_subscriptions.contact = Contact.objects.get(
                    id=contact_and_company_id
                )

            elif Company.objects.filter(id=contact_and_company_id):
                shopturbo_subscriptions.company = Company.objects.get(
                    id=contact_and_company_id
                )

        if start_date:
            start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
            shopturbo_subscriptions.start_date = start_date

        if end_date:
            end_date = datetime.strptime(end_date, "%Y-%m-%d").date()
            shopturbo_subscriptions.end_date = end_date

        if status:
            shopturbo_subscriptions.status = status

        if subscription_status:
            shopturbo_subscriptions.subscription_status = subscription_status

        if freq:
            shopturbo_subscriptions.frequency = freq

        if freq_time:
            shopturbo_subscriptions.frequency_time = freq_time

        if billing_timing:
            shopturbo_subscriptions.billing_timing = billing_timing

        if tax_rate:
            shopturbo_subscriptions.tax_rate = tax_rate

        if last_ordered_date:
            last_ordered_date = datetime.strptime(last_ordered_date, "%Y-%m-%d").date()
            shopturbo_subscriptions.last_ordered_date = last_ordered_date

        if discount_checkbox and discount:
            shopturbo_subscriptions.discounts = discount
        else:
            shopturbo_subscriptions.discounts = None

        if shipping_checkbox and shipping:
            shopturbo_subscriptions.shipping_cost = shipping
        else:
            shopturbo_subscriptions.shipping_cost = None

        if subscription_shipping_tax_status:
            shopturbo_subscriptions.shipping_cost_tax_status = (
                subscription_shipping_tax_status
            )

        if "generate-order" in request.POST:
            shopturbo_subscriptions.prior_to_next = prior_to
        else:
            shopturbo_subscriptions.prior_to_next = None

        if prior_time:
            shopturbo_subscriptions.prior_to_time = prior_time

        shopturbo_subscriptions.item_variant = None

        checker = request.POST.get("item-checker", None)
        if not checker:
            shopturbo_subscriptions_item_price_active_id = []
            shopturbo_subscriptions_custom_items_active = []
            item_id_list = items

            items_custom_name = request.POST.getlist("items_custom_name", [])
            items_custom_price = request.POST.getlist("items_custom_price", [])
            items_custom_currency = request.POST.getlist("items_custom_currency", [])
            items_custom_number = request.POST.getlist("items_custom_number", [])
            for idx, item in enumerate(item_id_list):
                if item:
                    items_custom_name.insert(idx, "")
                    items_custom_price.insert(idx, "")
                    items_custom_currency.insert(idx, "")
                    items_custom_number.insert(idx, "")
                else:
                    susbcriptions_items.insert(idx, "")

            item_total_price = 0
            item_currency = subscription_currency
            number_item_total = 0
            for idx, item_id in enumerate(item_id_list):
                if item_id == "" or not item_id:
                    continue

                item = ShopTurboItems.objects.get(id=item_id)

                if susbcriptions_items[idx] != "":
                    shopturbo_item_subscriptions = (
                        ShopTurboItemsSubscriptions.objects.get(
                            id=susbcriptions_items[idx]
                        )
                    )
                    shopturbo_item_subscriptions.item = item
                else:
                    shopturbo_item_subscriptions = (
                        ShopTurboItemsSubscriptions.objects.create(
                            item=item,
                            subscriptions=shopturbo_subscriptions,
                            currency=item.currency,
                        )
                    )
                    shopturbo_item_subscriptions.save(
                        log_data={"status": "create", "workspace": workspace}
                    )
                shopturbo_item_subscriptions.currency = item.currency
                shopturbo_subscriptions_item_price_active_id.append(
                    shopturbo_item_subscriptions.id
                )

                # Safely access number_of_items with bounds checking
                if idx < len(number_of_items):
                    shopturbo_item_subscriptions.number_item = number_of_items[idx]
                else:
                    shopturbo_item_subscriptions.number_item = 1  # Default value

                # Safely access items_price_ids with bounds checking
                if idx < len(items_price_ids) and items_price_ids[idx]:
                    items_price = ShopTurboItemsPrice.objects.filter(
                        id=items_price_ids[idx]
                    ).first()
                    shopturbo_item_subscriptions.item_price = items_price
                    shopturbo_item_subscriptions.item_price_order = items_price
                    item.price = items_price.price

                shopturbo_item_subscriptions.item_price_order = item.price
                if float(shopturbo_item_subscriptions.number_item) > 0.0:
                    shopturbo_item_subscriptions.total_price = (
                        shopturbo_item_subscriptions.item_price_order
                        * float(shopturbo_item_subscriptions.number_item)
                    )
                else:
                    shopturbo_item_subscriptions.total_price = (
                        shopturbo_item_subscriptions.item_price_order
                    )

                shopturbo_item_subscriptions.save(log_data={"workspace": workspace})

                item_total_price += shopturbo_item_subscriptions.total_price
                item_currency = shopturbo_item_subscriptions.currency
                number_item_total += float(shopturbo_item_subscriptions.number_item)

            for idx, item_custom in enumerate(items_custom_name):
                if item_custom == "" or not item_custom:
                    continue

                # Safely access items_custom_number with bounds checking
                if idx < len(items_custom_number):
                    number_item = items_custom_number[idx]
                    if items_custom_number[idx] == "":
                        number_item = 0
                else:
                    number_item = 0

                # Safely access items_custom_price with bounds checking
                if idx < len(items_custom_price):
                    if type(items_custom_price[idx]) is str:
                        if "," in items_custom_price[idx]:
                            items_custom_price[idx] = items_custom_price[idx].replace(
                                ",", ""
                            )
                        items_custom_price[idx] = float(items_custom_price[idx])
                    custom_price = items_custom_price[idx]
                else:
                    custom_price = 0.0

                # Safely access susbcriptions_items with bounds checking
                if idx < len(susbcriptions_items) and susbcriptions_items[idx] != "":
                    shopturbo_item_subscriptions = (
                        ShopTurboItemsSubscriptions.objects.get(
                            id=susbcriptions_items[idx]
                        )
                    )
                    shopturbo_item_subscriptions.item = None
                    shopturbo_item_subscriptions.custom_item_name = item_custom
                    shopturbo_item_subscriptions.number_item = number_item

                    # Safely access items_custom_currency with bounds checking
                    if idx < len(items_custom_currency):
                        shopturbo_item_subscriptions.currency = items_custom_currency[
                            idx
                        ]
                    else:
                        shopturbo_item_subscriptions.currency = (
                            shopturbo_subscriptions.currency
                        )

                    shopturbo_item_subscriptions.item_price_order = float(custom_price)

                else:
                    # Safely access items_custom_currency with bounds checking
                    if (
                        idx < len(items_custom_currency)
                        and items_custom_currency[idx] != ""
                        and items_custom_currency[idx] is not None
                    ):
                        custom_currency = items_custom_currency[idx]
                    else:
                        custom_currency = shopturbo_subscriptions.currency
                    shopturbo_item_subscriptions, _ = (
                        ShopTurboItemsSubscriptions.objects.get_or_create(
                            subscriptions=shopturbo_subscriptions,
                            custom_item_name=item_custom,
                        )
                    )
                    shopturbo_item_subscriptions.item_price_order = custom_price
                    shopturbo_item_subscriptions.number_item = number_item
                    shopturbo_item_subscriptions.currency = custom_currency
                    shopturbo_subscriptions_custom_items_active.append(item_custom)
                    shopturbo_item_subscriptions.save(
                        log_data={"status": "create", "workspace": workspace}
                    )

                if float(shopturbo_item_subscriptions.number_item) >= 0.0:
                    shopturbo_item_subscriptions.total_price = (
                        shopturbo_item_subscriptions.item_price_order
                        * float(shopturbo_item_subscriptions.number_item)
                    )
                else:
                    shopturbo_item_subscriptions.total_price = (
                        shopturbo_item_subscriptions.item_price_order
                    )

                shopturbo_item_subscriptions.save(log_data={"workspace": workspace})

                item_total_price += shopturbo_item_subscriptions.total_price
                number_item_total += float(shopturbo_item_subscriptions.number_item)

            delete_subscription_items = (
                ShopTurboItemsSubscriptions.objects.filter(
                    subscriptions=shopturbo_subscriptions
                )
                .exclude(id__in=shopturbo_subscriptions_item_price_active_id)
                .exclude(
                    custom_item_name__in=shopturbo_subscriptions_custom_items_active
                )
            )
            for delete_subscription_item in delete_subscription_items:
                delete_subscription_item.delete(log_data={"workspace": workspace})

            shopturbo_subscriptions.currency = item_currency
            shopturbo_subscriptions.total_price = item_total_price
            shopturbo_subscriptions.number_item = number_item_total

        else:
            currency = request.POST.get("currency", None)
            price = request.POST.get("price", None)
            if isinstance(price, str):
                if "," in price:
                    price = price.replace(",", "")

            shopturbo_subscriptions.item = None
            shopturbo_subscriptions.number_item = 1
            shopturbo_subscriptions.currency = currency
            shopturbo_subscriptions.total_price = price

        if shopturbo_subscriptions.tax:
            shopturbo_subscriptions.total_price = (
                shopturbo_subscriptions.total_price
                + (shopturbo_subscriptions.total_price * shopturbo_subscriptions.tax)
                / 100
            )
        if shopturbo_subscriptions.shipping_cost:
            shopturbo_subscriptions.total_price = (
                shopturbo_subscriptions.total_price
                + shopturbo_subscriptions.shipping_cost.value
            )

        shopturbo_subscriptions.save(
            log_data={"status": "create", "workspace": workspace}
        )

        save_custom_property(request, shopturbo_subscriptions)

        # draft to active checker
        if was_draft:
            trigger_subscriptions_draft_to_active(
                shopturbo_subscriptions.id, workspace.user.first(), hs_language
            )

    elif "delete-subscriptions" in request.POST:
        shopturbo_subscriptions.status = "archived"
        shopturbo_subscriptions.save(log_data={"workspace": workspace})

    elif "restore-subscriptions" in request.POST:
        if has_quota(workspace, SUBSCRIPTION_USAGE_CATEGORY):
            shopturbo_subscriptions.status = "active"
            shopturbo_subscriptions.save(log_data={"workspace": workspace})

    elif "update_orders" in request.POST:
        orders = request.POST.getlist("orders", [])
        if orders and orders != [""]:
            shopturbo_subscriptions.orders.clear()
            order_objs = ShopTurboOrders.objects.filter(id__in=orders)
            shopturbo_subscriptions.orders.add(*order_objs)
        else:
            shopturbo_subscriptions.orders.clear()

    return True


def import_subscription_to_hubspot(request):
    access_token = request.POST.get("hubspot_access_token")
    id = request.POST.get("channel_id", None)
    hub_domain = request.POST.get("hub_domain", None)
    hubspot_object_name = request.POST.get("hubspot_object_name", None)
    object_type = request.POST.get("object_type", None)
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    config = get_export_import_object_config(object_type)
    DataTable = config["DataTable"]
    DataTableValueCustomField = config["DataTableValueCustomField"]
    DataTableNameCustomField = config["DataTableNameCustomField"]

    channel = Channel.objects.filter(id=id, name=hub_domain).first()
    hsuser = HSOwner.objects.filter(
        hub_id=channel.account_id, hub_domain=channel.name
    ).first()
    object_fields = HSMapField.objects.filter(hsuser=hsuser, data_name=object_type)
    mapFields = []
    hubspot_fields = []
    for field in object_fields:
        mapFields.append(
            {
                "hubspot_name": field.hubspot_name,
                "hubspot_type": field.hubspot_type,
                "sanka_name": field.sanka_name,
                "sanka_type": field.sanka_type,
            }
        )
        hubspot_fields.append(field.hubspot_name)

    history = None
    task_name = (
        f"Import {channel.integration.slug} - {channel.name} subscriptions"
        if lang != "ja"
        else f"{channel.integration.slug} - {channel.name} 納品書をインポート"
    )
    history = TransferHistory.objects.create(
        workspace=workspace,
        user=request.user,
        status="running",
        type="import_subscription",
        name=task_name,
        channel=channel or None,
    )

    try:
        valid, message = check_validation_token_hubspot(access_token)
        if valid:
            print("token hubspot is valid")
        elif not valid and message == "Invalid access token":
            if "Power Subscription" in channel.name:
                access_token = refresh_hubspot_token(channel.id)
            else:
                access_token = refresh_hubspot_token(channel.id)
        else:
            raise Exception("Error when check validation token", message)
    except Exception as e:
        traceback.print_exc()
        print("error ", str(e))
        if lang == "ja":
            return render(
                request,
                "data/shopturbo/hubspot-fields-table-mapping.html",
                {
                    "get_object_response": False,
                    "message": "HubSpotとの操作中にエラーが発生しました",
                },
            )
        else:
            return render(
                request,
                "data/shopturbo/hubspot-fields-table-mapping.html",
                {
                    "get_object_response": False,
                    "message": "Failed when refresh token. Please reauthenticate",
                },
            )

    try:
        EXCLUDED_FIELDS = ["item_name", "durations", "name", "total_price"]
        ok, response = get_records_object(
            access_token, hubspot_object_name, hubspot_fields
        )
        if ok:
            records = response
            for record in records:
                properties = record["properties"]

                shopturbo_data = {}
                for field in object_fields:
                    if field.sanka_name not in EXCLUDED_FIELDS:
                        if field.hubspot_type in ["date", "datetime"]:
                            date_string = properties[field.hubspot_name]
                            date_value = (
                                parser.parse(date_string) if date_string else None
                            )
                            shopturbo_data[field.sanka_name] = date_value

                        elif field.sanka_name == "customer":
                            customerInfo = properties["customer"][1:].split("_")
                            customer_id, name = customerInfo[0], customerInfo[1]
                            bill_object = DataTable.objects.filter(
                                id=properties["id"],
                                workspace=get_workspace(request.user),
                            ).first()
                            if bill_object:
                                company = Company.objects.filter(
                                    workspace=get_workspace(request.user),
                                    company_id=customer_id,
                                    name=name,
                                ).first()
                                contact = Contact.objects.filter(
                                    workspace=get_workspace(request.user),
                                    contact_id=customer_id,
                                    name=name,
                                ).first()
                                if company:
                                    bill_object.company = company
                                    bill_object.contact = None
                                if contact:
                                    bill_object.contact = contact
                                    bill_object.company = None
                                bill_object.save()
                        else:
                            hubspot_value = properties[field.hubspot_name]
                            if hubspot_value is not None:
                                shopturbo_data[field.sanka_name] = hubspot_value

                shopturbo_object, created = DataTable.objects.update_or_create(
                    id=uuid.UUID(properties["id"]),
                    workspace=workspace,
                    defaults=shopturbo_data,
                )

                # custom fields
                for field in object_fields:
                    sanka_field = field.sanka_name
                    if sanka_field[:2] == "c_":
                        sanka_field = " ".join(sanka_field[2:].split("____"))
                    customField = DataTableNameCustomField.objects.filter(
                        workspace=workspace, name=sanka_field
                    ).first()
                    if customField:
                        custom_value = None
                        hubspot_value = properties[field.hubspot_name]
                        custom_value, created = (
                            DataTableValueCustomField.objects.get_or_create(
                                field_name=customField, estimate=shopturbo_object
                            )
                        )

                        if custom_value and customField.type != "tag":
                            if field.hubspot_type == "number":
                                custom_value.value_number = float(hubspot_value)
                            elif field.hubspot_type == "datetime":
                                date_string = properties[field.hubspot_name]
                                date_value = (
                                    parser.parse(date_string) if date_string else None
                                )
                                custom_value.value_time = date_value
                            else:
                                custom_value.value = hubspot_value

                            custom_value.save()

            history.status = "completed"
            history.progress = 100
            history.save()

            if lang == "ja":
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="データのエクスポートに成功しました",
                    type="success",
                )
            else:
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="Data imported successfully",
                    type="success",
                )
            return HttpResponse(200)
        else:
            raise Exception(response)

    except Exception as e:
        traceback.print_exc()
        print(f"Import data object error: {str(e)}")
        DiscordNotification().send_message(f"Import data object error: {str(e)}")

        if lang == "ja":
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="HubSpotへのインポートに失敗しました",
                type="error",
            )
        else:
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="Failed import to Hubspot",
                type="error",
            )

        return HttpResponse(500)


@login_or_hubspot_required
@require_GET
def autocomplete_subscription(request):
    workspace = get_workspace(request.user)
    search = request.GET.get("search", "")
    ids = request.GET.getlist("ids[]", "")

    # Filter out invalid IDs (empty strings, "None", etc.) and validate UUIDs
    valid_ids = []
    if ids:
        for id_value in ids:
            if id_value and id_value != "None" and is_valid_uuid(id_value):
                valid_ids.append(id_value)

    filter_condition = Q(workspace=workspace) & Q(status="active")

    # filter with edit permission
    permission = get_permission(object_type=TYPE_OBJECT_SUBSCRIPTION, user=request.user)
    filter_condition &= get_permission_filter(
        permission, request.user, permission_type="edit"
    )

    page = int(request.GET.get("page", 1))

    results_per_page = 10 if len(valid_ids) <= 0 else len(valid_ids)
    start = (page - 1) * results_per_page
    end = start + results_per_page

    if search:
        app_setting = AppSetting.objects.filter(
            workspace=workspace, app_target=SHOPTURBO_APP_TARGET
        ).first()

        if app_setting:
            filter_condition &= apply_subscriptions_search_setting(
                app_setting, search.lower()
            ) | Q(name__icontains=search.lower())

    if valid_ids:
        filter_condition &= Q(id__in=valid_ids)

    obj, created = ObjectManager.objects.get_or_create(
        workspace=workspace, page_group_type=TYPE_OBJECT_SUBSCRIPTION
    )
    if created:
        try:
            col_display = ",".join(DEFAULT_OBJECT_DISPLAY[TYPE_OBJECT_SUBSCRIPTION])
        except:
            col_display = "subscriptions_id"
        obj.column_display = col_display
        obj.save()
    else:
        col_display = obj.column_display if obj.column_display else "subscriptions_id"

    columns = col_display.split(",")
    results = ShopTurboSubscriptions.objects.filter(filter_condition).order_by(
        "-created_at"
    )[start:end]
    data = [
        {
            "id": item.id,
            "text": get_object_display_based_columns(
                TYPE_OBJECT_SUBSCRIPTION,
                item,
                columns,
                workspace.timezone,
                request.LANGUAGE_CODE,
            ),
        }
        for item in results
    ]

    return JsonResponse({"results": data, "pagination": {"more": results.exists()}})


def subscription_association_drawer(request):
    if request.method == "GET":
        # {"source": "{{source}}", "object_id": "{{obj.id}}", "page": "{{page}}", "view_id": "{{view_id}}", "module": "{{module}}"}
        source = request.GET.get("source", None)
        object_id = request.GET.get("object_id", None)
        page = request.GET.get("page", 1)
        view_id = request.GET.get("view_id", None)
        module = request.GET.get("module", None)
        object_type = request.GET.get("object_type", None)
        from_object = request.GET.get("from", None)
        label_id = request.GET.get("label_id", '')

        if source == TYPE_OBJECT_ORDER:
            obj = ShopTurboOrders.objects.get(id=object_id)
        elif source == TYPE_OBJECT_INVOICE:
            obj = Invoice.objects.get(id=object_id)
        elif source == TYPE_OBJECT_COMPANY:
            obj = None
            company = Company.objects.get(id=object_id)
            subscription_objs = ShopTurboSubscriptions.objects.filter(company=company)
        elif source == TYPE_OBJECT_JOURNAL:
            obj = JournalEntry.objects.get(id=object_id)
        elif source == TYPE_OBJECT_CONTACT:
            obj = Contact.objects.get(id=object_id)
            
        label = AssociationLabel.objects.filter(id=label_id).first()
        context = {
            "source": source,
            "object_id": object_id,
            "page": page,
            "view_id": view_id,
            "obj": obj,
            "module": module,
            "object_type": object_type,
            "label": label,
        }

        if source == TYPE_OBJECT_COMPANY:
            context["subscription_objs"] = subscription_objs

        if from_object:
            context["from"] = from_object

        return render(
            request, "data/association/default-create-add/subscriptions.html", context
        )
    else:
        return HttpResponse(200)


def get_subscription_object_options(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    q = request.GET.get("q")
    page = request.GET.get("page", 1)
    customer_id = request.GET.get("customer_id", None)

    filter_conditions = Q(workspace=workspace, status="active")
    if customer_id:
        filter_conditions &= Q(company=customer_id) | Q(contact=customer_id)
    if q:
        q_id = q
        try:
            if q[0] == "#" or q[0] == "0":
                q_id = int(q[1:].replace(" ", ""))
        except Exception as e:
            print(e)
        filter_conditions &= (
            Q(subscriptions_id__icontains=q_id)
            | Q(company__name__icontains=q)
            | Q(contact__name__icontains=q)
        )
    subscription_objs = ShopTurboSubscriptions.objects.filter(
        filter_conditions
    ).order_by("-subscriptions_id")

    res = []
    ITEMS_PER_PAGE = 30
    subscriptions_paginator = Paginator(subscription_objs, ITEMS_PER_PAGE)
    paginated_subscriptions = []
    more_pagination = False
    if page:
        try:
            subscription_page_content = subscriptions_paginator.page(
                page if page else 1
            )
            paginated_subscriptions = subscription_page_content.object_list
            more_pagination = subscription_page_content.has_next()
        except EmptyPage:
            pass

    for subscription in paginated_subscriptions:
        sub_dict = {
            "id": str(subscription.id),
            "text": f"#{subscription.subscriptions_id:04d} | ",
        }
        if subscription.contact:
            sub_dict["text"] += f"{display_contact_name(subscription.contact, lang)}"
        elif subscription.company:
            sub_dict["text"] += f"{subscription.company.name}"

        res.append(sub_dict)

    context = {"results": res, "pagination": {"more": more_pagination}}

    return JsonResponse(context)


def generate_stripe_payment_link(request, channel_id, subscription_id):
    lang = request.LANGUAGE_CODE
    try:
        stripe_channel = Channel.objects.get(
            id=channel_id,
            integration__slug="stripe",
            authenticated=True,
        )
    except Channel.DoesNotExist:
        print(f"Stripe channel with ID {channel_id} not found")
        return HttpResponse("Stripe channel not found", status=404)

    try:
        subscription = ShopTurboSubscriptions.objects.get(
            id=subscription_id, workspace=stripe_channel.workspace
        )
    except ShopTurboSubscriptions.DoesNotExist:
        print(
            f"Subscription with ID {subscription_id} not found in workspace {stripe_channel.workspace.id}"
        )
        return HttpResponse("Subscription not found", status=404)

    platform_subscription = ShopTurboSubscriptionPlatforms.objects.filter(
        source_subscription=subscription, channel__id=channel_id
    ).first()
    if not platform_subscription:
        return HttpResponse("Subscription not found", status=404)

    stripe.api_key = stripe_channel.api_secret
    if platform_subscription.payment_status == "paid":
        return render(
            request, "data/static/payment-completed.html", {"menu_key": "home"}
        )
    if platform_subscription.payment_id:
        try:
            # Set the API key from the channel before making the Stripe API call
            stripe.api_key = stripe_channel.api_secret

            # Log the payment_id for debugging
            print(
                f"Attempting to retrieve Stripe session with ID: {platform_subscription.payment_id}"
            )

            try:
                session = stripe.checkout.Session.retrieve(
                    platform_subscription.payment_id
                )
            except:
                session = {}
            if session and hasattr(session, "status") and session.status == "open":
                return redirect(session.url)
            if session.status == "complete":
                update_subscription_payment_status(platform_subscription)
                subscription.subscription_status = "active"
                subscription.save()
                return render(
                    request, "data/static/payment-completed.html", {"menu_key": "home"}
                )
        except Exception as e:
            print(f"Error retrieving Stripe session: {str(e)}")

    if (
        not platform_subscription.start_date_field
        or platform_subscription.start_date_field == "subscription_start_date"
    ):
        payment_start_date = subscription.start_date
    else:
        payment_start_date = ShopTurboSubscriptionsValueCustomField.objects.filter(
            field_name__id=platform_subscription.start_date_field,
            subscriptions=subscription,
        ).first()
        if payment_start_date:
            payment_start_date = payment_start_date.value_time
    if not payment_start_date:
        payment_start_date = subscription.start_date

    desired_date = payment_start_date  # This is your April 1st date

    # Create a UTC datetime for noon on the desired date
    # This ensures that regardless of timezone, the date will be the same in most of the world
    # Create datetime in subscription timezone first
    local_tz = pytz.timezone(subscription.workspace.timezone)
    local_datetime = datetime(
        desired_date.year, desired_date.month, desired_date.day, 0, 0, 0
    )
    local_datetime = local_tz.localize(local_datetime)

    # Convert to UTC
    utc_start_date_time = local_datetime.astimezone(pytz.UTC)
    unix_start_time = int(utc_start_date_time.timestamp())

    # Calculate next month's date
    last_datetime_next_month = get_last_datetime_next_month(
        subscription.workspace.timezone
    )

    # Get first day of current month in payment timezone
    now = timezone.now()
    local_first_day = local_tz.localize(datetime(now.year, now.month, 1, 0, 0, 0))
    utc_first_day = local_first_day.astimezone(pytz.UTC)

    upfront_full_payment = False
    if utc_start_date_time < utc_first_day:
        return HttpResponse(
            "Subscription start date is invalid: subscription at least need to start from this month",
            status=400,
        )

    if utc_start_date_time > last_datetime_next_month:
        pass

    elif utc_start_date_time < timezone.now():
        # Find the next occurrence of the same day of month

        next_day = min(desired_date.day, last_datetime_next_month.day)

        # Create the future date (noon UTC)
        next_date = datetime(
            last_datetime_next_month.year,
            last_datetime_next_month.month,
            next_day,
            0,
            0,
            0,
            tzinfo=local_tz,
        )
        unix_start_time = int(next_date.timestamp())

        print(
            f"Payment start date was in the past. Using next occurrence on: {next_date.strftime('%Y-%m-%d')}"
        )

    # Log the exact date being sent to Stripe for debugging
    print(
        f"Setting Stripe billing start date to: {datetime.fromtimestamp(unix_start_time, pytz.UTC).strftime('%Y-%m-%d %H:%M:%S %Z')}"
    )

    try:
        # Create Stripe subscription payment link
        stripe_payment_link_result = create_stripe_payment_link_for_subscription(
            channel_id,
            subscription_id,
            platform_subscription.payment_allow_discount,
            unix_start_time=unix_start_time,
            upfront_full_payment=upfront_full_payment,
            lang=lang,
        )

        if not stripe_payment_link_result or "url" not in stripe_payment_link_result:
            print(
                f"Failed to create Stripe payment link for subscription {subscription_id}: {stripe_payment_link_result}"
            )
            return HttpResponse("Failed to create payment link", status=404)

        stripe_payment_link = stripe_payment_link_result.get("url", "")
        stripe_payment_link_id = stripe_payment_link_result.get("id", "")
        platform_subscription.payment_id = stripe_payment_link_id
        platform_subscription.save()

        return redirect(stripe_payment_link)
    except stripe.AuthenticationError:
        Notification.objects.create(
            workspace=stripe_channel.workspace,
            message=f"Failed to create Stripe payment link due to authentication error. Please reconnect your Stripe integration ({stripe_channel.name}).",
            message_ja=f"認証エラーのため、Stripeサブスクリプションをキャンセルできませんでした。Stripe統合（{stripe_channel.name}）を再接続してください。",
            type="error",
        )
        return HttpResponse(
            "支払いリンクの作成中にエラーが発生しました"
            if lang == "ja"
            else "Error creating payment link",
            status=500,
        )
    except Exception as e:
        print(f"Error creating Stripe payment link: {str(e)}")
        traceback.print_exc()
        return HttpResponse("Error creating payment link", status=500)


def subscription_stripe_payment_paid(request, channel_id, subscription_id):
    lang = request.LANGUAGE_CODE
    err_res = HttpResponse("Error checking payment status", status=500)
    if lang == "ja":
        err_res = HttpResponse("支払い状況の確認中にエラーが発生しました", status=500)

    try:
        stripe_channel = Channel.objects.get(
            id=channel_id,
            integration__slug="stripe",
            authenticated=True,
        )
    except Channel.DoesNotExist:
        print(f"Stripe channel with ID {channel_id} not found")
        return err_res

    try:
        subscription = ShopTurboSubscriptions.objects.get(
            id=subscription_id, workspace=stripe_channel.workspace
        )
    except ShopTurboSubscriptions.DoesNotExist:
        print(
            f"Subscription with ID {subscription_id} not found in workspace {stripe_channel.workspace.id}"
        )
        return err_res

    platform = ShopTurboSubscriptionPlatforms.objects.filter(
        source_subscription=subscription,
        channel=stripe_channel,
    ).first()
    if not platform:
        print(f"Platform stripe for subscription {subscription_id} not found")
        return err_res

    stripe.api_key = stripe_channel.api_secret
    if platform.payment_id:
        try:
            # Set the API key from the channel before making the Stripe API call
            stripe.api_key = stripe_channel.api_secret

            # Log the payment_id for debugging
            print(
                f"Attempting to retrieve Stripe session with ID: {platform.payment_id}"
            )

            try:
                session = stripe.checkout.Session.retrieve(platform.payment_id)
            except:
                session = {}
            if hasattr(session, "status") and session.status != "complete":
                return err_res

            platform.payment_status = "paid"
            platform.save()
            subscription.subscription_status = "active"
            subscription.save()
            return render(
                request, "data/static/payment-completed.html", {"menu_key": "home"}
            )
        except Exception as e:
            print(f"Error retrieving Stripe session: {str(e)}")
            print(f"Stripe API Key used: {stripe_channel.api_secret[:5]}...")
            print(
                f"Channel ID: {channel_id}, Subscription ID: {subscription_id}, Payment ID: {platform.payment_id}"
            )
    return err_res


def sync_subscription_header_extractor(request):
    """
    Extract headers for Salesforce subscription import mapping.
    Similar to sync_invoice_header_extractor but for subscriptions.
    """

    workspace = get_workspace(request.user)
    template_path = "data/shopturbo/sync-subscriptions-import-mapping.html"
    if request.method == "POST":
        mapping = None
        if "action_index" in request.POST:
            action_index = request.POST.get("action_index")
            select_integration_ids = request.POST.get(
                f"select_integration_ids-{action_index}"
            )
        else:
            select_integration_ids = request.POST.get("select_integration_ids")

        try:
            channel = Channel.objects.get(id=select_integration_ids)
        except Exception:
            return HttpResponse(404)

        # Platform columns for subscriptions - based on ShopTurboSubscriptions model
        platform_columns = [
            "create_new",
            "customer",
            "item_name",
            "item_price",
            "item_amount",
            "currency",
            "total_price",
            "subscription_status",
            "start_date",
            "end_date",
            "frequency",
            "frequency_time",
            "tax_rate",
            "upcoming_invoice_date",
            "owner",
        ]

        subscriptionnamecustomfield = (
            ShopTurboSubscriptionsNameCustomField.objects.filter(
                workspace=workspace
            )
            .exclude(type__in=["image", "user", "formula"])
            .values_list("name", flat=True)
        )
        platform_columns.extend(subscriptionnamecustomfield)

        if 'selected_custom_object' in request.POST:
            selected_custom_object = request.POST.get("selected_custom_object", None)

            if not selected_custom_object:
                return HttpResponse("Custom object not selected", status=400)

            mapping_type = request.POST.get("mapping_type", "custom_object")
            platform_columns = []
            header_list = []
            function_type = request.POST.get("function_type", "import")
            default_mapping_field = None

            # Sub Property (none for subscriptions currently)
            sub_property_columns = {}

            if mapping_type == "custom_object":
                if channel.integration.slug == "salesforce":
                    header_list = []
                    mapping_fields = None

                    if selected_custom_object:
                        mapping_fields = get_subscription_mapping_fields(
                            str(channel.id), selected_custom_object
                        )

                    if mapping_fields:
                        # Convert subscription mapping fields to header_list format
                        for field in mapping_fields:
                            field_name = field.get("name", "")
                            field_label = field.get("label", field_name)

                            # Skip system fields and certain lookup fields
                            if field_name not in ["sanka_id", "platform", "platform_id"]:
                                default_value = ""

                                # Set default mappings for common subscription fields
                                if field_name in ["Name", "Subject", "Title"]:
                                    default_value = "create_new"  # Most custom objects use custom field names
                                elif field_name in [
                                    "Amount",
                                    "Total",
                                    "Amount__c",
                                    "Price__c",
                                ]:
                                    default_value = "total_price"
                                elif field_name in ["Currency", "CurrencyIsoCode"]:
                                    default_value = "currency"
                                elif field_name in ["Account", "AccountId", "Company__c"]:
                                    default_value = "company"
                                elif field_name in ["Contact", "ContactId", "Contact__c"]:
                                    default_value = "contact"
                                elif field_name in ["Status", "Stage", "Status__c"]:
                                    default_value = "subscription_status"
                                elif field_name in [
                                    "StartDate",
                                    "Start_Date__c",
                                    "CreatedDate",
                                ]:
                                    default_value = "start_date"
                                elif field_name in [
                                    "EndDate",
                                    "End_Date__c",
                                    "Due_Date__c",
                                ]:
                                    default_value = "end_date"
                                elif field_name in ["Frequency", "Frequency__c"]:
                                    default_value = "frequency"
                                elif field_name in [
                                    "Frequency_Time__c",
                                    "Frequency_Type__c",
                                ]:
                                    default_value = "frequency_time"
                                elif field_name in [
                                    "Item",
                                    "Product",
                                    "Item__c",
                                    "Product__c",
                                ]:
                                    default_value = "item_name"
                                elif field_name in [
                                    "Item_Price__c",
                                    "Unit_Price__c",
                                    "Price__c",
                                ]:
                                    default_value = "item_price"
                                elif field_name in [
                                    "Quantity",
                                    "Quantity__c",
                                    "Number_Item__c",
                                ]:
                                    default_value = "item_amount"
                                elif field_name in ["OwnerId"]:
                                    default_value = "owner"
                                elif field_name in [
                                    "Description",
                                    "Notes",
                                    "Description__c",
                                ]:
                                    default_value = "create_new"
                                elif field_name in ["Tax_Rate__c", "Tax__c"]:
                                    default_value = "tax_rate"
                                elif field_name in [
                                    "upcoming_invoice_date__c",
                                    "Next_Billing_Date__c",
                                ]:
                                    default_value = "upcoming_invoice_date"
                                else:
                                    # Custom fields will be created as new
                                    default_value = "create_new"

                                header_list.append(
                                    {
                                        "value": field_name,
                                        "name": field_label,
                                        "name_ja": field_label,  # Using same label for Japanese
                                        "skip": False,
                                        "default": default_value,
                                    }
                                )
                elif channel.integration.slug == "hubspot":
                    hubspot_group_name = ""

                    if function_type:
                        try:
                            access_token = refresh_hubspot_token(channel.id)
                            platform_object = get_schema_detail_by_object_type_id(
                                channel.id, selected_custom_object
                            )
                            _, custom_object_schema_properties = get_schema_properties(
                                access_token, selected_custom_object, []
                            )
                            # Process the custom object properties
                            for property in custom_object_schema_properties:
                                if function_type == "import":
                                    _exc = ["sanka_id"]
                                else:
                                    _exc = ["sanka_id", "platform_id"]
                                if property.get("name", "") not in _exc:
                                    header_list.append(
                                        {
                                            "value": f"{property.get('name', '')}|{property.get('type', '')}|{platform_object.labels.plural}",
                                            "options": property.get("option"),
                                            "name": f"{platform_object.labels.plural} - {property.get('label', property.get('name', ''))}",
                                            "name_ja": f"{platform_object.labels.plural} - {property.get('label', property.get('name', ''))}",
                                            "skip": False,
                                            "default": default_mapping_field,
                                        }
                                    )

                            if custom_object_schema_properties:
                                property = custom_object_schema_properties[0]
                                hubspot_group_name = property.get("group_name", "")
                        except Exception as e:
                            print(f"Error fetching HubSpot custom object schema: {str(e)}")

            
            try:
                try:
                    last_mapping = SubscriptionMappingFields.objects.filter(
                        workspace=workspace,
                        platform=channel.integration.slug,
                        custom_object_name__isnull=True,
                    ).first()
                    if last_mapping:
                        mapping = last_mapping
                    else:
                        mapping, _ = SubscriptionMappingFields.objects.get_or_create(
                            workspace=workspace,
                            platform=channel.integration.slug,
                            custom_object_name=selected_custom_object,
                        )
                except:
                    mapping, _ = SubscriptionMappingFields.objects.get_or_create(
                        workspace=workspace,
                        platform=channel.integration.slug,
                        custom_object_name=selected_custom_object,
                    )
                    pass
                
                if mapping.input_data:
                    for item in header_list:
                        if item["value"] in mapping.input_data:
                            item["default"] = mapping.input_data[item["value"]]["value"]
                            item["skip"] = mapping.input_data[item["value"]]["skip"]
            except Exception:
                pass

            context = {
                "header_list": header_list,
                "platform_columns": platform_columns,
                "sub_property_columns": sub_property_columns,
                "default_mapping_field": default_mapping_field,
                "function_type": function_type,
                "mapping_type": mapping_type,
                "object_type": "shopturbo_subscriptions",
                "channel": channel,
                "selected_custom_object": selected_custom_object,
                "platform": channel.integration.slug,
            }
        else:
            if channel.integration.slug == "ecforce":
                header_list = ECFORCE_SUBSCRIPTION_HEADER_LIST('')
            
            
            try:
                try:
                    last_mapping = SubscriptionMappingFields.objects.filter(
                        workspace=workspace,
                        platform=channel.integration.slug,
                        channel=channel
                    ).first()
                    if last_mapping:
                        mapping = last_mapping
                    else:
                        mapping, _ = SubscriptionMappingFields.objects.get_or_create(
                            workspace=workspace,
                            platform=channel.integration.slug,
                            custom_object_name='',
                            channel=channel
                        )
                except:
                    mapping, _ = SubscriptionMappingFields.objects.get_or_create(
                        workspace=workspace,
                        platform=channel.integration.slug,
                        custom_object_name='',
                        channel=channel
                    )
                    pass
                
                if mapping.input_data:
                    for item in header_list:
                        if item["value"] in mapping.input_data:
                            item["default"] = mapping.input_data[item["value"]]["value"]
                            item["skip"] = mapping.input_data[item["value"]]["skip"]
            except Exception:
                pass

            context = {
                "header_list": header_list,
                "platform_columns": platform_columns,
                "object_type": "shopturbo_subscriptions",
                "channel": channel,
                "platform": channel.integration.slug,
            }

        return render(request, template_path, context)

    return HttpResponse("Method not allowed", status=405)
