"""
Streaming CSV exporter for HubSpot Orders (Deals).
Handles memory-efficient export of large datasets.
"""

import asyncio
import csv
import gc
import time
from datetime import datetime
from typing import Any, AsyncIterator, Dict, List, Optional
from pathlib import Path

from django import db
from django.db.models import Prefetch

from data.models import (
    ShopTurboOrders,
    ShopTurboItemsOrders,
    ShopTurboOrdersValueCustomField,
    ShopTurboItemsOrdersValueCustomField,
)
from data.models.customer import (
    CompanyPlatforms,
    Contact,
    Company,
    ContactsPlatforms,
    ContactsValueCustomField,
    CompanyValueCustomField,
)
from utils.logger import logger

from .models import (
    OrderCSVRow,
    LineItemCSVRow,
    ContactCSVRow,
    CompanyCSVRow,
    OrderExportConfig,
    CSVChunkInfo,
)


class StreamingOrderCSVExporter:
    """
    Exports orders to CSV files using streaming to handle large datasets efficiently.
    """

    def __init__(
        self,
        workspace_id: str,
        config: Optional[OrderExportConfig] = None,
        mapping_custom_fields: Optional[Dict[str, str]] = None,
        mapping_status_custom_fields: Optional[Dict[str, str]] = None,
        owners: Optional[List[Dict[str, Any]]] = None,
        dealstage_options: Optional[Dict[str, str]] = None,
        temp_dir: Optional[str] = None,
        task_id: Optional[str] = None,
        channel_id: Optional[str] = None,
    ):
        self.workspace_id = workspace_id
        self.config = config or OrderExportConfig()
        self.mapping_custom_fields = mapping_custom_fields or {}
        self.mapping_status_custom_fields = mapping_status_custom_fields or {}
        self.owners = owners or []
        self.dealstage_options = (
            dealstage_options or {}
        )  # label -> internal value mapping
        self.task_id = task_id
        self.channel_id = channel_id

        # Track operation counts for consolidated logging
        self.processed_count = 0
        self.last_logged_count = 0

        # Set up temporary directory
        if temp_dir:
            self.temp_dir = Path(temp_dir)
            if task_id:
                self.temp_dir = self.temp_dir / str(task_id)
            self.temp_dir.mkdir(parents=True, exist_ok=True)
        else:
            base_dir = Path("/tmp/sanka-hubspot-orders")
            if task_id:
                self.temp_dir = base_dir / str(task_id)
            else:
                self.temp_dir = base_dir
            self.temp_dir.mkdir(parents=True, exist_ok=True)

        # Split mappings by object type
        self.deal_mappings = {}
        self.contact_mappings = {}
        self.company_mappings = {}
        self._split_mappings_by_object()

    def _normalize_field_name(self, field_name: str) -> str:
        """
        Normalize field names to HubSpot standard names.

        Args:
            field_name: Original field name

        Returns:
            Normalized field name
        """
        # Common field normalizations
        normalizations = {
            "deal_name": "dealname",
            "deal_stage": "dealstage",
            # Add more normalizations as needed
        }

        return normalizations.get(field_name, field_name)

    def _split_mappings_by_object(self):
        """
        Split field mappings by object type (deal, contact, company).
        Normalizes field names and categorizes them by target object.
        """
        if not self.mapping_custom_fields:
            return

        for hubspot_field, sanka_field in self.mapping_custom_fields.items():
            # Check if the field has an object type suffix
            if "|" in hubspot_field:
                field_name, object_type = hubspot_field.rsplit("|", 1)
                field_name = self._normalize_field_name(field_name)

                if object_type == "deal":
                    self.deal_mappings[field_name] = sanka_field
                elif object_type == "contact":
                    self.contact_mappings[field_name] = sanka_field
                elif object_type == "company":
                    self.company_mappings[field_name] = sanka_field
                else:
                    # Unknown object type, treat as deal field
                    self.deal_mappings[hubspot_field] = sanka_field
            else:
                # No object type specified, treat as deal field
                normalized_name = self._normalize_field_name(hubspot_field)
                self.deal_mappings[normalized_name] = sanka_field

    def _format_date_value(self, value: Any, field_name: str = "") -> Optional[str]:
        """
        Format date values to MM/DD/YYYY format for HubSpot compatibility.

        Args:
            value: The date value to format (string, datetime, or None)
            field_name: Optional field name for context (helps identify date fields)

        Returns:
            Formatted date string in MM/DD/YYYY format or None
        """
        if not value:
            return None

        # Handle datetime objects
        if isinstance(value, datetime):
            return value.strftime("%m/%d/%Y")

        # Handle string dates
        if isinstance(value, str):
            # Check if this looks like a date field
            is_date_field = (
                "date" in field_name.lower() or "closedate" in field_name.lower()
            )

            # Try to parse ISO 8601 format (2020-05-12T01:16:59.000Z)
            if "T" in value and (
                value.endswith("Z") or "+" in value or value.count(":") >= 2
            ):
                try:
                    # Remove timezone info for parsing
                    if value.endswith("Z"):
                        value = value[:-1]
                    elif "+" in value:
                        value = value.split("+")[0]
                    elif "-" in value and value.count("-") > 2:
                        # Handle timezone like -05:00
                        parts = value.rsplit("-", 1)
                        if ":" in parts[-1]:
                            value = parts[0]

                    # Parse the datetime
                    date_obj = datetime.fromisoformat(value)
                    return date_obj.strftime("%m/%d/%Y")
                except (ValueError, TypeError):
                    pass

            # Try to parse YYYY-MM-DD format
            if len(value) == 10 and value[4] == "-" and value[7] == "-":
                try:
                    date_obj = datetime.strptime(value, "%Y-%m-%d")
                    return date_obj.strftime("%m/%d/%Y")
                except (ValueError, TypeError):
                    pass

            # If it's already in MM/DD/YYYY format, return as is
            if len(value) == 10 and value[2] == "/" and value[5] == "/":
                return value

            # For date fields that couldn't be parsed, return None to skip
            if is_date_field:
                # NOTE: Skip logging for now
                # logger.warning(
                #     f"Could not parse date value '{value}' for field '{field_name}'"
                # )
                return None

        return None

    async def export_orders_to_csv(
        self,
        order_ids: Optional[List[str]] = None,
    ) -> List[CSVChunkInfo]:
        """
        Export orders to CSV files.

        Args:
            order_ids: Optional list of specific order IDs to export

        Returns:
            List of CSVChunkInfo objects describing generated files
        """
        logger.info(f"Starting order CSV export for workspace {self.workspace_id}")

        csv_chunks = []
        chunk_number = 1

        # Build queryset with optimizations (synchronously)
        queryset = self._build_orders_queryset(order_ids)

        # Get total count for progress tracking
        logger.debug("Getting total order count...")
        count_start = time.time()
        total_count = await queryset.acount()
        logger.debug(f"Count query took {time.time() - count_start:.2f}s")

        # Early exit if no orders to export
        if total_count == 0:
            logger.info("No orders to export")
            return csv_chunks

        logger.info(f"Exporting {total_count} orders to CSV")

        # Reset processed count for accurate progress tracking
        self.processed_count = 0
        self.last_logged_count = 0

        # Process in chunks
        async for chunk_info in self._export_orders_in_chunks(
            queryset, chunk_number, total_count
        ):
            csv_chunks.append(chunk_info)
            chunk_number += 1

        logger.info(f"Generated {len(csv_chunks)} CSV files for {total_count} orders")
        return csv_chunks

    async def export_line_items_to_csv(
        self,
        order_ids: Optional[List[str]] = None,
    ) -> List[CSVChunkInfo]:
        """
        Export line items to CSV files.

        Args:
            order_ids: Optional list of order IDs to export line items for

        Returns:
            List of CSVChunkInfo objects for line item files
        """
        if not self.config.include_line_items:
            return []

        logger.info(f"Starting line items CSV export for workspace {self.workspace_id}")

        csv_chunks = []
        chunk_number = 1

        # Build line items queryset (synchronously)
        queryset = self._build_line_items_queryset(order_ids)

        # Get total count for progress tracking
        logger.debug("Getting total line items count...")
        total_count = await queryset.acount()

        # Early exit if no line items to export
        if total_count == 0:
            logger.info("No line items to export")
            return csv_chunks

        logger.info(f"Exporting {total_count} line items to CSV")

        # Process in chunks
        async for chunk_info in self._export_line_items_in_chunks(
            queryset, chunk_number, total_count
        ):
            csv_chunks.append(chunk_info)
            chunk_number += 1

        logger.info(
            f"Generated {len(csv_chunks)} CSV files for {total_count} line items"
        )
        return csv_chunks

    def _build_orders_queryset(self, order_ids: Optional[List[str]] = None):
        """Build optimized queryset for orders"""
        queryset = ShopTurboOrders.objects.filter(workspace_id=self.workspace_id)

        if order_ids:
            queryset = queryset.filter(id__in=order_ids)

        # Prefetch related data to avoid N+1 queries
        queryset = queryset.select_related(
            "company",
            "contact",
        ).prefetch_related(
            "order_platforms",
            Prefetch(
                "shopturbo_custom_field_relations",
                queryset=ShopTurboOrdersValueCustomField.objects.select_related(
                    "field_name"
                ),
            ),
        )

        # NOTE: Temporary Replacement from Company ID & Contact ID using Platform
        # Prefetch platform associations if channel_id is available
        # if self.channel_id:
        #     queryset = queryset.prefetch_related(
        #         Prefetch(
        #             "company__company_platform",
        #             queryset=CompanyPlatforms.objects.filter(
        #                 channel_id=self.channel_id
        #             ),
        #             to_attr="channel_platforms",
        #         ),
        #         Prefetch(
        #             "contact__contact_platform",
        #             queryset=ContactsPlatforms.objects.filter(
        #                 channel_id=self.channel_id
        #             ),
        #             to_attr="channel_platforms",
        #         ),
        #     )

        return queryset

    def _build_line_items_queryset(self, order_ids: Optional[List[str]] = None):
        """Build optimized queryset for line items"""
        queryset = ShopTurboItemsOrders.objects.all()

        if order_ids:
            queryset = queryset.filter(order_id__in=order_ids)
        else:
            # Filter by workspace through order relationship
            queryset = queryset.filter(order__workspace_id=self.workspace_id)

        # Prefetch related data
        queryset = queryset.select_related(
            "order",
            "item",
            "item_variant",
            "item_price",
        ).prefetch_related(
            Prefetch(
                "shopturboitemsordersvaluecustomfield_set",
                queryset=ShopTurboItemsOrdersValueCustomField.objects.select_related(
                    "field_name"
                ),
            )
        )

        return queryset

    async def _export_orders_in_chunks(
        self,
        queryset,
        start_chunk_number: int,
        total_count: int,
    ) -> AsyncIterator[CSVChunkInfo]:
        """Export orders in memory-efficient chunks"""

        logger.info(
            f"Starting chunked export: {total_count} total orders, "
            f"chunk size: {self.config.chunk_size}"
        )

        chunk_number = start_chunk_number
        current_file = None
        current_writer = None
        current_row_count = 0
        current_order_count = 0
        headers_written = False
        file_path = None
        offset = 0

        while True:
            # Log batch fetch start
            batch_start = offset
            batch_end = min(offset + self.config.chunk_size, total_count)
            logger.info(
                f"Fetching batch: records {batch_start}-{batch_end} of {total_count}"
            )

            # Time the batch fetch
            fetch_start = time.time()

            # Fetch batch using simple slicing (more efficient)
            batch = await asyncio.to_thread(
                lambda: list(queryset[offset : offset + self.config.chunk_size])
            )

            fetch_time = time.time() - fetch_start

            if not batch:
                logger.debug(f"No more records after offset {offset}")
                break

            logger.info(
                f"Fetched {len(batch)} records in {fetch_time:.2f}s "
                f"({len(batch) / fetch_time:.0f} records/sec)"
            )

            # Process each order in batch
            process_start = time.time()
            for order in batch:
                # Convert order to CSV row (now synchronous)
                csv_row = await self._order_to_csv_row(order)

                # Initialize new file if needed
                if current_file is None:
                    file_path = self.temp_dir / f"orders_chunk_{chunk_number}.csv"
                    current_file = open(file_path, "w", newline="", encoding="utf-8")
                    current_writer = csv.DictWriter(
                        current_file,
                        fieldnames=self._get_csv_headers(),
                        extrasaction="ignore",
                    )

                    if not headers_written or chunk_number == start_chunk_number:
                        current_writer.writeheader()
                        headers_written = True

                # Write row
                current_writer.writerow(csv_row.to_csv_dict())
                current_row_count += 1
                current_order_count += 1

                # Check if we need to start a new file
                file_size = current_file.tell()
                if (
                    current_row_count >= self.config.max_rows_per_file
                    or file_size >= self.config.max_file_size_mb * 1024 * 1024
                ):
                    # Close current file and yield chunk info
                    current_file.close()

                    yield CSVChunkInfo(
                        file_path=str(file_path),
                        row_count=current_row_count,
                        file_size=file_size,
                        order_count=current_order_count,
                        has_header=True,
                        chunk_number=chunk_number,
                    )

                    # Reset for next chunk
                    chunk_number += 1
                    current_file = None
                    current_writer = None
                    current_row_count = 0
                    current_order_count = 0

            # Log processing time
            process_time = time.time() - process_start
            if process_time > 0:
                logger.debug(
                    f"Processed {len(batch)} orders in {process_time:.2f}s "
                    f"({len(batch) / process_time:.0f} orders/sec)"
                )

            # Memory cleanup after processing batch
            processed_batch_count = len(batch)
            self.processed_count += processed_batch_count

            # Log progress periodically (every 500 records)
            if self.processed_count - self.last_logged_count >= 500:
                progress_pct = (
                    (self.processed_count / total_count) * 100 if total_count > 0 else 0
                )
                logger.info(
                    f"Progress: {self.processed_count}/{total_count} orders "
                    f"({progress_pct:.1f}%), current chunk: {chunk_number}"
                )
                self.last_logged_count = self.processed_count

            # Perform garbage collection periodically (every 5000 records)
            if self.processed_count % 5000 == 0:
                db.reset_queries()  # Clear Django query cache
                gc.collect()  # Force garbage collection
                logger.debug(f"Memory cleanup at {self.processed_count} records")

            # Free memory after processing batch
            del batch

            offset += self.config.chunk_size

        # Yield final chunk if exists
        if current_file and current_row_count > 0:
            file_size = current_file.tell()
            current_file.close()

            yield CSVChunkInfo(
                file_path=str(file_path),
                row_count=current_row_count,
                file_size=file_size,
                order_count=current_order_count,
                has_header=True,
                chunk_number=chunk_number,
            )

        # Final memory cleanup
        db.reset_queries()
        gc.collect()
        logger.info(
            f"Orders export completed: {self.processed_count}/{total_count} orders processed"
        )

    async def _export_line_items_in_chunks(
        self,
        queryset,
        start_chunk_number: int,
        total_count: int,
    ) -> AsyncIterator[CSVChunkInfo]:
        """Export line items in memory-efficient chunks"""

        chunk_number = start_chunk_number
        current_file = None
        current_writer = None
        current_row_count = 0
        headers_written = False
        file_path = None
        offset = 0
        processed_line_items = 0

        while True:
            # Log batch fetch start
            batch_start = offset
            batch_end = min(offset + self.config.chunk_size, total_count)
            logger.debug(
                f"Fetching line items batch: records {batch_start}-{batch_end} of {total_count}"
            )

            # Fetch batch using simple slicing
            batch = await asyncio.to_thread(
                lambda: list(queryset[offset : offset + self.config.chunk_size])
            )

            if not batch:
                break

            # Process each line item in batch
            for line_item in batch:
                # Convert line item to CSV row (now synchronous)
                csv_row = self._line_item_to_csv_row(line_item)

                # Initialize new file if needed
                if current_file is None:
                    file_path = self.temp_dir / f"line_items_chunk_{chunk_number}.csv"
                    current_file = open(file_path, "w", newline="", encoding="utf-8")
                    current_writer = csv.DictWriter(
                        current_file,
                        fieldnames=self._get_line_item_fieldnames(csv_row),
                        extrasaction="ignore",
                    )

                    if not headers_written or chunk_number == start_chunk_number:
                        current_writer.writeheader()
                        headers_written = True

                # Write row
                current_writer.writerow(csv_row.to_csv_dict())
                current_row_count += 1

                # Check if we need to start a new file
                file_size = current_file.tell()
                if (
                    current_row_count >= self.config.max_rows_per_file
                    or file_size >= self.config.max_file_size_mb * 1024 * 1024
                ):
                    # Close current file and yield chunk info
                    current_file.close()

                    yield CSVChunkInfo(
                        file_path=str(file_path),
                        row_count=current_row_count,
                        file_size=file_size,
                        order_count=0,  # Line items don't have order count
                        has_header=True,
                        chunk_number=chunk_number,
                    )

                    # Reset for next chunk
                    chunk_number += 1
                    current_file = None
                    current_writer = None
                    current_row_count = 0

            # Memory cleanup after processing batch
            processed_batch_count = len(batch)
            processed_line_items += processed_batch_count

            # Log progress periodically (every 500 records)
            if processed_line_items % 500 == 0 and processed_line_items > 0:
                progress_pct = (
                    (processed_line_items / total_count) * 100 if total_count > 0 else 0
                )
                logger.info(
                    f"Line items progress: {processed_line_items}/{total_count} "
                    f"({progress_pct:.1f}%)"
                )

            # Free memory after processing batch
            del batch

            # Perform periodic garbage collection
            if processed_line_items % 5000 == 0 and processed_line_items > 0:
                db.reset_queries()  # Clear Django query cache
                gc.collect()  # Force garbage collection
                logger.debug(f"Memory cleanup at {processed_line_items} line items")

            offset += self.config.chunk_size

        # Yield final chunk if exists
        if current_file and current_row_count > 0:
            file_size = current_file.tell()
            current_file.close()

            yield CSVChunkInfo(
                file_path=str(file_path),
                row_count=current_row_count,
                file_size=file_size,
                order_count=0,
                has_header=True,
                chunk_number=chunk_number,
            )

        # Final memory cleanup for line items
        db.reset_queries()
        gc.collect()
        logger.info(
            f"Line items export completed: {processed_line_items}/{total_count} processed"
        )

    def _extract_order_custom_fields(self, order: ShopTurboOrders) -> Dict[str, Any]:
        """Extract custom fields from order using prefetched data"""
        custom_fields = {}

        # Only extract fields that are mapped as deal fields
        if not self.deal_mappings:
            return custom_fields

        # Use prefetched custom field relations (no new queries)
        for custom_value in order.shopturbo_custom_field_relations.all():
            if custom_value.field_name and custom_value.field_name.name:
                field_name = custom_value.field_name.name
                # Check if this field is mapped as a deal field
                if field_name in self.deal_mappings.values():
                    # Find the HubSpot property name for this Sanka field
                    for hubspot_prop, sanka_field in self.deal_mappings.items():
                        if sanka_field == field_name:
                            # Check if we have a value to process
                            if custom_value.value_time:
                                # Handle datetime field
                                formatted_date = self._format_date_value(
                                    custom_value.value_time, hubspot_prop
                                )
                                if formatted_date:
                                    custom_fields[hubspot_prop] = formatted_date
                            elif custom_value.value_number is not None:
                                # Handle numeric field
                                custom_fields[hubspot_prop] = custom_value.value_number
                            elif custom_value.value:
                                # Try to format as date if it looks like a date
                                formatted_date = self._format_date_value(
                                    custom_value.value, hubspot_prop
                                )
                                if formatted_date:
                                    custom_fields[hubspot_prop] = formatted_date
                                else:
                                    # Not a date, use as is
                                    custom_fields[hubspot_prop] = custom_value.value
                            break

        return custom_fields

    async def _order_to_csv_row(self, order: ShopTurboOrders) -> OrderCSVRow:
        """Convert a ShopTurboOrders instance to OrderCSVRow - optimized async version"""

        # Get platform data from prefetched relation (no new query)
        platform_data = None
        for platform in order.order_platforms.all():  # Using prefetched data
            platform_data = platform
            break  # Take first platform

        # Extract custom fields first to check for owner_email
        custom_fields = {}
        owner_email = None

        if self.config.include_custom_fields:
            custom_fields = self._extract_order_custom_fields(order)

            # Check if owner_email is in custom fields
            if "owner_email" in custom_fields:
                owner_email = custom_fields.get("owner_email")
                # Remove owner_email from custom fields as we'll handle it separately
                del custom_fields["owner_email"]

        # Implement dealname fallback logic
        # Priority: order_id → platform_order_id → UUID
        dealname = None
        if order.order_id:
            dealname = f"Order #{order.order_id}"
        elif platform_data and platform_data.platform_order_id:
            dealname = f"Order #{platform_data.platform_order_id}"
        else:
            # Use UUID as last resort
            dealname = f"Order {str(order.id)[:8]}"  # Use first 8 chars of UUID for readability

        # Handle dealstage mapping if configured
        dealstage = None
        if "dealstage" in self.deal_mappings and self.dealstage_options:
            # Get the dealstage value from custom fields or order status
            sanka_field = self.deal_mappings.get("dealstage")
            raw_dealstage = custom_fields.get(sanka_field) or order.status

            if raw_dealstage and self.dealstage_options:
                # Map label to internal value
                dealstage = self.dealstage_options.get(raw_dealstage, raw_dealstage)
                # Remove from custom fields to avoid duplication
                if sanka_field in custom_fields:
                    del custom_fields[sanka_field]

        if self.mapping_status_custom_fields:
            if order.delivery_status:
                dealstage = self.mapping_status_custom_fields.get(order.delivery_status)

        if not dealstage:
            # Fallback to order status if no mapping
            dealstage = order.status or "active"

        # Build base row
        csv_row = OrderCSVRow(
            sanka_record_id=str(order.id),
            dealname=dealname,  # Now with proper fallback
            amount=order.total_price,
            closedate=order.order_at,
            dealstage=dealstage,  # Now with proper mapping
            pipeline="default",  # Can be customized
            order_id=order.order_id,
            platform=order.platform,
            platform_order_id=platform_data.platform_order_id
            if platform_data
            else None,
            currency=order.currency,
            tax=order.tax,
            total_price_without_tax=order.total_price_without_tax,
            shipping_cost=None,  # TODO: Get from shipping_cost relationship if needed
            discount_amount=None,  # TODO: Get from discounts relationship if needed
            delivery_status=order.delivery_status,
            order_type=order.order_type,
            memo=order.memo,
            company_sanka_id=str(order.company.id) if order.company else None,
            contact_sanka_id=str(order.contact.id) if order.contact else None,
        )

        # Handle HubSpot owner assignment from email
        if owner_email and self.owners:
            # Find owner by email
            for owner in self.owners:
                if owner.get("email") == owner_email:
                    custom_fields["hubspot_owner_id"] = owner.get("id")
                    break

        if order.company and self.channel_id:
            # Use prefetched platform data if available
            custom_fields["company__association"] = str(order.company.id)
            # NOTE: Temporary Replacement from Company ID
            # if hasattr(order.company, 'channel_platforms'):
            #     # Prefetched data is available as a list
            #     if order.company.channel_platforms:
            #         company_platform = order.company.channel_platforms[0]
            #         if company_platform:
            #             custom_fields["company__association"] = str(company_platform.platform_id)
            # else:
            #     # Fallback to query if prefetch wasn't applied (shouldn't happen normally)
            #     company_platform = await CompanyPlatforms.objects.filter(
            #         company=order.company, channel_id=self.channel_id
            #     ).afirst()
            #     if company_platform:
            #         custom_fields["company__association"] = str(company_platform.platform_id)

        if order.contact and self.channel_id:
            custom_fields["contact__association"] = str(order.contact.id)
            # NOTE: Temporary Replacement from Contact ID
            # Use prefetched platform data if available
            # if hasattr(order.contact, 'channel_platforms'):
            #     # Prefetched data is available as a list
            #     if order.contact.channel_platforms:
            #         contact_platform = order.contact.channel_platforms[0]
            #         if contact_platform:
            #             custom_fields["contact__association"] = str(contact_platform.platform_id)
            # else:
            #     # Fallback to query if prefetch wasn't applied (shouldn't happen normally)
            #     contact_platform = await ContactsPlatforms.objects.filter(
            #         contact=order.contact, channel_id=self.channel_id
            #     ).afirst()
            #     if contact_platform:
            #         custom_fields["contact__association"] = str(contact_platform.platform_id)

        # Add remaining custom fields
        csv_row.custom_fields = custom_fields

        return csv_row

    def _line_item_to_csv_row(self, line_item: ShopTurboItemsOrders) -> LineItemCSVRow:
        """Convert a ShopTurboItemsOrders instance to LineItemCSVRow - optimized synchronous version"""

        csv_row = LineItemCSVRow(
            sanka_record_id=str(line_item.id),
            order_sanka_id=str(line_item.order.id),
            item_sanka_id=str(line_item.item.id) if line_item.item else None,
            name=line_item.custom_item_name
            or (line_item.item.name if line_item.item else None),
            description=line_item.item.description if line_item.item else None,
            quantity=line_item.number_item,
            price=line_item.item_price_order,
            amount=line_item.total_price,
            sku=line_item.item.sku if line_item.item else None,
            platform_item_id=line_item.platform_item_id,
            item_status=line_item.item_status,
            currency=line_item.currency,
        )

        # Add custom fields for line items if needed
        if self.config.include_custom_fields:
            custom_fields = {}
            # Check if line items have custom fields (similar to orders)
            # This would need the proper relationship if it exists
            csv_row.custom_fields = custom_fields

        return csv_row

    def _get_csv_headers(self) -> List[str]:
        """Get CSV headers based on deal mappings only."""
        headers = ["sanka_record_id"]

        # Add standard deal fields that are always included
        standard_fields = [
            "dealname",
            "amount",
            "closedate",
            "pipeline",
            "currency",
            "contact__association",
            "company__association",
        ]
        headers.extend(standard_fields)

        # Only include dealstage if we have mapping for it and options available
        if "dealstage" in self.deal_mappings and self.dealstage_options:
            headers.append("dealstage")

        # Add mapped deal custom fields from deal_mappings
        if self.deal_mappings:
            for hubspot_field in self.deal_mappings.keys():
                # Skip standard fields already added
                if (
                    hubspot_field not in headers
                    and hubspot_field not in standard_fields
                ):
                    headers.append(hubspot_field)

        # Add hubspot_owner_id if mapped in deal mappings
        if "hubspot_owner_id" in self.deal_mappings:
            if "hubspot_owner_id" not in headers:
                headers.append("hubspot_owner_id")

        return headers

    def _get_line_item_fieldnames(self, sample_row: LineItemCSVRow) -> List[str]:
        """Get all fieldnames for line item CSV"""
        base_fields = list(sample_row.to_csv_dict().keys())
        return sorted(base_fields)

    def cleanup_temp_files(self):
        """Clean up temporary CSV files"""
        try:
            import shutil

            if self.temp_dir.exists():
                # Only clean up task-specific directory if task_id is used
                # Otherwise just clean up the CSV files
                if self.task_id:
                    shutil.rmtree(self.temp_dir)
                    logger.info(f"Cleaned up task directory: {self.temp_dir}")
                else:
                    # Clean up CSV files only
                    for csv_file in self.temp_dir.glob("*.csv"):
                        csv_file.unlink()
                    logger.info(f"Cleaned up CSV files in {self.temp_dir}")
        except Exception as e:
            logger.error(f"Error cleaning up temp files: {e}")

    async def export_contacts_to_csv(
        self,
        order_ids: Optional[List[str]] = None,
    ) -> List[CSVChunkInfo]:
        """
        Export contacts related to orders to CSV files.

        Args:
            order_ids: Optional list of order IDs to export related contacts for

        Returns:
            List of CSVChunkInfo objects for contact files
        """
        try:
            # Only export if we have contact field mappings
            if not self.contact_mappings:
                logger.info("No contact field mappings, skipping contact export")
                return []

            logger.info(
                f"Starting contact CSV export for workspace {self.workspace_id}"
            )
            logger.debug(f"Contact mappings: {self.contact_mappings}")

            csv_chunks = []

            # Import Contact model

            # Get unique contact IDs from orders
            queryset = self._build_orders_queryset(order_ids)
            contact_ids = set()

            # Get all unique contact IDs from orders
            # Load orders into memory first (queryset has prefetch_related)
            logger.debug("Loading orders to collect contact IDs...")
            orders_list = await asyncio.to_thread(
                lambda: list(queryset.select_related("contact"))
            )
            for order in orders_list:
                if order.contact_id:
                    contact_ids.add(str(order.contact_id))
            logger.debug(
                f"Found {len(contact_ids)} unique contacts from {len(orders_list)} orders"
            )

            if not contact_ids:
                logger.info("No contacts found in orders")
                return []

            logger.info(f"Exporting {len(contact_ids)} unique contacts to CSV")

            # Build CSV file
            file_path = self.temp_dir / "contacts.csv"

            # Get contacts with custom fields
            contacts = Contact.objects.filter(
                id__in=list(contact_ids), workspace_id=self.workspace_id
            ).prefetch_related(
                Prefetch(
                    "contact_custom_field_relations",
                    queryset=ContactsValueCustomField.objects.select_related(
                        "field_name"
                    ),
                )
            )

            rows_written = 0
            file_size = 0

            # Load all contacts into memory (they're already filtered by IDs)
            logger.debug("Loading contacts from database...")
            contacts_list = await asyncio.to_thread(lambda: list(contacts))
            logger.debug(f"Loaded {len(contacts_list)} contacts")

            with open(file_path, "w", newline="", encoding="utf-8") as csv_file:
                writer = None
                headers_written = False

                for contact in contacts_list:
                    # Convert contact to CSV row
                    csv_row = self._contact_to_csv_row(contact)

                    if not headers_written:
                        # Determine headers based on available data
                        headers = self._get_contact_csv_headers(csv_row)
                        writer = csv.DictWriter(
                            csv_file, fieldnames=headers, extrasaction="ignore"
                        )
                        writer.writeheader()
                        headers_written = True

                    writer.writerow(csv_row.to_csv_dict())
                    rows_written += 1

                    # Progress logging
                    if rows_written % 1000 == 0:
                        logger.info(f"Written {rows_written} contact rows")

            file_size = file_path.stat().st_size

            chunk_info = CSVChunkInfo(
                file_path=str(file_path),
                row_count=rows_written,
                file_size=file_size,
                order_count=0,  # Not applicable for contacts
                chunk_number=1,
            )
            csv_chunks.append(chunk_info)

            logger.info(f"Exported {rows_written} contacts to {file_path}")
            return csv_chunks
        except Exception as e:
            logger.error(f"Error exporting contacts to CSV: {str(e)}", exc_info=True)
            raise

    async def export_companies_to_csv(
        self,
        order_ids: Optional[List[str]] = None,
    ) -> List[CSVChunkInfo]:
        """
        Export companies related to orders to CSV files.

        Args:
            order_ids: Optional list of order IDs to export related companies for

        Returns:
            List of CSVChunkInfo objects for company files
        """
        try:
            # Only export if we have company field mappings
            if not self.company_mappings:
                logger.info("No company field mappings, skipping company export")
                return []

            logger.info(
                f"Starting company CSV export for workspace {self.workspace_id}"
            )
            logger.debug(f"Company mappings: {self.company_mappings}")

            csv_chunks = []

            # Import Company model

            # Get unique company IDs from orders
            queryset = self._build_orders_queryset(order_ids)
            company_ids = set()

            # Get all unique company IDs from orders
            # Load orders into memory first (queryset has prefetch_related)
            logger.debug("Loading orders to collect company IDs...")
            orders_list = await asyncio.to_thread(
                lambda: list(queryset.select_related("company"))
            )
            for order in orders_list:
                if order.company_id:
                    company_ids.add(str(order.company_id))
            logger.debug(
                f"Found {len(company_ids)} unique companies from {len(orders_list)} orders"
            )

            if not company_ids:
                logger.info("No companies found in orders")
                return []

            logger.info(f"Exporting {len(company_ids)} unique companies to CSV")

            # Build CSV file
            file_path = self.temp_dir / "companies.csv"

            # Get companies with custom fields
            companies = Company.objects.filter(
                id__in=list(company_ids), workspace_id=self.workspace_id
            ).prefetch_related(
                Prefetch(
                    "company_custom_field_relations",
                    queryset=CompanyValueCustomField.objects.select_related(
                        "field_name"
                    ),
                )
            )

            rows_written = 0
            file_size = 0

            # Load all companies into memory (they're already filtered by IDs)
            logger.debug("Loading companies from database...")
            companies_list = await asyncio.to_thread(lambda: list(companies))
            logger.debug(f"Loaded {len(companies_list)} companies")

            with open(file_path, "w", newline="", encoding="utf-8") as csv_file:
                writer = None
                headers_written = False

                for company in companies_list:
                    # Convert company to CSV row
                    csv_row = self._company_to_csv_row(company)

                    if not headers_written:
                        # Determine headers based on available data
                        headers = self._get_company_csv_headers(csv_row)
                        writer = csv.DictWriter(
                            csv_file, fieldnames=headers, extrasaction="ignore"
                        )
                        writer.writeheader()
                        headers_written = True

                    writer.writerow(csv_row.to_csv_dict())
                    rows_written += 1

                    # Progress logging
                    if rows_written % 1000 == 0:
                        logger.info(f"Written {rows_written} company rows")

            file_size = file_path.stat().st_size

            chunk_info = CSVChunkInfo(
                file_path=str(file_path),
                row_count=rows_written,
                file_size=file_size,
                order_count=0,  # Not applicable for companies
                chunk_number=1,
            )
            csv_chunks.append(chunk_info)

            logger.info(f"Exported {rows_written} companies to {file_path}")
            return csv_chunks
        except Exception as e:
            logger.error(f"Error exporting companies to CSV: {str(e)}", exc_info=True)
            raise

    def _contact_to_csv_row(self, contact) -> ContactCSVRow:
        """
        Convert a Contact object to ContactCSVRow.

        Args:
            contact: Contact object

        Returns:
            ContactCSVRow object
        """
        csv_row = ContactCSVRow(
            sanka_object_id=str(contact.id),
            firstname=contact.name,  # Contact model uses 'name' field
            lastname=contact.last_name,
            email=contact.email,
            phone=contact.phone_number,  # Contact model uses 'phone_number' field
            company=contact.company,  # Contact.company is a CharField, not a ForeignKey
        )

        # Add mapped custom fields
        for hubspot_field, sanka_field in self.contact_mappings.items():
            # Extract custom field value from contact
            value = self._extract_contact_custom_field(contact, sanka_field)
            if value is not None:
                csv_row.custom_fields[hubspot_field] = value

        return csv_row

    def _company_to_csv_row(self, company) -> CompanyCSVRow:
        """
        Convert a Company object to CompanyCSVRow.

        Args:
            company: Company object

        Returns:
            CompanyCSVRow object
        """
        csv_row = CompanyCSVRow(
            sanka_object_id=str(company.id),
            name=company.name,
            domain=company.url
            if hasattr(company, "url")
            else None,  # Company uses 'url' field
            phone=company.phone_number,
            industry=getattr(company, "industry", None),  # May not exist
            city=getattr(company, "city", None),  # May not exist
            state=getattr(company, "state", None),  # May not exist
            country=getattr(company, "country", None),  # May not exist
        )

        # Add mapped custom fields
        for hubspot_field, sanka_field in self.company_mappings.items():
            # Extract custom field value from company
            value = self._extract_company_custom_field(company, sanka_field)
            if value is not None:
                csv_row.custom_fields[hubspot_field] = value

        return csv_row

    def _extract_contact_custom_field(self, contact, field_name: str):
        """Extract custom field value from contact."""
        # Check standard fields first
        if hasattr(contact, field_name):
            value = getattr(contact, field_name)
            # Format dates if needed
            formatted_date = self._format_date_value(value, field_name)
            return formatted_date if formatted_date else value

        # Check custom fields using prefetched relations
        for custom_value in contact.contact_custom_field_relations.all():
            if custom_value.field_name and custom_value.field_name.name == field_name:
                # Handle different value types
                if custom_value.value_time:
                    # Handle datetime field
                    formatted_date = self._format_date_value(
                        custom_value.value_time, field_name
                    )
                    return formatted_date if formatted_date else custom_value.value_time
                elif custom_value.value_number is not None:
                    # Handle numeric field
                    return custom_value.value_number
                elif custom_value.value:
                    # Try to format as date if it looks like a date
                    formatted_date = self._format_date_value(
                        custom_value.value, field_name
                    )
                    return formatted_date if formatted_date else custom_value.value

        return None

    def _extract_company_custom_field(self, company, field_name: str):
        """Extract custom field value from company."""
        # Check standard fields first
        if hasattr(company, field_name):
            value = getattr(company, field_name)
            # Format dates if needed
            formatted_date = self._format_date_value(value, field_name)
            return formatted_date if formatted_date else value

        # Check custom fields using prefetched relations
        for custom_value in company.company_custom_field_relations.all():
            if custom_value.field_name and custom_value.field_name.name == field_name:
                # Handle different value types
                if custom_value.value_time:
                    # Handle datetime field
                    formatted_date = self._format_date_value(
                        custom_value.value_time, field_name
                    )
                    return formatted_date if formatted_date else custom_value.value_time
                elif custom_value.value_number is not None:
                    # Handle numeric field
                    return custom_value.value_number
                elif custom_value.value:
                    # Try to format as date if it looks like a date
                    formatted_date = self._format_date_value(
                        custom_value.value, field_name
                    )
                    return formatted_date if formatted_date else custom_value.value

        return None

    def _get_contact_csv_headers(self, sample_row: ContactCSVRow) -> List[str]:
        """Get CSV headers for contact export based on available data."""
        headers = ["sanka_object_id"]

        # Add standard fields that have values
        if sample_row.firstname:
            headers.append("firstname")
        if sample_row.lastname:
            headers.append("lastname")
        if sample_row.email:
            headers.append("email")
        if sample_row.phone:
            headers.append("phone")
        if sample_row.company:
            headers.append("company")

        # Add custom field headers
        headers.extend(sorted(sample_row.custom_fields.keys()))

        return headers

    def _get_company_csv_headers(self, sample_row: CompanyCSVRow) -> List[str]:
        """Get CSV headers for company export based on available data."""
        headers = ["sanka_object_id"]

        # Add standard fields that have values
        if sample_row.name:
            headers.append("name")
        if sample_row.domain:
            headers.append("domain")
        if sample_row.phone:
            headers.append("phone")
        if sample_row.industry:
            headers.append("industry")
        if sample_row.city:
            headers.append("city")
        if sample_row.state:
            headers.append("state")
        if sample_row.country:
            headers.append("country")

        # Add custom field headers
        headers.extend(sorted(sample_row.custom_fields.keys()))

        return headers
