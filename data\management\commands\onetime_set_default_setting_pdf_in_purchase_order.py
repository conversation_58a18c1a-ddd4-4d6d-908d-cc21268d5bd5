from django.core.management.base import BaseCommand
from data.models import *
from data.constants.properties_constant import *
from utils.makeshop import pull_makeshop_items
from utils.utility import is_valid_uuid
import json

class Command(BaseCommand):
    def handle(self, *args, **options):
        workspaces = Workspace.objects.all()
        for workspace in workspaces:
            app_setting = AppSetting.objects.filter(
                workspace=workspace, app_target='shopturbo').first()
            if app_setting:
                if app_setting.purchase_order_note:
                    app_setting_child = AppSettingChild.objects.filter(
                        app_setting=app_setting, target_name=TYPE_OBJECT_PURCHASE_ORDER + '_note').first()
                    if not app_setting_child:
                        app_setting_child = AppSettingChild.objects.create(
                            app_setting=app_setting, target_name=TYPE_OBJECT_PURCHASE_ORDER + '_note')

                    app_setting_child.value = app_setting.purchase_order_note
                    app_setting_child.save()
                if app_setting.purchase_order_send_from:
                    app_setting_child = AppSettingChild.objects.filter(
                        app_setting=app_setting, target_name=TYPE_OBJECT_PURCHASE_ORDER + '_send_from').first()
                    if not app_setting_child:
                        app_setting_child = AppSettingChild.objects.create(
                            app_setting=app_setting, target_name=TYPE_OBJECT_PURCHASE_ORDER + '_send_from')

                    app_setting_child.value = app_setting.purchase_order_send_from
                    app_setting_child.save()
                if app_setting.purchase_order_tax:
                    app_setting_child = AppSettingChild.objects.filter(
                        app_setting=app_setting, target_name=TYPE_OBJECT_PURCHASE_ORDER + '_tax').first()
                    if not app_setting_child:
                        app_setting_child = AppSettingChild.objects.create(
                            app_setting=app_setting, target_name=TYPE_OBJECT_PURCHASE_ORDER + '_tax')

                    app_setting_child.value = app_setting.purchase_order_tax
                    app_setting_child.save()
                
            
