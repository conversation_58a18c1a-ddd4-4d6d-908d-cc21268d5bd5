import ast
from datetime import datetime, timedelta
import json
import traceback

import aiohttp
from hatchet_sdk import Context, WorkflowRunRef

from data.models import Channel, TransferHistory

from ..workflows import import_bcart_orders_subprocess

# from utils.bcart import import_bcart_orders as bcart_import_util
from utils.bgjobs.handler import (
    aio_set_bg_job_completed,
    aio_set_bg_job_failed,
    aio_set_bg_job_running,
)
from utils.logger import logger

from ..models import ImportBcartOrdersSubprocessPayload, ImportOrdersBcartPayload
from ..workflows import import_bcart_orders


@import_bcart_orders.task(
    name="ImportBcartOrdersTask",
    execution_timeout=timedelta(hours=12),
    schedule_timeout=timedelta(hours=1),
)
async def import_bcart_orders_task(
    input: ImportOrdersBcartPayload, ctx: Context
) -> dict:
    """
    Child task for importing B-Cart orders
    """
    logger.info("Run B-Cart orders import child task")
    logger.info(f"Input: {input}")

    history_id = input.history_id
    task = None

    try:
        task = await TransferHistory.objects.select_related("workspace", "user").aget(
            id=history_id
        )
        task.keep_alive = True
        await task.asave()
    except TransferHistory.DoesNotExist:
        logger.warning("TransferHistory does not exist")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])

    if input.background_job_id:
        await aio_set_bg_job_running(input.background_job_id)

    try:
        # Process mapping fields
        mapping_custom_fields = (
            input.mapping_custom_fields
            if input.mapping_custom_fields and input.mapping_custom_fields != "None"
            else {}
        )

        # Parse string mappings if needed
        if isinstance(mapping_custom_fields, str):
            mapping_custom_fields = ast.literal_eval(mapping_custom_fields)

        # Call the B-Cart import utility
        await bcart_pagination_async(
            input.channel_id,
            mapping_custom_fields,
            key_item_field=input.key_item_field,
            key_customer_field=input.key_customer_field,
            how_to_import=input.how_to_import,
            how_to_import_customer=input.how_to_import_customer,
            import_filter=input.import_filter,
            history_id=task.id,
        )
        # await bcart_import_util(
        #     input.channel_id,
        #     mapping_custom_fields,
        #     key_item_field=input.key_item_field,
        #     key_customer_field=input.key_customer_field,
        #     how_to_import=input.how_to_import,
        #     how_to_import_customer=input.how_to_import_customer,
        #     import_filter=input.import_filter,
        # )

        logger.info("Successfully imported B-Cart orders")

        if task:
            await task.arefresh_from_db()
            task.status = "completed"
            await task.asave(update_fields=["status"])

        if input.background_job_id:
            await aio_set_bg_job_completed(input.background_job_id)

    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error importing Shopify orders: {str(e)}")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])


async def bcart_pagination_async(
    channel_id,
    mapping_custom_fields,
    key_item_field,
    key_customer_field,
    how_to_import,
    how_to_import_customer,
    import_filter,
    history_id,
) -> bool:
    """
    Handle B-cart pagination by spawning child workflows for each page (async version)
    """
    try:
        has_more = True
        page_count = 0
        total_order_count = 0
        order_count = 100

        logger.info("Starting bcart pagination")

        bcart_task_result = []

        offset = 0
        task = (
            await TransferHistory.objects.filter(id=history_id)
            .order_by("-created_at")
            .select_related("user")
            .afirst()
        )
        while has_more:
            page_count += 1
            logger.info(f"Processing page {page_count}")

            try:
                channel = await Channel.objects.select_related(
                    "workspace", "integration"
                ).aget(id=channel_id)
                access_token = (
                    channel.access_token
                    + channel.page_access_token
                    + channel.refresh_token
                    + channel.ms_refresh_token
                )
                order_url = "https://api.bcart.jp/api/v1/orders"
                # platform = "b-cart"
                workspace = channel.workspace
                task = (
                    await TransferHistory.objects.filter(id=history_id)
                    .order_by("-created_at")
                    .afirst()
                )

                try:
                    async with aiohttp.ClientSession() as session:
                        if order_count < 100:
                            has_more = False
                            logger.info(
                                "No more orders to process, breaking pagination loop"
                            )

                        headers = {"Authorization": f"Bearer {access_token}"}
                        order_param = {"limit": 100, "offset": offset, "complete": 1}

                        start_datetime = None
                        end_datetime = None
                        if import_filter:
                            try:
                                if import_filter.get("created_at"):
                                    key = import_filter["created_at"].get("key")
                                    value = import_filter["created_at"].get("value")
                                    if isinstance(value, str):
                                        value = datetime.strptime(value, "%Y-%m-%d")
                                    if key == "start_date":
                                        start_datetime = value
                                    elif key == "end_date":
                                        end_datetime = value
                                elif import_filter.get("updated_at"):
                                    key = import_filter["updated_at"].get("key")
                                    value = import_filter["updated_at"].get("value")
                                    if isinstance(value, str):
                                        value = datetime.strptime(value, "%Y-%m-%d")
                                    if key == "start_date":
                                        start_datetime = value
                                    elif key == "end_date":
                                        end_datetime = value
                            except:
                                pass

                        if start_datetime:
                            order_param["ordered_at__gte"] = start_datetime.strftime(
                                "%Y-%m-%d %H:%M:%S"
                            )
                        if end_datetime:
                            order_param["ordered_at__lte"] = end_datetime.strftime(
                                "%Y-%m-%d %H:%M:%S"
                            )

                        async with session.get(
                            order_url, headers=headers, params=order_param
                        ) as resp:
                            data = await resp.json()
                            print("order request: ", data)
                            order_data = data.get("orders", [])

                        order_count = len(order_data)
                        total_order_count += order_count
                        logger.info(
                            f"INFO === Fetched {len(order_data)} orders from B-Cart API page {page_count}, total orders {total_order_count}"
                        )

                    # Create sub-history for this page
                    sub_history = await TransferHistory.objects.acreate(
                        workspace=workspace,
                        user=task.user if task and task.user else None,
                        status="pending",
                        type="import_orders",
                        name=f"Import B-cart Deals. Subprocess of {task.id if task else 'unknown'}",
                        parent=task,
                    )

                    # Create payload for subprocess
                    subprocess_payload = ImportBcartOrdersSubprocessPayload(
                        channel_id=channel_id,
                        mapping_custom_fields=json.dumps(mapping_custom_fields),
                        key_item_field=key_item_field,
                        key_customer_field=key_customer_field,
                        how_to_import=how_to_import,
                        how_to_import_customer=how_to_import_customer,
                        import_filter=import_filter,
                        history_id=str(history_id),
                        sub_history_id=str(sub_history.id),
                        order_datas=json.dumps(order_data),
                    )

                    # Call the subprocess task directly
                    hatchet_task = await import_bcart_orders_subprocess.aio_run_no_wait(
                        subprocess_payload
                    )

                    logger.info(
                        f"[DEBUG] New bcart subprocess is running with id: {hatchet_task.workflow_run_id}"
                    )
                    offset += 100

                    await TransferHistory.objects.filter(
                        id=task.id,
                        total_number__lt=total_order_count.aupdate(
                            total_number=total_order_count
                        ),
                    )

                    bcart_task_result.append(hatchet_task)
                except Exception as e:
                    logger.error(f"Error fetching orders from B-Cart API {e}")
                    page_count -= 1

            except Exception:
                logger.error(
                    f"Error in pagination loop traceback: {traceback.format_exc()}"
                )
                return False

        all_success = True
        try:
            # Waiting for all subprocesses to complete
            results = []
            logger.info(
                f"Waiting for {len(bcart_task_result)} B-Cart subprocesses to complete"
            )
            for task in bcart_task_result:
                assert isinstance(task, WorkflowRunRef), (
                    "Expected task to be of type WorkflowRunRef"
                )
                result = await task.aio_result()
                result = (
                    result.get("importbcartorderssubprocesstask", result)
                    if isinstance(result, dict)
                    else result
                )

                results.append(result)

            # Check results of all subprocesses
            logger.info(f"Checking results of {len(results)} B-Cart subprocesses")
            for index, result in enumerate(results):
                logger.info(f"Subprocess at index {index} result: {result}")
                if not result.get("success", False):
                    error_message = result.get("error", "Unknown error")
                    logger.error(f"Subprocess at index {index} failed: {error_message}")
                    all_success = False
        except Exception:
            logger.error(
                f"Error waiting for B-Cart subprocesses: {traceback.format_exc()}"
            )

        logger.info(f"Successfully processed {page_count} pages")
        return all_success

    except Exception:
        logger.error(f"Error in bcart_pagination_async: {traceback.format_exc()}")
        return False
