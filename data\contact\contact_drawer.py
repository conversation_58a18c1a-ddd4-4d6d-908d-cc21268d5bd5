import ast
import re
from urllib.parse import unquote
import uuid

from django.core.paginator import Paginator
from django.db.models import Q
from django.http import HttpResponse
from django.shortcuts import render

from data.constants.constant import (
    COUNTRY_CODE,
    DEALS_COLUMNS_DISPLAY,
    DEFAULT_COLUMNS_CASE,
    DEFAULT_COLUMNS_COMPANY,
    DEFAULT_COLUMNS_CONTACTS,
    DEFAULT_PERMISSION,
    EXCLUDE_SYNC_CHANNEL_NAME,
)
from data.constants.properties_constant import (
    TYPE_OBJECT_CASE,
    TYPE_OBJECT_COMPANY,
    TYPE_OBJECT_CONTACT,
)
from data.models import (
    AppLog,
    AppSetting,
    Association,
    AssociationLabel,
    CASE_STATUS,
    Channel,
    Company,
    CompanyList,
    CompanyNameCustomField,
    Contact,
    ContactLineChannel,
    ContactList,
    ContactsNameCustomField,
    CustomProperty,
    DEALS_NUMBER_FORMAT,
    Deals,
    DealsNameCustomField,
    Estimate,
    ExportTemplate,
    InventoryWarehouse,
    Invoice,
    Message,
    MessageBodyTemplate,
    Notes,
    PropertySet,
    ShopTurboItems,
    ShopTurboSubscriptions,
    VIEW_MODE,
    View,
    ViewFilter,
)
from utils.association_label_utils import get_association_label_display_name
from utils.decorator import login_or_hubspot_required
from utils.properties.page_object import get_page_object
from utils.properties.properties import (
    get_default_prompt_set,
    get_default_property_set,
    get_properties_with_details,
)
from utils.utility import apply_item_search_setting, get_workspace, is_valid_uuid
from utils.workspace import get_permission
from utils.logger import logger


@login_or_hubspot_required
def load_drawer(request):
    from data.constants.properties_constant import TYPE_OBJECT_CASE
    drawer_type = request.GET.get("drawer_type", False)
    workspace = get_workspace(request.user)
    companies = Company.objects.filter(workspace=workspace)
    lang = request.LANGUAGE_CODE

    if drawer_type == "contacts":
        section = request.GET.get("section", None)
        predefined_data = request.GET.getlist("predefined-data", [])
        add_on = request.GET.get("addon_source", None)

        from_object = request.GET.get("from", None)

        set_id = request.GET.get("set_id", None)
        view_id = request.GET.get("view_id", None)

        permission = get_permission(TYPE_OBJECT_CONTACT, request.user)
        if not permission:
            permission = DEFAULT_PERMISSION

        if set_id:
            property_set = PropertySet.objects.filter(id=set_id).first()
        else:
            if view_id == "None":
                view_id = None
            if view_id:
                view = View.objects.get(id=view_id)
                if view.form and not from_object:
                    property_set = view.form
                else:
                    condition_filter = Q(
                        workspace=workspace, target=TYPE_OBJECT_CONTACT
                    )
                    condition_filter &= Q(as_default=True) | Q(name__isnull=True)
                    property_set = PropertySet.objects.filter(condition_filter).first()
            else:
                condition_filter = Q(workspace=workspace, target=TYPE_OBJECT_CONTACT)
                condition_filter &= Q(as_default=True) | Q(name__isnull=True)
                property_set = PropertySet.objects.filter(condition_filter).first()

        if section == "create" and not property_set:
            _ = get_default_property_set(TYPE_OBJECT_CONTACT, workspace, lang)
            property_set = (
                PropertySet.objects.filter(
                    workspace=workspace, target=TYPE_OBJECT_CONTACT
                )
                .order_by("created_at")
                .first()
            )

        properties = {"list_all": []}
        if property_set:
            for p in property_set.children:
                properties["list_all"].append(p)

        CustomFieldMap = {}
        explorecustomfield = ContactsNameCustomField.objects.filter(
            workspace=workspace
        ).order_by("order")
        for ctf in explorecustomfield:
            CustomFieldMap[str(ctf.id)] = ctf

        if add_on:
            properties, CustomFieldMap = get_default_prompt_set(
                workspace, TYPE_OBJECT_CONTACT, ContactsNameCustomField
            )

        property_sets = PropertySet.objects.filter(
            workspace=workspace, target=TYPE_OBJECT_CONTACT
        ).order_by("created_at")

        association_labels_sanka_true = AssociationLabel.objects.filter(
            workspace=workspace,
            object_source=TYPE_OBJECT_CONTACT,
            created_by_sanka=True,
        )
        association_label_list_sanka_true = [
            association_label.label
            for association_label in association_labels_sanka_true
        ]
        association_labels_sanka_false = AssociationLabel.objects.filter(
            workspace=workspace,
            object_source=TYPE_OBJECT_CONTACT,
            created_by_sanka=False,
        )
        association_label_list_sanka_false = [
            get_association_label_display_name(association_label, lang)
            for association_label in association_labels_sanka_false
        ]
        association_label_list = (
            association_label_list_sanka_true + association_label_list_sanka_false
        )
        # association label relation
        related_association_labels = AssociationLabel.objects.filter(
            created_by_sanka=False,
            workspace=workspace,
            object_target__icontains=TYPE_OBJECT_CONTACT,
        ).order_by("created_at")
        for related_association_label in related_association_labels:
            association_label_list.append(
                get_association_label_display_name(related_association_label, lang)
            )

        context = {
            "predefined_data": predefined_data,
            "create_type": add_on,
            "companies": companies,
            "country_code": COUNTRY_CODE,
            "LANGUAGE_CODE": request.LANGUAGE_CODE,
            "CustomFieldMap": CustomFieldMap,
            "properties": properties,
            "set_id": set_id,
            "object_type": TYPE_OBJECT_CONTACT,
            "property_sets": property_sets,
            "permission": permission,
            "association_label_list": association_label_list,
        }

        if request.GET.get("type_association", "") == "create-association":
            context["type_association"] = "create-association"
            context["source"] = request.GET.get("source", "")
            context["source_object_id"] = request.GET.get("object_id", "")
            context["custom_object_id"] = request.GET.get("custom_object_id", "")

        # return render(request, 'data/contacts/account-explorer-contacts-drawer.html', context)vf
        return render(
            request,
            "data/contacts/manage-sync-settings-shopturbo-contacts-single-entry.html",
            context,
        )

    elif drawer_type == "contact-action":
        section = request.GET.get("section", None)
        if section == "action_history":
            action_tab = request.GET.get("action_tab", None)

            context = {
                "contact_ids": request.GET.getlist("contact_ids", []),
                "action_tab": action_tab,
                "object_type": TYPE_OBJECT_CONTACT,
            }
            return render(
                request,
                "data/shopturbo/manage-sync-actions-settings-contact.html",
                context,
            )

        elif section == "bulk-action":
            page = request.GET.get("page", 1)

            contact_id = request.GET.get("contact_ids")
            selected_contact_ids = []
            if contact_id and contact_id != "[]":
                contact_ids = request.GET.get("contact_ids")
                contact_ids = ast.literal_eval(contact_ids)
                contacts = Contact.objects.filter(
                    workspace=workspace, id__in=contact_ids
                )
                selected_contact_ids = [str(contact.id) for contact in contacts]

            context = {
                "object_action": {
                    "additional_params": {
                        "contact_ids": selected_contact_ids,
                        "action_type": "bulk",
                    },
                },
                "object_type": TYPE_OBJECT_CONTACT,
                "page": page,
            }
            return render(
                request, "data/shopturbo/shopturbo-manage-action.html", context
            )

    elif drawer_type == "create-message":
        templates = MessageBodyTemplate.objects.filter(
            workspace=workspace, object_type__isnull=True
        )
        context = {
            "channels": Channel.objects.filter(
                workspace=get_workspace(request.user),
                integration__slug__in=[
                    "gmail",
                    "twilio-sms",
                    "twitter",
                    "line",
                    "smtp",
                    "whatsapp",
                ],
            ),
            "lists": Contact.objects.filter(
                workspace=get_workspace(request.user), status="active"
            ),
            "line_contact_lists": ContactLineChannel.objects.filter(
                workspace=get_workspace(request.user)
            ),
            "contacts": Contact.objects.filter(
                workspace=get_workspace(request.user), status="active"
            ),
            "country_code": COUNTRY_CODE,
            "templates": templates,
        }
        return render(request, "data/partials/create-message-drawer.html", context)

    elif drawer_type == "manage-message":
        message_id = request.GET.get("message_id", False)
        templates = MessageBodyTemplate.objects.filter(
            workspace=workspace, object_type__isnull=True
        )
        context = {
            "channels": Channel.objects.filter(
                workspace=get_workspace(request.user),
                integration__slug__in=[
                    "gmail",
                    "twilio-sms",
                    "twitter",
                    "line",
                    "smtp",
                ],
            ),
            "lists": Contact.objects.filter(
                workspace=get_workspace(request.user), status="active"
            ),
            "line_contact_lists": ContactLineChannel.objects.filter(
                workspace=get_workspace(request.user)
            ),
            "country_code": COUNTRY_CODE,
            "templates": templates,
        }
        if message_id:
            message = Message.objects.filter(id=message_id).first()
            if message:
                context["message"] = message
        return render(request, "data/partials/create-message-drawer.html", context)

    elif drawer_type == "companies":
        predefined_data = request.GET.getlist("predefined-data")
        add_on = request.GET.get("addon_source", None)
        section = request.GET.get("section", None)

        permission = get_permission(TYPE_OBJECT_COMPANY, request.user)
        if not permission:
            permission = DEFAULT_PERMISSION

        exploreCustomField = CompanyNameCustomField.objects.filter(
            workspace=get_workspace(request.user)
        )

        companies = []
        company_list = CompanyList.objects.filter(workspace=workspace).exclude(
            status="unused"
        )

        set_id = request.GET.get("set_id", None)
        view_id = request.GET.get("view_id", None)
        if set_id:
            property_set = PropertySet.objects.filter(id=set_id).first()
        else:
            if view_id == "None":
                view_id = None
            if view_id:
                view = View.objects.filter(
                    id=view_id, target=TYPE_OBJECT_COMPANY
                ).first()
                if view:
                    if view.form:
                        property_set = view.form
                    else:
                        condition_filter = Q(
                            workspace=workspace, target=TYPE_OBJECT_COMPANY
                        )
                        condition_filter &= Q(as_default=True) | Q(name__isnull=True)
                        property_set = PropertySet.objects.filter(
                            condition_filter
                        ).first()
                else:
                    condition_filter = Q(
                        workspace=workspace, target=TYPE_OBJECT_COMPANY
                    )
                    condition_filter &= Q(as_default=True) | Q(name__isnull=True)
                    property_set = PropertySet.objects.filter(condition_filter).first()
            else:
                condition_filter = Q(workspace=workspace, target=TYPE_OBJECT_COMPANY)
                condition_filter &= Q(as_default=True) | Q(name__isnull=True)
                property_set = PropertySet.objects.filter(condition_filter).first()

        if section == "create" and not property_set:
            _ = get_default_property_set(TYPE_OBJECT_COMPANY, workspace, lang)
            property_set = (
                PropertySet.objects.filter(
                    workspace=workspace, target=TYPE_OBJECT_COMPANY
                )
                .order_by("created_at")
                .first()
            )

        properties = {"list_all": []}
        if property_set:
            set_id = str(property_set.id)
            for p in property_set.children:
                properties["list_all"].append(p)

        CustomFieldMap = {}
        explorecustomfield = CompanyNameCustomField.objects.filter(
            workspace=workspace
        ).order_by("order")
        for ctf in explorecustomfield:
            CustomFieldMap[str(ctf.id)] = ctf

        if add_on:
            properties, CustomFieldMap = get_default_prompt_set(
                workspace, TYPE_OBJECT_COMPANY, CompanyNameCustomField
            )
        property_sets = PropertySet.objects.filter(
            workspace=workspace, target=TYPE_OBJECT_COMPANY
        ).order_by("created_at")

        association_labels_sanka_true = AssociationLabel.objects.filter(
            workspace=workspace,
            object_source=TYPE_OBJECT_COMPANY,
            created_by_sanka=True,
        )
        association_label_list_sanka_true = [
            association_label.label
            for association_label in association_labels_sanka_true
        ]
        association_labels_sanka_false = AssociationLabel.objects.filter(
            workspace=workspace,
            object_source=TYPE_OBJECT_COMPANY,
            created_by_sanka=False,
        )
        association_label_list_sanka_false = [
            get_association_label_display_name(association_label, lang)
            for association_label in association_labels_sanka_false
        ]
        association_label_list = (
            association_label_list_sanka_true + association_label_list_sanka_false
        )
        # association label relation
        related_association_labels = AssociationLabel.objects.filter(
            created_by_sanka=False,
            workspace=workspace,
            object_target__icontains=TYPE_OBJECT_COMPANY,
        ).order_by("created_at")
        for related_association_label in related_association_labels:
            association_label_list.append(
                get_association_label_display_name(related_association_label, lang)
            )

        context = {
            "predefined_data": predefined_data,
            "create_type": add_on,
            "company_list": company_list,
            "explorecustomfield": exploreCustomField,
            "companies": companies,
            "country_code": COUNTRY_CODE,
            "object_type": TYPE_OBJECT_COMPANY,
            "source": TYPE_OBJECT_COMPANY,
            "properties": properties,
            "CustomFieldMap": CustomFieldMap,
            "property_sets": property_sets,
            "set_id": set_id,
            "permission": permission,
            "association_label_list": association_label_list,
        }

        if request.GET.get("type_association", "") == "create-association":
            context["type_association"] = "create-association"
            context["source"] = request.GET.get("source", "")
            context["source_object_id"] = request.GET.get("object_id", "")
            context["module"] = request.GET.get("module", "")

        import_export_type = request.GET.get("import_export_type", None)
        if import_export_type:
            return render(request, "data/contacts/entry-bulk-company.html", context)
        else:
            return render(request, "data/contacts/entry-single-company.html", context)

    elif drawer_type == "contacts-view-sync":
        contact_ids = request.GET.get("contact_ids", None)
        if contact_ids:
            contact_ids = contact_ids.split(",")

        platforms = ["shopify"]
        channels = Channel.objects.filter(
            workspace=get_workspace(request.user), integration__slug__in=platforms
        )

        context = {"channels": channels, "contact_ids": contact_ids}
        return render(
            request, "data/contacts/account-explorer-contacts-sync.html", context
        )

    elif drawer_type == "list":
        data_selector_status = request.GET.get("type", None)
        if data_selector_status == "data-selector-contents":
            data_filter = request.GET.get("data_filter", None)
            list_type = request.GET.get("list_type", None)
            field_choice = None

            data_type = "string"
            if "created" in data_filter:
                data_type = "date"

            if list_type == "contact":
                contactsCustomFieldName = ContactsNameCustomField.objects.filter(
                    name__iexact=data_filter, workspace=workspace
                ).first()
                if contactsCustomFieldName:
                    if (
                        contactsCustomFieldName.type == "number"
                        or contactsCustomFieldName.type == "formula"
                    ):
                        data_type = "number"

            elif list_type == "companies":
                companiesCustomFieldName = CompanyNameCustomField.objects.filter(
                    name__iexact=data_filter, workspace=workspace
                ).first()
                if companiesCustomFieldName:
                    if (
                        companiesCustomFieldName.type == "number"
                        or companiesCustomFieldName.type == "formula"
                    ):
                        data_type = "number"

            if "status" in data_filter:
                field_choice = ["active", "archived"]

            predefined_data_filter = request.GET.get("predefined_data_filter", None)
            if predefined_data_filter:
                predefined_data_filter = ast.literal_eval(predefined_data_filter)

            context = {
                "data_type": data_type,
                "predefined_data_filter": predefined_data_filter,
                "data_filter": data_filter,
                "list_type": list_type,
                "field_choice": field_choice,
            }
            return render(
                request, "data/contacts/list-data-filter-selector.html", context
            )

        elif data_selector_status == "data-selector":
            list_type = request.GET.get("list_type", None)

            if list_type == "contact":
                column_values = [str(v.name) for v in Contact._meta.fields]
                for column in [
                    "image_file",
                    "id",
                    "lang",
                    "workspace",
                    "updated_at",
                    "company",
                    "location",
                    "last_name",
                ]:
                    column_values.remove(column)
                contactsnamecustomfield = ContactsNameCustomField.objects.filter(
                    workspace=workspace
                ).values_list("id", flat=True)
                contactsnamecustomfield = [
                    str(uuid) for uuid in contactsnamecustomfield
                ]
                column_values.extend(contactsnamecustomfield)

                channels = Channel.objects.filter(
                    workspace=get_workspace(request.user),
                    integration__slug__in=[
                        "hubspot",
                        "shopify",
                        "freee",
                        "rakuten",
                        "salesforce",
                        "b-cart",
                    ],
                )
                for channel in channels:
                    column_values.insert(
                        len(column_values), f"{str(channel.name)} - contact id"
                    )

            elif list_type == "company":
                column_values = [str(v.name) for v in Company._meta.fields]
                for column in ["id", "workspace"]:
                    column_values.remove(column)
                companiesnamecustomfield = CompanyNameCustomField.objects.filter(
                    workspace=workspace
                ).values_list("id", flat=True)
                companiesnamecustomfield = [
                    str(uuid) for uuid in companiesnamecustomfield
                ]
                column_values.extend(companiesnamecustomfield)

                channels = Channel.objects.filter(
                    workspace=get_workspace(request.user),
                    integration__slug__in=["hubspot", "salesforce", "b-cart"],
                )
                for channel in channels:
                    column_values.insert(
                        len(column_values), f"{str(channel.name)} - company id"
                    )

            context = {"column_values": column_values, "list_type": list_type}
            return render(request, "data/contacts/data-selector-list.html", context)

        else:
            return render(
                request, "data/contacts/account-explorer-contacts-list-drawer.html"
            )

    elif drawer_type == "deals":
        print("[DEBUG] contact_drawer - deals drawer_type")
        print("[DEBUG] contact_drawer - All GET parameters:")
        for key, value in request.GET.items():
            print(f"[DEBUG]   {key}: {value}")

        section = request.GET.get("section")
        view_id = request.GET.get("view_id")
        set_id = request.GET.get("set_id")
        module_slug = request.GET.get("module", None)

        permission = get_permission(TYPE_OBJECT_CASE, request.user)
        if not permission:
            permission = DEFAULT_PERMISSION

        if section == "single-entry":
            predefined_data = request.GET.get("predefined-data", [])
            if predefined_data:
                predefined_data = ast.literal_eval(predefined_data)
                predefined_data = {
                    predefined_data[i]: predefined_data[i + 1]
                    for i in range(0, len(predefined_data), 2)
                }

            set_id = request.GET.get("set_id", None)
            if set_id == "None":
                set_id = None
            view_id = request.GET.get("view_id", None)
            if set_id:
                property_set = PropertySet.objects.filter(id=set_id).first()
            else:
                if view_id == "None":
                    view_id = None
                if view_id:
                    view = View.objects.filter(
                        id=view_id, target=TYPE_OBJECT_CASE
                    ).first()
                    if view:
                        if view.form:
                            property_set = view.form
                        else:
                            condition_filter = Q(
                                workspace=workspace, target=TYPE_OBJECT_CASE
                            )
                            condition_filter &= Q(as_default=True) | Q(
                                name__isnull=True
                            )
                            property_set = PropertySet.objects.filter(
                                condition_filter
                            ).first()
                    else:
                        condition_filter = Q(
                            workspace=workspace, target=TYPE_OBJECT_CASE
                        )
                        condition_filter &= Q(as_default=True) | Q(name__isnull=True)
                        property_set = PropertySet.objects.filter(
                            condition_filter
                        ).first()
                else:
                    condition_filter = Q(workspace=workspace, target=TYPE_OBJECT_CASE)
                    condition_filter &= Q(as_default=True) | Q(name__isnull=True)
                    property_set = PropertySet.objects.filter(condition_filter).first()

            properties = {"list_all": []}
            if property_set:
                set_id = str(property_set.id)
                for p in property_set.children:
                    properties["list_all"].append(p)

            show_line_items = False
            if property_set:
                default_properties = property_set.default_properties
                deal_line_items = property_set.deal_line_items.all()
                default_properties_length = 0
                if default_properties:
                    default_properties_length = len(
                        ast.literal_eval(default_properties)
                    )
                if default_properties_length > 0 or len(deal_line_items) > 0:
                    show_line_items = True

            # associate label
            association_labels_sanka_true = AssociationLabel.objects.filter(
                workspace=workspace,
                object_source=TYPE_OBJECT_CASE,
                created_by_sanka=True,
            )
            association_label_list_sanka_true = [
                association_label.label
                for association_label in association_labels_sanka_true
            ]
            association_labels_sanka_false = AssociationLabel.objects.filter(
                workspace=workspace,
                object_source=TYPE_OBJECT_CASE,
                created_by_sanka=False,
            )
            association_label_list_sanka_false = [
                get_association_label_display_name(association_label, lang)
                for association_label in association_labels_sanka_false
            ]
            association_label_list = (
                association_label_list_sanka_true + association_label_list_sanka_false
            )

            # if show_line_items:
            #     if 'line_item' not in properties['list_all']:
            #         if len(properties['list_all']) > 5:
            #             properties['list_all'].insert(4, 'line_item')
            #         else:
            #             properties['list_all'].append('line_item')

            CustomFieldMap = {}
            explorecustomfield = DealsNameCustomField.objects.filter(
                workspace=workspace
            ).order_by("order")
            for ctf in explorecustomfield:
                CustomFieldMap[str(ctf.id)] = ctf

            contacts = Contact.objects.filter(workspace=workspace, status="active")
            companies = Company.objects.filter(workspace=workspace, status="active")
            context = {
                "predefined_data": predefined_data,
                "section": section,
                "contact_id_preselected": request.GET.get("contact_id", None),
                "company_id_preselected": request.GET.get("company_id", None),
                "companies": companies,
                "contacts": contacts,
                "DealsCustomFieldName": DealsNameCustomField.objects.filter(
                    workspace=workspace
                ),
                "subscriptions": ShopTurboSubscriptions.objects.filter(
                    workspace=workspace
                ).order_by("subscriptions_id"),
                "locations": InventoryWarehouse.objects.filter(
                    workspace=workspace, usage_status="active"
                ).order_by("created_at"),
                "dummy_case": Deals(workspace=workspace),
                "view_id": view_id,
                "set_id": set_id,
                "object_type": TYPE_OBJECT_CASE,
                "CASE_STATUS": CASE_STATUS,
                "allowed_statuses": [s[0] for s in CASE_STATUS],
                "properties": properties,
                "CustomFieldMap": CustomFieldMap,
                "module_slug": module_slug,
                "show_line_items": show_line_items,
                "permission": permission,
                "association_label_list": association_label_list,
            }
            print(context)

            # Association Related Create
            context["associate_id"] = request.GET.get("associate_id", "")
            context["object_id"] = request.GET.get("object_id", "")
            context["source"] = request.GET.get("source")
            context["page"] = request.GET.get("page", 1)
            context["associate_view_id"] = request.GET.get("associate_view_id", "")

            if request.GET.get("type_association", "") == "create-association":
                context["type_association"] = "create-association"
                context["source"] = request.GET.get("source", "")
                context["source_object_id"] = request.GET.get("object_id", "")
                context["module"] = request.GET.get("module", "")

            return render(request, "data/contacts/deals-drawer.html", context)

        elif section == "bulk-entry":
            context = {"section": section, "view_id": view_id}
            return render(request, "data/contacts/deals-drawer.html", context)

        elif section == "bulk-action":
            page = request.GET.get("page", 1)

            case_id = request.GET.get("case_ids")
            selected_case_ids = []
            if case_id and case_id != "[]":
                case_ids = request.GET.get("case_ids")
                case_ids = ast.literal_eval(case_ids)
                cases = Deals.objects.filter(workspace=workspace, id__in=case_ids)
                selected_case_ids = [str(case.id) for case in cases]
            context = {
                "object_action": {
                    "additional_params": {
                        "case_ids": selected_case_ids,
                        "action_type": "bulk",
                    },
                },
                "object_type": TYPE_OBJECT_CASE,
                "page": page,
            }
            return render(
                request, "data/shopturbo/shopturbo-manage-action.html", context
            )

        elif section == "action_history":
            section = request.GET.get("section", None)
            action_tab = request.GET.get("action_tab", None)

            context = {
                "case_ids": request.GET.getlist("case_ids", []),
                "action_tab": action_tab,
                "object_type": TYPE_OBJECT_CASE,
            }
            return render(
                request,
                "data/shopturbo/manage-sync-actions-settings-case.html",
                context,
            )
        else:
            property_sets = PropertySet.objects.filter(
                workspace=workspace, target=TYPE_OBJECT_CASE
            ).order_by("created_at")

            predefined_data = request.GET.get("predefined-data", [])
            context = {
                "predefined_data": predefined_data,
                "contact_id_preselected": request.GET.get("contact_id", None),
                "company_id_preselected": request.GET.get("company_id", None),
                "section": section,
                "view_id": view_id,
                "set_id": set_id,
                "module_slug": module_slug,
                "property_sets": property_sets,
            }

            # Add association parameters for case creation from other objects
            if request.GET.get("type_association", "") == "create-association":
                print(
                    "[DEBUG] contact_drawer - type_association == 'create-association'"
                )
                context["type_association"] = "create-association"
                context["source"] = request.GET.get("source", "")
                context["source_object_id"] = request.GET.get("object_id", "")
                context["object_id"] = request.GET.get("object_id", "")
                context["module"] = request.GET.get("module", "")
                context["page"] = request.GET.get("page", "")
                print(f"[DEBUG] contact_drawer - context source: {context['source']}")
                print(
                    f"[DEBUG] contact_drawer - context object_id: {context['object_id']}"
                )

            # Also handle direct association parameters (for backward compatibility)
            if request.GET.get("source"):
                print("[DEBUG] contact_drawer - direct source parameter found")
                context["source"] = request.GET.get("source", "")
                context["object_id"] = request.GET.get("object_id", "")
                context["module"] = request.GET.get("module", "")
                context["page"] = request.GET.get("page", "")
                print(
                    f"[DEBUG] contact_drawer - direct context source: {context['source']}"
                )
                print(
                    f"[DEBUG] contact_drawer - direct context object_id: {context['object_id']}"
                )
            import_export_type = request.GET.get("import_export_type", None)
            if import_export_type:
                return render(
                    request,
                    "data/contacts/deals-create-drawer-bulk-entry.html",
                    context,
                )
            else:
                return render(
                    request,
                    "data/contacts/deals-create-drawer-single-entry.html",
                    context,
                )

    elif drawer_type == "manage-deal":
        deal_id = request.GET.get("deal_id")
        view_id = request.GET.get("view_id", None)
        module_slug = request.GET.get("module", None)
        if view_id == "None":
            view_id = None

        deal = Deals.objects.get(id=deal_id)

        # Only load currently selected customers for the case, not all customers
        # This improves performance by avoiding loading all workspace customers upfront
        selected_companies = (
            deal.company.all() if deal.company.exists() else Company.objects.none()
        )
        selected_contacts = (
            deal.contact.all() if deal.contact.exists() else Contact.objects.none()
        )

        notes = Notes.objects.filter(
            deal=deal, workspace=get_workspace(request.user)
        ).order_by("-created_at")
        DealsCustomFieldName = DealsNameCustomField.objects.filter(workspace=workspace)

        app_logs = AppLog.objects.filter(workspace=workspace, deal=deal).order_by(
            "-created_at"
        )

        target = "customer_case"
        permission = get_permission(target, request.user)
        if not permission:
            permission = DEFAULT_PERMISSION

        set_id = request.GET.get("set_id", None)
        if set_id == "None":
            set_id = None
        view_id = request.GET.get("view_id", None)
        if set_id:
            property_set = PropertySet.objects.filter(id=set_id).first()
        else:
            if view_id == "None":
                view_id = None
            if view_id:
                view = View.objects.filter(id=view_id, target=TYPE_OBJECT_CASE).first()
                if view:
                    if view.form:
                        property_set = view.form
                    else:
                        condition_filter = Q(
                            workspace=workspace, target=TYPE_OBJECT_CASE
                        )
                        condition_filter &= Q(as_default=True) | Q(name__isnull=True)
                        property_set = PropertySet.objects.filter(
                            condition_filter
                        ).first()
                else:
                    condition_filter = Q(workspace=workspace, target=TYPE_OBJECT_CASE)
                    condition_filter &= Q(as_default=True) | Q(name__isnull=True)
                    property_set = PropertySet.objects.filter(condition_filter).first()
            else:
                condition_filter = Q(workspace=workspace, target=TYPE_OBJECT_CASE)
                condition_filter &= Q(as_default=True) | Q(name__isnull=True)
                property_set = PropertySet.objects.filter(condition_filter).first()

        properties = {"list_all": []}
        if property_set:
            set_id = str(property_set.id)
            for p in property_set.children:
                properties["list_all"].append(p)

        CustomFieldMap = {}
        explorecustomfield = DealsNameCustomField.objects.filter(
            workspace=workspace
        ).order_by("order")
        for ctf in explorecustomfield:
            CustomFieldMap[str(ctf.id)] = ctf

        cases = [deal]

        case_associates = Association.objects.filter(
            workspace=workspace, case_associate__isnull=False
        )
        associates = Association.objects.filter(
            workspace=workspace, associate_type=TYPE_OBJECT_CASE
        )

        for note in notes:
            print(note.description)

        # associate label
        association_labels = AssociationLabel.objects.filter(
            workspace=workspace, object_source=TYPE_OBJECT_CASE
        ).order_by("created_at")
        association_labels_sanka_true = AssociationLabel.objects.filter(
            workspace=workspace, object_source=TYPE_OBJECT_CASE, created_by_sanka=True
        )
        association_label_list_sanka_true = [
            association_label.label
            for association_label in association_labels_sanka_true
        ]
        association_labels_sanka_false = AssociationLabel.objects.filter(
            workspace=workspace, object_source=TYPE_OBJECT_CASE, created_by_sanka=False
        )
        association_label_list_sanka_false = [
            get_association_label_display_name(association_label, lang)
            for association_label in association_labels_sanka_false
        ]
        association_label_list = (
            association_label_list_sanka_true + association_label_list_sanka_false
        )

        # related associate label
        related_association_labels = AssociationLabel.objects.filter(
            created_by_sanka=False,
            workspace=workspace,
            object_target__icontains=TYPE_OBJECT_CASE,
        ).order_by("created_at")

        # Together
        show_line_items = False
        if property_set:
            default_properties = property_set.default_properties
            deal_line_items = property_set.deal_line_items.all()
            default_properties_length = 0
            if default_properties:
                default_properties_length = len(ast.literal_eval(default_properties))
            if default_properties_length > 0 or len(deal_line_items) > 0:
                show_line_items = True

        # if show_line_items:
        #     if 'line_item' not in properties['list_all']:
        #         if len(properties['list_all']) > 5:
        #             properties['list_all'].insert(4, 'line_item')
        #         else:
        #             properties['list_all'].append('line_item')

        property_sets = PropertySet.objects.filter(
            workspace=workspace, target=TYPE_OBJECT_CASE
        ).order_by("created_at")

        if deal and deal.owner and deal.owner.user:
            permission += f"|{deal.owner.user.id}#{request.user.id}"
        context = {
            "deal": deal,
            "app_logs": app_logs,
            "selected_companies": selected_companies,
            "selected_contacts": selected_contacts,
            "DealsCustomFieldName": DealsCustomFieldName,
            "deal_number_format": DEALS_NUMBER_FORMAT,
            "CASE_STATUS": CASE_STATUS,
            "allowed_statuses": [s[0] for s in CASE_STATUS],
            "notes": notes,
            "view_id": view_id,
            "set_id": set_id,
            "permission": permission,
            "object_type": TYPE_OBJECT_CASE,
            "subscriptions": ShopTurboSubscriptions.objects.filter(
                workspace=workspace
            ).order_by("subscriptions_id"),
            "locations": InventoryWarehouse.objects.filter(
                workspace=workspace, usage_status="active"
            ).order_by("created_at"),
            "case_associates": case_associates,
            "associates": associates,
            "object_action": {
                "additional_params": {
                    "case_id": deal.id,
                    "case_ids": [str(item.id) for item in cases],
                },
            },
            "properties": properties,
            "CustomFieldMap": CustomFieldMap,
            "module_slug": module_slug,
            "module": module_slug,
            "estimates": Estimate.objects.filter(workspace=workspace),
            "invoices": Invoice.objects.filter(workspace=workspace),
            "customer_selector_id": uuid.uuid4(),
            "dummy_case": Deals(workspace=workspace),
            "side_drawer": request.GET.get("side_drawer"),
            "show_line_items": show_line_items,
            "property_sets": property_sets,
            "from": request.GET.get("from", None),
            "association_labels": association_labels,
            "association_label_list": association_label_list,
            "related_association_labels": related_association_labels,
        }

        hide_associated_data = request.GET.get("hide_associated_data", None)
        if hide_associated_data:
            context["hide_associated_data"] = True

        source_url = request.GET.get("source_url", None)
        if source_url:
            source_url = unquote(source_url)
            context["source_url"] = source_url
        return render(request, "data/contacts/profile-deals-detail.html", context)

    elif drawer_type == "contacts-view-settings":
        view_id = request.GET.get("view_id", None)
        type = request.GET.get("type", None)
        if type == "advance-filter":
            data_filter = request.GET.get("data_filter", None)
            app_type = request.GET.get("app_type", None)
            filter_options = request.GET.get("filter_options", None)
            data_type = "string"
            field_choice = []

            # Checking Default Columns
            if app_type == "contact":
                fields = Contact._meta.fields
                if data_filter in ["status", "contact_list"]:
                    if data_filter == "status":
                        field_choice = [("active", "Active"), ("archived", "Archived")]

                    elif data_filter == "contact_list":
                        Contact_Lists = ContactList.objects.filter(workspace=workspace)
                        for Contact_List in Contact_Lists:
                            field_choice.append(
                                (str(Contact_List.id), Contact_List.name)
                            )

            elif app_type == "companies":
                fields = Company._meta.fields
                if data_filter in ["status"]:
                    if data_filter == "status":
                        field_choice = [("active", "Active"), ("archived", "Archived")]

            elif app_type == "deals":
                lang = request.LANGUAGE_CODE
                fields = Deals._meta.fields
                if data_filter in ["status", "case_status"]:
                    if data_filter == "status":
                        field_choice = [("active", "Active"), ("archived", "Archived")]
                        if lang == "ja":
                            field_choice = [
                                (k, DEALS_COLUMNS_DISPLAY[k][lang])
                                for k in ["active", "archived"]
                            ]

                    elif data_filter == "case_status":
                        field_choice = CASE_STATUS

                        custom_property = CustomProperty.objects.filter(
                            workspace=workspace, model="data_deals", name=data_filter
                        ).first()

                        if custom_property:
                            if custom_property.value:
                                field_choice = []
                                custom_property.value = ast.literal_eval(
                                    custom_property.value
                                )
                                for item in custom_property.value:
                                    field_choice.append(
                                        (item, custom_property.value[item])
                                    )

            default_columns_dict = {
                field.name: field.get_internal_type() for field in fields
            }
            if data_filter in default_columns_dict.keys():
                for check in ["float", "integer"]:
                    if check in default_columns_dict[data_filter].lower():
                        data_type = "number"
                        break
                for check in ["date"]:
                    if check in default_columns_dict[data_filter].lower():
                        data_type = "date"
                        break
            else:
                if app_type == "contacts":
                    if is_valid_uuid(data_filter):
                        customfieldname = ContactsNameCustomField.objects.filter(
                            id=data_filter
                        ).first()
                    else:
                        customfieldname = ContactsNameCustomField.objects.filter(
                            name__iexact=data_filter, workspace=workspace
                        ).first()
                    if customfieldname:
                        if (
                            customfieldname.type == "number"
                            or customfieldname.type == "formula"
                        ):
                            data_type = "number"
                        elif (
                            customfieldname.type == "date"
                            or customfieldname.type == "date_time"
                        ):
                            data_type = customfieldname.type

                elif app_type == "companies":
                    if is_valid_uuid(data_filter):
                        customfieldname = CompanyNameCustomField.objects.filter(
                            id=data_filter
                        ).first()
                    else:
                        customfieldname = CompanyNameCustomField.objects.filter(
                            name__iexact=data_filter, workspace=workspace
                        ).first()
                    if customfieldname:
                        if (
                            customfieldname.type == "number"
                            or customfieldname.type == "formula"
                        ):
                            data_type = "number"
                        elif (
                            customfieldname.type == "date"
                            or customfieldname.type == "date_time"
                        ):
                            data_type = customfieldname.type

                elif app_type == "deals":
                    if is_valid_uuid(data_filter):
                        customfieldname = DealsNameCustomField.objects.filter(
                            id=data_filter
                        ).first()
                    else:
                        customfieldname = DealsNameCustomField.objects.filter(
                            name__iexact=data_filter, workspace=workspace
                        ).first()
                    if customfieldname:
                        if (
                            customfieldname.type == "number"
                            or customfieldname.type == "formula"
                        ):
                            data_type = "number"
                        elif (
                            customfieldname.type == "date"
                            or customfieldname.type == "date_time"
                        ):
                            data_type = customfieldname.type

            predefined_data_filter = request.GET.get("predefined_data_filter", None)
            if predefined_data_filter:
                predefined_data_filter = ast.literal_eval(predefined_data_filter)
                if not filter_options:
                    filter_options = predefined_data_filter.get("key")

            context = {
                "data_filter": data_filter,
                "data_type": data_type,
                "app_type": app_type,
                "predefined_data_filter": predefined_data_filter,
                "field_choice": field_choice,
                "uuid": uuid.uuid4(),
                "filter_options": filter_options,
                "is_advanced_search": request.GET.get("is_advanced_search", False),
                "filter_status": request.GET.get("filter_status", False),
            }
            return render(
                request, "data/contacts/list-data-filter-selector.html", context
            )

        else:
            page = request.GET.get("page", False)

            view_type = VIEW_MODE

            column_values = []

            namecustomfieldchoices = []
            default_view_choices = []

            if page == "contacts":
                view_type = view_type[0:1]  # only table is required

                target = "contacts"
                # column_values = [str(v.name) for v in Contact._meta.fields]
                page_object = get_page_object(target)
                column_values = page_object["base_columns"]
                exclude_custom_types = page_object["exclude_custom_types"]
                contactsnamecustomfield = (
                    ContactsNameCustomField.objects.filter(workspace=workspace)
                    .exclude(type__in=exclude_custom_types)
                    .values_list("id", flat=True)
                )
                contactsnamecustomfield = [
                    str(uuid) for uuid in contactsnamecustomfield
                ]
                column_values.extend(contactsnamecustomfield)

                # association label
                association_labels = AssociationLabel.objects.filter(
                    workspace=workspace,
                    object_source=TYPE_OBJECT_CONTACT,
                    created_by_sanka=True,
                ).order_by("created_at")
                for association_label in association_labels:
                    column_values.append(str(association_label.label))

                association_labels = AssociationLabel.objects.filter(
                    workspace=workspace,
                    object_source=TYPE_OBJECT_CONTACT,
                    created_by_sanka=False,
                ).order_by("created_at")
                for association_label in association_labels:
                    column_values.append(
                        get_association_label_display_name(association_label, lang)
                    )

                # association label related
                related_association_labels = AssociationLabel.objects.filter(
                    created_by_sanka=False,
                    workspace=workspace,
                    object_target__icontains=TYPE_OBJECT_CONTACT,
                ).order_by("created_at")
                for related_association_label in related_association_labels:
                    column_values.append(
                        get_association_label_display_name(
                            related_association_label, lang
                        )
                    )

                channels = Channel.objects.filter(
                    workspace=get_workspace(request.user),
                    integration__slug__in=[
                        "hubspot",
                        "shopify",
                        "freee",
                        "ec-cube",
                        "line",
                        "rakuten",
                        "salesforce",
                        "b-cart",
                    ],
                )
                for channel in channels:
                    if channel.integration.slug == "line":
                        column_values.insert(
                            len(column_values),
                            f"{str(channel.id)} - {str(channel.name)} - Line User ID",
                        )
                    else:
                        column_values.insert(
                            len(column_values), f"{str(channel.name)} - contact id"
                        )
                for field in ["image_file", "property_set"]:
                    if field in column_values:
                        column_values.remove(field)

                namecustomfieldchoices = ContactsNameCustomField.objects.filter(
                    workspace=workspace, type="choice"
                )
                column_values.remove("contact_id")
                column_values.append("first_name")

                namecustomfield_contact_lists = ContactsNameCustomField.objects.filter(
                    workspace=workspace, type="contact_list"
                )
                for namecustomfield_contact_list in namecustomfield_contact_lists:
                    if str(namecustomfield_contact_list.id) in column_values:
                        column_values.remove(str(namecustomfield_contact_list.id))
                if namecustomfield_contact_lists:
                    column_values.append("contact_list")

                # delete duplicate column_values
                if column_values:
                    column_values = list(set(column_values))

                pre_default_column = DEFAULT_COLUMNS_CONTACTS.copy()

            elif page == "companies":
                view_type = view_type[0:1]  # only table is required

                target = "companies"
                page_object = get_page_object("company")
                column_values = page_object["base_columns"]
                exclude_custom_types = page_object["exclude_custom_types"]
                # column_values = [str(v.name) for v in Company._meta.fields]

                companynamecustomfield = (
                    CompanyNameCustomField.objects.filter(workspace=workspace)
                    .exclude(type__in=exclude_custom_types)
                    .values_list("id", flat=True)
                )
                companynamecustomfield = [str(uuid) for uuid in companynamecustomfield]
                column_values.extend(companynamecustomfield)

                # Add association labels
                # association label
                association_labels = AssociationLabel.objects.filter(
                    workspace=workspace,
                    object_source=TYPE_OBJECT_COMPANY,
                    created_by_sanka=True,
                ).order_by("created_at")
                for association_label in association_labels:
                    column_values.append(str(association_label.label))

                association_labels = AssociationLabel.objects.filter(
                    workspace=workspace,
                    object_source=TYPE_OBJECT_COMPANY,
                    created_by_sanka=False,
                ).order_by("created_at")
                for association_label in association_labels:
                    column_values.append(
                        get_association_label_display_name(association_label, lang)
                    )

                # association label related
                related_association_labels = AssociationLabel.objects.filter(
                    created_by_sanka=False,
                    workspace=workspace,
                    object_target__icontains=TYPE_OBJECT_COMPANY,
                ).order_by("created_at")
                for related_association_label in related_association_labels:
                    column_values.append(
                        get_association_label_display_name(
                            related_association_label, lang
                        )
                    )

                namecustomfieldchoices = CompanyNameCustomField.objects.filter(
                    workspace=workspace, type="choice"
                )
                column_values.remove("company_id")

                channels = Channel.objects.filter(
                    workspace=get_workspace(request.user),
                    integration__slug__in=["hubspot", "salesforce", "b-cart"],
                )

                for channel in channels:
                    if channel.integration.slug == "line":
                        column_values.insert(
                            len(column_values),
                            f"{str(channel.id)} - {str(channel.name)} - Line User ID",
                        )
                    else:
                        column_values.insert(
                            len(column_values), f"{str(channel.name)} - company id"
                        )

                pre_default_column = DEFAULT_COLUMNS_COMPANY.copy()

                section = request.GET.get("section", None)
                if section == "integrations":
                    import_export_type = request.GET.get("import_export_type", None)
                    platforms = [
                        "hubspot",
                        "shopify",
                        "freee",
                        "ec-cube",
                        "line",
                        "salesforce",
                    ]

                    checkbox = None
                    company_ids = request.GET.get("company_ids", None)
                    if company_ids:
                        checkbox = ast.literal_eval(company_ids)

                    if checkbox is None:
                        checkbox = list(
                            str(company_id)
                            for company_id in Company.objects.filter(
                                workspace=workspace, status="active"
                            ).values_list("id", flat=True)
                        )

                    channels = Channel.objects.filter(
                        workspace=get_workspace(request.user),
                        integration__slug__in=platforms,
                    )

                    for name in EXCLUDE_SYNC_CHANNEL_NAME:
                        channels = channels.exclude(name__icontains=name)

                    count_companies = len(checkbox)
                    # count_contacts = Contact.objects.filter(workspace=workspace, status='active').count()

                    context = {
                        "company_ids": checkbox,
                        "channels": channels,
                        "count_companies": count_companies,
                        "import_export_type": import_export_type,
                    }

                    return render(
                        request,
                        "data/contacts/manage-sync-settings-shopturbo-companies-import-export.html",
                        context,
                    )

            elif page == "deals":
                lang = request.LANGUAGE_CODE
                target = "customer_case"
                column_values = [str(v.name) for v in Deals._meta.fields]
                companynamecustomfield = DealsNameCustomField.objects.filter(
                    workspace=workspace
                ).values_list("id", flat=True)
                companynamecustomfield = [str(uuid) for uuid in companynamecustomfield]
                column_values.extend(companynamecustomfield)

                # Add integration properties (platform columns)
                from utils.properties.non_default_properties import (
                    get_integration_properties,
                )

                integration_props = get_integration_properties(
                    TYPE_OBJECT_CASE, workspace
                )
                column_values.extend(integration_props)

                # Add association labels
                association_labels_source = AssociationLabel.objects.filter(
                    workspace=workspace,
                    object_source=TYPE_OBJECT_CASE,
                    created_by_sanka=False,
                ).values_list("id", flat=True)
                association_labels_target = AssociationLabel.objects.filter(
                    workspace=workspace,
                    object_target__icontains=TYPE_OBJECT_CASE,
                    created_by_sanka=False,
                ).values_list("id", flat=True)
                association_label_ids = [
                    str(uuid) for uuid in association_labels_source
                ] + [str(uuid) for uuid in association_labels_target]
                column_values.extend(association_label_ids)
                if not request.GET.get("import_export_type", None):
                    for companynamecustomfield_id in companynamecustomfield:
                        if (
                            DealsNameCustomField.objects.filter(
                                id=companynamecustomfield_id
                            )
                            .first()
                            .type
                            == "price-information"
                        ):
                            column_values.append(
                                f"price_information|total_price|{companynamecustomfield_id}"
                            )
                            column_values.append(
                                f"price_information|total_price_without_tax|{companynamecustomfield_id}"
                            )
                column_values.remove("kanban_order")
                column_values.remove("currency")

                # Customer Object
                column_values.append("customer")
                _contact_properties = get_properties_with_details(
                    "contacts", workspace, lang, includes=[{"id": "name"}]
                )
                for p in _contact_properties:
                    column_values.append(f"customer|contact|{p['id']}")

                _company_properties = get_properties_with_details(
                    "company", workspace, lang
                )
                for p in _company_properties:
                    column_values.append(f"customer|company|{p['id']}")

                namecustomfieldchoices = DealsNameCustomField.objects.filter(
                    workspace=workspace, type="choice"
                )
                column_values.remove("deal_id")

                default_view_choices = [
                    field.name for field in Deals._meta.fields if field.choices
                ]
                default_view_choices.remove("status")

                view_type = view_type[0:1] + view_type[2:3]

                pre_default_column = DEFAULT_COLUMNS_CASE.copy()

                # Case <> Task Default Association
                column_values.append("tasks")
                # Case <> Estimate Default Association
                column_values.append("estimates")
                # Case <> Invoice Default Association
                column_values.append("invoices")

            if "id" in column_values:
                column_values.remove("id")
            if "workspace" in column_values:
                column_values.remove("workspace")

            view_id = request.GET.get("view_id", None)
            view_filter = None
            if view_id:
                view = View.objects.get(id=view_id)
                view_filter = ViewFilter.objects.filter(view=view).first()
                if view_filter and not view_filter.column:
                    view_filter.column = pre_default_column
                elif not view_filter:
                    # Create a new view_filter if none exists
                    view_filter = ViewFilter()
                    view_filter.column = pre_default_column
            else:
                view_filter = ViewFilter()
                view_filter.column = pre_default_column

            download_view = request.GET.get("download_view", None)

            context = {
                "VIEW_MODE": view_type,
                "view_filter": view_filter,
                "column_values": column_values,
                "page": page,
                "namecustomfieldchoices": namecustomfieldchoices,
                "default_view_choices": default_view_choices,
                "download_view": download_view,
            }

            if page == "contacts":
                context["list_of_contact_lists"] = ContactList.objects.filter(
                    workspace=workspace
                ).order_by("-created_at")

            import_export_type = request.GET.get("import_export_type", None)
            if import_export_type:
                export_template = ExportTemplate.objects.filter(
                    workspace=workspace, target=TYPE_OBJECT_COMPANY, as_default=True
                ).first()
                if export_template:
                    if export_template.properties:
                        view_filter.column = export_template.properties.split(",")
                context["view_filter"] = view_filter
                return render(
                    request,
                    "data/contacts/manage-view-contacts-app-export.html",
                    context,
                )

            return render(
                request, "data/contacts/manage-view-contacts-app.html", context
            )

    elif drawer_type == "price-table":
        filter_conditions = Q(workspace=workspace)
        search_q = request.GET.get("q")
        if search_q:
            match_special_char = re.search(r"#(\d+)", search_q)
            if match_special_char:
                number = match_special_char.group(1)  # Get the number part
                if number:
                    from utils.utility import get_item_id_search_filter

                    filter_conditions &= get_item_id_search_filter(number)
            else:
                app_setting, _ = AppSetting.objects.get_or_create(
                    workspace=workspace, app_target="shopturbo"
                )
                search_ = search_q.split()
                for search_key in search_:
                    ##### BE CAREFUL TO INCLUDE WORKSPACE ####
                    from utils.utility import get_item_id_search_filter

                    filter_conditions &= apply_item_search_setting(
                        app_setting, search_key.lower()
                    ) | get_item_id_search_filter(search_key.lower())

        obj_id = request.GET.get("obj_id", None)
        obj_type = request.GET.get("obj_type", None)
        price_precentage = request.GET.get("price_precentage", None)
        tax_precentage = request.GET.get("tax_precentage", None)
        CustomFieldName = request.GET.get("CustomFieldName", None)
        if price_precentage == "None":
            price_precentage = None

        shopturbo_items = (
            ShopTurboItems.objects.filter(filter_conditions)
            .order_by("-item_id")
            .distinct("item_id", "id")
            .exclude(status="archived")
        )
        paginator = Paginator(shopturbo_items, 9)
        page_number = request.GET.get("page", 1)
        shopturbo_items = paginator.get_page(page_number)

        if request.GET.get("request_type", None) == "pagination":
            response = render(
                request,
                "data/partials/price-table-row-partial.html",
                {
                    "shopturbo_items": shopturbo_items,
                    "obj_id": obj_id,
                    "obj_type": obj_type,
                    "price_precentage": price_precentage,
                    "CustomFieldName": CustomFieldName,
                    "tax_precentage": tax_precentage,
                    "paginator": paginator,
                },
            )
            return response
        elif request.GET.get("request_type", None) == "apply-price":
            item_id = request.GET.get("item_id")
            shopturbo_item = ShopTurboItems.objects.get(id=item_id)
            response = render(
                request,
                "data/partials/price-table-row-partial.html",
                {
                    "shopturbo_items": [shopturbo_item],
                    "obj_id": obj_id,
                    "obj_type": obj_type,
                    "price_precentage": price_precentage,
                    "CustomFieldName": CustomFieldName,
                    "tax_precentage": tax_precentage,
                },
            )
            return response

        context = {
            "shopturbo_items": shopturbo_items,
            "paginator": paginator,
            "obj_id": obj_id,
            "obj_type": obj_type,
            "price_precentage": price_precentage,
            "tax_precentage": tax_precentage,
            "CustomFieldName": CustomFieldName,
        }
        return render(request, "data/partials/price-table.html", context)

    elif drawer_type == "contact_bulk_edit":
        account_ids = request.GET.get("account_ids", None)
        if account_ids:
            account_ids = account_ids.split(",")

        context = {
            "account_ids": account_ids,
            "companies": Company.objects.filter(workspace=workspace),
            "explorecustomfield": ContactsNameCustomField.objects.filter(
                workspace=workspace
            ),
        }
        return render(request, "data/contacts/contact_bulk_edit.html", context)

    elif drawer_type == "company_bulk_edit":
        account_ids = request.GET.get("account_ids", None)
        if account_ids:
            account_ids = account_ids.split(",")

        context = {
            "account_ids": account_ids,
            "explorecustomfield": CompanyNameCustomField.objects.filter(
                workspace=workspace
            ),
        }
        return render(request, "data/contacts/company_bulk_edit.html", context)

    elif drawer_type == "sync_contacts":
        platforms = ["hubspot", "shopify", "freee", "ec-cube", "line"]
        checkbox = None

        if "checkbox" in request.GET:
            checkbox = request.GET.getlist("checkbox", False)

        channels = Channel.objects.filter(
            workspace=get_workspace(request.user), integration__slug__in=platforms
        )

        for name in EXCLUDE_SYNC_CHANNEL_NAME:
            channels = channels.exclude(name__icontains=name)

        count_contacts = Contact.objects.filter(
            workspace=workspace, status="active"
        ).count()

        context = {
            "contact_ids": checkbox,
            "channels": channels,
            "count_contacts": count_contacts,
        }
        return render(request, "data/contacts/manage-sync-contact-drawer.html", context)

    elif drawer_type == "download_csv":
        view_id = request.GET.get("view_id", None)

        page = request.GET.get("page", False)
        view_type = VIEW_MODE
        column_values = []

        namecustomfieldchoices = []

        if page == "contacts":
            target = "contacts"
            column_values = [str(v.name) for v in Contact._meta.fields]
            contactsnamecustomfield = ContactsNameCustomField.objects.filter(
                workspace=workspace
            ).values_list("id", flat=True)
            contactsnamecustomfield = [str(uuid) for uuid in contactsnamecustomfield]
            column_values.extend(contactsnamecustomfield)
            channels = Channel.objects.filter(
                workspace=get_workspace(request.user),
                integration__slug__in=[
                    "hubspot",
                    "shopify",
                    "freee",
                    "ec-cube",
                    "rakuten",
                    "salesforce",
                    "b-cart",
                ],
            )
            for channel in channels:
                column_values.insert(
                    len(column_values), f"{str(channel.name)} - contact id"
                )
            for field in ["image_file", "lang", "location", "last_name"]:
                column_values.remove(field)

            namecustomfieldchoices = ContactsNameCustomField.objects.filter(
                workspace=workspace, type="choice"
            )
            column_values.remove("contact_id")
            column_values.append("lists")

        if page == "companies":
            target = "companies"
            column_values = [str(v.name) for v in Company._meta.fields]
            companynamecustomfield = CompanyNameCustomField.objects.filter(
                workspace=workspace
            ).values_list("id", flat=True)
            companynamecustomfield = [str(uuid) for uuid in companynamecustomfield]
            column_values.extend(companynamecustomfield)

            namecustomfieldchoices = CompanyNameCustomField.objects.filter(
                workspace=workspace, type="choice"
            )
            column_values.remove("company_id")
            column_values.append("lists")

        column_values.remove("id")
        column_values.remove("workspace")

        if view_id:
            view = View.objects.filter(
                workspace=workspace, target=target, id=view_id
            ).first()
        else:
            view, _ = View.objects.get_or_create(
                workspace=workspace, target=target, title__isnull=True
            )

        view_filter = ViewFilter.objects.filter(view=view).first()
        if view_filter:
            try:
                column = (
                    ast.literal_eval(view_filter.column)
                    if view_filter.column
                    else column_values
                )
            except (ValueError, SyntaxError):
                # If literal_eval fails, try to parse as comma-separated string or use default
                if isinstance(view_filter.column, list):
                    column = view_filter.column
                else:
                    column = (
                        [col.strip() for col in view_filter.column.split(",")]
                        if view_filter.column
                        else column_values
                    )
            view_filter.column = column
        else:
            view_filter, _ = ViewFilter.objects.get_or_create(view=view)
            view_filter.column = column_values
            view_filter.save()

        if page == "contacts":
            if "status" in column_values:
                column_values.remove("status")
            if view_filter:
                if "status" in view_filter.column:
                    view_filter_column = view_filter.column
                    view_filter_column_download = []
                    for column in view_filter_column:
                        if column != "status":
                            view_filter_column_download.append(column)
                    view_filter.column = view_filter_column_download

        context = {
            "VIEW_MODE": view_type,
            "view_filter": view_filter,
            "column_values": column_values,
            "namecustomfieldchoices": namecustomfieldchoices,
            "page": page,
            "view_id": view_id,
            "view": view,
        }

        return render(request, "data/contacts/manage-contacts-download.html", context)

    return HttpResponse(200)
