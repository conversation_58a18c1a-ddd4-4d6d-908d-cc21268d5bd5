import ast
from collections import defaultdict
from datetime import timedelta
from datetime import datetime
import html
import json
import random
import re
from threading import current_thread
from typing import List, Literal
from typing import Any, Type, Union
import urllib.parse
import uuid

from PIL import Image
from dateutil.parser import *
from django import template
from django.apps import apps
from django.db.models import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Case,
    Count,
    F,
    Field,
    Max,
    Min,
    Q,
    Value,
    When,
)
from django.db.models import QuerySet
from django.template.defaultfilters import stringfilter
from django.urls import translate_url
from django.utils import timezone
import markdown as md

from data.constants.associate_constant import ASSOCIATE_MAP
from data.constants.constant import (
    BILLING_CYCLE_MAPPER,
    CURRENCY_MODEL,
    NEXT_ENGINE_PAYMENT_METHOD_LIST,
    NEXT_ENGINE_SHIPPING_METHOD_LIST,
    SYSTEM_SETTING_TYPE,
    TYPE_TO_RULE,
)
from data.constants.date_constant import DEFAULT_DATE_FORMAT
from data.constants.properties_constant import *
from data.constants.stripe_constant import *
from data.models import *
from utils import actions, reports, utility
from utils.contact import *
from utils.currency_symbols.currency_symbols import CurrencySymbols
from utils.date import format_date_to_str
from utils.events import *
from utils.logger import logger
from utils.meter import has_quota, sync_usage
from utils.openaiAPI import OpenaiAPI
from utils.properties.properties import (
    base_model_to_object_type,
    get_page_object,
    property_display,
)
from utils.utility import (
    get_workspace,
    is_iso_format,
    is_valid_uuid,
    needs_normalization,
    normalize_japanese_text,
    translate_language,
)
from utils.workflow import get_nodes

from .commerce_tags import (
    company_platform_name,
    contact_platform_name,
    get_billings_items,
    get_billings_items_count,
    get_billings_items_id,
    get_currencies,
    get_currencies_text_symbol,
    get_customer_item_price,
    get_inventory_platforms,
    get_item_platforms,
    get_order,
    get_order_items,
    get_predefined_currency,
    get_predefined_currency_line_items,
    get_purchase_order,
    get_purchase_order_items,
    inventory_platform_name,
    item_platform_name,
    order_platform_fulfillment_status,
    order_platform_name,
    order_platform_order_id,
    order_platform_order_status,
    order_platform_payment_status,
    subscription_platform_name,
    subs_platform_id,
)
from .customtags.accounting_tags import register as accounting_filters_register
from .customtags.custom_field_tags import register as custom_field_filters_register
from .customtags.display_tags import register as display_filters_register
from .customtags.json_tags import register as json_filters_register
from .customtags.list_tags import register as list_filters_register
from .customtags.object_tags import register as object_filters_register
from .customtags.permission_tags import register as permission_filters_register
from .customtags.search_tags import register as search_filters_register
from .customtags.string_tags import is_uuid, register as string_filters_register
from .customtags.time_tags import register as time_filters_register
from .datetime_tags import (
    convert_string_date,
    date_diff,
    format_time,
    get_current_time,
    get_holidays,
    is_time_passed,
    local_date,
    local_time,
    parse_date,
    remove_time,
    str_datetime,
    str_datetime_ja,
    unix_to_localtime,
)
from .formatting_tags import (
    floatformat_filter,
    format_price,
    multiply,
    remove_comma,
    remove_dash,
    seal_format_token,
    use_thousand_separator,
    use_thousand_separator_string,
    use_thousand_separator_string_with_currency,
    use_thousand_separator_string_with_currency_journal,
)
from .utility_tags import (
    combine_text,
    customer_converter_display,
    date_converter,
    get_date_list,
    get_month_list,
    stringify,
)
from utils.line_items import combine_item_dict_to_list

_requests = {}


def get_current_request():
    """Get the current request from thread local storage."""
    t = current_thread()
    if t not in _requests:
        return None
    return _requests[t]


# Middleware to store request in thread local storage


class RequestMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        _requests[current_thread()] = request
        response = self.get_response(request)
        return response


ai = OpenaiAPI()


register = template.Library()

# Re-register imported utility tags with this module's register
register.filter("customer_converter_display")(customer_converter_display)
register.filter("date_converter")(date_converter)
register.simple_tag(name="combine_text")(combine_text)
register.filter("stringify")(stringify)
register.filter("get_month_list")(get_month_list)
register.filter("get_date_list")(get_date_list)

# Re-register imported formatting tags with this module's register
register.filter("format_price")(format_price)
register.filter("multiply")(multiply)
register.filter("floatformat")(floatformat_filter)
register.filter("remove_dash")(remove_dash)
register.filter("remove_comma")(remove_comma)
register.filter("seal_format_token")(seal_format_token)
register.filter("use_thousand_separator")(use_thousand_separator)
register.filter("use_thousand_separator_string")(use_thousand_separator_string)
register.filter("use_thousand_separator_string_with_currency")(
    use_thousand_separator_string_with_currency
)
register.filter("use_thousand_separator_string_with_currency_journal")(
    use_thousand_separator_string_with_currency_journal
)

# Re-register imported datetime tags with this module's register
register.simple_tag(takes_context=True)(get_holidays)
register.simple_tag(takes_context=True)(local_time)
register.simple_tag()(local_date)
register.filter("remove_time")(remove_time)
register.filter("str_datetime")(str_datetime)
register.filter("str_datetime_ja")(str_datetime_ja)
register.filter("format_time")(format_time)
register.filter("date_diff")(date_diff)
register.filter("parse_date")(parse_date)
register.simple_tag(name="get_current_time")(get_current_time)
register.simple_tag()(unix_to_localtime)
register.filter("is_time_passed")(is_time_passed)
register.filter(name="convert_string_date")(convert_string_date)

# Re-register imported commerce tags with this module's register
register.filter(name="company_platform_name")(company_platform_name)
register.filter(name="contact_platform_name")(contact_platform_name)
register.filter(name="subscription_platform_name")(subscription_platform_name)
register.filter(name="item_platform_name")(item_platform_name)
register.filter(name="get_item_platforms")(get_item_platforms)
register.filter(name="inventory_platform_name")(inventory_platform_name)
register.filter(name="get_inventory_platforms")(get_inventory_platforms)
register.filter(name="order_platform_name")(order_platform_name)
register.filter(name="order_platform_order_id")(order_platform_order_id)
register.filter(name="subs_platform_id")(subs_platform_id)
register.filter(name="order_platform_payment_status")(order_platform_payment_status)
register.filter(name="order_platform_fulfillment_status")(
    order_platform_fulfillment_status
)
register.filter(name="order_platform_order_status")(order_platform_order_status)
register.filter("get_currencies")(get_currencies)
register.filter("get_currencies_text_symbol")(get_currencies_text_symbol)
register.simple_tag()(get_predefined_currency)
register.simple_tag()(get_predefined_currency_line_items)
register.filter("get_order_items")(get_order_items)
register.filter("get_purchase_order_items")(get_purchase_order_items)
register.filter("get_order")(get_order)
register.filter("get_purchase_order")(get_purchase_order)
register.simple_tag(name="get_customer_item_price")(get_customer_item_price)
register.filter("get_billings_items")(get_billings_items)
register.filter("get_billings_items_count")(get_billings_items_count)
register.filter("get_billings_items_id")(get_billings_items_id)


# ------------------------------------- REGITSER THE CUSTOM TAGS -------------------------------------
register.tags.update(string_filters_register.tags)
register.filters.update(string_filters_register.filters)
register.tags.update(time_filters_register.tags)
register.filters.update(time_filters_register.filters)
register.tags.update(accounting_filters_register.tags)
register.filters.update(accounting_filters_register.filters)
register.tags.update(list_filters_register.tags)
register.filters.update(list_filters_register.filters)
register.tags.update(json_filters_register.tags)
register.filters.update(json_filters_register.filters)
register.tags.update(display_filters_register.tags)
register.filters.update(display_filters_register.filters)
register.tags.update(object_filters_register.tags)
register.filters.update(object_filters_register.filters)
register.tags.update(permission_filters_register.tags)
register.filters.update(permission_filters_register.filters)
register.tags.update(search_filters_register.tags)
register.filters.update(search_filters_register.filters)
register.tags.update(custom_field_filters_register.tags)
register.filters.update(custom_field_filters_register.filters)


@register.filter()
@stringfilter
def markdown(value):
    return md.markdown(value, extensions=["markdown.extensions.fenced_code"])


@register.simple_tag
def combine_text_with_pipe(*texts):
    """Combine multiple texts with pipe separator"""
    cleaned_texts = [str(text) if text is not None else "" for text in texts]
    return "|".join(cleaned_texts)


@register.filter
def render_markdown(value):
    # Enable Markdown extensions for better table rendering
    extensions = [
        "markdown.extensions.tables",  # Add support for tables
        "markdown.extensions.fenced_code",  # Optional: for fenced code blocks
    ]

    html = md.markdown(value, extensions=extensions)

    html = html.replace("<table>", '<table class="custom-table">')

    return html


@register.filter(name="random_bg")
def random_bg(bg):
    bg_list = [
        "background: linear-gradient(90deg, rgba(2,0,36,1) 0%, rgba(9,9,121,1) 35%, rgba(0,212,255,1) 100%);",
        "background: linear-gradient(0deg, rgba(34,193,195,1) 0%, rgba(253,187,45,1) 100%);",
        "background: radial-gradient(circle, rgba(63,94,251,1) 0%, rgba(252,70,107,1) 100%);",
        "background: linear-gradient(90deg, rgba(131,58,180,1) 0%, rgba(253,29,29,1) 50%, rgba(252,176,69,1) 100%);",
        "background: radial-gradient(circle, rgba(238,174,202,1) 0%, rgba(148,187,233,1) 100%);",
        "background-image: linear-gradient(180deg, #0093E9 0%, #80D0C7 100%);",
        "background-image: linear-gradient(45deg, #85FFBD 0%, #FFFB7D 100%);",
        "background-image: linear-gradient(19deg, #B721FF 0%, #21D4FD 100%);",
        "background: linear-gradient(to right, #03001e, #7303c0, #ec38bc, #fdeff9); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */",
        "background-image: linear-gradient( 135deg, #F6416C 10%, #FFF6B7 100%);",
        "background-image: linear-gradient( 135deg, #28C76F 10%, #81FBB8 100%);",
    ]
    bg = random.choice(bg_list)

    return bg


@register.simple_tag(takes_context=True)
def query_transform(context, **kwargs):
    query = context["request"].GET.copy()
    # logger.info(query)

    if "page" in query:
        del query["page"]
    if "drawer_type" in query:
        del query["drawer_type"]
        # logger.info(query)

    for k, v in kwargs.items():
        query[k] = v
    return query.urlencode()


@register.simple_tag(takes_context=True)
def get_page_query(context, **kwargs):
    query = context["request"].GET.copy()
    if "drawer_type" in query:
        del query["drawer_type"]
        # logger.info(query)

    for k, v in kwargs.items():
        query[k] = v
    return query.urlencode()


@register.filter(name="get_line_name_channel")
def get_line_name_channel(column_display: str):
    column_displays = column_display.split(" - ")
    if len(column_displays) == 3:
        return column_displays[1] + " - " + column_displays[2]
    else:
        return column_displays


@register.filter(name="get_line_channel_id")
def get_line_channel_id(column_display: str):
    column_displays = column_display.split(" - ")
    if len(column_displays) == 3:
        print(column_displays[0])
        return column_displays[0]
    else:
        return column_displays


@register.filter(name="get_shopify_contact_fields")
def get_shopify_contact_fields(user):
    # channel = Channel.objects.get(pk=channel_id)
    workspace = utility.get_workspace(user)

    contacts_columns = ["name", "last_name", "email", "phone_number"]
    contactsnamecustomfield = (
        ContactsNameCustomField.objects.filter(workspace=workspace)
        .exclude(type__in=["image", "user", "formula"])
        .values_list("name", flat=True)
    )

    contacts_columns.extend(contactsnamecustomfield)
    return contacts_columns


@register.simple_tag()
def convert_lang_url(url, lang_code=None):
    url = translate_url(url, lang_code)
    url = url.replace("http://", "https://")
    return url


@register.filter(name="difference_value")
def difference_value(val1, val2):
    if val1 and val2:
        vals = float(val1) - float(val2)
    else:
        vals = 0
    return abs(vals)


@register.filter(name="file_list")
def file_list(post):
    files = PostFile.objects.filter(content=post)
    file_list = []
    for file in files:
        file_list.append(file.id)

    return file_list


@register.filter
def latest_message_body(message_thread):
    latest_message = Message.objects.filter(thread=message_thread).order_by("-sent_at")
    if latest_message:
        latest_message = latest_message[0]
        if latest_message.body:
            msg = latest_message.body
            if "\n\n" in msg:
                msg = msg.replace("\n\n", " - ")
            return msg
        else:
            return None
    else:
        return None


@register.filter(name="post_file_id_list")
def post_file_id_list(post):
    post_file_id_list = []
    files = PostFile.objects.filter(content=post)
    for f in files:
        post_file_id_list.append(f.id)

    return post_file_id_list


@register.filter(name="ipfs_converter")
def ipfs_converter(ipfs_url):
    if ipfs_url.startswith("ipfs://"):
        ipfs_url = ipfs_url.replace("ipfs://", "https://ipfs.io/ipfs/")
        return ipfs_url
    else:
        return ipfs_url


@register.filter(name="arweave_converter")
def arweave_converter(url):
    if url.startswith("ar://"):
        url = url.replace("ar://", "https://arweave.net/")
        return url
    else:
        return url


@register.filter
def get_item_by_index(li, i):
    try:
        if len(li) == 0 or len(li) == i:
            return None
        return li[i]
    except:
        return None


@register.filter
def get_metric_display(metric_value):
    metric_display_mapper = {
        "followers": "Followers",
        "following": "Following",
        "engagement": "Engagement",
        "posts": "Post Performance",
        "num_posts": "Number of Posts",
        "impressions": "Impressions",
        "retweets": "Retweets",
        "likes": "Likes",
        "analytics_members_new": "Community - New",
        "analytics_members_churned": "Community - Churned",
        "analytics_members_returned": "Community - Returned",
        "floor_price": "Floor Price",
        "floor_cap": "Floor Cap",
    }

    if metric_value not in metric_display_mapper:
        return metric_value
    else:
        return metric_display_mapper[metric_value]


@register.filter
def get_platform_display(value):
    display_mapper = {
        "space": "Space",
        "twitter": "Twitter",
        "discord": "Discord",
        "instagram": "Instagram",
        "facebook": "Facebook",
        "youtube": "Youtube",
        "tiktok": "Tiktok",
        "ethereum": "Ethereum",
        "solana": "Solana",
        "polygon": "Polygon",
        "astar": "Astar",
    }

    if value not in display_mapper:
        return value
    else:
        return display_mapper[value]


@register.filter
def action_input_has_input(input_dict_list: List[dict], action_input: str):
    for input in input_dict_list:
        if input["slug"] == action_input:
            return True
    return False


@register.filter
def splitext(text: str, separator=None):
    if not separator:
        separator = "."
    if text:
        text = text.split(separator)[0]
    return text


@register.filter
def translate_metric(metric: str, lang: Literal["en", "ja"]):
    return reports.translate_metric(metric, lang)


@register.filter
def first_channel_platform(channels):
    if isinstance(channels, list):
        return channels[0].platform
    elif isinstance(channels, dict):
        values = channels.values()
        channel = next(iter(values), None)
        return channel[0].integration.slug

    return None


@register.filter
def get_page_report(paginator_item_begin):
    MAX_PAGE_CONTENT = 30
    if paginator_item_begin:
        page = (paginator_item_begin + (MAX_PAGE_CONTENT - 1)) / MAX_PAGE_CONTENT
        page = int(page)
    else:
        page = None
    return page


@register.filter
def delete_parameter(string):
    if "?" in string:
        string = string.split("?")[0]
    return string


@register.filter
def object_to_list(val):
    return [val]


content = """{{{automation:generate-tweets}}}
## Introduction to Social Media in Japan

Social media has become an integral part of people's lives worldwide, and Japan is no exception.
"""


@register.filter
def url_encode(query):
    try:
        qs = urllib.parse.quote(query)
    except:
        qs = urllib.parse.quote(query.encode("utf8"))
    return qs


@register.filter
def get_attr(obj, name):
    try:
        if not name:
            return obj
        if type(obj) == dict:
            return obj[name]
        if type(obj) == list:
            return obj[int(name)]  # Assuming name is an index for lists
        if type(obj) == defaultdict:
            return obj.get(name, "")

        # Handle many-to-many relationships
        attr = getattr(obj, name)
        # Check if it's a ManyRelatedManager (Django's class for M2M relationships)
        if str(type(attr)).find("ManyRelatedManager") != -1:
            # Return all related objects as a list
            return attr.all()

        # Check if it's a RelatedManager (could be other relationship types)
        if str(type(attr)).find("RelatedManager") != -1:
            # Return all related objects as a list
            return attr.all()

        return attr

    except:
        return None


@register.filter
def get_dict_value(dictionary, key):
    key = str(key)
    return dictionary.get(key, {})


@register.filter
def get_nested_dict_value(dictionary, key, nested_key):
    """Gets a value from a nested dictionary using variable keys."""
    # Convert primary key to string if necessary, matching DELIVERY_STATUS_DISPLAY keys
    key = str(key)
    inner_dict = dictionary.get(key, {})  # Get inner dict or empty dict
    return inner_dict.get(nested_key, "")  # Get nested value or empty string


@register.filter
def get_line_item_dict_value(dictionary, key):
    key = str(key)
    return dictionary.get(key, "")


@register.filter
def safe_get_company_bills(company):
    """Safely get bills for a company with error handling"""
    try:
        from data.models.expensebill import Bill

        return Bill.objects.filter(company=company).order_by("-created_at")
    except Exception as e:
        import logging

        logger = logging.getLogger(__name__)
        logger.error(f"Error getting bills for company {company.id}: {e}")
        return []


@register.filter
def reverse_namespace(namespace):
    return reverse(namespace, host="app")


@register.filter
def as_tuple_with(obj, arg):
    return (obj, arg)


@register.filter
def data_form_filter(obj_list, data_filter: dict):
    """This function help filter data on Sanka Action Form rendering."""
    if not data_filter:
        return obj_list
    operator = data_filter["operator"]
    field = data_filter["field"] if "field" in data_filter else None
    value_type = data_filter["value_type"]
    value = data_filter["value"]

    if operator not in ["==", "!=", "<", ">", "<=", ">=", "in", "not-in"]:
        return obj_list

    res = []
    for obj_ in obj_list:
        obj = obj_
        if field:
            if type(obj_) == dict:
                obj = obj_[field]
            else:
                obj = getattr(obj_, field)

        if operator == "==":
            if obj == value:
                res.append(obj_)
        elif operator == "!=":
            if obj != value:
                res.append(obj_)
        elif operator == "<":
            if obj < value:
                res.append(obj_)
        elif operator == ">":
            if obj > value:
                res.append(obj_)
        elif operator == ">=":
            if obj >= value:
                res.append(obj_)
        elif operator == "<=":
            if obj <= value:
                res.append(obj_)
        elif operator == "in":
            if obj in value:
                res.append(obj_)
        elif operator == "not-in":
            if obj not in value:
                res.append(obj_)

    return res


@register.filter
def get_value(d: dict, value: str):
    if not (d and value):
        return ""
    if value in d:
        return d[value]
    else:
        return ""


@register.filter
def parse_ad_metrics(value, type="money"):
    res = ""
    if value:
        result = value
        if type == "google_money_int":
            result = value / 1000000
            res = "{:d}".format(int(result))
        elif type == "google_money":
            result = value / 1000000
            res = "{:.2f}".format(result)
        elif type == "JPY":
            res = "{:,.0f}".format(result)
        else:
            res = "{:,.2f}".format(result)
    return res


@register.filter
def log_it(obj):
    """Log the input object."""
    logger.info(obj)


@register.filter
def dict_to_list(data):
    if data:
        return [{"key": k, "value": data[k]} for k in data]
    else:
        return []


@register.filter
def flatten_output_data(json_obj):
    if type(json_obj) == str:
        try:
            json_obj = ast.literal_eval(json_obj)
        except:
            return json_obj
    return actions.flatten_output_data(json_obj, key_stopper="type")


@register.filter
def get_value_using_dot_separator(data, key):
    return actions.get_value_using_dot_separator(data, key)


@register.filter
def get_workflow_nodes(obj):
    if obj:
        return get_nodes(obj)
    else:
        return []


@register.filter
def get_all_workflow_integrations(obj):
    if obj:
        res = []
        nodes = get_nodes(obj)
        for node in nodes:
            if node.integration and node.integration not in res:
                res.append(node.integration)
        return res
    else:
        return []


@register.filter
def get_workflows(action):
    workflows = []
    nodes = ActionNode.objects.filter(action=action, workflow__isnull=False)
    for node in nodes:
        workflows.append(node.workflow)
    workflows = list(set(workflows))
    return workflows


@register.filter
def get_page_traffic(page):
    last_seven_days = datetime.now() - timedelta(days=7)
    traffic = AnalyticsEvent.objects.filter(
        page=page, created_at__gte=last_seven_days
    ).count()
    return traffic


@register.simple_tag(name="remove_time_with_lang")
def remove_time_with_lang(val, language_code):
    try:
        if isinstance(val, datetime):
            if language_code == "en":
                return str(val)[:10]
            else:
                print("here")
                return val.date()
    except Exception as e:
        print(e)
        return str(val)[:10]


@register.filter
def as_int(text):
    try:
        res = int(text)
        return res
    except:
        return text


@register.filter(name="comma_tag")
def comma_tag(text):
    if text:
        text = text.split(",")
        text_list = []
        for t in text:
            text_list.append(t.strip())
        # remove last string and empty string
        text_list = [x for x in text_list if x is not None]
        # text_list = list(filter(None, text_list))
        return text_list


@register.simple_tag()
def get_number_of_actions_from_workflow(nodes):
    return len(nodes)


@register.simple_tag()
def get_cost_minutes_from_workflow(nodes):
    minutes = 0
    for node in nodes:
        if node.cost_minutes:
            minutes += node.cost_minutes
    return minutes


@register.filter()
def get_filtering_rule(type):
    return TYPE_TO_RULE[type]


@register.filter()
def last_goal_update_status(updates):
    if updates:
        update = updates.order_by("-date")[0]

        return update.get_status_display()
    return None


@register.filter()
def get_workspace_members(user):
    workspace = utility.get_workspace(user)
    return workspace.user.all()


@register.filter()
def replace_dot_with_dash(input):
    return input.replace(".", "-")


@register.filter()
def replace_underscore_with_space(input):
    return input.replace("_", " ")


@register.filter()
def replace_space_with_strip(input):
    input = input.lower()
    return input.replace(" ", "-")


@register.filter()
def get_task_action_trackers(task):
    return task.taskactiontracker_set.all().order_by("created_at")


@register.filter()
def template_counter(automation_tag):
    count = Workflow.objects.filter(collection=automation_tag).count()
    return count


@register.simple_tag()
def estimated_due(credit, usage):
    if usage >= credit:
        return 0
    else:
        credit = credit - usage
        return credit


@register.filter(name="to_str")
def to_str(value):
    return str(value)


@register.filter(name="to_drawer_type")
def to_drawer_type(value):
    return str(value[:-1])


@register.filter(name="to_list")
def to_list(value):
    try:
        return ast.literal_eval(value)
    except:
        return value


@register.filter()
def get_current_reviewer(task_action_tracker):
    for at in task_action_tracker.actiontracker_set.all():
        if at.status == "review":
            return at.input_data["assignee"]
    return []


@register.filter()
def dummy_users_by_lang(dummy_users, lang):
    try:
        dummy_users = dummy_users.filter(language=lang)
    except:
        return None
    return dummy_users


@register.simple_tag()
def get_30d_traffic(event, slug):
    start_date = timezone.now() - timezone.timedelta(days=30)
    end_date = timezone.now()

    app_session_count = (
        AnalyticsEvent.objects.filter(
            created_at__date__range=[start_date, end_date], event=event, slug=slug
        )
        .values("session")
        .annotate(total=Count("session"))
    )
    return app_session_count.count()


@register.filter
def get_app_solutions(app, count=None):
    if app:
        app_solutions = Solution.objects.filter(apps=app, category="usecase").order_by(
            "order"
        )[:9]
        if count:
            app_solutions = app_solutions[:count]
        return app_solutions


@register.filter
def get_app_workflows(app, lang):
    solutions = get_app_solutions(app)
    if lang == "ja":
        workflows = Workflow.objects.filter(
            created_by_sanka=True, solution__in=solutions, status="published"
        )
    else:
        workflows = Workflow.objects.filter(
            created_by_sanka=True, solution__in=solutions, status="published"
        )

    if len(workflows) < 3:
        if lang == "ja":
            workflows = Workflow.objects.filter(
                created_by_sanka=True, status="published"
            )
        else:
            workflows = Workflow.objects.filter(
                created_by_sanka=True, status="published"
            )
    return workflows[:4]


@register.filter(name="get_files_name")
def get_files_name(text):
    filename = re.sub(r"[^/]+/[0-9a-fA-F-]+\.", "", text)
    return filename


@register.filter(name="get_hierarchy_display_column")
def get_hierarchy_display_column(label: str):
    label = label.split("#")
    label = label[0]
    return label


@register.simple_tag(name="get_member_parent_list")
def get_member_parent_list(request, obj=None, type_object=None):
    workspace = get_workspace(request.user)
    print("type_object: ", type_object)
    if type_object == "company":
        if obj:
            companies = (
                Company.objects.filter(workspace=workspace)
                .exclude(id=obj.id)
                .order_by("-company_id")
            )
        else:
            companies = Company.objects.filter(workspace=workspace).order_by(
                "-company_id"
            )
        return companies

    return []


# REMOVED: get_value_custom_field_company_objects - Use optimization_tags instead
# REMOVED: get_value_custom_field_orders_objects - Use optimization_tags instead


@register.filter(name="get_value_choice_custom_field_orders")
def get_value_choice_custom_field_orders(field_name_id, order_id):
    if is_valid_uuid(field_name_id):
        OrdersCustomFieldValue = ShopTurboOrdersValueCustomField.objects.filter(
            field_name__id=field_name_id, field_name__type="choice", orders__id=order_id
        ).last()
        if OrdersCustomFieldValue:
            return OrdersCustomFieldValue.value
    else:
        shopturbo_order = ShopTurboOrders.objects.filter(id=order_id).first()
        field_value = getattr(shopturbo_order, field_name_id, None)
        return field_value

    return ""


# REMOVED: get_value_custom_field_Items - Use optimization_tags instead
# REMOVED: get_value_custom_field_items_objects - Use optimization_tags instead


# DEPRECATED: get_value_custom_field_inventory - Will be removed after migration to optimization_tags
@register.filter(name="get_value_custom_field_inventory")
def get_value_custom_field_inventory(field_name_id, inventory_id):
    """
    DEPRECATED: This function causes N+1 queries and will be removed.
    Use {% get_custom_field_value_display inventory_custom_fields inventory.id field.id %} instead.
    """
    InventoryCustomFieldValue = ShopTurboInventoryValueCustomField.objects.filter(
        field_name__id=field_name_id, inventory__id=inventory_id
    ).last()
    if InventoryCustomFieldValue:
        if InventoryCustomFieldValue.field_name.type == "image":
            if InventoryCustomFieldValue.file:
                return InventoryCustomFieldValue.file.url
        else:
            return InventoryCustomFieldValue.value
    return ""


# DEPRECATED: get_value_custom_field_inventory_transaction - Will be removed after migration to optimization_tags
@register.filter(name="get_value_custom_field_inventory_transaction")
def get_value_custom_field_inventory_transaction(field_name_id, transaction_id):
    """
    DEPRECATED: This function causes N+1 queries and will be removed.
    Use {% get_custom_field_value_display transaction_custom_fields transaction.id field.id %} instead.
    """
    TransactionCustomFieldValue = InventoryTransactionValueCustomField.objects.filter(
        field_name__id=field_name_id, transaction__id=transaction_id
    ).last()
    if TransactionCustomFieldValue:
        if (
            TransactionCustomFieldValue.field_name.type == "image"
            or TransactionCustomFieldValue.field_name.type == "file"
        ):
            return TransactionCustomFieldValue.inventorytransactionvaluefile_set.last()

        elif TransactionCustomFieldValue.field_name.type == "image_group":
            return TransactionCustomFieldValue.inventorytransactionvaluefile_set.all()

        elif TransactionCustomFieldValue.field_name.type == "purchase_order":
            if TransactionCustomFieldValue.value:
                purchase_order = PurchaseOrders.objects.get(
                    pk=TransactionCustomFieldValue.value
                )
                return purchase_order
        elif TransactionCustomFieldValue.field_name.type == "subscription":
            if TransactionCustomFieldValue.value:
                subscription = ShopTurboSubscriptions.objects.get(
                    pk=TransactionCustomFieldValue.value
                )
                # return f'#{"%04d" % subscription.subscriptions_id}'
                return subscription
        else:
            return TransactionCustomFieldValue.value
    return ""


# DEPRECATED: get_value_custom_field_inventory_warehouse - Will be removed after migration to optimization_tags
@register.filter(name="get_value_custom_field_inventory_warehouse")
def get_value_custom_field_inventory_warehouse(field_name_id, warehouse_id):
    """
    DEPRECATED: This function causes N+1 queries and will be removed.
    Use {% get_optimized_inventory_warehouse_field_value warehouse.id field.id %} instead.
    """
    from data.models.inventory import InventoryWarehouseValueCustomField
    from utils.logger import logger

    logger.info(
        f"🔍 DEBUG get_value_custom_field_inventory_warehouse: field_name_id={field_name_id}, warehouse_id={warehouse_id}"
    )

    WarehouseCustomFieldValue = InventoryWarehouseValueCustomField.objects.filter(
        field_name__id=field_name_id, warehouse__id=warehouse_id
    ).last()

    if WarehouseCustomFieldValue:
        logger.info(
            f"🔍 DEBUG: Found value - ID: {WarehouseCustomFieldValue.id}, Value: {WarehouseCustomFieldValue.value}"
        )
        return WarehouseCustomFieldValue
    else:
        logger.warning(
            f"🔍 DEBUG: No value found for field_name_id={field_name_id}, warehouse_id={warehouse_id}"
        )
        # Also check if any values exist for this warehouse at all
        all_values = InventoryWarehouseValueCustomField.objects.filter(
            warehouse__id=warehouse_id
        )
        logger.info(
            f"🔍 DEBUG: Total values for warehouse {warehouse_id}: {all_values.count()}"
        )
        if all_values.exists():
            for val in all_values[:5]:  # Log first 5 values
                logger.info(
                    f"🔍 DEBUG: Existing value - field_name_id: {val.field_name.id}, value: {val.value}"
                )
        return False


# DEPRECATED: get_value_custom_field_inventory_objects - Will be removed after migration to optimization_tags
@register.filter(name="get_value_custom_field_inventory_objects")
def get_value_custom_field_inventory_objects(field_name_id, inventory_id):
    """
    DEPRECATED: This function causes N+1 queries and will be removed.
    Use {% get_custom_field_value inventory_custom_fields inventory.id field.id %} instead.
    """
    OrdersCustomFieldValue = ShopTurboInventoryValueCustomField.objects.filter(
        field_name__id=field_name_id, inventory__id=inventory_id
    ).last()
    return OrdersCustomFieldValue


# DEPRECATED: get_value_custom_field_inventory_transaction_objects - Will be removed after migration to optimization_tags
@register.filter(name="get_value_custom_field_inventory_transaction_objects")
def get_value_custom_field_inventory_transaction_objects(field_name_id, transaction_id):
    """
    DEPRECATED: This function causes N+1 queries and will be removed.
    Use {% get_custom_field_value transaction_custom_fields transaction.id field.id %} instead.
    """
    TransactionCustomFieldValue = InventoryTransactionValueCustomField.objects.filter(
        field_name__id=field_name_id, transaction__id=transaction_id
    ).last()
    return TransactionCustomFieldValue


# DEPRECATED: get_value_custom_field_inventory_warehouse_objects - Will be removed after migration to optimization_tags
@register.filter(name="get_value_custom_field_inventory_warehouse_objects")
def get_value_custom_field_inventory_warehouse_objects(field_name_id, warehouse_id):
    """
    DEPRECATED: This function causes N+1 queries and will be removed.
    Use {% get_optimized_inventory_warehouse_field_object warehouse.id field.id %} instead.
    """
    from data.models.inventory import InventoryWarehouseValueCustomField

    WarehouseCustomFieldValue = InventoryWarehouseValueCustomField.objects.filter(
        field_name__id=field_name_id, warehouse__id=warehouse_id
    ).last()
    return WarehouseCustomFieldValue


# For Billings adnd Purchase Order app custom tags


@register.filter(name="filter_order")
def filter_order(model, param):
    if model:
        order_params = [p.strip() for p in param.split(",")]
        model = model.order_by(*order_params)
    return model


@register.filter(name="filter_item_property_in_order")
def filter_item_property_in_order(models, param):
    param = param.replace("item - ", "")
    param = param.replace("商品 - ", "")

    result = []
    if models:
        models = models.order_by("order_view")
    else:
        return result
    workspace = models[0].order.workspace

    custom_field = ShopTurboItemsNameCustomField.objects.filter(
        workspace=workspace, name=param
    ).first()
    for model in models:
        item = model.item
        custom_value = ShopTurboItemsValueCustomField.objects.filter(
            field_name=custom_field, items=item
        ).first()
        result.append(custom_value)

    return result


@register.filter(name="filter_price")
def filter_price(model, param):
    if model:
        model = model.annotate(
            default_first=Case(
                When(default=True, then=Value(True)),
                default=Value(False),
                output_field=BooleanField(),
            )
        ).order_by("-default_first", param)
    return model


@register.simple_tag(name="filter_price_by_currency")
def filter_price_by_currency(item_price, currency):
    # Handle case when item_price is a string or None
    if item_price is None or isinstance(item_price, str):
        return []

    # If it's a valid queryset, filter by currency
    try:
        item_price = item_price.filter(currency=currency).order_by("-created_at")
        return item_price
    except AttributeError:
        # Return empty list if item_price doesn't have filter method
        return []


@register.simple_tag(name="get_item_customer_price")
def get_item_customer_price(model: ShopTurboItems = None, customer=None):
    shopturbo_customer_item_price = None
    if model and customer:
        filter_conditions = Q(item=model)
        filter_conditions &= Q(contact_id=customer) | Q(company_id=customer)
        shopturbo_customer_item_price = ShopTurboCustomerItemsPrice.objects.filter(
            filter_conditions
        ).order_by("created_at")
    return shopturbo_customer_item_price


@register.filter(name="shopturbo_price_range")
def shopturbo_price_range(item_model):
    price_range = ShopTurboItemsVariations.objects.filter(items=item_model).aggregate(
        highest_price=Max("price"), lowest_price=Min("price")
    )

    return {
        "highest_price": price_range.get("highest_price", 0),
        "lowest_price": price_range.get("lowest_price", 0),
    }


@register.filter(name="maps_view_columns")
def maps_view_columns(contacts_columns, idx):
    return contacts_columns[idx]


@register.simple_tag(name="translate_lang", takes_context=True)
def translate_lang(context, BaseString, language_code):
    return translate_language(BaseString, language_code)


@register.filter
def to_tagify_json(value):
    """Convert custom field value to Tagify-compatible JSON"""
    if not value:
        return ""

    try:
        # If it's already a string representation of a list
        if isinstance(value, str):
            # Convert the Python-repr string into a Python object, then dump as JSON.
            parsed = ast.literal_eval(value)
            return json.dumps(parsed)

        # If it's already a list/dict
        elif isinstance(value, (list, dict)):
            return json.dumps(value)

        return ""
    except Exception:
        return ""


@register.filter(name="get_shopturboitem_order")
def get_shopturboitem_order(models, type):
    if type == "number_of_items":
        number_of_items = 0
        for model in models:
            number_of_items += model.number_item
        return number_of_items

    elif type == "item_price_order":
        item_price_order = 0
        for idx, model in enumerate(models):
            if model.item_price_order:
                item_price_order += int(model.number_item) * model.item_price_order

        return item_price_order

    elif type == "total_price":
        total_price = 0
        for model in models:
            total_price += model.total_price

        if models:
            if models[0].order.tax:
                total_price = total_price + (total_price * (models[0].order.tax / 100))

        return total_price
    elif type == "name":
        names = ""
        for idx, model in enumerate(models):
            if idx == 0:
                names += model.item.name
            else:
                names += " | " + model.item.name

        return names

    return 0


@register.filter(name="get_ltv_ad")
def get_ltv_ad_tag(ad_id):
    return get_ltv_ad(ad_id)


@register.filter(name="get_roas_ad")
def get_roas_ad_tag(ad_id, is_campaign=True):
    return get_roas_ad(ad_id, is_campaign)


@register.filter(name="get_order_amount_ad")
def get_order_amount_ad_tag(ad_id):
    return get_order_amount_ad(ad_id)


@register.filter(name="get_total_price_sum")
def get_total_price_sum_tag(ad_id):
    return get_total_price_sum(ad_id)


@register.filter(name="get_file_extension")
def get_file_extension(value):
    return value.split(".")[-1]


@register.simple_tag()
def timezone_items():
    return dict(Workspace.timezone.field.choices).items()


@register.filter(name="normalized_double_bytes")
def normalized_double_bytes(text):
    if needs_normalization(text):
        text = normalize_japanese_text(text)
    return text


@register.filter()
def get_index(li_data, i):
    try:
        res = li_data[i]
    except:
        return None
    return res


@register.filter()
def filter_input_parser(input_parser):
    if type(input_parser) != list or (
        type(input_parser) == str
        and input_parser.startswith("[")
        and input_parser.endswith("]")
    ):
        return input_parser

    all = []
    for data in input_parser:
        all.append(data["value"])

    return ",".join(all)


@register.filter()
def sort_order(objects):
    # Handle cases where objects is not a QuerySet (e.g., None, string, etc.)
    if not objects or not hasattr(objects, "order_by"):
        return []

    try:
        objects = objects.order_by(F("order").asc(nulls_last=True))
        return objects
    except Exception:
        # Return empty list if any error occurs
        return []


@register.filter()
def order_by(li, col):
    """Order queryset by input column."""
    return li.order_by(col)


@register.filter(name="isnumeric")
def isnumeric(value):
    return str(value).isnumeric()


@register.filter()
def get_display_slip_type(row_type="", request=None):
    if row_type and row_type in DEFAULT_SLIPS_TYPE_DISPLAY:
        row_type = DEFAULT_SLIPS_TYPE_DISPLAY[row_type][request.LANGUAGE_CODE]
    elif is_valid_uuid(row_type):
        customField = SlipNameCustomField.objects.filter(id=row_type).first()
        if customField:
            row_type = customField.name
    return row_type


@register.filter
def get_display_purchase_order_status(row_type="", request=None):
    if row_type and row_type["value"] in PURCHASE_ORDER_STATUS_DISPLAY:
        row_type = PURCHASE_ORDER_STATUS_DISPLAY[row_type["value"]][
            request.LANGUAGE_CODE
        ]
    else:
        row_type = row_type["label"]
    return row_type


@register.filter
def get_display_tax_type(row_type="", request=None):
    if row_type and row_type in DEFAULT_TAX_DISPLAY:
        row_type = DEFAULT_TAX_DISPLAY[row_type][request.LANGUAGE_CODE]
    return row_type


@register.simple_tag
def get_value_custom_line_item(item_object_id, line_item_id, object_type):
    if object_type == "commerce_orders":
        line_item = ShopTurboItemsOrdersValueCustomField.objects.filter(
            field_name__id=line_item_id, item_order__id=item_object_id
        ).first()
        return line_item
    return None


# Search Custom Field by it's Object


@register.filter()
def get_hierarchy_display(arg, request):
    workspace = utility.get_workspace(request.user)
    if arg:
        arg = arg.split("|")
        custom_field_id = arg[0]
        hierarchy_label = arg[2]
        custom_field = CompanyNameCustomField.objects.filter(
            id=custom_field_id, workspace=workspace
        ).first()
        if custom_field:
            fields_hierarchy = ast.literal_eval(custom_field.choice_value)
            for f_ in fields_hierarchy:
                if f_["label"] == hierarchy_label:
                    return custom_field.name + " - " + f_["label"]
    return arg


@register.simple_tag(name="get_name_custom_field_objects")
def get_name_custom_field_objects(object_type, custom_field_id):
    page_obj = get_page_object(object_type)
    custom_model = page_obj["custom_model"]
    custom_field = custom_model.objects.filter(id=custom_field_id).first()
    return custom_field


@register.filter()
def get_column_display(arg, request):
    lang = request.LANGUAGE_CODE
    workspace = utility.get_workspace(request.user)
    result = property_display(arg, lang, workspace)
    # print(f"DEBUG GET_COLUMN_DISPLAY: Input arg: {arg}")
    # print(f"DEBUG GET_COLUMN_DISPLAY: Result: {result}")
    # if isinstance(result, dict) and 'commerce_inventory|' in str(arg):
    #     print(
    #         f"DEBUG GET_COLUMN_DISPLAY: Inventory custom property result name: {result.get('name', 'NO_NAME')}")
    return result


@register.filter()
def checking_volume_price(
    item_price: ShopTurboItemsPrice, shopturbo_item_order: ShopTurboItemsOrders = None
):
    if item_price.volume_price and shopturbo_item_order:
        if shopturbo_item_order.number_item:
            number_item = int(shopturbo_item_order.number_item)
            for volume_price in item_price.shopturbo_item_volume_price.all():
                # volume_price: ",volume_price)
                if not volume_price.minimum:
                    volume_price.minimum = 0
                if volume_price.maximum:
                    if (
                        number_item >= volume_price.minimum
                        and number_item <= volume_price.maximum
                    ):
                        return volume_price.price
                else:
                    if number_item >= volume_price.minimum:
                        return volume_price.price
    return 0


@register.filter()
def get_inventory_column_display(row_type, warehouse):
    custom_field = InventoryWarehouseNameCustomField.objects.get(pk=row_type)
    custom_field_value = ""
    if custom_field:
        custom_value = InventoryWarehouseValueCustomField.objects.filter(
            field_name=custom_field, warehouse=warehouse
        ).first()
        if custom_field.name in ("Location Name", "ロケーション"):
            formula = custom_field.choice_value
            for index, custom_name in enumerate(ast.literal_eval(formula)):
                custom_value = InventoryWarehouseValueCustomField.objects.filter(
                    field_name__id=custom_name["value"], warehouse=warehouse
                ).first()
                if custom_value:
                    if custom_value.value:
                        if index == 0:
                            custom_field_value = custom_field_value + custom_value.value
                        else:
                            custom_field_value = (
                                custom_field_value + "-" + custom_value.value
                            )
            if custom_field_value:
                if custom_field_value[0] == "-":
                    custom_field_value = custom_field_value[1:]
        else:
            if custom_value:
                if custom_value.value:
                    custom_field_value = custom_value.value
    return custom_field_value


@register.filter()
def format_display_column(row_type=""):
    if row_type:
        return row_type.lower().replace(" ", "_")


@register.filter(name="get_component_quantity")
def get_component_quantity(type_id, parent_group_id_component_id):
    parent_group_id = parent_group_id_component_id.split("|")[0]
    component_id = parent_group_id_component_id.split("|")[1]
    custom_field = ShopTurboItemsNameCustomField.objects.filter(id=type_id).first()
    if custom_field:
        field_value = ShopTurboItemsValueCustomField.objects.filter(
            field_name=custom_field, items=parent_group_id
        ).first()
        if field_value:
            component = field_value.components.filter(
                item__id=parent_group_id, item_component__id=component_id
            ).first()
            if component:
                return str(component.quantity)

    return ""


@register.simple_tag(takes_context=True)
def get_display_property(context, row_type, page_group_type):
    request = context["request"]
    lang = request.LANGUAGE_CODE
    page_obj = get_page_object(page_group_type, lang)
    if row_type in page_obj["columns_display"]:
        return page_obj["columns_display"][row_type][lang]
    return ""


@register.filter
def get_object_records(data_filter, request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    object_type = OBJECT_SLUG_TO_OBJECT_TYPE.get(data_filter)
    if not object_type:
        return ""
    page_obj = get_page_object(object_type, lang)
    base_model = page_obj["base_model"]

    if base_model:
        field_names = [field.name for field in base_model._meta.get_fields()]
        if "usage_status" in field_names:
            objects = base_model.objects.filter(
                workspace=workspace, usage_status="active"
            )
        else:
            objects = base_model.objects.filter(workspace=workspace, status="active")
        return objects
    return ""


@register.filter(name="checking_log_related_application")
def checking_log_related_application(app_log: AppLog):
    exist = False

    # Get all fields dynamically
    fields = [f.name for f in AppLog._meta.get_fields() if isinstance(f, Field)]

    # remove
    for ex_field in [
        "id",
        "workspace",
        "user",
        "action",
        "target",
        "field_name",
        "old_value",
        "new_value",
        "created_at",
    ]:
        fields.remove(ex_field)

    # Print values for each field
    for field in fields:
        value = getattr(app_log, field)
        if value:
            exist = True
            break

    return exist


@register.filter(name="page_group_to_object")
def page_group_to_object(page_group, lang="en"):
    if page_group in OBJECT_GROUP_TYPE:
        return OBJECT_GROUP_TYPE[page_group][lang]
    else:
        return page_group


@register.filter(name="page_group_to_object_singular")
def page_group_to_object_singular(page_group, lang="en"):
    if page_group in OBJECT_GROUP_TYPE_SINGULAR:
        try:
            return OBJECT_GROUP_TYPE_SINGULAR[page_group][lang]
        except KeyError:
            return page_group
    else:
        if is_valid_uuid(page_group):
            try:
                custom_field = CustomObject.objects.get(id=page_group)
                return custom_field.name
            except CustomObject.DoesNotExist:
                return page_group
        return page_group


@register.filter
def system_setting_type(value, request):
    lang = request.LANGUAGE_CODE

    if value:
        try:
            # Note: "employees" and "user management" refer to the same entity here.
            if value == "user_management":
                if lang == "ja":
                    return f"従業員/{SYSTEM_SETTING_TYPE[value][lang]}"
                else:
                    return f"Employees/{SYSTEM_SETTING_TYPE[value][lang]}"
            return SYSTEM_SETTING_TYPE[value][lang]
        except KeyError:
            pass

    return value


@register.filter(name="page_group_to_object_plural")
def page_group_to_object_plural(page_group, lang="en"):
    if page_group in OBJECT_GROUP_TYPE_PLURAL:
        try:
            return OBJECT_GROUP_TYPE_PLURAL[page_group][lang]
        except KeyError:
            return page_group
    else:
        if is_valid_uuid(page_group):
            try:
                custom_field = CustomObject.objects.get(id=page_group)
                return custom_field.name
            except CustomObject.DoesNotExist:
                return page_group
        return page_group


@register.filter(name="page_group_to_name")
def page_group_to_name(value):
    if value:
        try:
            value = value[:-1]
            return value
        except:
            return value
    return


@register.filter(name="shorten_uuid")
def shorten_uuid(id):
    return str(id)[:4]


@register.filter(name="get_json_value")
def get_json_value(dictionary: dict, key):
    try:
        return dictionary.get(key)
    except:
        return ""


@register.simple_tag(name="get_association_label_on_target_object")
def get_association_label_on_target_object(
    association_label: AssociationLabel, object_target: str
):
    search_term = association_label.object_source
    condition = Q(object_source=object_target, created_by_sanka=True)
    condition &= (
        Q(object_target__exact=search_term)
        | Q(object_target__startswith=f"{search_term},")
        | Q(object_target__endswith=f",{search_term}")
        | Q(object_target__contains=f",{search_term},")
    )
    association_label = AssociationLabel.objects.filter(condition).first()

    if association_label:
        return association_label.label
    else:
        return None


@register.simple_tag(takes_context=True)
def get_association_label(context, object_type, label_name: str):
    # workspace
    request = context["request"]
    workspace = get_workspace(request.user)
    
    if is_valid_uuid(label_name):
        association_label = AssociationLabel.objects.filter(id=label_name).first()
    else:
        association_label = AssociationLabel.objects.filter(
            workspace=workspace, object_source=object_type, label=label_name
        ).first()

    if association_label:
        return association_label
    else:
        return None


# @register.filter()
# def get_quote_obj(id):
#     if id and is_valid_uuid(id):
#         try:
#             return Quote.objects.get(id=id)
#         except:
#             pass
#     return None


@register.filter()
def is_uuid(id):
    try:
        if is_valid_uuid(id):
            return True
        else:
            return False
    except:
        return False


@register.filter(name="get_next_engine_payments_choices")
def get_next_engine_payments_choices(lang):
    return NEXT_ENGINE_PAYMENT_METHOD_LIST[lang]


@register.filter(name="get_next_engine_shipping_choices")
def get_next_engine_shipping_choices(lang):
    return NEXT_ENGINE_SHIPPING_METHOD_LIST[lang]


@register.filter(name="parse_header_item")
def parse_header_item(dictionary, key="value"):
    try:
        return dictionary.get(str(key))
    except:
        return


@register.filter(name="parse_header_item_internal")
def parse_header_item_internal(dictionary, key="value"):
    try:
        value = dictionary.get(str(key))
        if value:
            internal_name = value.split("|")[0]
            return internal_name
        else:
            return ""
    except:
        return ""


@register.filter(name="get_bundle_item_id")
def get_bundle_item_id(item: ShopTurboItems):
    item_ids = []
    if item:
        bundles = item.shopturboitemsbundle_set.all()
        item_ids = [str(bundle_item.item_bundle.id) for bundle_item in bundles]
    return item_ids


@register.filter()
def sort_services(services):
    # Handle cases where services is not a QuerySet (e.g., None, string, etc.)
    if not services or not hasattr(services, "filter"):
        return []

    try:
        services = services.filter(type="service").order_by(
            F("order").asc(nulls_last=True)
        )
        return services
    except Exception:
        # Return empty list if any error occurs
        return []


@register.filter()
def sort_articles_content_detail(articles, type):
    # Handle cases where articles is not a QuerySet (e.g., None, string, etc.)
    if not articles or not hasattr(articles, "filter"):
        return []

    try:
        articles = articles.filter(status="published", category=type).order_by(
            F("order").asc(nulls_last=True)
        )
        return articles
    except Exception:
        # Return empty list if any error occurs
        return []


@register.filter
def convert_user_id_to_user(user_id):
    try:
        user = User.objects.filter(id=user_id).first()
        return user
    except:
        return None


@register.filter
def in_list_semicolon(value, arg):
    if arg:
        return value in arg.split(";")
    else:
        return False


@register.filter
def in_text(value, arg):
    return value in arg


@register.filter
def get_list(value, separator=","):
    if value:
        return value.split(separator)
    else:
        return []


@register.filter
def separate_email_subject(value, separator="\n\n"):
    if value:
        return value.split(separator)
    else:
        return []


@register.filter
def get_volume_price(item_price: ShopTurboItemsPrice):
    volume_prices = []
    for volume_price in item_price.shopturbo_item_volume_price.all():
        volume_prices.append(
            {
                "min": volume_price.minimum,
                "max": volume_price.maximum,
                "price": volume_price.price,
            }
        )

    return volume_prices


@register.filter
def convert_currency_to_symbol(currency):
    try:
        symbol = CurrencySymbols.get_symbol(currency)
        if symbol != None:
            return symbol
        else:
            return currency
    except:
        return currency


@register.filter(name="parse_contact_phone_number")
def parse_contact_phone_number(phone_number, parsing_type="all"):
    header = ""
    body = ""
    try:
        if len(phone_number) > 0:
            body = phone_number.replace("+", "")
            header, body = phone_number.split("-")
    except:
        header = ""
    if parsing_type == "body":
        return body
    elif parsing_type == "header":
        return header
    else:
        return header + body


@register.filter
def make_range(value):
    return range(int(value))


@register.filter
def to_positive(value):
    return (value) * -1


@register.filter()
def checking_uuid_customfields(uuid, custom_field_type):
    if custom_field_type == "items":
        if ShopTurboItemsNameCustomField.objects.filter(id=uuid).first():
            return True
    elif custom_field_type == "inventory":
        if ShopTurboInventoryNameCustomField.objects.filter(id=uuid).first():
            return True
    elif custom_field_type == "inventory-transaction":
        if InventoryTransactionNameCustomField.objects.filter(id=uuid).first():
            return True
    elif custom_field_type == "orders":
        if ShopTurboOrdersNameCustomField.objects.filter(id=uuid).first():
            return True
    elif custom_field_type == "subscriptions":
        if ShopTurboSubscriptionsNameCustomField.objects.filter(id=uuid).first():
            return True
    elif custom_field_type == "contacts":
        if ContactsNameCustomField.objects.filter(id=uuid).first():
            return True
    elif custom_field_type == "company":
        if CompanyNameCustomField.objects.filter(id=uuid).first():
            return True
    elif custom_field_type == "deals":
        if DealsNameCustomField.objects.filter(id=uuid).first():
            return True
    elif custom_field_type == "task":
        if TaskCustomFieldName.objects.filter(id=uuid).first():
            return True
    return False


@register.filter(name="model_filter")
def model_filter(model, param):
    if model:
        param = ast.literal_eval(param)
        model = model.filter(**param)
    return model


@register.filter()
def fix_url(url):
    if url:
        if url.startswith("http://") or url.startswith("https://"):
            return url
        else:
            return "http://" + url
    return ""


@register.filter
def subscription_length(start_date, end_date):
    if not start_date:
        return ""
    if not end_date:
        end_date = timezone.now()

    try:
        diff = (
            (end_date.year - start_date.year) * 12 + end_date.month - start_date.month
        )
        if end_date.day <= start_date.day:
            diff -= 1
        if diff <= 0:
            return 1
        return diff
    except ValueError:
        return ""


@register.simple_tag
def total_revenue(start_date, end_date, total_price):
    if not start_date or not total_price:
        return ""
    if end_date == None:
        end_date = timezone.now()

    try:
        total_price = float(total_price)

        total_months = (end_date.year - start_date.year) * 12 + (
            end_date.month - start_date.month
        )
        if end_date.day <= start_date.day:
            total_months -= 1
        if total_months <= 0:
            total_months = 1

        revenue = total_months * total_price
        return "{:,}".format(revenue)
    except (ValueError, AttributeError):
        return ""


@register.filter
def check_integer(value, value_type=None):
    num_val = 0
    list_return = []
    try:
        if value_type == "list":
            value = value.ast_literal_eval(value)
            for val in value:
                num_val = float(val)
                if num_val.is_integer():
                    num_val = str(int(num_val))
                list_return.append(num_val)
            return list_return
        else:
            num_val = float(value)
            if num_val.is_integer():
                num_val = int(num_val)
            return num_val
    except:
        return num_val


@register.filter()
def sort_solutions(solutions):
    solutions = solutions.order_by(F("order").asc(nulls_last=True))
    return solutions


@register.filter()
def sort_child_solutions(child_solutions):
    """Sort child solutions by their order field, with null values last"""
    child_solutions = child_solutions.order_by(F("order").asc(nulls_last=True))
    return child_solutions


@register.filter(name="filename")
def filename(value):
    return value.split("/")[-1]


@register.filter
def float_to_int(val):
    if isinstance(val, float):
        if val.is_integer():
            return int(val)
        return val
    else:
        return val


@register.filter()
def is_list(value):
    return isinstance(value, list)


@register.filter()
def is_dict(value):
    try:
        return isinstance(ast.literal_eval(value), dict)
    except json.JSONDecodeError:
        return False
    except Exception:
        return False


@register.filter
def is_default_vormula_price(item: ShopTurboItems):
    return item.shopturbo_item_price.filter(default=True).exists()


@register.simple_tag
def range_to(start, end, step=1):
    return [i for i in range(int(start), int(end) + step, step)]


@register.filter
def workspace_has_quota(workspace, category):
    sync_usage(workspace, category)
    return has_quota(workspace, category)


@register.filter(name="get_customer_value")
def get_customer_value(obj, column):
    value = None
    field_name = column.replace("Customer-", "")

    try:
        if obj.contact:
            customer_values = ContactsValueCustomField.objects.filter(
                contact=obj.contact, field_name__name=field_name
            )
            if len(customer_values) > 0:
                customer_value = customer_values[0]
                value = customer_value.value
    except Exception as e:
        print("get_customer_value ERROR", e)
    return value


@register.filter
def get_workspace_usage(workspace, category):
    meter, created = Meter.objects.get_or_create(workspace=workspace, category=category)
    if created:
        sync_usage(workspace, category)
        meter.refresh_from_db()
    if meter.count == None:
        meter.count = 0
        meter.save()
    return meter.count


@register.filter
def get_storage_limit(workspace, category):
    try:
        usage_limit = UsageLimit.objects.get(workspace=workspace, category=category)
    except UsageLimit.DoesNotExist:
        tier = STARTER_PRICING_TIER
        if workspace.subscription in [STARTER_PRICING_TIER, STANDARD_PRICING_TIER]:
            tier = workspace.subscription
        # Use .first() instead of .get() to handle potential duplicates gracefully
        base_pricing = (
            BasePricing.objects.filter(
                tier=tier,
                category=category,
                payment_frequency=MONTHLY,
                is_base_plan=False,
            )
            .order_by("created_at")
            .first()
        )

        if not base_pricing:
            # Return default limit if no BasePricing record found
            return 100
        limit = base_pricing.limit

        usage_limit, created = UsageLimit.objects.get_or_create(
            workspace=workspace, category=category, defaults={"value": limit}
        )
        if created:
            sync_usage(workspace, category)
        else:
            # Update the value if the record already exists
            usage_limit.value = limit
            usage_limit.save()
    except UsageLimit.MultipleObjectsReturned:
        # Handle the case where multiple records exist - use the first one
        usage_limit = (
            UsageLimit.objects.filter(workspace=workspace, category=category)
            .order_by("created_at")
            .first()
        )
    return usage_limit.value


@register.filter(name="get_currency_symbol")
def get_currency_symbol(currency):
    try:
        if currency is None:
            # Default to JPY for Japanese users, USD for others
            from django.utils.translation import get_language

            lang = get_language()
            currency = "JPY" if lang == "ja" else "USD"
            print(
                f"Currency was None, defaulting to {currency} based on language {lang}"
            )

        currency = str(currency).upper()
        currency_dict = {code: symbol for code, _, symbol in CURRENCY_MODEL}

        currency_symbol = currency_dict.get(currency)
        if currency_symbol is None:
            print(f"No symbol found for currency code: {currency}")
            return currency
    except Exception as e:
        print(f"Error in get_currency_symbol: {e}")
        currency_symbol = currency
    return currency_symbol


@register.filter
def get_storage_title(storage_category, lang):
    try:
        return USAGE_CATEGORIES_TITLE[storage_category][lang]
    except:
        return ""


@register.filter
def metric_name(metric, lang):
    if metric not in PANEL_METRIC_TITLE:
        return None
    return PANEL_METRIC_TITLE[lang]


@register.filter
def get_journal_category_property_translated(selected_category, request):
    try:
        lang = request.LANGUAGE_CODE
        default_choice_list = {}
        for key, category in DEFAULT_JOURNAL_CATEGORY.copy().items():
            for choice in category["choices"]:
                default_choice_list[choice["value"]] = [choice["en"], choice["ja"]]
        if selected_category["value"] in default_choice_list:
            if lang == "ja":
                lang = 1
            else:
                lang = 0
            return default_choice_list[selected_category["value"]][lang]
        else:
            return selected_category["label"]
    except Exception:
        print(selected_category)
        pass

    return ""


@register.filter
def get_default_status_display(obj, field="status"):
    try:
        obj_type = None
        # Handle case when obj is a string
        if isinstance(obj, str):
            # If obj is a string like 'invoice', we need to get the model class
            # This is a fallback for when a string is passed instead of a model instance
            if obj == "invoice":
                from data.models import Invoice

                obj = Invoice
            elif obj == "estimate":
                from data.models import Estimate

                obj = Estimate
            elif obj == "receipt":
                from data.models import Receipt

                obj = Receipt
            elif obj == "deliveryslip":
                from data.models import DeliverySlip

                obj = DeliverySlip
            elif obj == "slip":
                from data.models import Slip

                obj = Slip
            else:
                # If we can't determine the model, return empty dict
                return {}

        # Continue with normal processing for model instances or classes
        if type(obj) != dict:
            obj = obj._meta.model
        else:
            obj = obj["model"]

        """ Refactor to simpler code for getting status display """
        page_group_type = base_model_to_object_type(obj)
        page_obj = get_page_object(page_group_type)
        editable_columns = page_obj["editable_columns"]
        try:
            status_display = editable_columns[field]
        except:
            status_display = {}

        if status_display:
            return status_display
        else:
            choices = obj._meta.get_field(field).choices
            if choices:
                for key, display_name in choices:
                    status_display[key] = {
                        "en": display_name,
                        "ja": translate_language(display_name, "ja"),
                    }
            else:
                logger.info("obj does not have choices for field: %s", field)
                return ""

        return status_display

    except Exception as e:
        traceback.print_exc()
        print("[get_default_status_display] Error: ,", e)

    return None


@register.simple_tag(takes_context=True)
def read_display_column_with_request(
    context, property, default_property_column_display
):
    request = context["request"]
    result = property

    if default_property_column_display and property:
        try:
            result = default_property_column_display[property][
                str(request.LANGUAGE_CODE)
            ]
        except Exception:
            pass

    return result


@register.filter()
def calculate_transaction_total_value(transaction, calculate_type=None):
    try:
        if calculate_type == "avg":
            if not transaction.average_price:
                return 0
            else:
                return float(transaction.average_price * transaction.transaction_amount)
        else:
            if not transaction.amount or not transaction.price:
                return 0
            else:
                return float(transaction.price * transaction.amount)
    except Exception as e:
        print(f"ERROR === custom_tags.py -- 2599: {e}")
        return None


@register.filter()
def calculate_transaction_avg_total_value(transaction, amount):
    try:
        if not transaction.average_price:
            return 0
        else:
            return float(transaction.average_price * int(amount))

    except Exception as e:
        print(f"ERROR === custom_tags.py -- 2599: {e}")
        return None


@register.filter()
def get_total_review(workflow):
    """
    Returns the total number of review actions for a given workflow.

    Parameters:
    - workflow: The workflow object for which to calculate the total review actions.

    Returns:
    - The total number of review actions for the given workflow.
    """
    return (
        WorkflowHistory.objects.filter(
            workflow=workflow, workflowactiontracker__actiontracker__status="review"
        )
        .distinct()
        .count()
    )


@register.filter()
def get_total_workflow_history(workflow):
    """
    Returns the total number of review actions for a given workflow.

    Parameters:
    - workflow: The workflow object for which to calculate the total review actions.

    Returns:
    - The total number of review actions for the given workflow.
    """
    return (
        WorkflowHistory.objects.filter(workflow=workflow)
        .exclude(workflowactiontracker__actiontracker__status="review")
        .distinct()
        .count()
    )


# Use for both Contact and Company


@register.simple_tag
def new_created_log(applog: AppLog, request=None):
    lang = "en"
    if request:
        lang = request.LANGUAGE_CODE

    if applog.item:
        return DISPLAY_COLUMN_NEW_CREATED_LOG["item"][lang]
    elif applog.inventory:
        return DISPLAY_COLUMN_NEW_CREATED_LOG["inventory"][lang]
    elif applog.order:
        return DISPLAY_COLUMN_NEW_CREATED_LOG["order"][lang]
    elif applog.subscriptions:
        return DISPLAY_COLUMN_NEW_CREATED_LOG["subscription"][lang]
    elif applog.contact:
        return DISPLAY_COLUMN_NEW_CREATED_LOG["contact"][lang]
    elif applog.company:
        return DISPLAY_COLUMN_NEW_CREATED_LOG["company"][lang]
    elif applog.invoice:
        return DISPLAY_COLUMN_NEW_CREATED_LOG["invoice"][lang]
    elif applog.estimate:
        return DISPLAY_COLUMN_NEW_CREATED_LOG["estimate"][lang]
    elif applog.deliveryslip:
        return DISPLAY_COLUMN_NEW_CREATED_LOG["deliveryslip"][lang]
    elif applog.receipt:
        return DISPLAY_COLUMN_NEW_CREATED_LOG["receipt"][lang]
    elif applog.task:
        return DISPLAY_COLUMN_NEW_CREATED_LOG["task"][lang]
    elif applog.purchaseorders:
        return DISPLAY_COLUMN_NEW_CREATED_LOG["purchaseorders"][lang]
    elif applog.purchaseitems:
        return DISPLAY_COLUMN_NEW_CREATED_LOG["purchaseitems"][lang]
    elif applog.expense:
        return DISPLAY_COLUMN_NEW_CREATED_LOG["expense"][lang]
    elif applog.bill:
        return DISPLAY_COLUMN_NEW_CREATED_LOG["bill"][lang]
    elif applog.deal:
        return DISPLAY_COLUMN_NEW_CREATED_LOG["deal"][lang]

    return "New Created Object"


@register.filter(name="get_inventory_future_amount")
def get_inventory_future_amount(inventory):
    try:
        if inventory:
            # Get the status from the request
            request = get_current_request()
            status = request.GET.get("status") if request else None

            # Filter transactions based on status
            if status == "archived":
                last_transaction = (
                    InventoryTransaction.objects.filter(
                        inventory=inventory, usage_status="archived"
                    )
                    .order_by("-transaction_date", "-created_at")
                    .first()
                )
            else:
                last_transaction = (
                    InventoryTransaction.objects.filter(
                        inventory=inventory, usage_status="active"
                    )
                    .order_by("-transaction_date", "-created_at")
                    .first()
                )

            if last_transaction:
                print(
                    f"[DEBUG inventory] Future amount from transaction for inventory {inventory.id}: {last_transaction.transaction_amount}"
                )
                return last_transaction.transaction_amount
            else:
                # If no transactions exist, fall back to total_inventory field instead of returning 0
                fallback_value = (
                    inventory.total_inventory
                    if inventory.total_inventory is not None
                    else 0
                )
                print(
                    f"[DEBUG inventory] No transactions for future amount, using fallback for inventory {inventory.id}: {fallback_value}"
                )
                return fallback_value
    except Exception as e:
        print(
            f"[ERROR inventory] Error in get_inventory_future_amount for inventory {inventory.id if inventory else 'None'}: {e}"
        )
        return 0


@register.filter(name="is_iso_format_filter")
def is_iso_format_filter(value):
    if type(value) == str:
        return is_iso_format(value)
    else:
        return False


@register.filter(name="check_applog_if_date__ignore_ms")
def check_applog_if_date__ignore_ms(app_log: AppLog):
    if type(app_log.old_value) == str and type(app_log.new_value) == str:
        if is_iso_format(app_log.old_value) and is_iso_format(app_log.new_value):
            date1 = datetime.fromisoformat(app_log.old_value)
            date2 = datetime.fromisoformat(app_log.new_value)

            # Truncate microseconds
            date1 = date1.replace(microsecond=0)
            date2 = date2.replace(microsecond=0)
            if date1 == date2:
                return False

    return True


@register.filter(name="is_number")
def is_number(value):
    try:
        float(value)
        return True
    except:
        return False


@register.filter(name="get_default_price_from_item")
def get_default_price_from_item(item: ShopTurboItems):
    if item:
        if item.shopturbo_item_price.filter(default=True).first():
            return item.shopturbo_item_price.filter(default=True).first().price
    return 0


@register.filter(name="is_not_empty")
def is_not_empty(value):
    if type(value) == list:
        if len(value) > 0:
            is_all_empty = all(item == None for item in value)
            if not is_all_empty:
                return True
    return False


@register.simple_tag(name="generate_uuid")
def generate_uuid():
    return str(uuid.uuid4())


@register.simple_tag(name="object_type__obj_item_id_combination")
def object_type__obj_item_id_combination(object_type, obj_item_id):
    data = f"{object_type}__{obj_item_id}"
    print("data: ", data)
    return str(data)


@register.simple_tag
def custom_property_value(prop_id, page_group_type, obj_id, request):
    workspace = utility.get_workspace(request.user)
    value = ""
    if page_group_type == TYPE_OBJECT_COMPANY:
        prop_val = CompanyValueCustomField.objects.filter(
            field_name__id=prop_id, field_name__workspace=workspace, company__id=obj_id
        ).first()
        if prop_val and prop_val.field_name:
            if prop_val.field_name.type == "number":
                value = prop_val.value_number
            elif prop_val.field_name.type == "date":
                value = prop_val.value_time
            else:
                value = prop_val.value
    elif page_group_type == TYPE_OBJECT_ESTIMATE:
        from data.models import EstimateValueCustomField

        prop_val = EstimateValueCustomField.objects.filter(
            field_name__id=prop_id, field_name__workspace=workspace, estimate__id=obj_id
        ).first()
        if prop_val and prop_val.field_name:
            if prop_val.field_name.type == "number":
                value = prop_val.value_number
            elif prop_val.field_name.type == "date":
                value = prop_val.value_time
            else:
                value = prop_val.value
    return value


@register.filter
def is_manage_object(workspace, page_group_type):
    if page_group_type not in TYPE_OBJECTS:
        return False

    return True


@register.filter(name="should_show_name_field")
def should_show_name_field(property, property_list):
    """
    Check if name field should be shown.
    If first_name is available, last_name will be ignored.
    """
    if property == "first_name":
        return True
    elif property == "last_name":
        return "first_name" not in property_list
    return False


@register.filter()
def convert_mapping_to_list(input_pairs):
    try:
        if input_pairs:
            new_input_pairs = []
            for input_pair in input_pairs:
                new_input_pairs.append(
                    [
                        input_pair,
                        input_pairs[input_pair][1],
                        input_pairs[input_pair][2],
                        input_pairs[input_pair][0],
                    ]
                )
            if new_input_pairs:
                return new_input_pairs
    except Exception as e:
        logger.info(f"[convert_mapping_to_list] Failed to get input_pairs: {e}")
        return []

    return []


@register.simple_tag(takes_context=True)
def date_format(context, date_value, with_time=False):
    try:
        request = context["request"]
        lang = request.LANGUAGE_CODE
        source_integration = request.GET.get("source_integration", None)
        if source_integration == "hubspot":
            workspace_id = request.GET.get("workspace_id", "")
            workspace = Workspace.objects.get(id=workspace_id)
            request.user = workspace.user.first()
        else:
            workspace = get_workspace(request.user)

        # If date_value is None, return empty string
        if date_value is None:
            return ""

        # Handle timezone-aware datetime objects
        if isinstance(date_value, datetime):
            # Get the workspace's date format
            date_format_object = DateFormat.objects.filter(
                workspace=workspace, is_workspace_level=True
            ).first()
            format = DEFAULT_DATE_FORMAT[lang]
            if date_format_object and date_format_object.value:
                format = date_format_object.value

            # Format the date using the workspace's timezone and format
            return format_date_to_str(
                date_value, format, workspace.timezone, with_time=with_time
            )

        return date_value
    except Exception as e:
        print(f"... ERROR === custom_tags.py -- 4622: {e}")
        # Return a more readable format if there's an error
        if isinstance(date_value, datetime):
            try:
                if with_time:
                    return date_value.strftime("%Y-%m-%d %H:%M")
                else:
                    return date_value.strftime("%Y-%m-%d")
            except:
                pass
        return date_value


@register.filter()
def get_billing_cycle(custom_object):
    if custom_object == "billing_cycle":
        return BILLING_CYCLE_MAPPER
    return ""


@register.filter()
def get_billing_cycle_value(value, lang):
    if value and lang:
        return BILLING_CYCLE_MAPPER[value][lang]
    return ""


@register.filter()
def object_type_slug(object_type, request):
    source_integration = request.GET.get("source_integration", None)
    if source_integration == "hubspot":
        channel_id = request.GET.get("channel_id", "")
        workspace = Channel.objects.get(id=channel_id).workspace
    else:
        workspace = get_workspace(request.user)
    if object_type:
        if is_valid_uuid(object_type):
            custom_object = CustomObject.objects.filter(
                id=object_type, workspace=workspace
            ).first()
            if custom_object:
                return custom_object.slug
            return None
        else:
            try:
                return OBJECT_TYPE_TO_SLUG[object_type]
            except:
                custom_object = CustomObject.objects.filter(
                    slug=object_type, workspace=workspace
                ).first()
                if custom_object:
                    return custom_object.slug
    return None


@register.simple_tag()
def price_info_drawer_type(predefined_item):
    drawer_type = "add-item"

    if "item" in predefined_item:
        if predefined_item["item"]:
            if not is_valid_uuid(predefined_item["item"]):
                drawer_type = "add-manual-item"

    return drawer_type


@register.simple_tag()
def allowedCurrencies():
    return [
        "JPY",
        "USD",
        "EUR",
        "GBP",
        "AUD",
        "CAD",
        "NZD",
        "CHF",
        "SGD",
        "CNY",
        "INR",
        "BRL",
        "MXN",
        "HKD",
        "NOK",
        "SEK",
        "DKK",
        "RUB",
        "ZAR",
        "TRY",
    ]


@register.filter(name="filter_pages_by_category")
def filter_pages_by_category(pages, category):
    if pages:
        return pages.filter(category=category).order_by("order")
    return None


@register.filter(name="filter_pages_by_category_order")
def filter_pages_by_category_order(pages, category):
    if pages:
        return pages.filter(category=category).order_by("order")
    return None


@register.simple_tag()
def get_question_answer(entry_id, question_id):
    result = "-"
    try:
        entry = FormResponse.objects.get(id=entry_id)
        question = FormQuestion.objects.get(id=question_id)

        if question.type in ["radio", "checkbox"]:
            answers = FormAnswer.objects.filter(response=entry, question=question)
            for answer in answers:
                answer_options = answer.select_options.all()
                for option in answer_options:
                    if result == "-":
                        result = option.option_text
                    else:
                        result = result + ", " + option.option_text
        elif question.type == "item-price-list":
            answers = FormAnswer.objects.filter(response=entry, question=question)
            for answer in answers:
                answer_options = answer.select_items.all()
                for item in answer_options:
                    if result == "-":
                        result = f"#{item.item_id:04d} - {item.name}"
                    else:
                        result = result + ", " + f"#{item.item_id:04d} - {item.name}"

        elif question.type == "object-choice-field":
            answers = FormAnswer.objects.filter(response=entry, question=question)

            page_obj = get_page_object(question.object_option)
            property_obj = (
                page_obj["custom_model"]
                .objects.filter(id=question.property_option)
                .first()
            )
            if not property_obj:
                return result

            choice_value = ast.literal_eval(property_obj.choice_value)

            for answer in answers:
                answer_choice = answer.select_choice
                for choice in choice_value:
                    if choice["value"] == answer_choice:
                        if result == "-":
                            result = choice["label"]
                            break
                        else:
                            result = result + ", " + choice["label"]

        else:
            answer = FormAnswer.objects.get(response=entry, question=question)
            result = answer.answer_text
    except:
        # traceback.print_exc()
        pass
    return result


@register.filter()
def get_project_objects(request, project_id=None):
    workspace = get_workspace(request.user)
    projects = None
    if workspace:
        projects = Projects.objects.filter(workspace=workspace).order_by("created_at")
    if project_id and projects:
        projects = projects.filter(id=project_id).first()
    return projects


@register.filter()
def get_association_customfield(association: Association):
    custom_model = None
    for attr_name, _ in ASSOCIATE_MAP.items():
        attr = getattr(association, attr_name, None)
        if attr:
            custom_model = attr
            break

    return custom_model


@register.filter()
def get_association_customfield_type(association: Association):
    association_type = None
    for attr_name, attr_type in ASSOCIATE_MAP.items():
        attr = getattr(association, attr_name, None)
        if attr:
            association_type = attr_type
            break

    return association_type


@register.inclusion_tag("data/common/contact_company_link.html", takes_context=True)
def render_contact_company_link(context, page_group_type, source_hx_target=None):
    try:
        bill = context.get("billing", None)
        order_id = context["request"].GET.get("order_id", "")
        module = context["request"].GET.get("module", "")

        bill_id = ""
        if bill:
            bill_id = str(bill.id)
    except:
        order_id = ""
        module = context["request"].GET.get("module", "")

    if source_hx_target == "#create-drawer-content":
        po_hx_vals_contact_data = '{"drawer_type":"orders","type":"create-contacts","addon_source":"addon-purchase-order","source_hx_target":"#create-drawer-content"}'
        po_hx_vals_company_data = '{"drawer_type":"orders","type":"create-company","addon_source":"addon-purchase-order","source_hx_target":"#create-drawer-content"}'
    parameter = {
        "commerce_orders": {
            "request": context["request"],
            "hx_get_contact": reverse("create_contact_from_order", host="app"),
            "hx_vals_contact": '{"drawer_type":"orders","type":"create-contacts","module":"'
            + module
            + '"}',
            "hx_target_contact": "#shopturbo-create-drawer-content",
            "hx_get_company": reverse("create_company_from_order", host="app"),
            "hx_vals_company": '{"drawer_type":"orders","type":"create-company","module":"'
            + module
            + '"}',
            "hx_target_company": "#shopturbo-create-drawer-content",
        },
        "commerce_orders_update": {
            "request": context["request"],
            "hx_get_contact": reverse("load_drawer_orders", host="app"),
            "hx_vals_contact": '{"drawer_type":"orders","type":"create-contacts-manage","order_id":"'
            + order_id
            + '"}',
            "hx_target_contact": "#create-association-wizard-content",
            "hx_get_company": reverse("load_drawer_orders", host="app"),
            "hx_vals_company": '{"drawer_type":"orders","type":"create-company-manage","order_id":"'
            + order_id
            + '"}',
            "hx_target_company": "#create-association-wizard-content",
        },
        "commerce_subscription": {
            "request": context["request"],
            "hx_get_contact": reverse("create_contact_from_subscriptions", host="app"),
            "hx_vals_contact": '{"drawer_type":"orders","type":"create-contacts","addon_source":"addon-subscriptions"}',
            "hx_target_contact": "#shopturbo-create-drawer-content",
            "hx_get_company": reverse("create_company_from_subscriptions", host="app"),
            "hx_vals_company": '{"drawer_type":"orders","type":"create-company","addon_source":"addon-subscriptions"}',
            "hx_target_company": "#shopturbo-create-drawer-content",
        },
        "billing": {
            "request": context["request"],
            "hx_get_contact": reverse("load_drawer_orders", host="app"),
            "hx_vals_contact": '{"drawer_type":"orders","type":"create-contacts","addon_source":"addon-billing"}',
            "hx_target_contact": "#create-new-drawer-content",
            "hx_get_company": reverse("load_drawer_orders", host="app"),
            "hx_vals_company": '{"drawer_type":"orders","type":"create-company","addon_source":"addon-billing"}',
            "hx_target_company": "#create-new-drawer-content",
        },
        "bill": {
            "request": context["request"],
            "hx_get_contact": reverse("create_contact_from_order", host="app"),
            "hx_vals_contact": '{"drawer_type":"orders","type":"create-contacts","addon_source":"addon-bill"}',
            "hx_target_contact": "#expenses_form",
            "hx_get_company": reverse("create_company_from_order", host="app"),
            "hx_vals_company": '{"drawer_type":"orders","type":"create-company","addon_source":"addon-bill"}',
            "hx_target_company": "#expenses_form",
        },
        "expense": {
            "request": context["request"],
            "hx_get_contact": reverse("create_contact_from_order", host="app"),
            "hx_vals_contact": '{"drawer_type":"orders","type":"create-contacts","addon_source":"addon-expense"}',
            "hx_target_contact": "#expenses_form",
            "hx_get_company": reverse("create_company_from_order", host="app"),
            "hx_vals_company": '{"drawer_type":"orders","type":"create-company","addon_source":"addon-expense"}',
            "hx_target_company": "#expenses_form",
        },
        "purchase-order": {
            "request": context["request"],
            "hx_get_contact": reverse("create_contact_from_order", host="app"),
            "hx_vals_contact": '{"drawer_type":"orders","type":"create-contacts","addon_source":"addon-purchase-order"}'
            if not source_hx_target
            else po_hx_vals_contact_data,
            "hx_target_contact": "#procurement_form"
            if not source_hx_target
            else source_hx_target,
            "hx_get_company": reverse("create_company_from_order", host="app"),
            "hx_vals_company": '{"drawer_type":"orders","type":"create-company","addon_source":"addon-purchase-order"}'
            if not source_hx_target
            else po_hx_vals_company_data,
            "hx_target_company": "#procurement_form"
            if not source_hx_target
            else source_hx_target,
        },
    }
    return parameter[page_group_type]


@register.simple_tag
def associate_header(customfield_type, language_code):
    associate_header = {
        TYPE_OBJECT_ORDER: {"ja": "受注", "default": "Orders"},
        TYPE_OBJECT_PURCHASE_ORDER: {"ja": "発注", "default": "Purchase Orders"},
        TYPE_OBJECT_CASE: {"ja": "案件", "default": "Cases"},
        TYPE_OBJECT_TASK: {"ja": "タスク", "default": "Tasks"},
    }

    associate_header = associate_header.get(customfield_type, {})
    return associate_header.get(language_code, associate_header.get("default", ""))


@register.filter()
def get_process_master_value(process_master, key):
    if process_master:
        dict_process_master = json.loads(process_master)
        return dict_process_master.get(key, 0)
    return 0


@register.filter()
def properties_filter_type(properties, filter_type):
    if properties:
        return properties.filter(type=filter_type)
    else:
        return None


@register.simple_tag(takes_context=True)
def get_csv_upload_template(context, page_group_type):
    request = context.get("request")
    if page_group_type in TEMPLATE_FILE:
        template_file = TEMPLATE_FILE[page_group_type][request.LANGUAGE_CODE]
    else:
        template_file = ""
    return template_file


@register.simple_tag(takes_context=True)
def get_excel_upload_template(context, page_group_type):
    request = context.get("request")
    if page_group_type in TEMPLATE_EXCEL_FILE:
        template_file = TEMPLATE_EXCEL_FILE[page_group_type][request.LANGUAGE_CODE]
    else:
        template_file = None
    return template_file


@register.filter
def object_field_value(obj, field_path: str):
    if "__" in field_path:
        fields = field_path.split("__")
        value = obj
        for field in fields:
            # Check if the attribute is a ManyToMany field
            if hasattr(value, "all"):
                value = value.all().first()
                if not value:
                    return ""
            if field.startswith("item_platform|"):
                if "-sku" in field.lower():
                    field_ = "platform_sku"
                elif "-item iD" in field.lower():
                    field_ = "platform_id"
                else:
                    field_ = "variant_id"
                id_ = field.replace("item_platform|", "")[:36]
                channel = Channel.objects.filter(
                    id=id_, workspace=obj.workspace
                ).first()
                value = ShopTurboItemsPlatforms.objects.filter(
                    item=value, channel=channel
                ).first()
                field = field_
                if not value:
                    return ""
            try:
                if field == "purchase_price_currency":
                    field = "currency"
                value = getattr(value, field)
            except:
                return ""
        return value
    else:
        return getattr(obj, field_path)


@register.filter()
def remove_first(source, content):
    res = source.replace(content, "", 1)
    return res


@register.filter
def parse_literal(value):
    try:
        decoded = html.unescape(value)
        return ast.literal_eval(decoded)
    except (ValueError, SyntaxError):
        return []


@register.simple_tag
def parse_dataset_header(field, lang, base_source):
    source, field_name = field.split(" - ", 1)

    if is_valid_uuid(source):
        return parse_dataset_association_header(field, lang, base_source)

    page_obj = get_page_object(source)
    custom_model = page_obj["custom_model"]
    columns_display = page_obj["columns_display"]

    obj_trans = REPORT_OBJECT_GROUP_TYPE.get(source, {"en": source, "ja": source})
    obj_trans = obj_trans.get(lang)

    if "custom_field__" in field_name:
        custom_field = field_name.replace("custom_field__", "")
        try:
            custom_props = custom_model.objects.get(id=custom_field)
            return f"{obj_trans} - {custom_props.name}"
        except:
            return f"{obj_trans} - {custom_field}"
    else:
        trans = columns_display.get(field_name, {"en": field_name, "ja": field_name})
        trans = trans.get(lang)

        return f"{obj_trans} - {trans}"


@register.simple_tag
def parse_dataset_association_header(field, lang, base_source):
    source, field_name = field.split(" - ", 1)
    association_labels = AssociationLabel.objects.filter(id=source).first()
    if association_labels:
        if association_labels.object_source == base_source:
            obj_name = association_labels.object_target
        elif association_labels.object_target == base_source:
            obj_name = association_labels.object_source

        page_obj = get_page_object(obj_name)
        custom_model = page_obj["custom_model"]
        columns_display = page_obj["columns_display"]

        if association_labels.created_by_sanka:
            obj_trans = REPORT_OBJECT_GROUP_TYPE.get(
                obj_name, {"en": obj_name, "ja": obj_name}
            )
            obj_trans = obj_trans.get(lang)
        else:
            obj_trans = (
                association_labels.label_ja
                if lang == "ja"
                else association_labels.label
            )
            if association_labels.object_target == base_source:
                assoc_name = association_labels.label_pair
                if assoc_name:
                    assoc_name = assoc_name.get(base_source)
                    if assoc_name:
                        obj_trans = assoc_name

        if "custom_field__" in field_name:
            custom_field = field_name.replace("custom_field__", "")
            try:
                custom_props = custom_model.objects.get(id=custom_field)
                return f"{obj_trans} - {custom_props.name}"
            except:
                return f"{obj_trans} - {custom_field}"
        else:
            trans = columns_display.get(
                field_name, {"en": field_name, "ja": field_name}
            )
            trans = trans.get(lang)

            return f"{obj_trans} - {trans}"

    return f"{source} - {field_name}"


@register.simple_tag
def parse_operations_pivot(str_, obj, lang, custom_val_trans):
    operations = {
        "sum": {"en": "Sum", "ja": "合計"},
        "average": {"en": "Average", "ja": "平均"},
        "count": {"en": "Count", "ja": "カウント"},
        "min": {"en": "Min", "ja": "最小"},
        "max": {"en": "Max", "ja": "最大"},
        "percentage_column": {
            "en": "Percentage Of Column",
            "ja": "パーセンテージ（列）",
        },
        "percentage_row": {"en": "Percentage Of Row", "ja": "パーセンテージ（行）"},
    }

    sorted_operations = sorted(operations, key=len, reverse=True)
    str_ = str_ if isinstance(str_, str) else str_.__str__()
    for op in sorted_operations:
        if str_.endswith(f"_{op}"):
            object_part = str_[: -(len(op) + 1)]
            operator_part = operations.get(op, {}).get(lang, op)
            field_obj, field = object_part.split(" - ", 1)
            page_obj = get_page_object(field_obj)

            if is_valid_uuid(field):
                custom_obj = page_obj["custom_model"].objects.filter(id=field).first()
                table_col = {
                    "en": custom_obj.name,
                    "ja": custom_obj.name,
                }
            else:
                table_col = page_obj["columns_display"].get(
                    field, {"en": field, "ja": "field"}
                )

            obj_trans = REPORT_OBJECT_GROUP_TYPE.get(
                field_obj, {"en": field_obj, "ja": field_obj}
            )
            if table_col:
                if lang == "ja":
                    result = obj_trans.get("ja") + " - " + table_col.get("ja")
                else:
                    result = obj_trans.get("en") + " - " + table_col.get("en")

            if lang == "ja":
                return f"{result}の{operator_part}"
            else:
                return f"{result} {operator_part}"

    if APPLICANT_DISPLAY_STATUS.get(str_):
        return APPLICANT_DISPLAY_STATUS.get(str_).get(lang)

    arr = str_.split(";")
    trans_arr = []
    for arr_ in arr:
        trans_arr.append(custom_val_trans.get(arr_, arr_))

    return ", ".join(trans_arr)


@register.filter(name="get_currency_of_base_currency")
def get_currency_of_base_currency(obj=None, lang="ja"):
    if obj:
        try:
            workspace = obj.workspace
            if workspace.currencies:
                value = ast.literal_eval(workspace.currencies)[0]
            else:
                if lang == "ja":
                    value = "JPY"
                else:
                    value = "USD"
            return value
        except:
            return ""
    return ""


@register.filter(name="get_first_uuid_hash")
def get_first_uuid_hash(uuid_str):
    import uuid

    if is_uuid(uuid_str):
        return str(uuid_str).split("-")[0]

    return str(uuid.uuid4()).split("-")[0]


@register.simple_tag(name="get_value_company_hierarchy")
def get_value_company_hierarchy(value=None, custom_field_name=None, company=None):
    if value and custom_field_name:
        try:
            custom_values = custom_field_name.companyvaluecustomfield_set.all()
            if custom_values:
                selected_value = custom_values.filter(
                    value=value, company=company
                ).first()
                if selected_value:
                    return selected_value
        except:
            return value
    return value


@register.filter("startswith")
def startswith(text, starts):
    if isinstance(text, str):
        return text.startswith(starts)
    return False


@register.filter("endswith")
def endswith(text, ends):
    if isinstance(text, str):
        return text.endswith(ends)
    return False


@register.filter("count_item_of_items")
def count_item_of_items(objects):  # Used In Purchase Order
    if objects:
        count_item = []
        for obj in objects:
            for obj_item in obj.purchase_order_object.all():
                count_item.append(obj_item)

        return count_item
    return objects


@register.simple_tag(name="apply_language_from_hubspot")
def apply_language_from_hubspot(hs_language):
    return hs_language


@register.filter()
def object_panel_type(value, request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)

    if value:
        if value == "manual":
            return "手入力" if lang == "ja" else "Manual Entry"
        try:
            return OBJECT_GROUP_TYPE[value][lang]
        except:
            val = value
            for _, objt in PANEL_SOURCE_TITLE.items():
                trans_obj = objt.get(value)
                if trans_obj:
                    val = trans_obj.get(lang)
                    break

            return val
    return value


@register.filter()
def events_metric_name(id_, user):
    workspace = get_workspace(user)
    prop = EventNameCustomProperties.objects.filter(workspace=workspace, id=id_).first()
    if prop:
        return prop.name
    else:
        return id_


@register.filter()
def custom_object_row(obj, custom_object):
    try:
        custom_object_attr = get_attr(obj, "custom_object_relation")
        custom_object_row = custom_object_attr.filter(custom_object=custom_object)
        return custom_object_row
    except Exception as e:
        print("[DEBUG] custom_object_row", e)
        return []


@register.filter()
def get_first_module_help_slug(solution):
    print("[DEBUG] solution", solution)
    try:
        help_page = (
            Page2.objects.filter(solution=solution, category="help")
            .order_by("order")
            .first()
        )
        if help_page:
            return help_page.slug
        else:
            return None
    except Exception as e:
        print(f"[ERROR] get_first_module_help_slug: {e}")
        return None


@register.simple_tag(name="translate_line_item_header")
def translate_line_item_header(value="", lang="", custom_trans={}):
    if custom_trans:
        res = custom_trans.get(value)
        if res:
            return res

    if value:
        if is_uuid(value):
            try:
                obj = ShopTurboItemsNameCustomField.objects.get(id=value)
                value = obj.name
            except:
                pass

        else:
            value = LINE_ITEM_DEFAULT.get(value)
            if lang == "ja":
                value = value.get("ja")
            else:
                value = value.get("en")
    return value


@register.simple_tag(name="translate_pdf_component")
def translate_pdf_component(
    value="", lang="", obj_trans={}, custom_model=None, custom_trans={}
):
    if custom_trans:
        res = custom_trans.get(value)
        if res:
            return res

    trans_contact = SEARCH_COLUMNS_DISPLAY
    app_label = "data"
    if value == "customer__name":
        if lang == "ja":
            value = "顧客 - 名"
        else:
            value = "Customer - Name"

        return value

    if value == "workspace__name":
        if lang == "ja":
            value = "ワークスペース - 名前"
        else:
            value = "Workspace - Name"

        return value

    if "contact__custom_field" in value:
        trans = trans_contact.get("contact")
        _, custom_id = value.split("|")
        res = ContactsNameCustomField.objects.filter(id=custom_id).first()
        if res:
            return f"{trans.get(lang)} - {res.name}"

    if "company__custom_field" in value:
        trans = trans_contact.get("company")
        _, custom_id = value.split("|")
        if "__" in custom_id:
            custom_id, custom_field, company_field = custom_id.split("__", 2)
        res = CompanyNameCustomField.objects.filter(id=custom_id).first()
        if res:
            if res.type == "hierarchy":
                trans = trans_contact.get(company_field, {})
                company_field = trans.get(lang, company_field)
                return f"{res.name}({custom_field}) - {company_field}"
            return f"{trans.get(lang)} - {res.name}"

    if "contact__" in value or "company__" in value:
        trans = trans_contact.get(value, {})
        return trans.get(lang, value)

    if "contact__" in value or "company__" in value:
        trans = trans_contact.get(value, {})
        return trans.get(lang, value)

    split_parts = value.split("|")
    if len(split_parts) == 3:
        # Integration
        object_integration, channel_id, channel_field = split_parts
        channel = Channel.objects.filter(id=channel_id).first()
        if object_integration == "order_platform":
            return_field = ""
            if channel:
                return_field = f"{channel.name}"

            return f"{return_field} - 受注ID" if lang == "ja" else f"{return_field} - Order Id"

    if value:
        if "custom_property__" in value:
            try:
                cleaned_field_id = value.replace("custom_property__", "", 1)
                model_class = apps.get_model(app_label, custom_model)
                if model_class:
                    if "__" in cleaned_field_id:
                        cleaned_field_id, field = cleaned_field_id.split("__")
                    obj = model_class.objects.get(id=cleaned_field_id)

                    if obj.type == "bill_objects":
                        assc_col_display = DISPLAY_COLUMNS_BILL.copy()
                        trans = assc_col_display.get(field)

                        value = trans.get(lang, value) if trans else value
                    elif obj.type == "invoice_objects":
                        assc_col_display = INVOICE_COLUMNS_DISPLAY.copy()
                        trans = assc_col_display.get(field)

                        value = trans.get(lang, value) if trans else value
                    elif obj.type == "order_objects":
                        assc_col_display = ORDERS_COLUMNS_DISPLAY.copy()
                        trans = assc_col_display.get(field)

                        value = trans.get(lang, value) if trans else value
                    elif obj.type == "contact":
                        assc_col_display = CONTACTS_COLUMNS_DISPLAY.copy()
                        trans = assc_col_display.get(field)

                        value = trans.get(lang, value) if trans else value
                    elif obj.type == "company":
                        assc_col_display = COMPANY_COLUMNS_DISPLAY.copy()
                        trans = assc_col_display.get(field)

                        value = trans.get(lang, value) if trans else value
                    elif obj.type == "purchase_order":
                        assc_col_display = PURCHASE_ORDER_COLUMNS_DISPLAY.copy()
                        trans = assc_col_display.get(field)

                        value = trans.get(lang, value) if trans else value
                    elif obj.type == "subscription":
                        assc_col_display = SUBSCRIPTIONS_COLUMNS_DISPLAY.copy()
                        trans = assc_col_display.get(field)

                        value = trans.get(lang, value) if trans else value
                    else:
                        value = obj.name
            except:
                pass

        elif "item." in value:
            value = LINE_ITEM_DEFAULT.get(value, {"en": value, "ja": value})
            if lang == "ja":
                value = value.get("ja")
            else:
                value = value.get("en")
        else:
            try:
                if "__" in value:
                    tbl, field = value.split("__")
                    obj = PANEL_FILTER.get(f"{tbl}s")
                    if obj:
                        value = obj.get(field, value)
                        value = value.get(lang, value)
                        return value
                value = obj_trans.get(value)
                if lang == "ja":
                    value = value.get("ja")
                else:
                    value = value.get("en")
            except:
                pass
    return value


@register.filter()
def is_show_module(workspace, module_object):
    """We're limiting what module to show. Campaign and session event are only available for specific workspace."""
    if module_object not in [TYPE_OBJECT_SESSION_EVENT]:
        return True
    # elif str(workspace.id) == 'df8427e1-781f-48c4-9f7c-8752e45110f3':
    #     return True

    return False


@register.simple_tag(name="get_task_status")
def get_task_status(task_status, language_code):
    status_dict = dict(
        PROJECT_TASK_STATUS_JA if language_code == "ja" else PROJECT_TASK_STATUS
    )
    return status_dict.get(task_status, task_status)


@register.filter(name="contains_current_blog")
def contains_current_blog(solution, current_blog_slug):
    """
    Check if a solution contains the current blog page.
    Used for blog sidebar accordion to determine which section should be expanded.
    """
    if not solution or not current_blog_slug:
        return False

    try:
        # Get all pages in this solution that are guides and published
        pages = solution.pages.filter(status="published", category="guides")
        # Check if any page matches the current blog slug
        return pages.filter(slug=current_blog_slug).exists()
    except Exception:
        return False


@register.filter()
def get_related_custom_object(column, obj):
    try:
        if "related_custom_object" in column:
            related_custom_object = column.split("|")
            if len(related_custom_object) == 3:
                related_custom_object = CustomObject.objects.get(
                    id=related_custom_object[2]
                )
                return obj.custom_object_relation.filter(
                    custom_object=related_custom_object
                )
    except:
        pass

    return ""


@register.simple_tag(name="convert_float")
def convert_float(value, decimal_place=0):
    try:
        decimal_place = int(decimal_place)
        return f"{float(value):.{decimal_place}f}"
    except (ValueError, TypeError):
        return value


@register.simple_tag(name="set_default_value_pdf_component")
def set_default_value_pdf_component(
    value="", lang="", main_model=None, custom_model=None
):
    """Determine a relevant default placeholder based on the field type in the model."""

    if value == "customer":
        # Handle 'customer' field (without __name suffix)
        if lang == "ja":
            return "ABC株式会社 御中"
        else:
            return "ABC Company"

    if value == "customer__name":
        if lang == "ja":
            # For Japanese, add the appropriate suffix based on the context
            # In preview mode, we'll assume it's a company and add 御中
            return "ABC株式会社 御中"
        else:
            return "ABC Company"

    if value == "workspace__name":
        if lang == "ja":
            return "ABCワークスペース"
        else:
            return "Workspace ABC"

    # For contact objects in preview mode
    if value == "contact__name":
        if lang == "ja":
            # For Japanese contacts, add 様 suffix
            return "山田 太郎 様"
        else:
            return "John Smith"

    split_parts = value.split("|")
    if len(split_parts) == 3:
        # Integration
        object_integration, channel_id, channel_field = split_parts
        channel = Channel.objects.filter(id=channel_id).first()
        if object_integration == "order_platform":
            return_field = ""
            if channel:
                return_field = f"{channel.name}"

            return f"{return_field} - 受注ID" if lang == "ja" else f"{return_field} - Order Id"
    app_label = "data"

    if "custom_property__" in value:
        pass
        custom_model_class = apps.get_model(app_label, custom_model)

        if not custom_model_class or not hasattr(custom_model_class, "_meta"):
            return f"[[{value}]]"

        cleaned_field_id = value.replace("custom_property__", "", 1)
        if "__" in cleaned_field_id:
            cleaned_field_id, field = cleaned_field_id.split("__")

        obj = custom_model_class.objects.get(id=cleaned_field_id)
        if obj.type == "bill_objects":
            return set_default_value_pdf_component(
                field, lang, "Bill", "BillNameCustomField"
            )
        elif obj.type == "invoice_objects":
            return set_default_value_pdf_component(
                field, lang, "Invoice", "InvoiceNameCustomField"
            )
        elif obj.type == "order_objects":
            return set_default_value_pdf_component(
                field, lang, "ShopTurboOrders", "ShopTurboOrdersNameCustomField"
            )
        elif obj.type == "contact":
            return set_default_value_pdf_component(
                field, lang, "Contacts", "ContactsNameCustomField"
            )
        elif obj.type == "company":
            return set_default_value_pdf_component(
                field, lang, "Company", "CompanyNameCustomField"
            )
        elif obj.type == "purchase_order":
            return set_default_value_pdf_component(
                field, lang, "PurchaseOrders", "PurchaseOrdersNameCustomField"
            )
        elif obj.type == "subscription":
            return set_default_value_pdf_component(
                field,
                lang,
                "ShopTurboSubscriptions",
                "ShopTurboSubscriptionsNameCustomField",
            )
        else:
            if "date" in obj.type:
                field = models.DateField
            elif obj.type == "choice":
                try:
                    json_string = obj.choice_value
                    data = ast.literal_eval(json_string)
                    value_to_label = {item["value"]: item["label"] for item in data}
                    _, random_label = random.choice(list(value_to_label.items()))

                    return random_label
                except json.JSONDecodeError:
                    return f"[[{value}]]"
            elif obj.type == "boolean":
                field = models.BooleanField
            elif obj.type == "number":
                field = models.FloatField
            elif obj.type == "text-area":
                field = models.TextField
            else:
                field = models.CharField
    else:
        main_model_class = apps.get_model(app_label, main_model)
        if not main_model_class or not hasattr(main_model_class, "_meta"):
            return f"[[{value}]]"  # Fallback if model is invalid

        try:
            field = main_model_class._meta.get_field(value)
        except:
            return f"[[{value}]]"

    # Assign placeholder based on field type
    if isinstance(field, models.DateField) or isinstance(field, models.DateTimeField):
        return datetime.today().strftime("%Y-%m-%d")
    elif isinstance(field, models.IntegerField):
        if value.endswith("_id") or value.startswith("id_"):
            return "0001"
        return "0"
    elif isinstance(field, models.FloatField) or isinstance(field, models.DecimalField):
        return "0.00"
    elif isinstance(field, models.TextField):
        return (
            "This is a sample text. It is used as a dummy text for testing different layouts."
            if lang != "ja"
            else "これはサンプルテキストです。文章は日本語で記述されており、ダミーテキストとして使用されます。"
        )
    elif isinstance(field, models.CharField):
        return "Sample Text" if lang != "ja" else "サンプルテキスト"
    elif isinstance(field, models.BooleanField):
        return "True"  # Or "False"
    elif isinstance(field, models.UUIDField):
        return "123e4567-e89b-12d3-a456-************"

    return f"[[{value}]]"  # Default fallback


@register.simple_tag(name="set_default_line_item_header")
def set_default_line_item_header(value="", lang="", obj_name=""):
    default_obj = {
        "apple": {
            "item.no": 1,
            "item.id": 1,
            "item.name": "Golden Apple" if lang == "en" else "ゴールデンアップル",
            "item.description": "A juicy and sweet golden apple"
            if lang == "en"
            else "ジューシーで甘いゴールデンアップル",
            "item.quantity": 17,
            "item.unit_price": "¥ 42.75",
            "item.tax": 0.0,
            "item.total": "¥ 726.75",
            "item.discount_rate": 0.0,
            "item.base_price": "¥ 45.00",
            "item.inventory": 250,
            "item_inventory_locations": "tokyo",
            "item_inventory_amount": 120,
        },
        "orange": {
            "item.no": 2,
            "item.id": 2,
            "item.name": "Valencia Orange" if lang == "en" else "バレンシアオレンジ",
            "item.description": "Fresh and citrusy Valencia oranges"
            if lang == "en"
            else "フレッシュで柑橘系のバレンシアオレンジ",
            "item.quantity": 24,
            "item.unit_price": "¥ 33.50",
            "item.tax": 0.0,
            "item.total": "¥ 804.00",
            "item.discount_rate": 0.0,
            "item.base_price": "¥ 35.00",
            "item.inventory": 340,
            "item_inventory_locations": "tokyo",
            "item_inventory_amount": 210,
        },
        "grapes": {
            "item.no": 3,
            "item.id": 3,
            "item.name": "Purple Grapes" if lang == "en" else "パープルグレープ",
            "item.description": "Seedless and fresh purple grapes"
            if lang == "en"
            else "種なしで新鮮なパープルグレープ",
            "item.quantity": 13,
            "item.unit_price": "¥ 55.20",
            "item.tax": 0.0,
            "item.total": "¥ 717.60",
            "item.discount_rate": 0.0,
            "item.base_price": "¥ 58.00",
            "item.inventory": 180,
            "item_inventory_locations": "tokyo",
            "item_inventory_amount": 95,
        },
    }

    if value:
        if is_uuid(value):
            try:
                obj = ShopTurboItemsNameCustomField.objects.get(id=value)
                value = obj.name
            except:
                pass
        else:
            default_ = default_obj.get(obj_name)
            value = default_.get(value)
            return value
    return value


@register.simple_tag(takes_context=True)
def subscription_platform_value(context, subscription, channel_id, field):
    request = context["request"]
    platform_subscription = ShopTurboSubscriptionPlatforms.objects.filter(
        source_subscription=subscription, channel__id=channel_id
    ).first()
    if not platform_subscription:
        return ""
    if field == "manage_payment_link":
        protocol = settings.SITE_URL.split("//")[0]
        manage_payment_link = protocol + reverse(
            "manage_stripe_subscription",
            host="static",
            kwargs={"channel_id": channel_id, "subscription_id": subscription.id},
        )
        return manage_payment_link
    if field == "subscription_id":
        field = "platform_id"
    elif field == "platform_status":
        if platform_subscription.channel.integration.slug == "stripe":
            val = getattr(platform_subscription, field, "") or ""
            if val in STRIPE_SUBSCRIPTION_STATUS_OPTIONS:
                return STRIPE_SUBSCRIPTION_STATUS_OPTIONS[val].get(
                    request.LANGUAGE_CODE, val
                )
            return val
    elif field == "payment_status":
        if platform_subscription.channel.integration.slug == "stripe":
            val = getattr(platform_subscription, field, "") or ""
            if val in STRIPE_PAYMENT_STATUS_OPTIONS:
                return STRIPE_PAYMENT_STATUS_OPTIONS[val].get(
                    request.LANGUAGE_CODE, val
                )
            return val
    return getattr(platform_subscription, field, "") or ""


@register.filter
def split_dictionary(value):
    """
    Converts a dict like { 'key': [a, b], 'value': [c, d] }
    into a list of dicts: [{ 'key': a, 'value': c }, { 'key': b, 'value': d }]
    """
    if not isinstance(value, dict):
        return []

    keys = value.get("key", [])
    values = value.get("value", [])

    # Ensure both are lists and the same length
    if (
        not isinstance(keys, list)
        or not isinstance(values, list)
        or len(keys) != len(values)
    ):
        return []

    return [{"key": k, "value": v} for k, v in zip(keys, values)]


@register.simple_tag
def get_heatmap_class(value, max_value):
    if not value or not isinstance(value, (int, float)) or not max_value:
        return "heatmap-0"

    ratio = value / max_value
    if ratio >= 0.8:
        return "heatmap-5"
    elif ratio >= 0.6:
        return "heatmap-4"
    elif ratio >= 0.4:
        return "heatmap-3"
    elif ratio >= 0.2:
        return "heatmap-2"
    else:
        return "heatmap-1"


@register.simple_tag(takes_context=True)
def get_stripe_payment_status_label(context, status):
    request = context["request"]
    return STRIPE_PAYMENT_STATUS_OPTIONS.get(status, {}).get(
        request.LANGUAGE_CODE, status
    )


@register.filter
def get_orders_association(obj):
    try:
        model_name = obj._meta.model_name
        if model_name == TYPE_OBJECT_INVOICE[:-1]:
            return obj.orders.all()
        else:
            return obj.shopturboorders_set.all()
    except Exception as e:
        print("Error at get_orders_association: ", e)
        return ""


@register.simple_tag(name="convert_size_bytes")
def convert_size_bytes(size_bytes, unit="MB"):
    units = {
        "B": 1,
        "KB": 1024,
        "MB": 1024 * 1024,
        "GB": 1024 * 1024 * 1024,
    }
    size = size_bytes / units[unit]
    return f"{round(size, 2)} {unit}"


@register.filter
def is_animated_gif_file(file):
    try:
        img = Image.open(file)
        return getattr(img, "is_animated", False)
    except IOError:
        return False


@register.filter
def check_access(permission, permission_type):
    return permission_type in permission


@register.simple_tag
def get_unique_model_labels(rows):
    label_map = {
        "stock_in": "commerce_inventory_transaction",
        "stock_out": "commerce_inventory_transaction",
        "revenue": "commerce_orders",
    }

    return list(dict.fromkeys(label_map.get(row.rows_source, "manual") for row in rows))


@register.filter
def unique(objects):
    seen = set()
    unique_objects = []

    for obj in objects:
        key = getattr(obj, "id", obj)

        if key not in seen:
            seen.add(key)
            unique_objects.append(obj)

    return unique_objects


@register.filter
def add_queryset(list1, list2):
    return list(list1) + list(list2)


@register.filter(name="resolve_item_name")
def resolve_item_name(item_name, workspace=None):
    """
    Resolve item name from UUID if needed.
    If item_name looks like a UUID, try to get the actual item name from ShopTurboItems.
    Otherwise, return the original item_name.
    """
    if not item_name:
        return item_name

    # Check if item_name looks like a UUID
    if is_valid_uuid(item_name):
        try:
            # Get workspace from current request if not provided
            if not workspace:
                request = get_current_request()
                if request and hasattr(request, "user"):
                    workspace = get_workspace(request.user)

            if workspace:
                # Try to find the item with this UUID
                item = ShopTurboItems.objects.filter(
                    workspace=workspace, id=item_name
                ).first()
                if item and item.name:
                    return item.name
        except Exception:
            pass  # If lookup fails, return the original item_name

    return item_name


@register.simple_tag
def build_pagination_url_with_selections(
    base_url, page_num, selected_ids=None, **kwargs
):
    """
    Template tag to build pagination URLs with preserved selections.
    Usage: {% build_pagination_url_with_selections pagination_url page_num selected_ids view_id=view_id status=status %}
    """
    from urllib.parse import urlencode

    # Debug logging
    print(f"DEBUG build_pagination_url_with_selections called:")
    print(f"  base_url: {base_url}")
    print(f"  page_num: {page_num}")
    print(f"  selected_ids: {selected_ids}")
    print(f"  selected_ids type: {type(selected_ids)}")
    print(f"  kwargs: {kwargs}")

    # Maximum number of selected items to store in URL to prevent URL length issues
    MAX_SELECTED_IDS_IN_URL = 100

    url_params = {"page": page_num}

    # Add selected IDs if present and within limits
    if selected_ids and len(selected_ids) <= MAX_SELECTED_IDS_IN_URL:
        if isinstance(selected_ids, (list, tuple, set)):
            selected_ids_str = ",".join(str(id) for id in selected_ids)
            url_params["selected_ids"] = selected_ids_str
            print(f"  Added selected_ids to URL: {selected_ids_str}")
        else:
            url_params["selected_ids"] = str(selected_ids)
            print(f"  Added selected_ids to URL (single): {selected_ids}")
    else:
        print(
            f"  No selected_ids added - selected_ids: {selected_ids}, len: {len(selected_ids) if selected_ids else 'N/A'}"
        )

    # Add other parameters
    url_params.update(kwargs)

    # Remove None values and empty strings
    url_params = {k: v for k, v in url_params.items() if v is not None and v != ""}

    final_url = f"{base_url}?{urlencode(url_params)}" if url_params else base_url
    print(f"  Final URL: {final_url}")

    return final_url


@register.simple_tag
def is_item_selected(item_id, selected_ids):
    """
    Template tag to check if an item is selected.
    Usage: {% is_item_selected item.id selected_ids %}
    """
    if not selected_ids:
        return False

    item_id_str = str(item_id)
    if isinstance(selected_ids, (list, tuple, set)):
        return item_id_str in [str(id) for id in selected_ids]
    return item_id_str == str(selected_ids)


@register.filter
def get_mapping_storage(transfer_history, request):
    """
    Template filter to get ImportMappingFields for a TransferHistory.
    Usage: {{ item|get_mapping_storage:request }}
    """
    try:
        from data.models.mapping import ImportMappingFields
        from data.constants.properties_constant import TYPE_OBJECT_ITEM
        import ast

        # Determine object type from transfer history type
        object_type_map = {
            "import_item": TYPE_OBJECT_ITEM,
            "import_order": "commerce_orders",
            "import_contact": "contacts",
            "import_inventory": "commerce_inventory",
        }

        object_type = object_type_map.get(transfer_history.type, TYPE_OBJECT_ITEM)

        # Get mapping storage for this workspace and object type
        mapping_storage = ImportMappingFields.objects.filter(
            workspace=transfer_history.workspace, object_type=object_type
        ).first()

        if mapping_storage:
            # Create a wrapper class to handle the data properly
            class MappingStorageWrapper:
                def __init__(self, storage):
                    self.storage = storage

                @property
                def key_field(self):
                    if self.storage.key_field:
                        # Handle both string and list formats
                        if isinstance(self.storage.key_field, list):
                            return (
                                self.storage.key_field[0]
                                if self.storage.key_field
                                else None
                            )
                        elif isinstance(self.storage.key_field, str):
                            try:
                                # Try to parse as list string
                                parsed = ast.literal_eval(self.storage.key_field)
                                if isinstance(parsed, list):
                                    return parsed[0] if parsed else None
                                return self.storage.key_field
                            except:
                                return self.storage.key_field
                    return None

                @property
                def component_key_field(self):
                    return self.storage.component_key_field

            return MappingStorageWrapper(mapping_storage)

        return None

    except Exception as e:
        # Return a mock object with None values if there's an error
        class MockMappingStorage:
            key_field = None
            component_key_field = None

        return MockMappingStorage()


@register.simple_tag(name="get_association_members")
def get_association_members(label_name: AssociationLabel, obj):
    """
    Get association members for a specific label and object

    Args:
        label_name: The label to filter by (e.g., "orders", "customer")
        obj: The object to get associations for

    Returns:
        QuerySet of Association objects
    """
    # Handle None label_name or obj
    if label_name is None or obj is None:
        return None

    try:
        data_reverse = AssociationLabelObject.objects.none()
        if label_name.created_by_sanka:
            data = AssociationLabelObject.get_for_object(obj, obj.workspace)
            # reverse object
            # get last one, because it's reverse object.
            reverse_object_label = label_name.object_target.split(",")[-1]
            data_reverse = data.filter(
                label__object_source=reverse_object_label, label__created_by_sanka=True
            )

        data = AssociationLabelObject.get_for_object(
            obj,
            obj.workspace,
            label=label_name,  # Filter by specific label
        )

        data = data | data_reverse

        return data
    except AssociationLabelObject.DoesNotExist as e:
        print(f"Error getting associations for label '{label_name}': {e}")
        return AssociationLabelObject.objects.none()
    except ValueError as e:
        print(f"Error getting associations for label '{label_name}': {e}")
        return AssociationLabelObject.objects.none()


@register.simple_tag(name="get_parent_association_members")
def get_parent_association_members(
    label_name: AssociationLabel, source_object: str, obj
):
    """
    Get all parent (source) association members that include the given obj as their associated child (target)

    Args:
        label_name: AssociationLabel instance to filter by
        source_object: source object's model name (e.g., "contact", "project")
        obj: the child/target object you want to find parent/source for

    Returns:
        QuerySet of AssociationLabelObject where obj is the target
    """
    try:
        # Get all associations where obj is the target
        data = AssociationLabelObject.get_for_target(
            target_obj=obj, workspace=obj.workspace, label=label_name
        )
        return data
    except Exception as e:
        print(f"Error getting parent associations for '{obj}': {e}")
        return AssociationLabelObject.objects.none()


@register.filter(name="get_association_object_by_type")
def get_association_object_by_type(
    association: QuerySet[AssociationLabelObject],
    accordion_type: Union[str, Type, Any],
) -> List[Any]:
    """
    Extract actual objects of specified type from AssociationLabelObject queryset.
    The object can be either source or target in the association.

    Args:
        association: QuerySet of AssociationLabelObject instances
        type_object: The type to filter by

    Returns:
        List of actual objects (e.g., Contact instances)
    """

    parts = accordion_type.split("|")
    if len(parts) < 3:
        # Handle cases where the accordion_type is not as expected, e.g., return empty list or log an error.
        return []
    object_id = parts[-1]
    accordion_object = parts[0]
    open_at = parts[1]

    if is_valid_uuid(accordion_object):
        accordion_object = TYPE_OBJECT_CUSTOM_OBJECT
    accordion_object_ = get_page_object(accordion_object, "en")
    accordion_object_base_model = accordion_object_["base_model"]

    if is_valid_uuid(open_at):
        open_at = TYPE_OBJECT_CUSTOM_OBJECT

    open_at_ = get_page_object(open_at, "en")
    open_at_base_model = open_at_["base_model"]

    association_object_model_name = accordion_object_base_model._meta.model_name.lower()
    open_at_model_name = open_at_base_model._meta.model_name.lower()

    filtered_associations = association.filter(
        Q(
            source_content_type__model=association_object_model_name,
            target_content_type__model=open_at_model_name,
        )
        | Q(
            source_content_type__model=open_at_model_name,
            target_content_type__model=association_object_model_name,
        )
    )

    print("filtered_associations:", filtered_associations)
    objects = []
    for assoc in filtered_associations:
        print(
            "assoc:", assoc.source_content_type.model, assoc.target_content_type.model
        )
        # Check if the object is the source
        if assoc.source_content_type.model == association_object_model_name and not (
            assoc.source_content_type.model == "customobjectpropertyrow"
            and assoc.target_content_type.model == "customobjectpropertyrow"
        ):
            if assoc.source_object:
                if str(assoc.source_object.id) == str(object_id):
                    objects.append(assoc.target_object)
                else:
                    objects.append(assoc.source_object)
        # Check if the object is the target
        elif assoc.target_content_type.model == association_object_model_name:
            if (
                assoc.source_content_type.model == "customobjectpropertyrow"
                and assoc.target_content_type.model == "customobjectpropertyrow"
            ):
                if str(assoc.source_object.custom_object.id) == str(accordion_object):
                    objects.append(assoc.source_object)
                else:
                    objects.append(assoc.target_object)
            elif assoc.target_object:
                if str(assoc.target_object.id) == str(object_id):
                    objects.append(assoc.source_object)
                else:
                    objects.append(assoc.target_object)

    try:
        if objects:
            return list(set(objects))
    except:
        return objects


@register.filter(name="get_object_dict")
def get_object_dict(id=None, object_type=None):
    if id and object_type:
        try:
            page_obj = get_page_object(object_type)
            base_model = page_obj["base_model"]
            return base_model.objects.filter(id=id).first()
        except Exception as e:
            logger.info(f"Error getting object dict: {e}")
    return ""


@register.filter(name="format_device_info")
def format_device_info(app_log):
    """
    Format device information for display in workspace logs.

    Args:
        app_log: AppLog instance

    Returns:
        str: Formatted device information string
    """
    try:
        from utils.request_info import format_device_info_display

        if not app_log:
            return ""

        # Check if any device info is available
        if not any([app_log.browser, app_log.os, app_log.device_type]):
            return ""

        return format_device_info_display(
            app_log.browser, app_log.os, app_log.device_type
        )
    except Exception as e:
        logger.error(f"Error formatting device info: {e}")
        return ""


@register.filter(name="has_device_info")
def has_device_info(app_log):
    """
    Check if AppLog has any device information available.

    Args:
        app_log: AppLog instance

    Returns:
        bool: True if device info is available
    """
    try:
        if not app_log:
            return False

        return any(
            [app_log.ip_address, app_log.browser, app_log.os, app_log.device_type]
        )
    except Exception as e:
        logger.error(f"Error checking device info: {e}")
        return False


@register.filter
def combine_dict_to_list(items_data=None, target=None):
    """
    Convert items dictionary structure to list of item objects
    Example input:
    {
        'items': ['858944f6-57bb-4229-bf73-bde972d7a338', ''],
        'item_variants': ['', ''],
        'items_price_ids': ['1000', ''],
        'number_of_items': ['11', ''],
        'items_custom_name': ['', 'manual'],
        'items_custom_price': ['', '100'],
        'items_custom_number': ['', '10'],
        'items_custom_currency': ['', 'USD']
    }

    Output: List of objects where each item has all properties
    """
    return combine_item_dict_to_list(items_data, target)


@register.filter
def to_float(value):
    try:
        return float(value)
    except (TypeError, ValueError):
        return 0


@register.filter
def filter_running_subtrack(track):
    """
    Check if a track has any running subtracks.
    Returns True if there are running subtracks, False otherwise.
    """
    if not track:
        return False

    try:
        return track.subtrack_set.filter(status="running").exists()
    except AttributeError:
        return False


@register.simple_tag
def should_show_add_button(from_source, object_targets):
    """
    Determines if the Add button should be shown in association labels based on the source object type.

    Args:
        from_source: The source object type (e.g., 'commerce_inventory', 'commerce_inventory_transaction')
        object_targets: Comma-separated string of target object types

    Returns:
        Boolean: True if the Add button should be shown, False otherwise
    """
    if not object_targets:
        return True

    # Split comma-separated target objects
    target_list = [target.strip() for target in object_targets.split(",")]

    # Hide Add button for specific combinations:
    # 1. From inventory and target is item
    if from_source == TYPE_OBJECT_INVENTORY and TYPE_OBJECT_ITEM in target_list:
        return False

    # 2. From inventory transaction and target is inventory
    if (
        from_source == TYPE_OBJECT_INVENTORY_TRANSACTION
        and TYPE_OBJECT_INVENTORY in target_list
    ):
        return False

    # Show Add button by default
    return True
