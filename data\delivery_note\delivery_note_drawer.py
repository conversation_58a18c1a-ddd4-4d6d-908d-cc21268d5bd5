import ast
import os
import uuid
from urllib.parse import unquote

from django.conf import settings
from django.db.models import Q
from django.http import HttpResponse
from django.shortcuts import render
from django.views.generic import View

from data.constants.associate_constant import *
from data.constants.commerce_constant import (APP_SETTING_CHILD_LIST,
                                             COMMERCE_APP_TARGET, DIRECTORY,
                                             NEXT_DIRECTORY)
from data.constants.properties_constant import *
from data.constants.properties_constant import (TYPE_OBJECT_DELIVERY_NOTE,
                                                TYPE_OBJECT_ESTIMATE,
                                                TYPE_OBJECT_INVOICE,
                                                TYPE_OBJECT_RECEIPT,
                                                TYPE_OBJECT_SLIP)
from data.models import *
from utils.decorator import login_or_hubspot_required
from utils.freee_bg_jobs.freee import *
from utils.properties.properties import (get_default_property_set, get_page_object,
                                         get_properties_from_set)
from utils.serializer import *
from utils.smtp import *
from utils.utility import (get_workspace, is_valid_uuid,
                           natural_sort_key)
from utils.workspace import get_permission

type_http = settings.SITE_URL.split("//")[0]

@login_or_hubspot_required
def delivery_note_drawer(request, id=None, object_type=TYPE_OBJECT_DELIVERY_NOTE):
    property_set = None
    set_id = None
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    if not object_type:
        object_type = request.GET.get('object_type', None)
    module_slug = request.GET.get('module')
    print("Handling for page: ", object_type)

    if not object_type:
        if lang == 'ja':
            return HttpResponse('オブジェクトタイプが無効です。リクエストを確認してください。')
        else:
            return HttpResponse('Invalid object_type. Please check your request.')

    module = Module.objects.filter(
        workspace=workspace, object_values__contains=object_type).order_by('order', 'created_at')

    if module_slug:
        module = module.filter(slug=module_slug)

    if module:
        module = module.first()
        module_slug = module.slug

    page_obj = get_page_object(object_type, lang)

    id_field = page_obj['id_field']
    page_title = page_obj['page_title']
    default_columns = page_obj['default_columns']
    if id_field and id_field not in default_columns:
        default_columns.insert(0, id_field)
    base_model = page_obj['base_model']
    custom_model = page_obj['custom_model']
    item_model = page_obj['item_model']
    try:
        purchase_item_model = page_obj['purchase_item_model']
    except:
        purchase_item_model = ''
    field_item_name = page_obj['field_item_name']
    obj_id = None
    items_show = []
    purchase_items_show = []
    filter = {}

    filter['workspace'] = get_workspace(request.user)

    properties = None
    drawer_type = request.GET.get('drawer_type')

    permission = get_permission(object_type=object_type, user=request.user)
    if not permission:
        permission = DEFAULT_PERMISSION

    section = request.GET.get('section', None)
    if section == 'bulk-action':
        selected_id = request.GET.get('selected_ids', None)
        if selected_id and selected_id != '[]' and len(selected_id) > 0:
            selected_ids = ast.literal_eval(selected_id)
            objects = base_model.objects.filter(id__in=selected_ids)
            selected_ids = [str(obj.id) for obj in objects]
            selected_object_ids = [str(obj.id) for obj in objects]
        else:
            selected_object_ids = []

        context = {
            'object_action': {
                'additional_params': {
                    'selected_ids': selected_object_ids,
                    'object_type': object_type
                },
            },
            'object_type': object_type,
        }
        return render(request, 'data/shopturbo/shopturbo-manage-action.html', context)

    if section == 'action_history':
        section = request.GET.get('section', None)
        action_tab = request.GET.get('action_tab', None)
        open_drawer = request.GET.get('open_drawer')

        context = {"selected_ids": request.GET.getlist("selected_ids", []),
                   "action_tab": action_tab, 'object_type': object_type, 'drawer_url': page_obj['drawer_url'], "open_drawer": open_drawer}
        print(context)
        return render(request, 'data/shopturbo/manage-sync-actions-settings-commerce.html', context)

    property_set_form = None
    set_id = request.GET.get('set_id')

    if object_type == TYPE_OBJECT_SLIP \
            or object_type == TYPE_OBJECT_ESTIMATE \
            or object_type == TYPE_OBJECT_RECEIPT \
            or object_type == TYPE_OBJECT_DELIVERY_NOTE \
            or object_type == TYPE_OBJECT_INVOICE \
            or object_type == 'billing':

        if set_id and set_id != 'None':
            property_set_form = PropertySet.objects.filter(id=set_id).first()
            _, properties = get_properties_from_set(
                set_id, object_type, workspace)

        else:
            view_id = request.GET.get('view_id', None)
            if not view_id or view_id == 'None':
                view = View.objects.filter(
                    workspace=workspace,
                    title='main',
                    target=object_type
                ).first()
            else:
                view = View.objects.filter(id=view_id).first()

            if view:
                form_set = PropertySet.objects.filter(
                    target=object_type, as_default=True, workspace=workspace).first()

                if not form_set:
                    # default property set
                    form_set = get_default_property_set(
                        object_type, workspace, lang)

                if view.target and object_type:
                    if view.target == object_type:
                        view.form = form_set
                        view.save()
                    else:
                        condition = Q(workspace=workspace,
                                      target=object_type,
                                      title__in=['main', None])
                        view = View.objects.filter(condition).first()
                else:
                    view.form = form_set
                    view.save()

                if view:
                    if view.form:
                        property_set_form = view.form
                        set_id = view.form.id

                set_id, properties = get_properties_from_set(
                    set_id, object_type, workspace)

    line_item_properties = []
    if property_set_form:
        if property_set_form.name == None and not property_set_form.children:
            property_set_form.children = DEFAULT_FORM_FIELD_COMMERCE
        properties['list_all'] = property_set_form.children
        if object_type == TYPE_OBJECT_ESTIMATE:
            line_item_properties = property_set_form.estimate_line_items.all()
        elif object_type == TYPE_OBJECT_RECEIPT:
            line_item_properties = property_set_form.receipt_line_items.all()
        elif object_type == TYPE_OBJECT_DELIVERY_NOTE:
            line_item_properties = property_set_form.delivery_slip_line_items.all()
        elif object_type == TYPE_OBJECT_INVOICE:
            line_item_properties = property_set_form.invoice_line_items.all()

    # Fields to remove
    fields_to_remove = ['line_item_name', 'line_item_status', 'line_item_price', 'line_item_tax',
                        'line_item_name', 'line_item_quantity', 'line_item_name_quantity', 'line_item_price_without_tax']
    if object_type in [TYPE_OBJECT_INVOICE, TYPE_OBJECT_ESTIMATE, TYPE_OBJECT_RECEIPT, TYPE_OBJECT_DELIVERY_NOTE]:
        fields_to_remove.extend(['customers__contact__first_name', 'customers__company__name',
                                'customers__contact__name', 'customers__contact__last_name'])
        fields_to_remove.extend(['created_at', 'updated_at', 'tax_rate', 'custom_price', 'discount',
                                'discount_option', 'cost_option', 'total_price', 'total_price_without_tax'])
    if properties:
        properties['list_all'] = [
            field for field in properties['list_all'] if field not in fields_to_remove]

    currency = request.GET.get("currency", None)
    if not currency:
        items = ShopTurboItems.objects.filter(
            workspace=get_workspace(request.user),
            status='active',
        )
    else:
        items = ShopTurboItems.objects.filter(
            workspace=get_workspace(request.user),
            status='active',
        ).filter(
            Q(shopturbo_item_price__currency=currency.upper()) |
            Q(shopturbo_item_price__isnull=True)
        )

    try:
        app_setting = AppSetting.objects.get(
            workspace=workspace, app_target=COMMERCE_APP_TARGET)
    except AppSetting.DoesNotExist:
        app_setting = None

    app_logs = None

    exclude_item_list = []
    item_prices_list = []
    exclude_item = []
    print("lokasi set_id: ", set_id)
    if 'available-items' in request.GET:  # item row
        if 'add_type' in request.GET:
            exclude_item_list = request.GET.getlist('selected-item-id', [])

            i = 0
            for item in exclude_item_list:
                if item:
                    exclude_item.append(item)
                else:
                    i += 1
            if i == len(exclude_item_list):  # If everything is empty string, make it empty list
                exclude_item_list = ['']

        context = {'index': (str(uuid.uuid4()))}

        context['currency'] = request.GET.get('currency')

        # line item property
        context['obj_item_id'] = request.GET.get('obj_item_id')
        context['object_type'] = object_type

        if exclude_item:
            context['exclude_item'] = exclude_item
        if 'item' in request.GET or 'purchase-item' in request.GET:
            if 'item' in request.GET:
                if '|' in request.GET.get('item'):
                    item_id = request.GET.get('item').split('|')[0]
                else:
                    item_id = request.GET.get('item')
            else:
                if '|' in request.GET.get('purchase-item'):
                    item_id = request.GET.get('purchase-item').split('|')[0]
                else:
                    item_id = request.GET.get('purchase-item')

            context['item'] = item_id

            if item_id:
                item_object = None
                if is_valid_uuid(item_id):
                    item_object = ShopTurboItems.objects.filter(
                        id=item_id).first()

                if item_object:
                    if 'contact_and_company' in request.GET:
                        customer_item_price = None
                        customer = request.GET.get('contact_and_company', None)
                        if customer:
                            try:
                                customer = Contact.objects.get(id=customer)
                            except:
                                customer = Company.objects.get(id=customer)
                            if customer._meta.object_name == 'Company':
                                customer_item_price = ShopTurboCustomerItemsPrice.objects.filter(
                                    item=item_object, company=customer).first()
                            else:
                                customer_item_price = ShopTurboCustomerItemsPrice.objects.filter(
                                    item=item_object, contact=customer).first()
                            if customer_item_price:
                                item_prices_list.append(
                                    customer_item_price.price)

                    if request.GET.get('items_cost_section'):
                        item_prices = ItemPurchasePrice.objects.filter(item=item_object, currency=context['currency']).order_by(
                            # price filter by currency (new): item filter by currency (old)
                            '-default')
                    else:
                        item_prices = ShopTurboItemsPrice.objects.filter(item=item_object, currency=context['currency']).order_by(
                            # price filter by currency (new): item filter by currency (old)
                            '-default')

                    # Check if item_prices queryset is empty -> add price zero if empety
                    if not item_prices.exists():
                        # Create a temporary fake object
                        if request.GET.get('items_cost_section'):
                            # Create fake ItemPurchasePrice object
                            fake_price = ItemPurchasePrice(
                                item=item_object,
                                price=0,
                                default=True,
                                currency=context['currency']
                            )
                            # If you need it as a queryset
                            item_prices = [fake_price]
                        else:
                            # Create fake ShopTurboItemsPrice object
                            fake_price = ShopTurboItemsPrice(
                                item=item_object,
                                price=0,
                                default=True,
                                currency=context['currency'],
                                tax=0
                            )
                            # If you need it as a queryset
                            item_prices = [fake_price]

                    for price in item_prices:
                        item_prices_list.append(price.price)

        context['item_price_list'] = item_prices_list

        if line_item_properties:
            context["line_item_properties"] = line_item_properties

        if 'price' in request.GET:
            context['price'] = request.GET.get('price')
        if 'amount' in request.GET:
            context['amount'] = request.GET.get('amount')
        if 'tax' in request.GET:
            context['tax'] = request.GET.get('tax')
        if 'add_type' in request.GET:
            context['add_type'] = request.GET.get('add_type')

        if request.GET.get('extras_section'):
            context['extras_section'] = ast.literal_eval(
                request.GET.get('extras_section'))
            return render(request, 'data/commerce/commerce-add-sections.html', context)

        if request.GET.get('items_cost_section'):
            return render(request, 'data/commerce/commerce-add-items-cost.html', context)

        return render(request, 'data/commerce/commerce-add-items.html', context)

    if id:  # manage drawer
        try:
            obj = base_model.objects.get(id=id)
        except:
            if lang == 'ja':
                response_msg = '無効なリクエスト、URLを確認してください'
            else:
                response_msg = 'Invalid request, Please check your url'
            return HttpResponse(response_msg)
        obj_id = getattr(obj, id_field, None)
        obj_id = f"{obj_id:04d}"
        if obj.currency:
            items = ShopTurboItems.objects.filter(workspace=get_workspace(
                request.user), status='active', currency=obj.currency.upper())

        if object_type in [
                TYPE_OBJECT_INVOICE,
                TYPE_OBJECT_ESTIMATE,
                TYPE_OBJECT_DELIVERY_NOTE,
                TYPE_OBJECT_RECEIPT,
                TYPE_OBJECT_SLIP]:
            # mapping
            log_object = object_type[:-1]
            setting_type = object_type[:-1]
            if object_type == TYPE_OBJECT_DELIVERY_NOTE:
                log_object = 'deliveryslip'
            elif object_type == TYPE_OBJECT_RECEIPT:
                log_object = 'receipt'

            print(log_object)
            app_logs = AppLog.objects.filter(
                **{'workspace': workspace, log_object: obj}).order_by('-created_at')
        else:
            app_logs = None

    else:  # create drawer
        obj = None
        try:
            contact_preselected = Contact.objects.get(
                id=request.GET.get('contact_id', None))
        except:
            contact_preselected = None

        try:
            company_preselected = Company.objects.get(
                id=request.GET.get('company_id', None))
        except:
            company_preselected = None

        for item in items:
            print(item.item_id, " ", item.name)

        COMMERCE_STATUS = None
        SLIP_TYPE = None

        if object_type == TYPE_OBJECT_SLIP:
            SLIP_TYPE = base_model._meta.get_field('slip_type').choices
        else:
            COMMERCE_STATUS = base_model._meta.get_field('status').choices

        # Association label setup for create drawer
        association_labels = AssociationLabel.objects.filter(workspace=workspace, object_source=object_type).order_by('created_at')
        association_labels_sanka_true = AssociationLabel.objects.filter(workspace=workspace, object_source=object_type, created_by_sanka=True)
        association_label_list_sanka_true = [association_label.label for association_label in association_labels_sanka_true]
        association_labels_sanka_false = AssociationLabel.objects.filter(workspace=workspace, object_source=object_type, created_by_sanka=False)
        association_label_list_sanka_false = [str(association_label.id) for association_label in association_labels_sanka_false]
        association_label_list = association_label_list_sanka_true + association_label_list_sanka_false
        
        # Related association labels
        related_association_labels = AssociationLabel.objects.filter(created_by_sanka=False, workspace=workspace, object_target__icontains=object_type).order_by("created_at")

        context = {
            'module': module_slug,
            'app_logs': app_logs,
            'obj': obj,
            'SLIP_TYPE': SLIP_TYPE,
            'COMMERCE_STATUS': COMMERCE_STATUS,
            'pdf_download_link': page_obj['pdf_download_link'],
            'post_obj_link': page_obj['new_obj_link'],
            'items': items,
            'items_show': items_show,
            'purchase_items_show': purchase_items_show,
            'drawer_type': drawer_type,
            'page_title': page_title,

            'object_type': object_type,

            'app_setting': app_setting,
            'contact_preselected': contact_preselected,
            'company_preselected': company_preselected,
            'NameCustomField': custom_model.objects.filter(workspace=workspace).order_by("order"),
            'properties': properties,
            'permission': permission,
            'hide_associated_data': True,
            'set_id': set_id,
            'ShippingCosts': ShopTurboShippingCost.objects.filter(workspace=workspace).order_by('created_at'),
            'association_label_list': association_label_list,
            'association_labels': association_labels,
            'related_association_labels': related_association_labels
        }

        if set_id:
            context["set_id"] = str(set_id)

        property_sets = PropertySet.objects.filter(
            workspace=workspace, target=object_type).order_by('created_at')
        print("== property_sets atas : ", property_sets)
        context['property_sets'] = property_sets

        NameCustomFieldMap = {}
        NameCustomFieldMaps = custom_model.objects.filter(workspace=workspace)
        for ctf in NameCustomFieldMaps:
            NameCustomFieldMap[str(ctf.id)] = ctf
        context['NameCustomFieldMap'] = NameCustomFieldMap

        if line_item_properties:
            context["line_item_properties"] = line_item_properties

        # gak di gawe
        source_url = request.GET.get('source_url', None)
        if source_url:
            source_url = unquote(source_url)
            context['source_url'] = source_url

        #
        for setting_ in APP_SETTING_CHILD_LIST:
            app_setting_child = AppSettingChild.objects.filter(
                app_setting=app_setting, target_name=object_type[:-1] + setting_, property_set=property_set).first()
            if not app_setting_child:
                app_setting_child = AppSettingChild.objects.create(
                    app_setting=app_setting, target_name=object_type[:-1] + setting_, property_set=property_set)
            setting_ = 'value_app_setting' + setting_
            context[setting_] = app_setting_child.value

        if request.GET.get('type', '') == 'create-association':
            context['type'] = 'create-association'
            context['source'] = request.GET.get('source', "")
            context['source_object_id'] = request.GET.get('object_id', '')

        if 'currency' in request.GET:
            if 'id' in request.GET:
                try:
                    obj = base_model.objects.get(id=request.GET.get('id'))
                    context['obj'] = obj
                except:
                    pass
            if 'form' in request.GET:
                form = request.GET.get('form')
                context['form'] = form
            return render(request, 'data/commerce/commerce-partial-add-items.html', context)
        else:
            view_id = request.GET.get('view_id', None)
            if view_id == 'None':
                view_id = None
            if not view_id:
                view = View.objects.filter(
                    workspace=workspace,
                    title='main',
                    target=object_type
                ).first()
            else:
                view = View.objects.filter(id=view_id).first()

            # Add fallback if view doesn't exist
            if not view:
                # Create default property set if needed
                default_property_set = PropertySet.objects.filter(
                    workspace=workspace, 
                    target=object_type,
                    as_default=True
                ).first()
                
                if not default_property_set:
                    default_property_set = get_default_property_set(object_type, workspace, lang)
                
                # Create default main view
                view = View.objects.create(
                    workspace=workspace,
                    title='main',
                    target=object_type,
                    form=default_property_set
                )

            context['form'] = view.form
            context['module'] = module_slug

            print("== base_model: ",base_model)
            context['obj'] = {'model': base_model,
                              'workspace': workspace}

            if request.GET.get('import_export_type'):
                context['upload_section'] = request.GET.get(
                    'import_export_type')
                return render(request, 'data/delivery_note/delivery-note-edit-form-import-export.html', context)

            context['manage_obj_link'] = page_obj['manage_obj_link']
            if 'commerce_entry_type' in request.GET:
                context['commerce_entry_type'] = request.GET.getlist('commerce_entry_type')[-1]
                return render(request, 'data/commerce/commerce-partial-add-items.html', context)

            # Construct the template name
            template_path = 'data/delivery_note/delivery-note-edit-form.html'

            return render(request, template_path, context)

    if request.method == 'GET':
        if lang == 'en':
            try:
                if obj.start_date:
                    obj.start_date = obj.start_date.strftime('%Y-%m-%d')
            except:
                pass

            try:
                if obj.due_date:
                    obj.due_date = obj.due_date.strftime('%Y-%m-%d')
            except:
                pass

        try:
            obj_item = item_model.objects.filter(
                **{field_item_name.replace('_', ''): obj}).order_by('created_at')
            obj.items = []
            obj.amounts = []
            obj.amount_items = []
            obj.tax_lists = []
            obj.obj_item_ids = []

            for item_ in obj_item:
                if item_.item_link:
                    obj.items.append(str(item_.item_link.id))
                else:
                    if item_.item_name:
                        obj.items.append(item_.item_name)
                    else:
                        obj.items.append("")  # For none name item

                obj.amounts.append(item_.amount_price)
                obj.amount_items.append(item_.amount_item)
                obj.tax_lists.append(item_.tax_rate)
                obj.obj_item_ids.append(str(item_.id))

            purchase_items_show = []
            item_ids = [''] * len(obj.items)
            sales_prices = [0] * len(obj.items)
            extras_section = [''] * len(obj.items)
            items_show = list(zip(item_ids, obj.items, obj.amount_items,
                              obj.amounts, obj.tax_lists, extras_section, obj.obj_item_ids, sales_prices))

            if purchase_item_model:  # This is For estimate only, not available to other bills object
                purchase_obj_item = purchase_item_model.objects.filter(
                    **{field_item_name.replace('_', ''): obj}).order_by('created_at')
                obj.purchase_items = []
                obj.purchase_amounts = []
                obj.purchase_amount_purchase_items = []
                obj.purchase_tax_lists = []

                if len(purchase_obj_item) == 0:
                    for item_ in obj_item:
                        if item_.item_link:
                            obj.purchase_items.append(str(item_.item_link.id))
                        else:
                            if item_.item_name:
                                obj.purchase_items.append(item_.item_name)
                            else:
                                obj.purchase_items.append(
                                    "")  # For none name item

                        if item_.item_link:
                            default_purchase_price = ItemPurchasePrice.objects.filter(
                                item=item_.item_link, default=True).first()
                        else:
                            default_purchase_price = None

                        if default_purchase_price:
                            obj.purchase_amounts.append(
                                default_purchase_price.price)
                        else:
                            obj.purchase_amounts.append(item_.amount_price)
                        obj.purchase_amount_purchase_items.append(
                            item_.amount_item)
                        obj.purchase_tax_lists.append(item_.tax_rate)
                else:
                    for item_ in purchase_obj_item:
                        if item_.item_link:
                            obj.purchase_items.append(str(item_.item_link.id))
                        else:
                            if item_.item_name:
                                obj.purchase_items.append(item_.item_name)
                            else:
                                obj.purchase_items.append(
                                    "")  # For none name item

                        obj.purchase_amounts.append(item_.amount_price)
                        obj.purchase_amount_purchase_items.append(
                            item_.amount_item)
                        obj.purchase_tax_lists.append(item_.tax_rate)

                item_ids = [''] * len(obj.purchase_items)
                extras_section = [''] * len(obj.purchase_items)
                purchase_items_show = list(zip(item_ids, obj.purchase_items, obj.purchase_amount_purchase_items,
                                               obj.purchase_amounts, obj.purchase_tax_lists, extras_section))

            if object_type == TYPE_OBJECT_ESTIMATE:
                section_objects = SectionItemEstimate.objects.filter(
                    workspace=workspace, estimate=obj)
                if section_objects:
                    for section in section_objects:
                        extras_section = {"section_position": section.position,
                                          "section_type": section.section_type, "section_value": section.value}
                        row_value = ('', '', '', '', '', extras_section, '')
                        items_show.insert(int(section.position), row_value)
            elif object_type == TYPE_OBJECT_DELIVERY_NOTE:
                section_objects = SectionItemDeliverySlip.objects.filter(
                    workspace=workspace, deliveryslip=obj)
                if section_objects:
                    for section in section_objects:
                        extras_section = {"section_position": section.position,
                                          "section_type": section.section_type, "section_value": section.value}
                        row_value = ('', '', '', '', '', extras_section, '')
                        items_show.insert(int(section.position), row_value)
            elif object_type == TYPE_OBJECT_INVOICE:
                section_objects = SectionItemInvoice.objects.filter(
                    workspace=workspace, invoice=obj)
                if section_objects:
                    for section in section_objects:
                        extras_section = {"section_position": section.position,
                                          "section_type": section.section_type, "section_value": section.value}
                        row_value = ('', '', '', '', '', extras_section, '')
                        items_show.insert(int(section.position), row_value)
            elif object_type == TYPE_OBJECT_RECEIPT:
                section_objects = SectionItemReceipt.objects.filter(
                    workspace=workspace, receipt=obj)
                if section_objects:
                    for section in section_objects:
                        extras_section = {"section_position": section.position,
                                          "section_type": section.section_type, "section_value": section.value}
                        row_value = ('', '', '', '', '', extras_section, '')
                        items_show.insert(int(section.position), row_value)
        except Exception as e:
            print('[Error at Billing Drawer]: ', e)

        # customize_template = CustomizePdfTemplate.objects.filter(
        #     setting_type=setting_type, workspace=workspace)
        customize_template = []

        list_template = []
        for filename in os.listdir(DIRECTORY):
            # Check if the file is an HTML file and contains 'pdf_pattern' in its name
            if filename.endswith('.html') and page_obj['pdf_pattern'] in filename:
                list_template.append(NEXT_DIRECTORY + filename)

        list_template = sorted(list_template, key=natural_sort_key)

        tmp_setting_type = setting_type
        if tmp_setting_type in ['invoice', 'estimate', 'receipt', 'delivery_slip', 'slip']:
            tmp_setting_type = f"{tmp_setting_type}s"

        if tmp_setting_type in [TYPE_OBJECT_ESTIMATE, TYPE_OBJECT_DELIVERY_NOTE, TYPE_OBJECT_INVOICE, TYPE_OBJECT_SLIP]:
            list_template = []
            customize_template = PdfTemplate.objects.filter(
                master_pdf__object_type=tmp_setting_type, workspace=workspace).order_by('master_pdf__name_en', 'master_pdf__name_ja')

        COMMERCE_STATUS = None
        SLIP_TYPE = None

        if object_type == TYPE_OBJECT_SLIP:
            SLIP_TYPE = base_model._meta.get_field('slip_type').choices
        else:
            COMMERCE_STATUS = base_model._meta.get_field('status').choices

        if obj and obj.owner and obj.owner.user:
            permission += f'|{obj.owner.user.id}#{request.user.id}'
            
        # Association label setup for manage drawer
        association_labels = AssociationLabel.objects.filter(workspace=workspace, object_source=object_type).order_by('created_at')
        association_labels_sanka_true = AssociationLabel.objects.filter(workspace=workspace, object_source=object_type, created_by_sanka=True)
        association_label_list_sanka_true = [association_label.label for association_label in association_labels_sanka_true]
        association_labels_sanka_false = AssociationLabel.objects.filter(workspace=workspace, object_source=object_type, created_by_sanka=False)
        association_label_list_sanka_false = [str(association_label.id) for association_label in association_labels_sanka_false]
        association_label_list = association_label_list_sanka_true + association_label_list_sanka_false
        
        # Related association labels
        related_association_labels = AssociationLabel.objects.filter(created_by_sanka=False, workspace=workspace, object_target__icontains=object_type).order_by("created_at")
            
        context = {
            'app_logs': app_logs,
            'SLIP_TYPE': SLIP_TYPE,
            'COMMERCE_STATUS': COMMERCE_STATUS,
            'module': module_slug,
            'object_type': object_type,
            'obj_id': obj_id,
            'app_setting': app_setting,
            'pdf_download_link': page_obj['pdf_download_link'],
            'post_obj_link': page_obj['update_obj_link'],
            'items': items,
            'drawer_type': drawer_type,
            'items_show': items_show,
            'purchase_items_show': purchase_items_show,
            'type': drawer_type,
            'obj': obj,
            'page_title': page_title,
            'type_http': type_http,
            'view_id': request.GET.get("view_id"),
            'NameCustomField': custom_model.objects.filter(workspace=workspace).order_by("order"),
            field_item_name: obj,
            'properties': properties,
            'permission': permission,
            'list_customize_template': customize_template,
            'list_template': list_template,
            'customer_selector_id': uuid.uuid4(),
            'side_drawer': request.GET.get('side_drawer'),
            'source': object_type,
            'ShippingCosts': ShopTurboShippingCost.objects.filter(workspace=workspace).order_by('created_at'),
            'association_label_list': association_label_list,
            'association_labels': association_labels,
            'related_association_labels': related_association_labels
        }

        if set_id:
            context["set_id"] = str(set_id)

        print("=========================== set_id: ", set_id)

        if line_item_properties:
            context["line_item_properties"] = line_item_properties

        NameCustomFieldMap = {}
        NameCustomFieldMaps = custom_model.objects.filter(workspace=workspace)
        for ctf in NameCustomFieldMaps:
            NameCustomFieldMap[str(ctf.id)] = ctf
        context['NameCustomFieldMap'] = NameCustomFieldMap

        hide_associated_data = request.GET.get('hide_associated_data', None)
        if hide_associated_data:
            context['hide_associated_data'] = True

        source_url = request.GET.get('source_url', None)
        if source_url:
            source_url = unquote(source_url)
            context['source_url'] = source_url

        for setting_ in APP_SETTING_CHILD_LIST:
            app_setting_child = AppSettingChild.objects.filter(
                app_setting=app_setting, target_name=object_type[:-1] + setting_, property_set=property_set).first()
            if not app_setting_child:
                app_setting_child = AppSettingChild.objects.create(
                    app_setting=app_setting, target_name=object_type[:-1] + setting_, property_set=property_set)
            setting_ = 'value_app_setting' + setting_
            context[setting_] = app_setting_child.value

        if 'currency' in request.GET:
            if 'id' in request.GET:
                obj = base_model.objects.get(id=request.GET.get('id'))
                context['obj'] = obj
            return render(request, 'data/commerce/commerce-partial-add-items.html', context)
        else:
            context['object_type'] = object_type
            if object_type == 'estimates':
                context['invoices'] = Invoice.objects.filter(
                    workspace=workspace)
                context['deals'] = Deals.objects.filter(
                    workspace=workspace, estimates__id=obj.id)
                context['order'] = ShopTurboOrders.objects.filter(
                    workspace=workspace, estimate=obj).first()
            if object_type == 'invoices':
                context['estimate'] = Estimate.objects.filter(
                    workspace=workspace, invoice=obj).first()
                context['deals'] = Deals.objects.filter(
                    workspace=workspace, invoices__id=obj.id)
                context['order'] = ShopTurboOrders.objects.filter(
                    workspace=workspace, invoice=obj).first()
            context['object_action'] = {
                'additional_params': {
                    'selected_ids': str(obj.id),
                    'object_type': object_type
                }
            }
            context['module'] = module_slug
            context['side_drawer'] = request.GET.get('side_drawer')

            property_sets = PropertySet.objects.filter(
                workspace=workspace, target=object_type).order_by('created_at')
            print("== property_sets: ", property_sets)
            context['property_sets'] = property_sets
            context['manage_obj_link'] = page_obj['manage_obj_link']
            if 'commerce_entry_type' in request.GET:
                context['commerce_entry_type'] = request.GET.get(
                    'commerce_entry_type')
                print("OKI")
                return render(request, 'data/commerce/commerce-partial-add-items.html', context)

            # Construct the template name
            template_path = 'data/delivery_note/delivery-note-edit-form.html'
            return render(request, template_path, context)

