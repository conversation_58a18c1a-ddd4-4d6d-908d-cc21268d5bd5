import ast
from datetime import datetime
import io
from io import BytesIO, StringIO
import json
import math
import os
import platform
import re
import traceback
from urllib.parse import quote
import uuid

from PIL import Image
from asgiref.sync import async_to_sync
import chardet
from django.core.files import File
from django.core.mail import EmailMessage
from django.core.paginator import EmptyPage, Paginator
from django.db.models import (
    BooleanField,
    Case,
    ExpressionWrapper,
    F,
    FloatField,
    OuterRef,
    Q,
    Subquery,
    Sum,
    Value,
    When,
)
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.template.loader import get_template, render_to_string
from django.utils import timezone
from django.utils.dateparse import parse_datetime
from django.views.decorators.http import require_GET
from django_hosts.resolvers import reverse
import pandas as pd
import pytz
import requests
from xhtml2pdf import pisa

from data.accounts.association_labels import save_association_label
from action.action import trigger_next_action
from data.association import update_associate_object
from data.constants.associate_constant import ASSOCIATE_MAP, SOURCE_ASSOCIATE_MAP
from data.constants.constant import (
    APP_TARGET_DISPLAY,
    CSV_DELIMITER_LIST_FIELD,
    DEFAULT_PERMISSION,
    INVENTORY_USAGE_CATEGORY,
    ITEM_USAGE_CATEGORY,
    OBJECTS_CF_TYPE,
    PURCHASE_ORDER_COLUMNS_DISPLAY,
    PURCHASE_ORDER_STATUS_DISPLAY,
    PURCHASE_ORDER_USAGE_CATEGORY,
    SHOPTURBO_NUMBER_FORMAT,
    USAGE_CATEGORIES_TITLE,
)
from data.constants.date_constant import DATE_FORMAT_PARSER
from data.constants.procurement_constant import (
    DIRECTORY,
    NEXT_DIRECTORY,
    PROCUREMENT_APP_SETTING_MANAGE_APP,
    PROCUREMENT_APP_SETTING_PURCHASE_ORDER,
    PROCUREMENT_APP_SLUG,
    PROCUREMENT_APP_TARGET,
)
from data.constants.properties_constant import (
    DEFAULT_OBJECT_DISPLAY,
    OBJECT_TYPE_TO_SLUG,
    TYPE_OBJECT_BILL,
    TYPE_OBJECT_INVENTORY,
    TYPE_OBJECT_INVENTORY_TRANSACTION,
    TYPE_OBJECT_ITEM,
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_PURCHASE_ORDER,
    TYPE_OBJECT_WORKFLOW,
)
from data.custom_pdf import generate_pdf, generate_pdf_bytes, get_custom_pdf_context
from data.models import (
    Action,
    ActionHistory,
    ActionNode,
    ActionTaskHistory,
    ActionTracker,
    AdvanceSearchFilter,
    AppLog,
    AppSetting,
    Association,
    Bill,
    BillNameCustomField,
    BillValueCustomField,
    BotPdfUpload,
    Channel,
    Company,
    Contact,
    CustomProperty,
    DateFormat,
    Expense,
    ExpenseFile,
    Group,
    InventoryTransaction,
    InventoryWarehouse,
    Invoice,
    ItemPurchasePrice,
    Module,
    Notification,
    ObjectManager,
    PURCHASE_ORDER_STATUS,
    PdfTemplate,
    PropertySet,
    PurchaseOrders,
    PurchaseOrdersItem,
    PurchaseOrdersItemNameCustomField,
    PurchaseOrdersItemValueCustomField,
    PurchaseOrdersNameCustomField,
    PurchaseOrdersValueCustomField,
    ShopTurboInventory,
    ShopTurboItems,
    ShopTurboItemsNameCustomField,
    ShopTurboItemsPrice,
    ShopTurboItemsValueCustomField,
    ShopTurboOrders,
    ShopTurboSubscriptions,
    TransferHistory,
    User,
    UserManagement,
    View,
    ViewFilter,
    WorkflowActionTracker,
    AssociationLabel,
    AssociationLabelObject,
)
from data.property import get_default_property_set, get_properties_with_details
from data.property import properties as forward_properties
from data.templatetags.custom_tags import (
    get_currency_symbol,
    use_thousand_separator_string_with_currency,
)
from sanka.settings import AWS_LOCATION, AWS_STORAGE_BUCKET_NAME, S3_CLIENT
from utils.actions import transfer_output_to_target_input
from utils.currency_symbols.currency_symbols import CurrencySymbols
from utils.decorator import login_or_hubspot_required
from utils.filter import apply_search_setting, build_view_filter
from utils.inventory import (
    re_calculate_inventory_stock,
    recalculate_transactions_after,
    update_inventory_stock_price,
)
from utils.inventory import create_inventory_transaction_helper
from utils.list_action_trigger_event import (
    trigger_workflow_by_inventory_less_than,
    trigger_workflow_by_purchase_order_item_status_event,
)
from utils.bgjobs.runner import trigger_bg_job
from utils.meter import (
    MODELS_TO_STORAGE_USAGE,
    get_workspace_available_storage,
    has_quota,
    sync_usage,
)
from utils.project import get_ordered_views
from utils.properties.properties import (
    get_object_display_based_columns,
    get_page_object,
)
from utils.utility import (
    assign_object_owner,
    build_redirect_url,
    get_redirect_workflow,
    get_workspace,
    is_valid_uuid,
    natural_sort_key,
    save_custom_property,
)
from utils.workflow import get_workflows_by_first_action_trigger
from utils.workspace import get_permission


@login_or_hubspot_required
def app_settings(request):
    app_setting, _ = AppSetting.objects.get_or_create(
        workspace=get_workspace(request.user), app_target=PROCUREMENT_APP_TARGET
    )
    print("== app_setting: ", app_setting)
    orders = []

    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    page_title = request.GET.get("page_title")
    page_group_type = request.GET.get("page_group_type")

    if request.method == "POST":
        app_setting.purchase_order_is_status = request.POST.get("sw_status", False)
        app_setting.save()
        if request.POST.get("app_settings") == TYPE_OBJECT_PURCHASE_ORDER:
            app_setting.purchase_order_email_body = request.POST.get(
                "default_email_content", ""
            )
            app_setting.purchase_order_pdf_title = request.POST.get("pdf_title", "")
            app_setting.purchase_order_pdf_font_type = request.POST.get(
                "selected_font_type", ""
            )
            app_setting.purchase_order_pdf_color = request.POST.get(
                "selected_color", ""
            )
            app_setting.purchase_order_pdf_template = request.POST.get(
                "selected_template", ""
            )
            app_setting.purchase_order_is_stamp = request.POST.get("sw_stamp", False)

            search_setting = request.POST.get("search_setting_purchase_order", "")
            app_setting.purchase_order_supplier_properties = ""
            try:
                app_setting.purchase_order_supplier_lock = bool(
                    request.POST.get("search_setting_purchase_order", False)
                )
                prop_valid = False
                item_supplier_id = request.POST.get("item-supplier", "")
                if app_setting.purchase_order_supplier_lock and item_supplier_id:
                    for prop in get_properties_with_details(
                        page_group_type=TYPE_OBJECT_ITEM, workspace=workspace, lang=lang
                    ):
                        if str(prop["id"]) == item_supplier_id and prop["type"] in [
                            "contact",
                            "company",
                        ]:
                            prop_valid = True
                            break
                if prop_valid:
                    app_setting.purchase_order_supplier_properties = item_supplier_id
            except:
                app_setting.purchase_order_supplier_lock = False

            # Default of search variable will be deleted after update the setting
            search_setting = search_setting.replace("id_predefined,", "")

            # If it only Id , will remove it as blank string
            if search_setting == "id_predefined":
                search_setting = ""

            selected_columns = ""
            selected_columns_display = ""
            selected_header = ""
            selected_header_display = ""
            selected_information = ""
            selected_information_display = ""
            if request.POST.get("selected_columns[]", None):
                selected_columns = ",".join(
                    request.POST.getlist("selected_columns[]", [])
                )

            if len(request.POST.getlist("selected_columns_display[]", [])) > 0:
                selected_columns_display = ",".join(
                    request.POST.getlist("selected_columns_display[]", [])
                )

            if request.POST.get("header_block[]", None):
                selected_header = ",".join(request.POST.getlist("header_block[]", []))

            if len(request.POST.getlist("header_block_display[]", [])) > 0:
                selected_header_display = ",".join(
                    request.POST.getlist("header_block_display[]", [])
                )

            if request.POST.get("information_block[]", None):
                selected_information = ",".join(
                    request.POST.getlist("information_block[]", [])
                )

            if request.POST.get("information_block_display[]", None):
                selected_information_display = ",".join(
                    request.POST.getlist("information_block_display[]", [])
                )

            selected_payment_block = request.POST.get("payment_block", "")
            selected_send_from_block = request.POST.get("send_from_block", "")
            selected_send_to_block = request.POST.get("send_to_block", "")
            selected_notes_block = request.POST.get("notes_block", "")
            selected_user_block = request.POST.get("user_block", "")
            selected_footer_template = request.POST.get("footer_template", "")
            send_from_adds = request.POST.get("send_from_adds", "")
            send_to_adds = request.POST.get("send_to_adds", "")
            sign_1 = request.POST.get("sign_1", "")
            sign_2 = request.POST.get("sign_2", "")
            payment_block_display = request.POST.get("payment_block_display", "")
            send_from_block_display = request.POST.get("send_from_block_display", "")
            send_to_block_display = request.POST.get("send_to_block_display", "")
            notes_block_display = request.POST.get("notes_block_display", "")
            user_block_display = request.POST.get("user_block_display", "")
            selected_bg_line_item_color = request.POST.get(
                "selected_bg_line_item_color", ""
            )
            selected_font_line_item_color = request.POST.get(
                "selected_font_line_item_color", ""
            )
            selected_font_line_item_size = request.POST.get(
                "selected_font_line_item_size", ""
            )
            if (
                selected_font_line_item_size
                and "px" not in selected_font_line_item_size
            ):
                selected_font_line_item_size = f"{selected_font_line_item_size}px"

            app_setting.search_setting_purchase_order = search_setting
            app_setting.purchase_order_pdf_line_item_table = selected_columns
            app_setting.purchase_order_pdf_line_item_table_display = (
                selected_columns_display
            )
            app_setting.purchase_order_pdf_header_block = selected_header
            app_setting.purchase_order_pdf_header_block_display = (
                selected_header_display
            )
            app_setting.purchase_order_pdf_payment_block = selected_payment_block
            app_setting.purchase_order_pdf_send_from_block = selected_send_from_block
            app_setting.purchase_order_pdf_send_to_block = selected_send_to_block
            app_setting.purchase_order_pdf_notes_block = selected_notes_block
            app_setting.purchase_order_pdf_footer_template = selected_footer_template
            app_setting.purchase_order_information_block = selected_information
            app_setting.purchase_order_information_block_display = (
                selected_information_display
            )
            app_setting.purchase_order_send_from_adds_block = send_from_adds
            app_setting.purchase_order_send_to_adds_block = send_to_adds
            app_setting.purchase_order_sign_1_block = sign_1
            app_setting.purchase_order_sign_2_block = sign_2
            app_setting.purchase_order_pdf_payment_block_display = payment_block_display
            app_setting.purchase_order_pdf_send_from_block_display = (
                send_from_block_display
            )
            app_setting.purchase_order_pdf_send_to_block_display = send_to_block_display
            app_setting.purchase_order_pdf_notes_block_display = notes_block_display
            app_setting.purchase_order_pdf_line_item_table_bg_color = (
                selected_bg_line_item_color
            )
            app_setting.purchase_order_pdf_line_item_table_font_color = (
                selected_font_line_item_color
            )
            app_setting.purchase_order_pdf_line_item_table_font_size = (
                selected_font_line_item_size
            )
            app_setting.purchase_order_pdf_user_block = selected_user_block
            app_setting.purchase_order_pdf_user_block_display = user_block_display

            app_setting.save()

        if request.POST.get("app_settings", None) == PROCUREMENT_APP_SETTING_MANAGE_APP:
            setting_type = request.POST.get("setting_type", "")
            files = request.FILES.getlist("stampUpload")
            if files:
                for file in files:
                    file.name = str(uuid.uuid4()) + "." + file.name.split(".")[-1]
                    setattr(app_setting, setting_type + "file", file)
                    app_setting.save()
                    break
            app_setting.save()

            if (
                "change-stamp-image" in request.POST
                or "delete-stamp-image" in request.POST
            ):
                if "delete-stamp-image" in request.POST:
                    folder = page_group_type.title().replace("_", "-") + "-files"
                    try:
                        image_file = getattr(app_setting, setting_type + "_file", file)
                        if "nyc3.digitaloceanspaces.com" in image_file.url:
                            file_name = image_file.url.split("/")[-1]
                            file_name = f"{folder}/{file_name}"
                            sc_file = image_file
                            S3_CLIENT.delete_object(
                                Bucket=AWS_STORAGE_BUCKET_NAME,
                                Key=f"{AWS_LOCATION}/{sc_file.file}",
                            )
                            setattr(app_setting, setting_type + "file", None)
                        app_setting.save()
                    except:
                        pass

                elif "change-stamp-image" in request.POST:
                    # Change New
                    # File [Image]
                    files = request.FILES.getlist("file-change-profile-pic")
                    for file in files:
                        file.name = str(uuid.uuid4()) + "." + file.name.split(".")[-1]
                        setattr(app_setting, setting_type + "_file", file)
                        app_setting.save()
                        break

                try:
                    image_file = getattr(app_setting, setting_type + "_file", file)
                except:
                    image_file = None

                context = {
                    "image_file": image_file,
                    "setting_type": setting_type,
                    "target_post_url": "procurement_settings",
                    "object_type": PROCUREMENT_APP_TARGET,
                    "app_slug": PROCUREMENT_APP_SLUG,
                }
                response = render(request, "data/partials/partial-stamp.html", context)
                return response

        elif request.POST.get("app_settings", None) == "manage_app_access":
            users = request.POST.getlist("users", [])
            groups = request.POST.getlist("groups", [])

            app_setting.user.clear()
            app_setting.group.clear()

            selected_users = User.objects.filter(id__in=users)
            if selected_users:
                for selected_user in selected_users:
                    app_setting.user.add(selected_user)

            selected_groups = Group.objects.filter(id__in=groups)
            if selected_groups:
                for selected_group in selected_groups:
                    app_setting.group.add(selected_group)

            app_setting.save()

        setting_type = request.POST.get("setting_type", TYPE_OBJECT_PURCHASE_ORDER)

        return forward_properties(request)

    role = UserManagement.objects.get(user=request.user, workspace=workspace)
    groups = Group.objects.filter(workspace=workspace)

    symbol = ""
    if app_setting.expense_currency:
        symbol = CurrencySymbols.get_symbol(app_setting.expense_currency)

    setting_type = request.GET.get("setting_type", TYPE_OBJECT_PURCHASE_ORDER)

    # Ensure default PDF template is set for the context if not already defined
    if (
        setting_type == TYPE_OBJECT_PURCHASE_ORDER
        and not app_setting.purchase_order_pdf_template
    ):
        app_setting.purchase_order_pdf_template = (
            "data/purchase_order/purchaseorderPDF-pattern-1.html"
        )

    context = {
        "object_type": PROCUREMENT_APP_TARGET,
        "app_slug": PROCUREMENT_APP_SLUG,
        "page_title": page_title,
        "page_group_type": page_group_type,
        "role": role,
        "groups": groups,
        "app_setting": app_setting,
        "expense_currency": app_setting.expense_currency,
        "symbol": symbol,
        "orders": orders,
        "PurchaseOrdersNameCustomField": PurchaseOrdersNameCustomField.objects.filter(
            workspace=workspace, name__isnull=False
        ).order_by("order"),
        "number_formats": SHOPTURBO_NUMBER_FORMAT,
        "properties_types": OBJECTS_CF_TYPE,
        "setting_type": setting_type,
    }
    return render(
        request, "data/purchase_order/manage-purchase-order-app.html", context
    )


# =========== Purchase Order


@login_or_hubspot_required
def purchase_drawer(request, id=None):
    view_id = request.GET.get("view_id", None)
    set_id = request.GET.get("set_id", None)
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    items_show = None

    # Get module slug from request or find default module for purchase orders
    module_slug = request.GET.get("module")
    if not module_slug:
        # Try to find a module that contains purchase orders
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_PURCHASE_ORDER
            )
            .order_by("order", "created_at")
            .first()
        )
        if module:
            module_slug = module.slug
        else:
            # If no module found, use a default value that won't cause URL reversal errors
            module_slug = "purchase_orders"

    print("====set_id: ", request.GET)

    selected_order_id = request.GET.get("selected_order_id", "")
    drawer_type = request.GET.get("drawer_type", None)
    type_association = request.GET.get("type_association", None)
    source = request.GET.get("source", TYPE_OBJECT_PURCHASE_ORDER)
    object_id = request.GET.get("object_id")
    if drawer_type == "purchaseorder":
        context = {
            "view_id": view_id,
            "set_id": set_id,
            "PURCHASE_ORDER_STATUS": PURCHASE_ORDER_STATUS,
            "module": module_slug,
            "side_drawer": request.GET.get("sidedrawer", ""),
        }
        if type_association:
            context["source"] = source
            context["type_association"] = type_association
            context["object_id"] = object_id

        # association label list for forms
        association_labels = AssociationLabel.objects.filter(
            workspace=workspace, object_source=TYPE_OBJECT_PURCHASE_ORDER
        ).order_by("created_at")
        association_labels_sanka_true = AssociationLabel.objects.filter(
            workspace=workspace,
            object_source=TYPE_OBJECT_PURCHASE_ORDER,
            created_by_sanka=True,
        )
        association_label_list_sanka_true = [
            association_label.label
            for association_label in association_labels_sanka_true
        ]
        association_labels_sanka_false = AssociationLabel.objects.filter(
            workspace=workspace,
            object_source=TYPE_OBJECT_PURCHASE_ORDER,
            created_by_sanka=False,
        )
        association_label_list_sanka_false = [
            str(association_label.id)
            for association_label in association_labels_sanka_false
        ]
        association_label_list = (
            association_label_list_sanka_true + association_label_list_sanka_false
        )

        context["association_labels"] = association_labels
        context["association_label_list"] = association_label_list

        return render(request, "data/purchase_order/purchase-order-form.html", context)

    page_obj = get_page_object(TYPE_OBJECT_PURCHASE_ORDER, lang)

    base_model = page_obj["base_model"]
    page_title = page_obj["page_title"]

    custom_field = CustomProperty.objects.filter(
        workspace=workspace, model=base_model._meta.db_table, name="status"
    )
    if custom_field:
        custom_field = custom_field.first()
        choices = ast.literal_eval(custom_field.value)

        choices = [{"label": choices[value], "value": value} for value in choices]

    else:
        choices = [
            {"label": PURCHASE_ORDER_STATUS_DISPLAY[value][lang], "value": value}
            for value, label in PURCHASE_ORDER_STATUS
        ]

    purchase = None
    choice_status = None
    section = None
    exclude_item_list = []
    exclude_item = []
    action_index = None
    try:
        app_setting = AppSetting.objects.get(
            workspace=workspace, app_target=PROCUREMENT_APP_TARGET
        )
        value_app_setting_tax = app_setting.purchase_order_tax
        value_app_setting_is_status = app_setting.purchase_order_is_status
    except AppSetting.DoesNotExist:
        app_setting = None

    supplier_lock = False
    if app_setting and app_setting.purchase_order_supplier_lock:
        supplier_lock = True

    current_index = ""
    permission = get_permission(
        object_type=TYPE_OBJECT_PURCHASE_ORDER, user=request.user
    )
    if not permission:
        permission = DEFAULT_PERMISSION

    if request.method == "GET":
        section = request.GET.get("section")
        tab = request.GET.get("tab")
        if "available-items" in request.GET:
            if "add_type" in request.GET:
                action_index = request.GET.get("action_index")
                if action_index:
                    exclude_item_list = request.GET.getlist(
                        f"selected-item-id-{action_index}", []
                    )
                else:
                    exclude_item_list = request.GET.getlist("selected-item-id", [])

                i = 0
                for item in exclude_item_list:
                    if item:
                        exclude_item.append(item)
                    else:
                        i += 1
                # If everything is empty string, make it empty list
                if i == len(exclude_item_list):
                    exclude_item_list = [""]

            if "id" in request.GET:
                id_purchase = request.GET.get("id")
                purchase = PurchaseOrders.objects.get(id=id_purchase)

    context = {
        "selected_order_id": selected_order_id,
        "source": source,
        "module": module_slug,
        "PURCHASE_ORDER_STATUS": PURCHASE_ORDER_STATUS,
        "view_id": view_id,
        "page_title": page_title,
        "app_setting": app_setting,
        "value_app_setting_tax": value_app_setting_tax,
        "value_app_setting_is_status": value_app_setting_is_status,
        "purchase": purchase,
        "obj": purchase,
        "PurchaseOrdersNameCustomField": PurchaseOrdersNameCustomField.objects.filter(
            workspace=workspace, name__isnull=False
        ).order_by("order"),
        "permission": permission,
        "object_type": TYPE_OBJECT_PURCHASE_ORDER,
        "tab": tab,
        "choices": choices,
        "supplier_lock": supplier_lock,
        "side_drawer": request.GET.get("sidedrawer", ""),
    }

    PurchaseOrdersCustomFieldMap = {}
    PurchaseOrdersCustomFieldName = PurchaseOrdersNameCustomField.objects.filter(
        workspace=workspace
    )
    for ctf in PurchaseOrdersCustomFieldName:
        PurchaseOrdersCustomFieldMap[str(ctf.id)] = ctf
    context["PurchaseOrdersCustomFieldMap"] = PurchaseOrdersCustomFieldMap

    if set_id == "None":
        set_id = None
    if set_id:
        property_set = PropertySet.objects.filter(id=set_id).first()
        print(property_set)
    else:
        if view_id == "None":
            view_id = None
        if view_id:
            view = View.objects.filter(
                id=view_id, target=TYPE_OBJECT_PURCHASE_ORDER
            ).first()
            if view:
                if view.form:
                    property_set = view.form
                else:
                    condition_filter = Q(
                        workspace=workspace, target=TYPE_OBJECT_PURCHASE_ORDER
                    )
                    condition_filter &= Q(as_default=True) | Q(name__isnull=True)
                    property_set = PropertySet.objects.filter(condition_filter).first()
            else:
                condition_filter = Q(
                    workspace=workspace, target=TYPE_OBJECT_PURCHASE_ORDER
                )
                condition_filter &= Q(as_default=True) | Q(name__isnull=True)
                property_set = PropertySet.objects.filter(condition_filter).first()
        else:
            condition_filter = Q(workspace=workspace, target=TYPE_OBJECT_PURCHASE_ORDER)
            condition_filter &= Q(as_default=True) | Q(name__isnull=True)
            property_set = PropertySet.objects.filter(condition_filter).first()

    if property_set:
        if property_set.name is None:
            property_set = get_default_property_set(
                TYPE_OBJECT_PURCHASE_ORDER, workspace, lang
            )

    choice_status = None
    properties = {"list_all": []}
    line_item_properties = None
    if property_set:
        properties["list_all"] = property_set.children
        # if property_set.custom_property:
        #     choice_status = property_set.custom_property.value
        line_item_properties = property_set.purchase_order_line_items.all()

    context["properties"] = properties
    context["property_set"] = property_set
    context["line_item_properties"] = line_item_properties

    page = 1
    if id:
        page = request.GET.get("page", 1)
        purchase = PurchaseOrders.objects.get(id=id)
        pois = PurchaseOrdersItem.objects.filter(purchase_order=purchase).order_by(
            "created_at"
        )
        item = []
        status = []
        amount = []
        item_id = []
        price = []
        tax = []
        poi_id = []
        items_show = None
        if pois:
            try:
                print(f"DEBUG: Processing {len(pois)} purchase order items")
                for poi in pois:
                    print(
                        f"DEBUG: POI {poi.id} - item_name: '{poi.item_name}', item: {poi.item}, item.id: {poi.item.id if poi.item else 'None'}"
                    )
                    poi_id.append(poi.id)
                    item.append(poi.item_name)
                    status.append(poi.item_status)
                    amount.append(poi.amount_item)
                    tax.append(poi.tax_rate)
                    price.append(poi.amount_price)
                    if poi.item:
                        item_id.append(str(poi.item.id))
                        print(f"DEBUG: Added item_id: {poi.item.id}")
                    else:
                        item_id.append("")
                        print(f"DEBUG: No item linked, added empty string")
                items_show = list(
                    zip(poi_id, item_id, item, amount, price, tax, status)
                )
                print(f"DEBUG: items_show structure: {items_show}")
            except Exception as e:
                print("Error item show: ", e)
                import traceback

                traceback.print_exc()
                pass

        if purchase.date:
            if lang == "en":
                purchase.date = str(purchase.date)[:10]
        else:
            purchase.date = ""

        app_logs = AppLog.objects.filter(
            workspace=workspace, purchaseorders=purchase
        ).order_by("-created_at")

        # customize_template = CustomizePdfTemplate.objects.filter(
        #     setting_type=TYPE_OBJECT_PURCHASE_ORDER, workspace=workspace)
        customize_template = []

        list_template = []
        for filename in os.listdir(DIRECTORY):
            # Check if the file is an HTML file and contains 'pdf_pattern' in its name
            if filename.endswith(".html") and page_obj["pdf_pattern"] in filename:
                list_template.append(NEXT_DIRECTORY + filename)

        list_template = sorted(list_template, key=natural_sort_key)
        customize_template = PdfTemplate.objects.filter(
            workspace=workspace, master_pdf__object_type=TYPE_OBJECT_PURCHASE_ORDER
        ).order_by("master_pdf__name_en", "master_pdf__name_ja")

        try:
            hide_associated_data = bool(request.GET.get("hide_associated_data", False))
        except:
            hide_associated_data = False

        context = {
            "selected_order_id": selected_order_id,
            "source": source,
            "module": module_slug,
            "PURCHASE_ORDER_STATUS": PURCHASE_ORDER_STATUS,
            "view_id": view_id,
            "app_setting": app_setting,
            "page_title": page_title,
            "items_show": items_show,
            "purchase": purchase,
            "obj": purchase,
            "PurchaseOrdersNameCustomField": PurchaseOrdersNameCustomField.objects.filter(
                workspace=workspace, name__isnull=False
            ).order_by("order"),
            "value_app_setting_tax": value_app_setting_tax,
            "value_app_setting_is_status": value_app_setting_is_status,
            "supplier_lock": supplier_lock,
            "permission": permission,
            "app_logs": app_logs,
            "object_type": TYPE_OBJECT_PURCHASE_ORDER,
            "tab": tab,
            "choices": choices,
            "list_template": list_template,
            "list_customize_template": customize_template,
            "page": page,
            "side_drawer": request.GET.get("sidedrawer", ""),
            "hide_associated_data": hide_associated_data,
            "object_action": {"additional_params": {"po_ids": str(purchase.id)}},
        }

    elif section:
        if choice_status:
            context["choice_status"] = choice_status
        if choices:
            context["choices"] = choices
        if set_id:
            context["set_id"] = set_id
        context["section"] = section
        context["view_id"] = view_id
        context["associate_id"] = request.GET.get("associate_id", "")
        context["object_id"] = request.GET.get("object_id", "")
        context["page"] = request.GET.get("page", 1)
        context["associate_view_id"] = request.GET.get("associate_view_id", "")
        context["purchase"] = {"model": base_model, "workspace": workspace}
        context["type_association"] = type_association
        context["source"] = source

        # association label list for forms
        association_labels = AssociationLabel.objects.filter(
            workspace=workspace, object_source=TYPE_OBJECT_PURCHASE_ORDER
        ).order_by("created_at")
        association_labels_sanka_true = AssociationLabel.objects.filter(
            workspace=workspace,
            object_source=TYPE_OBJECT_PURCHASE_ORDER,
            created_by_sanka=True,
        )
        association_label_list_sanka_true = [
            association_label.label
            for association_label in association_labels_sanka_true
        ]
        association_labels_sanka_false = AssociationLabel.objects.filter(
            workspace=workspace,
            object_source=TYPE_OBJECT_PURCHASE_ORDER,
            created_by_sanka=False,
        )
        association_label_list_sanka_false = [
            str(association_label.id)
            for association_label in association_labels_sanka_false
        ]
        association_label_list = (
            association_label_list_sanka_true + association_label_list_sanka_false
        )

        context["association_labels"] = association_labels
        context["association_label_list"] = association_label_list

        return render(
            request, "data/purchase_order/purchase-order-create-form.html", context
        )

    if choice_status:
        context["choice_status"] = choice_status
    if choices:
        context["choices"] = choices
    if set_id:
        context["set_id"] = set_id
    if line_item_properties:
        context["line_item_properties"] = line_item_properties

    if "available-items" in request.GET:
        context["count_item"] = int(request.GET.get("available-items")) + 1
        context["type_data"] = "purchase"
        context["drawer_type"] = "purchaseorder"
        context["poi_id"] = None
        if current_index:
            context["action_index"] = current_index
        if exclude_item:
            context["exclude_item"] = exclude_item
        if "add_type" in request.GET:
            context["add_type"] = request.GET.get("add_type")
            if (
                request.GET.get("add_type") == "select_item"
                or request.GET.get("add_type") == "manual_item"
            ):
                action_index = request.GET.get("action_index")
                if action_index:
                    context["action_index"] = action_index
                return render(
                    request,
                    "data/purchase_order/purchase-order-manual-item.html",
                    context,
                )
            return HttpResponse("")
        else:
            return render(
                request,
                "data/purchase_order/purchase-order-partial-add-items.html",
                context,
            )
    else:
        context["properties"] = properties
        context["PurchaseOrdersCustomFieldMap"] = PurchaseOrdersCustomFieldMap
        context["po_associates"] = Association.objects.filter(
            workspace=workspace, purchase_order_associate__isnull=False
        )
        context["associates"] = Association.objects.filter(
            workspace=workspace, associate_type=TYPE_OBJECT_PURCHASE_ORDER
        )

        context["view_id"] = view_id
        context["page"] = request.GET.get("page", 1)
        if not id:
            context["purchase"] = {"model": "purchaseorders", "workspace": workspace}

        # association label list for forms
        association_labels = AssociationLabel.objects.filter(
            workspace=workspace, object_source=TYPE_OBJECT_PURCHASE_ORDER
        ).order_by("created_at")
        association_labels_sanka_true = AssociationLabel.objects.filter(
            workspace=workspace,
            object_source=TYPE_OBJECT_PURCHASE_ORDER,
            created_by_sanka=True,
        )
        association_label_list_sanka_true = [
            association_label.label
            for association_label in association_labels_sanka_true
        ]
        association_labels_sanka_false = AssociationLabel.objects.filter(
            workspace=workspace,
            object_source=TYPE_OBJECT_PURCHASE_ORDER,
            created_by_sanka=False,
        )
        association_label_list_sanka_false = [
            str(association_label.id)
            for association_label in association_labels_sanka_false
        ]
        association_label_list = (
            association_label_list_sanka_true + association_label_list_sanka_false
        )

        context["association_labels"] = association_labels
        context["association_label_list"] = association_label_list

        return render(request, "data/purchase_order/purchase-order-form.html", context)


@login_or_hubspot_required
def purchaseEdit(request, id=None):
    print("Request POST data received.")
    set_id = request.POST.get("set_id", None)
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)

    if request.method == "POST":
        associate_id = request.POST.get("associate_id", None)
        object_id = request.POST.get("object_id", None)
        page = request.POST.get("page", 1)
        source = request.POST.get("source", None)
        selected_order_id = request.POST.get("selected_order_id", "")
        associate_view_id = request.POST.get("associate_view_id", None)
        associate_type = None
        custom_model = None

        object_type = request.POST.get("object_type", None)
        module_slug = request.POST.get("module", None)
        if not object_type:
            object_type = TYPE_OBJECT_PURCHASE_ORDER

        if associate_id and object_id and source:
            module_object_slug = OBJECT_TYPE_TO_SLUG[source]
            module = Module.objects.filter(
                workspace=workspace, object_values__contains=source
            ).order_by("order", "created_at")
            if module:
                module = module.first()
                module_slug = module.slug

            try:
                associate = Association.objects.get(id=associate_id)
                associate_type = associate.associate_type
                if associate_type != source:
                    attr_name = SOURCE_ASSOCIATE_MAP.get(source, None)
                    if attr_name:
                        associate_attribute = getattr(associate, attr_name, None)
                        if associate_attribute:
                            custom_model = associate_attribute
                else:
                    for attr_name, _ in ASSOCIATE_MAP.items():
                        attr = getattr(associate, attr_name, None)
                        if attr:
                            custom_model = attr
                            break

            except Exception as e:
                print(f"Error: {e}")

            if not custom_model:
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="アソシエーションの取得に失敗しました。",
                        type="error",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Failed to retrieve the association.",
                        type="error",
                    )

                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
        else:
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_PURCHASE_ORDER]
            module = Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_PURCHASE_ORDER
            ).order_by("order", "created_at")

            if module_slug:
                module = module.filter(slug=module_slug)

            if module:
                module = module.first()
                module_slug = module.slug

        if "submit_csv_upload" in request.POST:
            purchase_order_bulk_create(request)

            if request.POST.get("view_id"):
                view_id = request.POST.get("view_id", None)
                if module:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                        + f"?view_id={view_id}"
                    )
            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        if request.POST.get("post") == "toggle":
            po = PurchaseOrders.objects.get(id=id)
            if po.usage_status == "archived":
                sync_usage(workspace, MODELS_TO_STORAGE_USAGE[PurchaseOrders])
                if has_quota(workspace, PURCHASE_ORDER_USAGE_CATEGORY):
                    po.usage_status = "active"
            else:
                po.usage_status = "archived"
            po.save(log_data={"user": request.user, "workspace": workspace})
            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?id={id}"
                )
            return redirect(reverse("main", host="app"))
        if request.POST.get("post") == "download_po":
            response = purchase_order_pdf_download(request, id=id)
            return response
        if request.POST.get("post") == "send_email":
            response = purchase_order_send_mail(request, id=id)
            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?id={id}"
                )
            return redirect(reverse("main", host="app"))

        notes = request.POST.get("notes")
        send_from = request.POST.get("send_from")
        tax_rate = request.POST.get("tax_rate")
        if not tax_rate:
            tax_rate = 0
        tax_option = request.POST.get("tax_option")
        status = request.POST.get("status", "")
        contact_and_company = request.POST.getlist("contact_and_company", None)
        currency = request.POST.get("currency", "")
        owner = request.POST.get("owner", None)

        item_request = request.POST.getlist("item")
        item = []
        item_ids = []

        for it in item_request:
            try:
                if "|" in it:
                    item_id = it.split("|")[0]
                    if not is_valid_uuid(item_id):
                        item_id = it.split("|")[2]

                    item.append(it.split("|")[0])
                    item_ids.append(item_id)
                else:
                    item.append(it)
                    item_ids.append("")
            except:
                print("Error pulling item: ".e)
        item_amount = request.POST.getlist("item_amount", "")
        item_status = request.POST.getlist("item_status", "")

        if not item_status:
            item_status = []
            for amount in item_amount:
                item_status.append("")

        amount = request.POST.getlist("amount", "")
        tax_item_rate = request.POST.getlist("tax_item_rate")

        date = request.POST.get("date", None)

        if not date:
            date = None

        if date:
            try:
                # First try Japanese format
                date = datetime.strptime(date, "%Y年%m月%d日").date()
            except ValueError:
                # If Japanese format fails, try ISO format
                date = datetime.strptime(date, "%Y-%m-%d").date()

        type_association = request.POST.get("type_association")
        source = request.POST.get("source", None)
        object_id = request.POST.get("object_id")

        if id:
            purchase = PurchaseOrders.objects.get(id=id)
        else:
            sync_usage(workspace, MODELS_TO_STORAGE_USAGE[PurchaseOrders])
            if not has_quota(workspace, PURCHASE_ORDER_USAGE_CATEGORY):
                entries_name = USAGE_CATEGORIES_TITLE[PURCHASE_ORDER_USAGE_CATEGORY][
                    "en"
                ]
                msg = f"{entries_name}, could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some existing {entries_name} to free up space."
                if lang == "ja":
                    entries_name = USAGE_CATEGORIES_TITLE[
                        PURCHASE_ORDER_USAGE_CATEGORY
                    ]["ja"]
                    msg = f"{entries_name},制限を超えたため作成できませんでした。サブスクリプション プランをアップグレードしてクォータを増やすか、既存の {entries_name} の一部をアーカイブしてスペースを解放します。"
                Notification.objects.create(
                    workspace=workspace, user=request.user, message=msg, type="error"
                )
                if module:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                    )
                return redirect(reverse("main", host="app"))
            purchase = PurchaseOrders.objects.create(workspace=workspace)
            purchase.save(
                log_data={
                    "user": request.user,
                    "status": "create",
                    "workspace": workspace,
                }
            )

        # Let maintain this code to make sure the position of the item (if dragged to another position) and update the information (cc@ Khanh)
        old_pois = PurchaseOrdersItem.objects.filter(
            purchase_order=purchase, created_at__lt=timezone.now()
        )

        for index, (
            item_name,
            item_price,
            item_amount,
            tax_item_rate,
            item_id,
            item_status,
        ) in enumerate(
            zip(item, amount, item_amount, tax_item_rate, item_ids, item_status)
        ):
            if item_id:
                poi = PurchaseOrdersItem.objects.create(
                    purchase_order=purchase, item_id=item_id
                )
                old_poi = (
                    old_pois.filter(purchase_order=purchase, item_id=item_id)
                    .exclude(id=poi.id)
                    .first()
                )
            else:
                poi = PurchaseOrdersItem.objects.create(purchase_order=purchase)
                old_poi = (
                    old_pois.filter(purchase_order=purchase).exclude(id=poi.id).first()
                )

            if not tax_item_rate:
                tax_item_rate = "0"

            if tax_option in ["unified_tax", "no_tax"]:
                tax_item_rate = "0"

            if not item_price:
                item_price = "0"
            else:
                item_price = item_price.replace(",", "")

            # DEBUG: Purchase order item creation in main.py
            print(f"[DEBUG MAIN] POI Creation - Line 1161")
            print(f"[DEBUG MAIN] poi.id: {poi.id}")
            print(f"[DEBUG MAIN] poi.item: {poi.item}")
            print(f"[DEBUG MAIN] item_name from form: '{item_name}'")
            print(f"[DEBUG MAIN] item_id from form: '{item_id}'")

            # FIX: If item_id is empty but item_name looks like UUID, set the foreign key
            if not item_id and item_name and len(item_name) == 36 and "-" in item_name:
                try:
                    from data.models import ShopTurboItems

                    item_obj = ShopTurboItems.objects.filter(
                        workspace=poi.purchase_order.workspace, id=item_name
                    ).first()
                    if item_obj:
                        poi.item = item_obj
                        poi.save(update_fields=["item"])
                        print(
                            f"[DEBUG MAIN] FIXED: Set foreign key relationship to {item_obj.name}"
                        )
                    else:
                        print(
                            f"[DEBUG MAIN] ERROR: No item found with UUID: {item_name}"
                        )
                except Exception as e:
                    print(f"[DEBUG MAIN] ERROR: Failed to set foreign key: {e}")

            if poi.item:
                print(f"[DEBUG MAIN] poi.item.name: '{poi.item.name}'")
                print(f"[DEBUG MAIN] poi.item.description: '{poi.item.description}'")
            else:
                print(f"[DEBUG MAIN] poi.item is None - foreign key not set!")

            # Ensure item_name contains actual item name, not UUID
            if poi.item and poi.item.name and poi.item.name != "None":
                poi.item_name = poi.item.name
                print(f"[DEBUG MAIN] Set item_name to item.name: '{poi.item_name}'")
            else:
                poi.item_name = item_name
                print(f"[DEBUG MAIN] Set item_name to form value: '{poi.item_name}'")
            poi.item_status = item_status
            poi.amount_price = item_price
            poi.amount_item = item_amount
            poi.tax_rate = tax_item_rate
            if tax_item_rate:
                # Remove currency symbol and commas before converting to float
                # Dictionary of common currency symbols to handle
                CURRENCY_SYMBOLS = {
                    "¥": "",  # Japanese Yen
                    "$": "",  # Dollar
                    "€": "",  # Euro
                    "£": "",  # British Pound
                    "₩": "",  # Korean Won
                    "₹": "",  # Indian Rupee
                    "₪": "",  # Israeli Shekel
                    "₱": "",  # Philippine Peso
                    "฿": "",  # Thai Baht
                    "kr": "",  # Swedish/Danish/Norwegian Krone
                    "Rp": "",  # Indonesian Rupiah
                    "Rs": "",  # Pakistani/Sri Lankan Rupee
                    "R$": "",  # Brazilian Real
                    "R": "",  # South African Rand
                    "₴": "",  # Ukrainian Hryvnia
                    "₽": "",  # Russian Ruble
                }

                clean_price = poi.amount_price
                # Remove any currency symbols
                for symbol in CURRENCY_SYMBOLS:
                    clean_price = clean_price.replace(symbol, "")
                # Remove any commas and whitespace
                clean_price = clean_price.replace(",", "").strip()

                clean_amount = poi.amount_item.replace(",", "").strip()
                poi.total_price_without_tax = float(clean_price) * float(clean_amount)
                poi.total_price = float(poi.total_price_without_tax) + (
                    float(poi.total_price_without_tax) * float(poi.tax_rate) / 100
                )
            poi.save(log_data={"user": request.user, "workspace": workspace})

            line_item_properties = {
                key: request.POST.getlist(key)
                for key in request.POST
                if key.startswith("line_item_property")
            }

            for key, value in line_item_properties.items():
                custom_field = PurchaseOrdersItemNameCustomField.objects.get(
                    id=key.split("|")[-1]
                )
                custom_field_value, _ = (
                    PurchaseOrdersItemValueCustomField.objects.get_or_create(
                        field_name=custom_field, purchaseordersitem=poi
                    )
                )
                custom_field_value.value = value[index]
                custom_field_value.save()

            # Check if updating existing purchase item choice property
            if old_poi:
                print(old_poi.item_status, item_status)
            if old_poi and old_poi.item_status and old_poi.item_status != item_status:
                trigger_workflow_by_purchase_order_item_status_event(
                    poi, request.user.username, lang=request.LANGUAGE_CODE
                )

        # Let maintain this code to make sure the position of the item (if dragged to another position) and update the information (cc@ Khanh)
        if old_pois:
            old_pois.delete()

        # Supllier Save
        # association Label
        association_label = AssociationLabel.objects.filter(
            workspace=workspace,
            object_source=TYPE_OBJECT_PURCHASE_ORDER,
            label__iexact="supplier",
        ).first()
        AssociationLabelObject.reset_associations_for_object(
            purchase, workspace, association_label
        )
        purchase.contact = None
        purchase.company = None
        for contact_and_company_id in contact_and_company:
            if Contact.objects.filter(id=contact_and_company_id):
                purchase.contact = Contact.objects.get(id=contact_and_company_id)
                purchase.company = None
                AssociationLabelObject.create_association(
                    purchase, purchase.contact, workspace, association_label
                )

            elif Company.objects.filter(id=contact_and_company_id):
                purchase.company = Company.objects.get(id=contact_and_company_id)
                purchase.contact = None
                AssociationLabelObject.create_association(
                    purchase, purchase.company, workspace, association_label
                )

        purchase.tax_option = tax_option
        purchase.notes = notes
        purchase.send_from = send_from
        purchase.status = status
        purchase.currency = currency
        purchase.date = date
        purchase.tax_rate = tax_rate

        assign_object_owner(purchase, owner, request, TYPE_OBJECT_PURCHASE_ORDER)

        purchase.save(log_data={"user": request.user, "workspace": workspace})

        purchase = handling_items(purchase)
        # custom_field(request,type_='purchase_order',obj=purchase)
        save_custom_property(request, purchase)

        # Save association labels
        save_association_label(request, purchase, TYPE_OBJECT_PURCHASE_ORDER)

        if type_association:
            if source == TYPE_OBJECT_INVENTORY_TRANSACTION and object_id:
                try:
                    transaction = InventoryTransaction.objects.get(
                        workspace=workspace, usage_status="active", id=object_id
                    )
                    purchase.inventory_transactions.add(transaction)
                except:
                    pass
                module_object_slug = OBJECT_TYPE_TO_SLUG[source]
                module = Module.objects.filter(
                    workspace=workspace, object_values__contains=source
                ).order_by("order", "created_at")
                if module:
                    module = module.first()
                    module_slug = module.slug

            elif source == TYPE_OBJECT_ORDER and object_id:
                try:
                    order = ShopTurboOrders.objects.filter(
                        workspace=workspace, status="active", id=object_id
                    ).first()
                    # order.purchase_orders.add(purchase)
                    # order.save(log_data={"user": request.user, "workspace": workspace})
                    
                    association_label = AssociationLabel.objects.filter(workspace=workspace,object_source=TYPE_OBJECT_ORDER, label__iexact=TYPE_OBJECT_PURCHASE_ORDER).first()
                    
                    if order and association_label:
                        AssociationLabelObject.create_association(order, purchase, workspace, association_label)

                except Exception as e:
                    print("Error association: ", e)
                module_object_slug = OBJECT_TYPE_TO_SLUG[source]
                module = Module.objects.filter(
                    workspace=workspace, object_values__contains=source
                ).order_by("order", "created_at")
                if module:
                    module = module.first()
                    module_slug = module.slug

                return redirect(
                    build_redirect_url(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                        if module
                        else reverse("main", host="app"),
                        id=object_id,
                        target=TYPE_OBJECT_ORDER,
                    )
                )
        if "update_inventory_transactions" in request.POST:
            inventory_transactions = request.POST.getlist("inventory_transactions", [])
            purchase.inventory_transactions.clear()
            for inventory_transaction_id in inventory_transactions:
                if InventoryTransaction.objects.filter(id=inventory_transaction_id):
                    transaction = InventoryTransaction.objects.get(
                        id=inventory_transaction_id
                    )
                    purchase.inventory_transactions.add(transaction)

    # Association Create
    if associate_type and object_id and source and custom_model:
        redirect_url = update_associate_object(
            request,
            associate_type,
            source,
            lang,
            object_id,
            custom_model,
            purchase,
            object_type,
            page,
            module_slug,
            module_object_slug,
            associate_view_id,
        )
        return redirect(redirect_url)

    if source and source != TYPE_OBJECT_PURCHASE_ORDER:
        module_object_slug = OBJECT_TYPE_TO_SLUG[source]
        module = (
            Module.objects.filter(workspace=workspace, object_values__contains=source)
            .order_by("order", "created_at")
            .first()
        )
        if module:
            module_slug = module.slug
            return redirect(
                build_redirect_url(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    if module
                    else reverse("main", host="app"),
                    id=selected_order_id,
                    target=source,
                )
            )

    if request.POST.get("view_id"):
        view_id = request.POST.get("view_id", None)

        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_PURCHASE_ORDER]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_PURCHASE_ORDER
            )
            .order_by("order", "created_at")
            .first()
        )
        if module:
            module_slug = module.slug
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?id={purchase.id}&view_id={view_id}&set_id={set_id}"
            )

        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_PURCHASE_ORDER]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_PURCHASE_ORDER
            )
            .order_by("order", "created_at")
            .first()
        )

        if module:
            module_slug = module.slug
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?id={purchase.id}&set_id={set_id}"
            )
        return redirect(reverse("main", host="app"))

    # Fallback redirect if other conditions in the POST block were not met
    po_module_object_slug = OBJECT_TYPE_TO_SLUG.get(
        TYPE_OBJECT_PURCHASE_ORDER, "purchase-orders"
    )
    po_module = (
        Module.objects.filter(
            workspace=workspace, object_values__contains=TYPE_OBJECT_PURCHASE_ORDER
        )
        .order_by("order", "created_at")
        .first()
    )

    final_module_slug = po_module.slug if po_module else "procurement"

    if purchase and hasattr(purchase, "id") and purchase.id:
        redirect_params = f"?id={purchase.id}"
        if set_id and set_id != "None":
            redirect_params += f"&set_id={set_id}"

        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={
                    "module_slug": final_module_slug,
                    "object_slug": po_module_object_slug,
                },
            )
            + redirect_params
        )
    else:
        # Fallback if purchase object or its ID is not available
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={
                    "module_slug": final_module_slug,
                    "object_slug": po_module_object_slug,
                },
            )
        )

    # The function originally might have implicitly returned None if not a POST request.
    # This change specifically addresses the error for POST requests.
    # If GET requests to this URL also need handling, that would be a separate consideration.


@login_or_hubspot_required
def purchase_order_bulk_create(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)

    csv = request.FILES.get("csv_upload", False)
    if not csv:
        Notification.objects.create(
            workspace=get_workspace(request.user),
            user=request.user,
            message="Failed to retrieve the CSV file.",
            type="error",
        )
        return redirect(reverse("purchaseorder", host="app"))

    file_columns = request.POST.getlist("order-file-column")
    sanka_properties = request.POST.getlist("order-sanka-properties")
    ignores = request.POST.getlist("order-ignore")

    import_method_order = request.POST.get("import-method-order")
    order_key_field = request.POST.getlist("order-key-field")

    try:
        with csv.open() as f:
            read_data = f.read()
            result = chardet.detect(read_data)
            encoding = result["encoding"]
            # #Reset Pointer
            f.seek(0)
            if encoding.lower() == "shift_jis":
                content = f.read()
                decoded_content = content.decode("shift_jis")
                string_data = StringIO(decoded_content)
                df = pd.read_csv(string_data, sep=",", encoding="shift_jis", dtype=str)
            else:
                df = pd.read_csv(f, dtype=str)
            df = df.dropna(axis=1, how="all")
            df = df.dropna(axis=0, how="all")
            df = df.reset_index(drop=True)

        original_file_name = csv.name
        transfer_history = TransferHistory.objects.create(
            workspace=workspace,
            user=request.user,
            status="running",
            type="import_purchase_order",
            name=original_file_name,
        )

        df = df.astype(str)
        sync_usage(workspace, MODELS_TO_STORAGE_USAGE[PurchaseOrders])
        for index_loop, row in df.iterrows():
            create_json_orders = {}
            create_json_orders_custom_field = {}
            supplier_target = None

            insert_items = []
            insert_number_items = []
            insert_price_items = []

            for idx, file_column in enumerate(file_columns):
                # check ignore
                if ignores[idx] == "True":
                    continue

                if PurchaseOrdersNameCustomField.objects.filter(
                    name=sanka_properties[idx], workspace=workspace, name__isnull=False
                ).order_by("order"):
                    create_json_orders_custom_field[sanka_properties[idx]] = row[
                        file_column
                    ]
                else:
                    obj = row[file_column]
                    if sanka_properties[idx] == "company":
                        obj, _ = Company.objects.get_or_create(
                            workspace=workspace, name=row[file_column]
                        )
                        obj.status = "active"
                        obj.save()
                    elif sanka_properties[idx] == "contact":
                        obj, _ = Contact.objects.get_or_create(
                            workspace=workspace, name=row[file_column]
                        )
                        obj.status = "active"
                        obj.save()
                    elif sanka_properties[idx] == "currency":
                        obj = row[file_column].upper()
                    elif "date" in sanka_properties[idx]:
                        try:
                            obj = datetime.strptime(
                                row[file_column], "%m/%d/%Y"
                            ).strftime("%Y-%m-%d")
                        except Exception:
                            print("ERROR === procurement.py -- 2067: {e}")
                            obj = None

                    if obj == "nan":
                        obj = None
                    row[file_column] = obj

                    try:
                        if not create_json_orders[sanka_properties[idx]]:
                            create_json_orders[sanka_properties[idx]] = row[file_column]
                    except:
                        create_json_orders[sanka_properties[idx]] = row[file_column]

                    # A. item
                    if "purchaseitem_id" in create_json_orders:
                        if "purchaseitem_id" in create_json_orders:
                            insert_items = create_json_orders.pop("purchaseitem_id")
                            insert_items = str(insert_items).split(
                                CSV_DELIMITER_LIST_FIELD
                            )
                        if "number_item" in create_json_orders:
                            insert_number_items = create_json_orders.pop("number_item")
                            insert_number_items = str(insert_number_items).split(
                                CSV_DELIMITER_LIST_FIELD
                            )
                        if "price_of_items" in create_json_orders:
                            insert_price_items = create_json_orders.pop(
                                "price_of_items"
                            )
                            insert_price_items = str(insert_price_items).split(
                                CSV_DELIMITER_LIST_FIELD
                            )

            supplier = None

            if "status" in create_json_orders:
                if create_json_orders["status"] not in dict(PURCHASE_ORDER_STATUS):
                    create_json_orders["status"] = "internal_approved"

            if import_method_order == "create_and_update":
                if order_key_field:
                    order_key_field_json = {}
                    for order_key in order_key_field:
                        order_key_field_json[order_key] = create_json_orders[order_key]

            elif import_method_order == "create":
                if "id_po" in create_json_orders:
                    create_json_orders.pop("id_po")

                if "supplier_company" in create_json_orders:
                    supplier = create_json_orders.pop("supplier_company")
                    if supplier:
                        supplier_target = "company"

                if "supplier_contact" in create_json_orders:
                    if not supplier_target:
                        supplier = create_json_orders.pop("supplier_contact")
                        if supplier:
                            supplier_target = "contact"
                    else:
                        create_json_orders.pop("supplier_contact")

                if not has_quota(workspace, PURCHASE_ORDER_USAGE_CATEGORY):
                    break

                order = PurchaseOrders.objects.create(
                    **create_json_orders, usage_status="active", workspace=workspace
                )

                if supplier and supplier_target:
                    if supplier_target == "contact":
                        supplier = Contact.objects.filter(
                            workspace=workspace, contact_id=int(supplier)
                        )
                        if supplier:
                            order.contact = supplier.first()
                    elif supplier_target == "company":
                        supplier = Company.objects.filter(
                            workspace=workspace, company_id=int(supplier)
                        )
                        if supplier:
                            order.company = supplier.first()
                order.save()
                # Write Custom Field
                for create_json_contacts_cs_field in create_json_orders_custom_field:
                    contact_name_custom_field = (
                        PurchaseOrdersNameCustomField.objects.get(
                            name=create_json_contacts_cs_field, workspace=workspace
                        )
                    )
                    CustomFieldValue, _ = (
                        PurchaseOrdersValueCustomField.objects.get_or_create(
                            field_name=contact_name_custom_field, purchaseorders=order
                        )
                    )
                    CustomFieldValue.value = create_json_orders_custom_field[
                        create_json_contacts_cs_field
                    ]
                    CustomFieldValue.save()

            elif import_method_order == "update":
                if order_key_field:
                    if "supplier_company" in create_json_orders:
                        index = order_key_field.index("supplier_company")
                        supplier = order_key_field.pop(index)
                        if supplier:
                            supplier_target = "company"

                    if "supplier_contact" in create_json_orders:
                        index = order_key_field.index("supplier_contact")
                        if not supplier_target:
                            supplier = order_key_field.pop(index)
                            if supplier:
                                supplier_target = "contact"
                        else:
                            order_key_field.pop(index)

                    id_po = "%04d" % int(create_json_orders["id_po"])
                    # if Data is not exist, Skip
                    order = PurchaseOrders.objects.filter(
                        id_po=id_po, workspace=workspace
                    ).first()
                    if order:
                        for order_key in order_key_field:
                            setattr(order, order_key, create_json_orders[order_key])

                        if supplier and supplier_target:
                            supplier = create_json_orders[supplier]
                            if supplier_target == "contact":
                                supplier = Contact.objects.filter(
                                    workspace=workspace, contact_id=int(supplier)
                                )
                                if supplier:
                                    order.contact = supplier.first()
                            elif supplier_target == "company":
                                supplier = Company.objects.filter(
                                    workspace=workspace, company_id=int(supplier)
                                )
                                if supplier:
                                    order.company = supplier.first()
                        order.save()

                        # Write Custom Field
                        for (
                            create_json_contacts_cs_field
                        ) in create_json_orders_custom_field:
                            contact_name_custom_field = (
                                PurchaseOrdersNameCustomField.objects.get(
                                    name=create_json_contacts_cs_field,
                                    workspace=workspace,
                                )
                            )
                            CustomFieldValue, _ = (
                                PurchaseOrdersValueCustomField.objects.get_or_create(
                                    field_name=contact_name_custom_field,
                                    purchaseorders=order,
                                )
                            )
                            CustomFieldValue.value = create_json_orders_custom_field[
                                create_json_contacts_cs_field
                            ]
                            CustomFieldValue.save()

            if order:
                item_records = [
                    {
                        "item": item,
                        "amount": amount
                        or "",  # Use empty string if amount is None/empty
                        "price": price or "",  # Use empty string if price is None/empty
                    }
                    for item, amount, price in zip(
                        insert_items,
                        insert_number_items or [""] * len(insert_items),
                        insert_price_items or [""] * len(insert_items),
                    )
                ]
                for item_record in item_records:
                    item = ShopTurboItems.objects.get(
                        workspace=workspace, item_id=item_record["item"]
                    )
                    if not order.currency:
                        order.currency = item.currency
                        order.save()
                    if not item_record["amount"]:
                        item_record["amount"] = 1
                    if not item_record["price"]:
                        item_price = ShopTurboItemsPrice.objects.filter(
                            item=item, default=True
                        ).first()
                        if item_price:
                            item_record["price"] = item_price.price
                        else:
                            item_record["price"] = 0

                    poi, _ = PurchaseOrdersItem.objects.get_or_create(
                        purchase_order=order, item=item
                    )
                    poi.amount_price = item_record["price"]
                    poi.amount_item = item_record["amount"]
                    poi.save()

            if not order.currency:
                # User language
                lang = request.user.verification.language
                if lang == "ja":
                    order.currency = "JPY"
                else:
                    order.currency = "USD"
                order.save()

            transfer_history.name = csv.name
            progress = 100 * (index_loop + 1) / df.shape[0]
            transfer_history.progress = progress
            transfer_history.save()

            if transfer_history.status == "canceled":
                break

        if transfer_history.status != "canceled":
            transfer_history.status = "completed"
            transfer_history.save()

    except Exception as e:
        traceback.print_exc()
        if lang == "ja":
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="CSV入力エラー: "
                + str(e)
                + " | ヘッダー情報を確認してください。テンプレートと同じフォーマットである必要があります。",
                type="error",
            )
        else:
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="Error CSV input: "
                + str(e)
                + " | Please check your column headers. It should be same as the template.",
                type="error",
            )

    return True


# Download PDF {finish migrate to billing}
@login_or_hubspot_required
def purchase_order_pdf_download(
    request,
    id=None,
    buffer=None,
    preview=None,
    in_html=None,
    send_email=None,
    group_type=TYPE_OBJECT_PURCHASE_ORDER,
    template_select=None,
):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    if not template_select:
        template_select = request.GET.get("template_select", None)
    page_obj = get_page_object(group_type, lang)

    is_preview = request.GET.get("is_preview", False)
    id_field = page_obj["id_field"]
    default_columns = page_obj["default_columns"]
    if id_field and id_field not in default_columns:
        default_columns.insert(0, id_field)
    base_model = page_obj["base_model"]
    field_item_name = page_obj["field_item_name"]

    page_group_type = "purchase_orders"
    # Define the desired height
    desired_height = 50
    desired_width = 90
    app_setting, _ = AppSetting.objects.get_or_create(
        workspace=workspace, app_target=PROCUREMENT_APP_TARGET
    )

    if not template_select:
        template_select = app_setting.purchase_order_pdf_template
        if (
            not template_select
        ):  # If it's still None or empty after checking app_setting
            template_select = "data/purchase_order/purchaseorderPDF-pattern-1.html"

    if id and not is_preview:
        try:
            obj = base_model.objects.get(id=id)
            # base_model Amount Price
            obj = handling_items(obj)

        except:
            module_object_slug = OBJECT_TYPE_TO_SLUG[group_type]
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=group_type
                )
                .order_by("order", "created_at")
                .first()
            )
            if module:
                module_slug = module.slug

                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        raw_html_content = ""

        if not is_valid_uuid(template_select):
            purchase_order_pdf_line_item_table = (
                app_setting.purchase_order_pdf_line_item_table.split(",")
                if app_setting.purchase_order_pdf_line_item_table
                else []
            )
            purchase_order_pdf_header_block = (
                app_setting.purchase_order_pdf_header_block.split(",")
                if app_setting.purchase_order_pdf_header_block
                else []
            )
            purchase_order_pdf_payment_block = (
                app_setting.purchase_order_pdf_payment_block
            )
            purchase_order_pdf_send_from_block = (
                app_setting.purchase_order_pdf_send_from_block
            )
            purchase_order_pdf_send_to_block = (
                app_setting.purchase_order_pdf_send_to_block
            )
            purchase_order_pdf_notes_block = app_setting.purchase_order_pdf_notes_block
            pdf_title = app_setting.purchase_order_pdf_title
            pdf_font_type = app_setting.purchase_order_pdf_font_type
            pdf_bg_color = app_setting.purchase_order_pdf_color
            information_block = (
                app_setting.purchase_order_information_block.split(",")
                if app_setting.purchase_order_information_block
                else []
            )
            send_from_adds_block = (
                app_setting.purchase_order_send_from_adds_block.replace("\n", "<br>")
                if app_setting.purchase_order_send_from_adds_block
                else ""
            )
            send_to_adds_block = (
                app_setting.purchase_order_send_to_adds_block.replace("\n", "<br>")
                if app_setting.purchase_order_send_to_adds_block
                else ""
            )
            sign_1_block = (
                app_setting.purchase_order_sign_1_block.replace("\n", "<br>")
                if app_setting.purchase_order_sign_1_block
                else ""
            )
            sign_2_block = (
                app_setting.purchase_order_sign_2_block.replace("\n", "<br>")
                if app_setting.purchase_order_sign_2_block
                else ""
            )

            purchase_order_pdf_line_item_table_display = (
                app_setting.purchase_order_pdf_line_item_table_display
            )
            purchase_order_pdf_header_block_display = (
                app_setting.purchase_order_pdf_header_block_display
            )
            purchase_order_pdf_payment_block_display = (
                app_setting.purchase_order_pdf_payment_block_display
            )
            purchase_order_pdf_send_from_block_display = (
                app_setting.purchase_order_pdf_send_from_block_display
            )
            purchase_order_pdf_send_to_block_display = (
                app_setting.purchase_order_pdf_send_to_block_display
            )
            purchase_order_pdf_notes_block_display = (
                app_setting.purchase_order_pdf_notes_block_display
            )
            purchase_order_pdf_line_item_table_bg_color = (
                app_setting.purchase_order_pdf_line_item_table_bg_color
            )
            purchase_order_pdf_line_item_table_font_color = (
                app_setting.purchase_order_pdf_line_item_table_font_color
            )
            purchase_order_pdf_information_display = (
                app_setting.purchase_order_information_block_display
            )
            purchase_order_pdf_line_item_table_font_size = (
                app_setting.purchase_order_pdf_line_item_table_font_size
            )

            if purchase_order_pdf_line_item_table_display:
                base_val = purchase_order_pdf_line_item_table
                custom_trans = purchase_order_pdf_line_item_table_display.split(",")
                purchase_order_pdf_line_item_table_display = {}

                for i in range(len(base_val)):
                    if len(custom_trans) <= i:
                        continue

                    if not custom_trans[i]:
                        continue

                    key = base_val[i]
                    if not key:
                        continue

                    purchase_order_pdf_line_item_table_display[key] = custom_trans[i]

            if purchase_order_pdf_header_block_display:
                base_val = purchase_order_pdf_header_block
                custom_trans = purchase_order_pdf_header_block_display.split(",")
                purchase_order_pdf_header_block_display = {}

                for i in range(len(base_val)):
                    if len(custom_trans) <= i:
                        continue

                    if not custom_trans[i]:
                        continue

                    key = base_val[i]
                    if not key:
                        continue

                    purchase_order_pdf_header_block_display[key] = custom_trans[i]

            if purchase_order_pdf_information_display:
                base_val = information_block
                custom_trans = purchase_order_pdf_information_display.split(",")
                purchase_order_pdf_information_display = {}

                for i in range(len(base_val)):
                    if len(custom_trans) <= i:
                        continue

                    if not custom_trans[i]:
                        continue

                    key = base_val[i]
                    if not key:
                        continue

                    purchase_order_pdf_information_display[key] = custom_trans[i]

            if purchase_order_pdf_payment_block_display:
                custom_trans = purchase_order_pdf_payment_block_display
                purchase_order_pdf_payment_block_display = {}
                purchase_order_pdf_payment_block_display[
                    purchase_order_pdf_payment_block
                ] = custom_trans

            if purchase_order_pdf_send_from_block_display:
                custom_trans = purchase_order_pdf_send_from_block_display
                purchase_order_pdf_send_from_block_display = {}
                purchase_order_pdf_send_from_block_display[
                    purchase_order_pdf_send_from_block
                ] = custom_trans

            if purchase_order_pdf_send_to_block_display:
                custom_trans = purchase_order_pdf_send_to_block_display
                purchase_order_pdf_send_to_block_display = {}
                purchase_order_pdf_send_to_block_display[
                    purchase_order_pdf_send_to_block
                ] = custom_trans

            if purchase_order_pdf_notes_block_display:
                custom_trans = purchase_order_pdf_notes_block_display
                purchase_order_pdf_notes_block_display = {}
                purchase_order_pdf_notes_block_display[
                    purchase_order_pdf_notes_block
                ] = custom_trans

            context = {
                "workspace": workspace,
                "is_preview": False,
                "lang": request.LANGUAGE_CODE,
                "pdf_line_item_table": purchase_order_pdf_line_item_table,
                "pdf_header_block": purchase_order_pdf_header_block,
                "pdf_payment_block": purchase_order_pdf_payment_block,
                "pdf_send_from_block": purchase_order_pdf_send_from_block,
                "pdf_send_to_block": purchase_order_pdf_send_to_block,
                "pdf_notes_block": purchase_order_pdf_notes_block,
                "obj_trans": PURCHASE_ORDER_COLUMNS_DISPLAY,
                "custom_model": "PurchaseOrdersNameCustomField",
                "main_model": "PurchaseOrders",
                "logo_url": app_setting.purchase_order_logo_file.url
                if app_setting.purchase_order_logo_file
                else "",
                "stamp_url": app_setting.purchase_order_stamp_file.url
                if app_setting.purchase_order_stamp_file
                else "",
                "pdf_title": pdf_title,
                "pdf_font_type": pdf_font_type,
                "pdf_bg_color": pdf_bg_color,
                "information_block": information_block,
                "send_from_adds_block": send_from_adds_block,
                "send_to_adds_block": send_to_adds_block,
                "sign_1_block": sign_1_block,
                "sign_2_block": sign_2_block,
                "pdf_line_item_table_display": purchase_order_pdf_line_item_table_display,
                "pdf_header_block_display": purchase_order_pdf_header_block_display,
                "pdf_payment_block_display": purchase_order_pdf_payment_block_display,
                "pdf_send_from_block_display": purchase_order_pdf_send_from_block_display,
                "pdf_send_to_block_display": purchase_order_pdf_send_to_block_display,
                "pdf_notes_block_display": purchase_order_pdf_notes_block_display,
                "pdf_line_item_table_bg_color": purchase_order_pdf_line_item_table_bg_color,
                "pdf_line_item_table_font_color": purchase_order_pdf_line_item_table_font_color,
                "pdf_information_display": purchase_order_pdf_information_display,
                "pdf_line_item_table_font_size": purchase_order_pdf_line_item_table_font_size,
            }
            raw_html_content = render_to_string(template_select, context)

        elif is_valid_uuid(template_select):
            template = PdfTemplate.objects.filter(
                workspace=workspace, id=template_select
            ).first()

            if not template:
                template = PdfTemplate.objects.filter(
                    workspace=workspace,
                    master_pdf__object_type=TYPE_OBJECT_PURCHASE_ORDER,
                    master_pdf__path__contains="pattern-1",
                ).first()

            # Store the template ID separately before changing template_select to the path
            template_id = template.id
            template_select = template.master_pdf.path

            context = get_custom_pdf_context(template.id, TYPE_OBJECT_PURCHASE_ORDER)
            context["logo_url"] = (
                app_setting.purchase_order_logo_file.url
                if app_setting.purchase_order_logo_file
                else ""
            )
            context["stamp_url"] = (
                app_setting.purchase_order_stamp_file.url
                if app_setting.purchase_order_stamp_file
                else ""
            )
            context["workspace"] = workspace
            context["lang"] = request.LANGUAGE_CODE
            raw_html_content = render_to_string(template_select, context)
        custom_field_obj = {}
        df_object = DateFormat.objects.filter(
            workspace=workspace, is_workspace_level=True
        ).first()

        # item need to be filled wiht
        # item.id, item.name, item.description, quantity, unit_price, total
        items = []
        custom_relation = obj.purchase_order_field_relations.all()
        for item in obj.purchase_order_object.all().order_by("created_at"):
            item_id = item.item.item_id if item.item else ""
            item_name = item.item.name if item.item else item.item_name
            item_description = item.item.description if item.item else ""
            tax = item.tax_rate if item.tax_rate else 0
            qty = (
                int(item.amount_item)
                if item.amount_item.is_integer()
                else item.amount_item
            )

            unit_price = item.amount_price if item.amount_price else 0
            total_price = float(unit_price) * float(item.amount_item)

            unit_price = (
                "{:,.2f}".format(unit_price)
                if obj.currency.upper() != "JPY"
                else "{:,.0f}".format(unit_price)
            )
            total_price = (
                "{:,.2f}".format(total_price)
                if obj.currency.upper() != "JPY"
                else "{:,.0f}".format(total_price)
            )

            items.append(
                {
                    "id": item_id,
                    "name": item_name,
                    "description": item_description,
                    "quantity": qty,
                    "unit_price": unit_price,
                    "tax": tax,
                    "total": total_price,
                }
            )

        for custom_field in custom_relation:
            field_name = custom_field.field_name.name if custom_field.field_name else ""
            field_id = custom_field.field_name.id if custom_field.field_name else ""
            if custom_field.field_name.type == "choice":
                json_string = custom_field.field_name.choice_value

                try:
                    data = ast.literal_eval(json_string)
                    value_to_label = {item["value"]: item["label"] for item in data}

                    arr = custom_field.value.split(";")
                    choiced_val = []
                    for val in arr:
                        val = value_to_label.get(val, "")
                        if val:
                            choiced_val.append(val)

                    custom_field_obj[field_name] = ", ".join(choiced_val)
                    custom_field_obj[f"{field_id}"] = ", ".join(choiced_val)
                except json.JSONDecodeError as e:
                    print(f"JSON Decode Error: {e}")
                    custom_field_obj[field_name] = ""
                    custom_field_obj[f"{field_id}"] = ""
            elif custom_field.field_name.type in ["date", "date_time"]:
                date_format = df_object.value if df_object else "DD/MM/YYYY"
                date_format = DATE_FORMAT_PARSER[df_object.value]
                if custom_field.field_name.type == "date_time":
                    date_format = f"{date_format} %H:%M"

                if custom_field.value:
                    date_obj = parse_datetime(custom_field.value)
                    date = date_obj.strftime(date_format)
                    if date_format == "%Yx%my%dz":
                        date = (
                            date.replace("x", "年")
                            .replace("y", "月")
                            .replace("z", "日")
                        )
                    custom_field_obj[field_name] = date
                    custom_field_obj[f"{field_id}"] = date
                else:
                    custom_field_obj[field_name] = ""
                    custom_field_obj[f"{field_id}"] = ""
            elif custom_field.field_name.type == "user":
                if not custom_field.value:
                    continue
                user = User.objects.filter(id=custom_field.value).first()
                full_name = ""
                if user:
                    full_name = user.get_full_name() if user else ""
                custom_field_obj[field_name] = full_name
                custom_field_obj[f"{field_id}"] = full_name
                custom_field_obj[f"{field_id}__obj"] = user
            elif custom_field.field_name.type == "bill_objects":
                if not custom_field.value:
                    continue
                bill_objects = Bill.objects.filter(id=custom_field.value).first()
                bill_id = ""
                if bill_objects:
                    bill_id = bill_objects.id_bill
                    bill_id = f"{int(bill_id):04d}"
                custom_field_obj[field_name] = bill_id
                custom_field_obj[f"{field_id}"] = bill_id
                custom_field_obj[f"{field_id}__obj"] = bill_objects
            elif custom_field.field_name.type == "invoice_objects":
                if not custom_field.value:
                    continue
                invoice = Invoice.objects.filter(id=custom_field.value).first()
                inv_id = ""
                if invoice:
                    inv_id = invoice.id_inv
                    inv_id = f"{int(inv_id):04d}"
                custom_field_obj[field_name] = inv_id
                custom_field_obj[f"{field_id}"] = inv_id
                custom_field_obj[f"{field_id}__obj"] = invoice
            elif custom_field.field_name.type == "order_objects":
                if not custom_field.value:
                    continue
                order = ShopTurboOrders.objects.filter(id=custom_field.value).first()
                order_id = ""
                if order:
                    order_id = order.order_id
                    order_id = f"{int(order_id):04d}"
                custom_field_obj[field_name] = order_id
                custom_field_obj[f"{field_id}"] = order_id
                custom_field_obj[f"{field_id}__obj"] = order
            elif custom_field.field_name.type == "contact":
                if not custom_field.value:
                    continue
                contact = Contact.objects.filter(id=custom_field.value).first()
                full_name = ""
                if contact:
                    full_name = f"{contact.last_name} {contact.name}"
                custom_field_obj[field_name] = full_name
                custom_field_obj[f"{field_id}"] = full_name
                custom_field_obj[f"{field_id}__obj"] = contact
            elif custom_field.field_name.type == "company":
                if not custom_field.value:
                    continue
                company = Company.objects.filter(id=custom_field.value).first()
                full_name = ""
                if company:
                    full_name = company.name
                custom_field_obj[field_name] = full_name
                custom_field_obj[f"{field_id}"] = full_name
                custom_field_obj[f"{field_id}__obj"] = company
            elif custom_field.field_name.type == "purchase_order":
                if not custom_field.value:
                    continue
                po = PurchaseOrders.objects.filter(id=custom_field.value).first()
                po_id = ""
                if po:
                    po_id = po.id_po
                    po_id = f"{int(po_id):04d}"
                custom_field_obj[field_name] = po_id
                custom_field_obj[f"{field_id}"] = po_id
                custom_field_obj[f"{field_id}__obj"] = po
            elif custom_field.field_name.type == "subscription":
                if not custom_field.value:
                    continue
                subscription = ShopTurboSubscriptions.objects.filter(
                    id=custom_field.value
                ).first()
                subscription_id = ""
                if subscription:
                    subscription_id = subscription.subscriptions_id
                    subscription_id = f"{int(subscription_id):04d}"
                custom_field_obj[field_name] = subscription_id
                custom_field_obj[f"{field_id}"] = subscription_id
                custom_field_obj[f"{field_id}__obj"] = subscription
            else:
                custom_field_obj[field_name] = custom_field.value
                custom_field_obj[f"{field_id}"] = custom_field.value

        if send_email:
            return async_to_sync(generate_pdf)(
                request,
                template_id if "template_id" in locals() else template_select,
                obj,
                items,
                custom_field_obj,
                workspace=workspace,
                as_http_response=False,
                raw_html_content=raw_html_content,
                setting_type=TYPE_OBJECT_PURCHASE_ORDER,
                footer_template=app_setting.purchase_order_pdf_footer_template,
            )
        return async_to_sync(generate_pdf)(
            request,
            template_id if "template_id" in locals() else template_select,
            obj,
            items,
            custom_field_obj,
            raw_html_content=raw_html_content,
            setting_type=TYPE_OBJECT_PURCHASE_ORDER,
            footer_template=app_setting.purchase_order_pdf_footer_template,
        )
        # Handling Stamp File
        try:
            print("Stamp : ", page_group_type)
            obj.stamp = getattr(app_setting, page_group_type[:-1] + "_stamp_file", None)
            obj.logo = getattr(app_setting, page_group_type[:-1] + "_logo_file", None)

            # Fetch the image from the URL
            response = requests.get(obj.stamp.url)
            image_data = BytesIO(response.content)

            # Open the image using Pillow
            image = Image.open(image_data)

            # Calculate the width to maintain the aspect ratio
            width_percent = desired_height / float(image.size[1])
            desired_width = int((float(image.size[0]) * float(width_percent)))
        except Exception as e:
            print("[Error In Stamp]: ", e)
            obj.stamp = ""
            obj.logo = ""

        # Handling Notes and Currency Symbol
        # obj.currency = CurrencySymbols.get_symbol(obj.currency) if obj.currency else None
        if obj.notes:
            obj.notes_list = obj.notes.splitlines()
            obj.notes = obj.notes_list

        if obj.send_from:
            obj.send_from_list = obj.send_from.splitlines()
            obj.send_from = obj.send_from_list

        # ============================== Handling PDF Title
        obj.pdf_title = page_obj["page_title"]
        if getattr(app_setting, field_item_name + "_pdf_title", None):
            obj.pdf_title = getattr(
                app_setting, page_group_type[:-1] + "_pdf_title", None
            )
        if getattr(app_setting, page_group_type[:-1] + "_is_stamp", None):
            obj.is_stamp = getattr(
                app_setting, page_group_type[:-1] + "_is_stamp", None
            )

        # Handling Export HTML to PDF

        obj_id = getattr(obj, id_field, None)
        try:
            # if getattr(app_setting,page_group_type[:-1] + "_pdf_template",None):
            #     template_path = next_directory + page_obj['pdf_pattern']  + getattr(app_setting,page_group_type[:-1] + "_pdf_template",None) + '.html'
            # else:
            #     template_path = 'data/purchase_order/' + page_obj['pdf_pattern'] + '1.html'
            template_path = "data/purchase_order/purchaseorderPDF-pattern-1.html"
            response = HttpResponse(content_type="application/pdf")
            name_pdf = str(obj_id) + ".pdf"
            encoded_filename = quote(name_pdf.encode("utf-8"))
            response["Content-Disposition"] = "attachment; filename=" + encoded_filename

            template = get_template(template_path)
        except:
            # template_path = 'data/purchase_order/' + page_obj['pdf_pattern']  + '1.html'
            template_path = "data/purchase_order/purchase-orderPDF.html"
            # setattr(app_setting, page_group_type[:-1] + "_pdf_template", re.findall(pattern,template_path)[0])
            # app_setting.save()
            response = HttpResponse(content_type="application/pdf")
            name_pdf = str(obj_id) + ".pdf"
            encoded_filename = quote(name_pdf.encode("utf-8"))
            response["Content-Disposition"] = "attachment; filename=" + encoded_filename

            template = get_template(template_path)

        if getattr(app_setting, page_group_type[:-1] + "_pdf_font_type", None):
            selected_font = getattr(
                app_setting, page_group_type[:-1] + "_pdf_font_type", None
            )
        else:
            selected_font = "static/fonts/NotoSansJP-Medium.ttf"

        if platform.system().lower() == "windows":
            selected_font = "/" + selected_font

        if getattr(app_setting, page_group_type[:-1] + "_pdf_color", None):
            set_color = getattr(app_setting, page_group_type[:-1] + "_pdf_color", None)
        else:
            set_color = None

        html = template.render(
            {
                page_group_type[:-1]: obj,
                "lang": lang,
                "desired_width": desired_width,
                "selected_font": selected_font,
                "set_color": set_color,
            }
        )

        if send_email:
            return obj, html, desired_width

        pisa_status = pisa.CreatePDF(html.encode("UTF-8"), dest=response)

    else:
        is_preview = request.GET.get("is_preview", False)
        is_layout = request.GET.get("is_layout", False)
        template_path = request.GET.get("template_path", "")

        purchase_order_pdf_line_item_table = (
            app_setting.purchase_order_pdf_line_item_table.split(",")
            if app_setting.purchase_order_pdf_line_item_table
            else []
        )
        purchase_order_pdf_header_block = (
            app_setting.purchase_order_pdf_header_block.split(",")
            if app_setting.purchase_order_pdf_header_block
            else []
        )
        purchase_order_pdf_payment_block = app_setting.purchase_order_pdf_payment_block
        purchase_order_pdf_send_from_block = (
            app_setting.purchase_order_pdf_send_from_block
        )
        purchase_order_pdf_send_to_block = app_setting.purchase_order_pdf_send_to_block
        purchase_order_pdf_notes_block = app_setting.purchase_order_pdf_notes_block
        pdf_title = app_setting.purchase_order_pdf_title
        pdf_font_type = app_setting.purchase_order_pdf_font_type
        pdf_bg_color = app_setting.purchase_order_pdf_color
        information_block = (
            app_setting.purchase_order_information_block.split(",")
            if app_setting.purchase_order_information_block
            else []
        )
        send_from_adds_block = (
            app_setting.purchase_order_send_from_adds_block.replace("\n", "<br>")
            if app_setting.purchase_order_send_from_adds_block
            else ""
        )
        send_to_adds_block = (
            app_setting.purchase_order_send_to_adds_block.replace("\n", "<br>")
            if app_setting.purchase_order_send_to_adds_block
            else ""
        )
        sign_1_block = (
            app_setting.purchase_order_sign_1_block.replace("\n", "<br>")
            if app_setting.purchase_order_sign_1_block
            else ""
        )
        sign_2_block = (
            app_setting.purchase_order_sign_2_block.replace("\n", "<br>")
            if app_setting.purchase_order_sign_2_block
            else ""
        )

        purchase_order_pdf_line_item_table_display = (
            app_setting.purchase_order_pdf_line_item_table_display
        )
        purchase_order_pdf_header_block_display = (
            app_setting.purchase_order_pdf_header_block_display
        )
        purchase_order_pdf_payment_block_display = (
            app_setting.purchase_order_pdf_payment_block_display
        )
        purchase_order_pdf_send_from_block_display = (
            app_setting.purchase_order_pdf_send_from_block_display
        )
        purchase_order_pdf_send_to_block_display = (
            app_setting.purchase_order_pdf_send_to_block_display
        )
        purchase_order_pdf_notes_block_display = (
            app_setting.purchase_order_pdf_notes_block_display
        )
        purchase_order_pdf_line_item_table_bg_color = (
            app_setting.purchase_order_pdf_line_item_table_bg_color
        )
        purchase_order_pdf_line_item_table_font_color = (
            app_setting.purchase_order_pdf_line_item_table_font_color
        )
        purchase_order_pdf_information_display = (
            app_setting.purchase_order_information_block_display
        )
        purchase_order_pdf_line_item_table_font_size = (
            app_setting.purchase_order_pdf_line_item_table_font_size
        )

        if purchase_order_pdf_line_item_table_display:
            base_val = purchase_order_pdf_line_item_table
            custom_trans = purchase_order_pdf_line_item_table_display.split(",")
            purchase_order_pdf_line_item_table_display = {}

            for i in range(len(base_val)):
                if len(custom_trans) <= i:
                    continue

                if not custom_trans[i]:
                    continue

                key = base_val[i]
                if not key:
                    continue

                purchase_order_pdf_line_item_table_display[key] = custom_trans[i]

        if purchase_order_pdf_header_block_display:
            base_val = purchase_order_pdf_header_block
            custom_trans = purchase_order_pdf_header_block_display.split(",")
            purchase_order_pdf_header_block_display = {}

            for i in range(len(base_val)):
                if len(custom_trans) <= i:
                    continue

                if not custom_trans[i]:
                    continue

                key = base_val[i]
                if not key:
                    continue

                purchase_order_pdf_header_block_display[key] = custom_trans[i]

        if purchase_order_pdf_information_display:
            base_val = information_block
            custom_trans = purchase_order_pdf_information_display.split(",")
            purchase_order_pdf_information_display = {}

            for i in range(len(base_val)):
                if len(custom_trans) <= i:
                    continue

                if not custom_trans[i]:
                    continue

                key = base_val[i]
                if not key:
                    continue

                purchase_order_pdf_information_display[key] = custom_trans[i]

        if purchase_order_pdf_payment_block_display:
            custom_trans = purchase_order_pdf_payment_block_display
            purchase_order_pdf_payment_block_display = {}
            purchase_order_pdf_payment_block_display[
                purchase_order_pdf_payment_block
            ] = custom_trans

        if purchase_order_pdf_send_from_block_display:
            custom_trans = purchase_order_pdf_send_from_block_display
            purchase_order_pdf_send_from_block_display = {}
            purchase_order_pdf_send_from_block_display[
                purchase_order_pdf_send_from_block
            ] = custom_trans

        if purchase_order_pdf_send_to_block_display:
            custom_trans = purchase_order_pdf_send_to_block_display
            purchase_order_pdf_send_to_block_display = {}
            purchase_order_pdf_send_to_block_display[
                purchase_order_pdf_send_to_block
            ] = custom_trans

        if purchase_order_pdf_notes_block_display:
            custom_trans = purchase_order_pdf_notes_block_display
            purchase_order_pdf_notes_block_display = {}
            purchase_order_pdf_notes_block_display[purchase_order_pdf_notes_block] = (
                custom_trans
            )

        context = {
            "workspace": workspace,
            "is_preview": is_preview,
            "preview_layout": is_layout,
            "lang": request.LANGUAGE_CODE,
            "pdf_line_item_table": purchase_order_pdf_line_item_table,
            "pdf_header_block": purchase_order_pdf_header_block,
            "pdf_payment_block": purchase_order_pdf_payment_block,
            "pdf_send_from_block": purchase_order_pdf_send_from_block,
            "pdf_send_to_block": purchase_order_pdf_send_to_block,
            "pdf_notes_block": purchase_order_pdf_notes_block,
            "obj_trans": PURCHASE_ORDER_COLUMNS_DISPLAY,
            "custom_model": "PurchaseOrdersNameCustomField",
            "main_model": "PurchaseOrders",
            "logo_url": app_setting.purchase_order_logo_file.url
            if app_setting.purchase_order_logo_file
            else "",
            "stamp_url": app_setting.purchase_order_stamp_file.url
            if app_setting.purchase_order_stamp_file
            else "",
            "pdf_title": pdf_title,
            "pdf_font_type": pdf_font_type,
            "pdf_bg_color": pdf_bg_color,
            "information_block": information_block,
            "send_from_adds_block": send_from_adds_block,
            "send_to_adds_block": send_to_adds_block,
            "sign_1_block": sign_1_block,
            "sign_2_block": sign_2_block,
            "pdf_line_item_table_display": purchase_order_pdf_line_item_table_display,
            "pdf_header_block_display": purchase_order_pdf_header_block_display,
            "pdf_payment_block_display": purchase_order_pdf_payment_block_display,
            "pdf_send_from_block_display": purchase_order_pdf_send_from_block_display,
            "pdf_send_to_block_display": purchase_order_pdf_send_to_block_display,
            "pdf_notes_block_display": purchase_order_pdf_notes_block_display,
            "pdf_line_item_table_bg_color": purchase_order_pdf_line_item_table_bg_color,
            "pdf_line_item_table_font_color": purchase_order_pdf_line_item_table_font_color,
            "pdf_line_item_table_font_size": purchase_order_pdf_line_item_table_font_size,
            "pdf_information_display": purchase_order_pdf_information_display,
        }

        # Generate the PDF
        if is_preview:
            setting_type = APP_TARGET_DISPLAY.get(TYPE_OBJECT_PURCHASE_ORDER)
            setting_type_display = ""
            if setting_type:
                if request.LANGUAGE_CODE == "ja":
                    setting_type_display = setting_type.get("ja", "") or ""
                    setting_type_display = f"{setting_type_display} - テンプレート"
                else:
                    setting_type_display = setting_type.get("en", "") or ""
                    setting_type_display = f"{setting_type_display} - Template"
            match = re.search(r"(\d+)\.html$", template_path)
            pdf_pattern = ""
            if match:
                pdf_pattern = int(match.group(1))  # Convert to integer
            else:
                pass
            context["title"] = f"{setting_type_display} - {pdf_pattern}"
            html_content = render_to_string(template_path, context)
            pdf_bytes = async_to_sync(generate_pdf_bytes)(
                html_content, "", app_setting.purchase_order_pdf_footer_template
            )

            # return HttpResponse(html_content) #TODO: debug only, remove when not needed
            response = HttpResponse(pdf_bytes, content_type="application/pdf")
        else:
            pass

        # Set the response headers
        response["Content-Disposition"] = 'inline; filename="order.pdf"'

        return response

    if buffer:
        print("Buffering")
        result = io.BytesIO()
        pisa_status = pisa.CreatePDF(html.encode("UTF-8"), result)
        result.seek(0)
        return result

    if preview:
        if in_html:
            return render(
                request, template_path, {page_group_type[:-1]: obj, "lang": lang}
            )

        if pisa_status.err:
            return HttpResponse("We had some errors <pre>" + html + "</pre>")

        response = HttpResponse(response, content_type="application/pdf")

        # Set the Content-Disposition header to inline to indicate in-browser preview
        response["Content-Disposition"] = 'inline; filename="{0}"'.format(
            page_obj["page_title"] + ".pdf"
        )
        return response
    else:
        return response


@login_or_hubspot_required
def purchase_order_send_mail(request, id):
    workspace = get_workspace(request.user)

    email = request.POST.get("email", "")
    cc_list = request.POST.get("cc_list", "")

    lang = request.LANGUAGE_CODE
    page_obj = get_page_object(TYPE_OBJECT_PURCHASE_ORDER, lang)

    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_PURCHASE_ORDER]
    module = (
        Module.objects.filter(
            workspace=workspace, object_values__contains=TYPE_OBJECT_PURCHASE_ORDER
        )
        .order_by("order", "created_at")
        .first()
    )
    if module:
        module_slug = module.slug

    id_field = page_obj["id_field"]
    page_title = page_obj["page_title"]
    default_columns = page_obj["default_columns"]
    if id_field and id_field not in default_columns:
        default_columns.insert(0, id_field)
    field_item_name = page_obj["field_item_name"]

    obj, html, _ = purchase_order_pdf_download(
        request, id=id, send_email=True, page_group_type=TYPE_OBJECT_PURCHASE_ORDER
    )

    obj_id = getattr(obj, id_field, None)

    customers = ""
    if obj.contact:
        customers = obj.contact.name
        if obj.contact.last_name:
            if lang == "ja":
                customers = obj.contact.last_name + " " + customers
            else:
                customers = customers + " " + obj.contact.last_name
    elif obj.company:
        customers = obj.company.name

    # Pls check this translation
    if lang == "ja":
        mail_subject = f"新しい{page_title}を受け取りました[{obj_id}]"
        message = f"{customers}さま\n{page_title}を添付致しました。ご査収いただきますようお願い致します。"
    else:
        mail_subject = f"You received a new {page_title} [{obj_id}]"
        message = f"Hi {customers}, \nThe attached is your {page_title}. Please take a look and should you have any questions contact us. Thank you."

    if request.POST.get("email_content"):
        message = request.POST.get("email_content")

    from_email = "Sanka <<EMAIL>>"

    try:
        to_email = [email]
        cc_emails = []
        cc_list = cc_list.replace("['", "").replace("']", "")
        if cc_list:
            lists_name = cc_list.split(",")
            for list_name in lists_name:
                cc_emails.append(list_name)
        result = io.BytesIO()
        pisa.CreatePDF(html.encode("UTF-8"), result, encoding="UTF-8")
        result.seek(0)
        result = result.read()

        email = EmailMessage(mail_subject, message, from_email, to_email, cc=cc_emails)

        name_pdf = obj_id + ".pdf"
        email.attach(name_pdf, result)
        try:
            email.send()
        except:
            if lang == "ja":
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="CCメールが送信先のメールと同じです.",
                    type="error",
                )
            else:
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="Email CC same as Send To Email.",
                    type="error",
                )

        # Create Meter Object #
        data = {}
        data["sent_" + field_item_name] = str(obj.id)
        ActionTracker.objects.create(
            workspace=workspace,
            status="success",
            input_data=data,
            output_data=data,
        )

        if lang == "ja":
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="メールは正常に送信されました.",
                type="success",
            )
        else:
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="Email was sent successfully.",
                type="success",
            )

    except Exception as e:
        print("Error at email send", e)

    if module:
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
        )
    return redirect(reverse("main", host="app"))


# =========== Purchase Item


@login_or_hubspot_required
def downloadPDF(request, id):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    purchase = PurchaseOrders.objects.get(id=id, workspace=workspace)
    pois = PurchaseOrdersItem.objects.filter(purchase_order=purchase)
    purchase.currency = CurrencySymbols.get_symbol(purchase.currency)
    if purchase.contact:
        purchase.supplier = purchase.contact.name
        if lang == "ja":
            if purchase.contact.last_name:
                purchase.supplier = purchase.contact.last_name + " " + purchase.supplier
            else:
                purchase.supplier = purchase.supplier
        else:
            if purchase.contact.last_name:
                purchase.supplier = purchase.supplier + " " + purchase.contact.last_name
            else:
                purchase.supplier = purchase.supplier
    else:
        purchase.supplier = purchase.company.name

    template_path = "data/purchase_order/purchase-orderPDF.html"
    response = HttpResponse(content_type="application/pdf")
    name_pdf = str(purchase.id_po) + ".pdf"
    encoded_filename = quote(name_pdf.encode("utf-8"))
    response["Content-Disposition"] = "attachment; filename=" + encoded_filename

    if purchase.notes:
        purchase.notes_list = purchase.notes.splitlines()
        purchase.notes = purchase.notes_list

    if purchase.send_from:
        purchase.send_from_list = purchase.send_from.splitlines()
        purchase.send_from = purchase.send_from_list

    template = get_template(template_path)

    html = template.render(
        {"purchaseorder": purchase, "purchase_order_items": pois, "lang": lang}
    )

    # pisa_status = pisa.CreatePDF(html.encode('UTF-8'), dest=response, link_callback=htmlpdf_link_callback)
    pisa_status = pisa.CreatePDF(html.encode("UTF-8"), dest=response)

    if pisa_status.err:
        return HttpResponse("We had some errors <pre>" + html + "</pre>")

    return response


def custom_field(request, type_=None, obj=None):
    workspace = get_workspace(request.user)
    if type_ == "purchase_order":
        objName = PurchaseOrdersNameCustomField
        objValue = PurchaseOrdersValueCustomField
    else:
        objName = BillNameCustomField
        objValue = BillValueCustomField

    # CustomField
    post_dict = request.POST.keys()
    keys_with_pipes = [key for key in post_dict if "|" in key]
    for key_pipe in keys_with_pipes:
        div_name = key_pipe
        data = request.POST.get(div_name, None)
        key_pipe = key_pipe.split("|")
        custom_field_name_id = key_pipe[-1]
        name_custom_field = objName.objects.get(id=custom_field_name_id)

        if type_ == "purchase_order":
            CustomFieldValue, _ = objValue.objects.get_or_create(
                field_name=name_custom_field, purchaseorders=obj
            )
        elif type_ == "purchase_item":
            CustomFieldValue, _ = objValue.objects.get_or_create(
                field_name=name_custom_field, purchaseitems=obj
            )
        else:
            CustomFieldValue, _ = objValue.objects.get_or_create(
                field_name=name_custom_field, bill=obj
            )

        if key_pipe[0] == "status_upload":
            if data == "deleted":
                image_file = CustomFieldValue.file
                if image_file:
                    if "nyc3.digitaloceanspaces.com" in image_file.url:
                        folder = "billings-customfield-files"
                        file_name = image_file.url.split("/")[-1]
                        file_name = f"{folder}/{file_name}"
                        sc_file = image_file
                        S3_CLIENT.delete_object(
                            Bucket=AWS_STORAGE_BUCKET_NAME,
                            Key=f"{AWS_LOCATION}/{sc_file.file}",
                        )
                    CustomFieldValue.file = None
                    CustomFieldValue.save(
                        log_data={"user": request.user, "workspace": workspace}
                    )
        elif key_pipe[0] == "choice":
            if name_custom_field.multiple_select:
                data = request.POST.getlist(div_name, None)
                data = ";".join(filter(None, data))
            CustomFieldValue.value = data
            CustomFieldValue.save(
                log_data={"user": request.user, "workspace": workspace}
            )
        else:
            CustomFieldValue.value = data
            CustomFieldValue.save(
                log_data={"user": request.user, "workspace": workspace}
            )

    file_dict = request.FILES.keys()
    file_keys_with_pipes = [key for key in file_dict if "|" in key]
    for file_keys in file_keys_with_pipes:
        key_pipe = file_keys.split("|")
        custom_field_name_id = key_pipe[3]

        file = request.FILES.get(file_keys, None)
        name_custom_field = objName.objects.get(id=custom_field_name_id)
        if type_ == "purchase_order":
            CustomFieldValue, _ = objValue.objects.get_or_create(
                field_name=name_custom_field, purchaseorders=obj
            )
        else:
            CustomFieldValue, _ = objValue.objects.get_or_create(
                field_name=name_custom_field, bill=obj
            )
        CustomFieldValue.file = file
        CustomFieldValue.save(log_data={"user": request.user, "workspace": workspace})

    # EndOfCustomfield
    return


@login_or_hubspot_required
def purchase(request, object_type=TYPE_OBJECT_PURCHASE_ORDER):
    lang = request.LANGUAGE_CODE
    manage_url = ""

    app_setting, _ = AppSetting.objects.get_or_create(
        workspace=get_workspace(request.user), app_target=PROCUREMENT_APP_TARGET
    )
    workspace = get_workspace(request.user)

    page_obj = get_page_object(object_type, lang)

    id_field = page_obj["id_field"]
    page_title = page_obj["page_title"]
    default_columns = page_obj["default_columns"]
    if id_field and id_field not in default_columns:
        default_columns.insert(0, id_field)
    base_model = page_obj["base_model"]
    row_detail_url = page_obj["row_detail_url"]
    custom_model = page_obj["custom_model"]

    page_type = page_obj["page_type"]
    item_model = page_obj["item_model"]
    default_columns = page_obj["default_columns"]
    search_fields = page_obj["search_fields"]
    custom_value_model = page_obj["custom_value_model"]
    field_item_name = page_obj["field_item_name"]
    custom_value_relation = page_obj["custom_value_relation"]
    additional_filter_fields = page_obj["additional_filter_fields"]
    choice_status = None

    choice_status = CustomProperty.objects.filter(
        workspace=workspace, model=base_model._meta.db_table
    )
    if choice_status:
        choice_ = []
        for choice in choice_status:
            if choice.value:
                choice_ += ast.literal_eval(choice.value)
        choice_status = choice_
    else:
        choice_status = None

    search_setting_field_name = None
    if object_type == TYPE_OBJECT_PURCHASE_ORDER:
        manage_url = "purchase_manage"
        search_setting_field_name = "search_setting_purchase_order"

    if not app_setting.search_setting_purchase_order:
        app_setting.search_setting_purchase_order = (
            "supplier__company__name,supplier__contact__name,send_from"
        )
        app_setting.save()

    isopen = None
    contact_id = None
    company_id = None
    if id:
        isopen = id
    if request.GET.get("id"):
        target = request.GET.get("target", None)
        if not target or target == "purchaseorder":
            isopen = request.GET.get("id")
        else:
            if target == "company":
                company_id = request.GET.get("id")
            if target == "contacts":
                contact_id = request.GET.get("id")

    if request.method == "GET":
        permission = get_permission(object_type=object_type, user=request.user)
        if not permission:
            permission = DEFAULT_PERMISSION

        if permission == "hide":
            context = {
                "permission": permission,
                "page_title": page_title,
            }
            return render(request, "data/purchase_order/purchase-order.html", context)

        view_id = request.GET.get("view_id", None)

        view = None
        if not View.objects.filter(workspace=workspace, target=object_type).exists():
            print("!!! Not exist any views, let create one.")
            view = View.objects.create(
                workspace=workspace,
                title="main",
                target=object_type,
            )
            view_filter = ViewFilter.objects.create(
                view=view,
                column=default_columns,
                view_type="list",
            )

        views = get_ordered_views(
            workspace=workspace, object_target=object_type, user=request.user
        )
        try:
            if view_id:
                view = View.objects.filter(id=view_id).first()
                if not view or (
                    view.is_private and (not request.user or view.user != request.user)
                ):
                    view = View.objects.filter(
                        workspace=workspace,
                        title="main",
                        target=object_type,
                    ).first()
                    view_id = view.id
            else:
                view = View.objects.filter(
                    workspace=workspace, title="main", target=object_type
                ).first()
            view_id = view.id
        except Exception as e:
            print(f"ERROR === procurement.py -- 2650: {e}")

        if not view:
            view = View.objects.filter(title="main", target=object_type).first()

        view_filter = view.viewfilter_set.first()

        # Adjusting for suppliers__name if showing in column
        if "suppliers__name" in view_filter.column:
            print("View filter column: ", view_filter.column)
            view_filter.column = view_filter.column.replace(
                "suppliers__name", "supplier"
            )
            view_filter.save()

        filter_condition = Q(workspace=workspace)

        advance_search = AdvanceSearchFilter.objects.filter(
            workspace=workspace, object_type=object_type, type="default"
        ).first()
        if not advance_search:
            advance_search = None

        if advance_search:
            if advance_search.search_settings:
                setattr(
                    app_setting,
                    search_setting_field_name,
                    advance_search.search_settings,
                )
                app_setting.save()
        print("search settings:", getattr(app_setting, search_setting_field_name))

        # Handle status filter for archived items
        status = request.GET.get("status")
        if status == "archived":
            filter_condition &= Q(usage_status="archived")
        else:
            # By default, show only active items
            filter_condition &= Q(usage_status="active")
        search_q = request.GET.get("q")
        search_filters = Q()
        if search_q and search_setting_field_name:
            search_setting_values = getattr(app_setting, search_setting_field_name)
            if search_setting_values:
                search_fields = search_setting_values.split(",")
                for s_field in search_fields:
                    search_filters |= apply_search_setting(
                        object_type, view_filter, s_field, search_q, force=True
                    )

            try:
                int(search_q)
                filter_condition &= search_filters | Q(**{id_field: search_q.lower()})
            except:
                filter_condition &= search_filters

        print("The filter: ", filter_condition)

        if advance_search:
            if advance_search.is_active:
                advance_search_filter = advance_search.search_filter
                advance_search_filter_status = advance_search.search_filter_status
                if advance_search_filter_status:
                    advance_search_filter = {
                        k: v
                        for k, v in advance_search_filter.items()
                        if v["value"] != ""
                        and advance_search_filter_status.get(k) != "False"
                    }
                else:
                    advance_search_filter = {
                        k: v
                        for k, v in advance_search_filter.items()
                        if v["value"] != ""
                    }
                try:
                    if (
                        advance_search_filter.get("usage_status", {}).get("value")
                        == "all"
                    ):
                        del advance_search_filter["usage_status"]
                except KeyError:
                    pass

                view_filter.filter_value = advance_search_filter

        filter_condition = build_view_filter(
            filter_condition,
            view_filter,
            object_type,
            force_filter_list=additional_filter_fields,
        )

        items = []

        try:
            # Collecting no id of item to obtain its id
            item_no_id = base_model.objects.filter(
                **{id_field + "__isnull": True, "workspace": workspace}
            )
            if item_no_id:
                for item in item_no_id:
                    item.save()

            items = base_model.objects.filter(filter_condition)

        except Exception as e:
            print(f"ERROR === procurement.py -- 3204: {e}")
            return HttpResponse(status=500)

        try:
            if object_type == TYPE_OBJECT_PURCHASE_ORDER:
                module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_PURCHASE_ORDER]
                module = (
                    Module.objects.filter(
                        workspace=workspace,
                        object_values__contains=TYPE_OBJECT_PURCHASE_ORDER,
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                module_slug = None
                if module.slug:
                    module_slug = module.slug
                if view_filter.sort_order_by:
                    order_method = view_filter.sort_order_method
                    order_by = view_filter.sort_order_by

                    if is_valid_uuid(order_by):
                        field_name = custom_model.objects.filter(id=order_by).first()
                        if field_name:
                            custom_value_subquery = custom_value_model.objects.filter(
                                **{
                                    custom_value_relation: OuterRef("pk"),
                                    "field_name": field_name,
                                }
                            )

                            if field_name.type in ["date", "date_time"]:
                                custom_value_subquery = custom_value_subquery.values(
                                    "value_time"
                                )[:1]
                            else:
                                custom_value_subquery = custom_value_subquery.values(
                                    "value"
                                )[:1]

                            items = items.annotate(
                                custom_value=Subquery(custom_value_subquery)
                            )

                            if field_name.type in ["date", "date_time"]:
                                items = items.annotate(
                                    null_field=Case(
                                        When(custom_value__isnull=True, then=Value(1)),
                                        default=Value(0),
                                        output_field=BooleanField(),
                                    )
                                )
                            else:
                                items = items.annotate(
                                    null_field=Case(
                                        When(**{"custom_value": ""}, then=Value(1)),
                                        default=Value(0),
                                        output_field=BooleanField(),
                                    )
                                )

                            if order_method == "asc":
                                items = items.order_by("null_field", "custom_value")
                            else:
                                items = items.order_by("null_field", "-custom_value")

                    else:
                        items = items.annotate(
                            null_field=Case(
                                When(**{f"{order_by}__isnull": True}, then=Value(1)),
                                default=Value(0),
                                output_field=BooleanField(),
                            )
                        )

                        if order_method == "asc":
                            items = items.order_by("null_field", order_by)
                        else:
                            items = items.order_by("null_field", "-" + order_by)
                else:
                    items = items.order_by(
                        f"-{id_field}" if id_field else "-created_at"
                    )
            else:
                items = items.order_by(f"-{id_field}" if id_field else "-created_at")
        except Exception as e:
            print("===== Debug Error at Procurement Items =====", e)
            items = items.order_by(f"-{id_field}" if id_field else "-created_at")

        # try:
        #     show_per_page = int(request.GET.get('show_per_page', 30))
        # except:
        #     show_per_page = 30

        pagination_number = view_filter.pagination
        if not pagination_number:
            pagination_number = 20
        show_per_page = pagination_number

        paginator_item_begin = 1
        paginator_item_end = show_per_page
        page_content = None
        paginator = None

        page = None
        page = request.GET.get("page")

        paginator = Paginator(items, show_per_page)
        total = 0
        more_pagination = False
        if items:
            total = len(items)
            if page:
                page_content = paginator.page(int(page))
                items = page_content.object_list
                paginator_item_begin = (show_per_page * int(page)) - (show_per_page - 1)
                paginator_item_end = show_per_page * int(page)
                more_pagination = page_content.has_next()

            else:
                page_content = paginator.page(1)
                items = page_content.object_list
                paginator_item_begin = show_per_page * 1 - (show_per_page - 1)
                paginator_item_end = show_per_page * 1
                more_pagination = page_content.has_next()

        module_slug = None
        module_object_slug = None
        if object_type in [TYPE_OBJECT_BILL]:
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_BILL]
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_BILL
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = None
            if "json_response" in request.GET:
                res = []
                for item in items:
                    res.append(
                        {
                            "id": str(item.id),
                            "text": f"#{getattr(item, id_field)} - {getattr(item, 'title')}",
                        }
                    )

                context = {"results": res, "pagination": {"more": more_pagination}}
                return JsonResponse(context)

        if object_type == TYPE_OBJECT_PURCHASE_ORDER:
            property_sets = PropertySet.objects.filter(
                workspace=workspace, target=TYPE_OBJECT_PURCHASE_ORDER
            ).order_by("created_at")

        context = {
            "total": total,
            "manage_url": manage_url,
            "paginator": paginator,
            "paginator_item_begin": paginator_item_begin,
            "paginator_item_end": paginator_item_end,
            "page_content": page_content,
            "page_title": page_title,
            "page_type": page_type,
            "items": items,
            "views": views,
            "current_view": view,
            "view_filter": view_filter,
            "app_setting": app_setting,
            "choice_status": choice_status,
            "search_q": search_q,
            "view_id": view.id,
            "view": view,
            "isopen": isopen,
            "set_id": request.GET.get("set_id", None),
            "contact_id": contact_id,
            "company_id": company_id,
            "permission": permission,
            "tab": request.GET.get("tab"),
            "property_sets": property_sets,
            "module_slug": module_slug,
            "module_object_slug": module_object_slug,
            "row_detail_url": row_detail_url,
            "side_drawer": request.GET.get("sidedrawer", ""),
            "status": request.GET.get("status"),
            "advance_search": advance_search,
        }
        return render(request, "data/purchase_order/purchase-order.html", context)

    elif request.method == "POST":
        item_ids = request.POST.getlist("items")
        view_id = request.POST.get("view_id")
        module_slug = request.POST.get("module", "")

        module_object_slug = OBJECT_TYPE_TO_SLUG[object_type]
        module = Module.objects.filter(
            workspace=workspace, object_values__contains=object_type
        ).order_by("order", "created_at")

        if module_slug:
            module = module.filter(slug=module_slug)
        if module:
            module = module.first()
            module_slug = module.slug

        try:
            if "flag_all" in request.POST:
                view_id = request.POST.get("view_id")
                view = View.objects.filter(id=view_id).first()
                if not view:
                    view = View.objects.filter(
                        title="main", target=object_type, workspace=workspace
                    ).first()
                view_filter = view.viewfilter_set.first()
                filter_conditions = Q(workspace=workspace)

                # Handle status filter for archived items
                status = request.GET.get("status")
                if status == "archived":
                    filter_conditions &= Q(usage_status="archived")
                else:
                    # By default, show only active items
                    filter_conditions &= Q(usage_status="active")
                filter_conditions = build_view_filter(
                    filter_conditions,
                    view_filter,
                    object_type,
                    force_filter_list=additional_filter_fields,
                )
                objects = base_model.objects.filter(filter_conditions)
                item_ids = objects.values_list("id", flat=True)
            else:
                list_objects = request.POST.getlist("items")
                objects = base_model.objects.filter(id__in=list_objects)
                item_ids = objects.values_list("id", flat=True)

            if "bulk_archive" in request.POST:
                base_model.objects.filter(id__in=item_ids).update(
                    usage_status="archived"
                )

            elif "bulk_activate" in request.POST:
                sync_usage(workspace, MODELS_TO_STORAGE_USAGE[base_model])
                available_entries = get_workspace_available_storage(
                    workspace, MODELS_TO_STORAGE_USAGE[base_model]
                )
                if available_entries is None or available_entries > 0:
                    base_model.objects.filter(
                        id__in=item_ids[:available_entries]
                    ).update(usage_status="active")
                    sync_usage(workspace, MODELS_TO_STORAGE_USAGE[base_model])

            elif "bulk_delete" in request.POST:
                base_model.objects.filter(id__in=item_ids).delete()

            elif "bulk_duplicate" in request.POST:
                sync_usage(workspace, MODELS_TO_STORAGE_USAGE[base_model])
                available_entries = get_workspace_available_storage(
                    workspace, MODELS_TO_STORAGE_USAGE[base_model]
                )
                if available_entries is None or available_entries > 0:
                    objects = base_model.objects.filter(
                        id__in=item_ids[:available_entries]
                    )
                    for obj in objects:
                        customFields = custom_value_model.objects.filter(
                            **{custom_value_relation: obj}
                        )
                        if object_type == TYPE_OBJECT_PURCHASE_ORDER:
                            obj_item = item_model.objects.filter(
                                **{field_item_name: obj}
                            ).order_by("created_at")

                        obj.id = None
                        setattr(obj, id_field, None)
                        obj.created_at = timezone.now()
                        obj.save()

                        for field in customFields:
                            field.id = None
                            setattr(field, custom_value_relation, obj)
                            field.save()

                            # custom_value_model.objects.create(**{custom_value_relation:obj, 'value':field.value,'field_name':field.field_name})

                        if object_type == TYPE_OBJECT_PURCHASE_ORDER:
                            if obj_item:
                                for item_ in obj_item:
                                    item_.id = None
                                    item_.created_at = timezone.now()
                                    setattr(item_, "purchase_order", obj)
                                    item_.save()

                    sync_usage(workspace, MODELS_TO_STORAGE_USAGE[base_model])

        except Exception as e:
            traceback.print_exc()
            print(f"ERROR === procurement.py --2719: {e}")

        if request.POST.get("query"):
            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?view_id={view_id}"
                    + "&"
                    + request.POST.get("query")
                )
        if module:
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?view_id={view_id}"
            )
        return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def csv_header_extractor(request):
    workspace = get_workspace(request.user)
    if request.method == "POST":
        object_type = request.POST.get("object_type")

        csv = request.FILES.get("csv_upload", False)
        if not csv:
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="Failed to retrieve the CSV file.",
                type="error",
            )
            return HttpResponse("")

        with csv.open() as f:
            read_data = f.read()
            result = chardet.detect(read_data)
            encoding = result["encoding"]
            # #Reset Pointer
            f.seek(0)
            if encoding.lower() == "shift_jis":
                content = f.read()
                decoded_content = content.decode("shift_jis")
                string_data = StringIO(decoded_content)
                df = pd.read_csv(string_data, sep=",", encoding="shift_jis", dtype=str)
            else:
                df = pd.read_csv(f, dtype=str)
            df = df.dropna(axis=1, how="all")
            df = df.dropna(axis=0, how="all")
            df = df.reset_index(drop=True)

        header_list = df.columns.tolist()

        sanka_columns = []
        if object_type == TYPE_OBJECT_PURCHASE_ORDER:
            sanka_columns = [
                "id_po",
                "status",
                "currency",
                "purchaseitem_id",
                "number_of_items",
                "price_of_items",
                "total_price",
                "total_price_without_tax",
                "date",
                "supplier_contact",
                "supplier_company",
            ]
            name_customfield = (
                PurchaseOrdersNameCustomField.objects.filter(
                    workspace=workspace, name__isnull=False
                )
                .exclude(type__in=["image", "user", "formula"])
                .values_list("name", flat=True)
            )
        else:
            return HttpResponse("")

        sanka_columns.extend(name_customfield)

        context = {
            "header_list": header_list,
            "sanka_columns": sanka_columns,
            "mapping_type": object_type,
        }
        return render(request, "data/purchase_order/csv-import-mapping.html", context)

    return HttpResponse(200)


@login_or_hubspot_required
def table_mapping_url(request):
    if request.method == "POST":
        mapping_type = request.POST.get("mapping_type")
        header_list = request.POST.get("header_list")
        sanka_columns = request.POST.get("sanka_columns")
        mapping_type = request.POST.get("mapping_type", None)

        context = {
            "header_list": ast.literal_eval(header_list),
            "sanka_columns": ast.literal_eval(sanka_columns),
            "mapping_type": mapping_type,
            "object_type": mapping_type,
        }

        return render(request, "data/purchase_order/mapping-table.html", context)

    return HttpResponse(200)


def handling_items(obj):
    """
    This function handles the calculation of total prices and tax amounts for purchase orders.
    It takes a purchase order object as input and processes its associated items to:
    1. Calculate total price with and without tax for each line item
    2. Handle tax inclusive vs exclusive calculations
    3. Aggregate totals across all items
    4. Update the purchase order object with calculated totals

    Args:
        obj: A PurchaseOrder object to process

    Returns:
        The updated PurchaseOrder object with calculated totals
    """
    type_obj = str(obj)

    if "PurchaseOrder" in type_obj:
        type_obj = PROCUREMENT_APP_SETTING_PURCHASE_ORDER
    else:
        return
    total_price = 0.0
    total_price_without_tax = 0.0
    obj_items = []
    obj_amounts = []
    obj_amount_items = []
    obj_tax_lists = []
    obj_total_price_without_tax = []
    obj_total_price = []

    # Handling Amount Price
    if type_obj == PROCUREMENT_APP_SETTING_PURCHASE_ORDER:
        obj = PurchaseOrders.objects.get(id=str(obj.id))
        objs_item = PurchaseOrdersItem.objects.filter(purchase_order=obj).order_by(
            "created_at"
        )
    try:
        obj.tax_inclusive = None
        obj.discount = None
        if objs_item:
            for obj_item in objs_item:
                if obj_item.item:
                    obj_items.append(obj_item.item.name)
                else:
                    obj_items.append(obj_item.item_name)
                obj_amounts.append(obj_item.amount_price)
                obj_amount_items.append(obj_item.amount_item)
                obj_tax_lists.append(obj_item.tax_rate)
                tax_item_rate = obj_item.tax_rate
                if not tax_item_rate:
                    tax_item_rate = "0"
                if obj.tax_inclusive:
                    obj_item.total_price_without_tax = (
                        float(obj_item.amount_price)
                        * float(obj_item.amount_item)
                        / (1 + (float(tax_item_rate) / 100.0))
                    )
                    obj_item.total_price = float(obj_item.amount_price) * float(
                        obj_item.amount_item
                    )
                else:
                    if tax_item_rate != "0" and tax_item_rate != "None":
                        if obj_item.amount_price and obj_item.amount_item:
                            obj_item.total_price_without_tax = float(
                                obj_item.amount_price
                            ) * float(obj_item.amount_item)
                        else:
                            obj_item.total_price_without_tax = 0
                        obj_item.total_price = (
                            float(obj_item.amount_price)
                            * (float(tax_item_rate) / 100.0)
                            + float(obj_item.amount_price)
                        ) * float(obj_item.amount_item)
                    else:
                        if (
                            obj_item.amount_price
                            and obj_item.amount_item
                            and obj_item.amount_price != "None"
                            and obj_item.amount_item != "None"
                        ):
                            obj_item.total_price_without_tax = float(
                                obj_item.amount_price
                            ) * float(obj_item.amount_item)
                            obj_item.total_price = float(obj_item.amount_price) * float(
                                obj_item.amount_item
                            )
                        else:
                            obj_item.total_price_without_tax = 0
                            obj_item.total_price = 0
                obj_total_price_without_tax.append(obj_item.total_price_without_tax)
                obj_total_price.append(obj_item.total_price)
                obj_item.save()
        else:
            obj_amounts = None
            try:
                if obj.item:
                    obj_items = ast.literal_eval(obj.item)
                try:
                    if obj.amount_item:
                        obj_amount_items = ast.literal_eval(obj.amount_item)
                except:
                    obj_amount_items = ["1"] * len(obj_items)
                try:
                    if obj.tax_list:
                        obj_tax_lists = ast.literal_eval(obj.tax_list)
                        if isinstance(obj_tax_lists, list):
                            obj_tax_lists = [str(obj.tax_list)]
                    else:
                        obj_tax_lists = ["0"] * len(obj_items)
                except:
                    obj_tax_lists = ["0"] * len(obj_items)

                if isinstance(obj_amounts, list):
                    obj_items_ = []
                    # Creating a new Estimate Item for obj doesn't have estimate item
                    for item, amount_item, amount_price, tax_item_rate in zip(
                        obj_items, obj_amount_items, obj_amounts, obj_tax_lists
                    ):
                        if not tax_item_rate:
                            tax_item_rate = "0"

                        if type_obj == PROCUREMENT_APP_SETTING_PURCHASE_ORDER:
                            obj_item = PurchaseOrdersItem.objects.create(estimate=obj)

                        # Haegwan deleted - migration to char field
                        # obj_item.item_name = item
                        if is_valid_uuid(item):
                            item = ShopTurboItems.objects.filter(id=item)
                            if item:
                                obj_item.item = item.last()
                                obj_items_.append(item.last().name)
                        else:
                            obj_item.item_name = item
                            obj_items_.append(item)
                        obj_item.amount_price = amount_price
                        obj_item.amount_item = amount_item

                        if obj.tax_inclusive:
                            obj_item.total_price_without_tax = (
                                float(amount_price)
                                * float(amount_item)
                                / (1 + (float(tax_item_rate) / 100.0))
                            )
                            obj_item.total_price = float(amount_price) * float(
                                amount_item
                            )
                        else:
                            if tax_item_rate != "0":
                                obj_item.total_price_without_tax = float(
                                    amount_price
                                ) * float(amount_item)
                                obj_item.total_price = (
                                    float(amount_price) * (float(tax_item_rate) / 100.0)
                                    + float(amount_price)
                                ) * float(amount_item)
                            else:
                                obj_item.total_price_without_tax = float(
                                    amount_price
                                ) * float(amount_item)
                                obj_item.total_price = float(amount_price) * float(
                                    amount_item
                                )

                        obj_item.tax_rate = tax_item_rate
                        obj_total_price_without_tax.append(
                            obj_item.total_price_without_tax
                        )
                        obj_total_price.append(obj_item.total_price)
                        obj_item.save()
                    obj_items = obj_items_
            except:
                pass

        if isinstance(obj_amounts, list):
            # Handling Amount of Tax Price
            for price, price_without_tax in zip(
                obj_total_price, obj_total_price_without_tax
            ):
                total_price_without_tax += float(price_without_tax)
                total_price += float(price)

            if obj.tax_rate and obj.tax_option != "item_based_tax":
                total_price = float(total_price_without_tax) * (
                    (100.0 + float(obj.tax_rate)) / 100.0
                )

            if obj.tax_option == "non_tax":
                total_price = total_price_without_tax

            obj.total_price_without_tax = total_price_without_tax
            obj.total_price = total_price
            if obj.tax_option == "item_based_tax":
                # Here IF POST TAX DISCOUNT
                obj.amount_total = obj.total_price
                # ELSE PRE TAX DISCOUNT
            else:
                obj.amount_total = obj.total_price_without_tax
            obj.amount_tax = obj.total_price - obj.total_price_without_tax
            obj.save()

            obj.total_price_without_tax = total_price_without_tax
            try:
                obj.tax_val_lists = ["0"] * len(obj_amounts)
                obj.totals = []
                index = 0
                for price, amount, tax in zip(
                    obj_amounts, obj_amount_items, obj_tax_lists
                ):
                    temp_price = (
                        price  # For handling not set price item or blank as the price
                    )
                    if tax is None:
                        tax = 0.0
                    if price == "":
                        temp_price = ""
                        price = 0.0
                    if price is None:
                        temp_price = None
                        price = 0.0
                    if obj.tax_option == "item_based_tax":
                        if obj.tax_inclusive:
                            obj.tax_val_lists[index] = (
                                float(price) * float(amount)
                            ) - (
                                float(price)
                                * float(amount)
                                / (1 + (float(tax) / 100.0))
                            )
                            obj_amounts[index] = float(price) / (
                                1 + (float(tax) / 100.0)
                            )
                            obj.totals.append(float(price) * float(amount))
                        else:
                            obj.tax_val_lists[index] = (
                                float(price) * float(amount)
                            ) * (float(tax) / 100.0)
                            obj.totals = obj_total_price_without_tax
                    else:
                        obj.totals.append(float(price) * float(amount))
                    if temp_price == "":
                        obj_amounts[index] = temp_price
                    index += 1

                obj.show = list(
                    zip(
                        obj_items,
                        obj_amount_items,
                        obj_amounts,
                        obj.tax_val_lists,
                        obj_tax_lists,
                        obj.totals,
                    )
                )
                obj.total_without_discount = obj.total_price_without_tax
                obj.total_price_without_discount = total_price

                # If There any discount:
                # To-DO add option for POST TAX DISCOUNT and PRE TAX DISCOUNT
                if obj.discount:
                    discount = 0.0
                    if obj.discount_option == "%":
                        # Here IF PRE TAX DISCOUNT
                        if obj.discount_tax_option == "pre_tax":
                            discount = obj.total_price_without_tax * (
                                float(obj.discount) / 100.0
                            )
                        # ELSE POST TAX DISCOUNT
                        else:
                            discount = obj.total_price * (float(obj.discount) / 100.0)

                    else:
                        discount = float(obj.discount)

                    # HERE IF PRE TAX DISCOUNT
                    # # ================== To Count discount each item (Pre Tax Discount)
                    if obj.discount_tax_option == "pre_tax":
                        total_price = 0.0
                        discount_item_price = 0.0
                        for tax, price_without_tax in zip(
                            obj_tax_lists, obj_total_price_without_tax
                        ):
                            discount_item_price = (
                                float(price_without_tax)
                                / obj.total_price_without_tax
                                * discount
                            )
                            discount_final_price = (
                                float(price_without_tax) - discount_item_price
                            )
                            if obj.tax_rate and obj.tax_option != "item_based_tax":
                                total_price = total_price + (
                                    discount_final_price
                                    * (1 + (float(obj.tax_rate)) / 100.0)
                                )
                            elif obj.tax_option == "item_based_tax":
                                total_price = total_price + (
                                    discount_final_price * (1 + (float(tax)) / 100.0)
                                )
                            else:
                                total_price += discount_final_price
                        obj.total_without_discount = obj.total_price_without_tax
                        obj.total_price_without_discount = total_price + discount
                        total_price_without_tax = obj.total_price_without_tax - discount
                    else:  # Post tax
                        obj.total_without_discount = obj.total_price_without_tax
                        total_price_without_tax = obj.total_price_without_tax - discount
                        obj.total_price_without_discount = total_price
                        total_price = obj.total_price - discount

                    obj.total_price_without_tax = total_price_without_tax
                    obj.total_price = total_price
                    obj.discount_value = discount
                    # print("Discount Saved")

                if obj.total_price_without_tax < 0:
                    obj.total_price_without_tax = 0
                if obj.total_price < 0:
                    obj.total_price = 0
                obj.save()
            except Exception as e:
                traceback.print_exc()
                print("Error: ", e)
    except Exception as e:
        traceback.print_exc()
        print("[Error Processing Objects]: ", e)
        print(obj.id)

    return obj


# @login_or_hubspot_required


def purchase_order_to_inventory_form(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")
        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)
        if postfix:
            po_ids = request.GET.getlist("po_ids" + postfix, None)
        else:
            po_ids = request.GET.get("po_ids")

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        is_object_action = True
        all_po = False
        update_inventory = False
        inventory_ids = []
        select_previous_po = None
        selected_location = None
        if postfix:
            is_object_action = False
            if node and node.input_data:
                if "select_previous_po" in node.input_data:
                    select_previous_po = node.input_data["select_previous_po"]
                elif "all_po" in node.input_data:
                    all_po = node.input_data["all_po"]
                elif "po_ids" in node.input_data:
                    po_ids = node.input_data["po_ids"]
                if "update_inventory" in node.input_data:
                    update_inventory = node.input_data["update_inventory"]
                    if "inventory_ids" in node.input_data:
                        inventory_ids = node.input_data["inventory_ids"]
                elif "selected_location" in node.input_data:
                    selected_location = node.input_data["selected_location"]
                    try:
                        selected_location = InventoryWarehouse.objects.get(
                            workspace=workspace, id=selected_location
                        )
                    except InventoryWarehouse.DoesNotExist:
                        selected_location = None

        pos = (
            PurchaseOrders.objects.filter(
                workspace=workspace,
                usage_status="active",
                purchase_order_object__isnull=False,
            )
            .order_by("-created_at")
            .distinct()
        )
        inventory_list = ShopTurboInventory.objects.filter(
            workspace=workspace, status="active"
        ).order_by("created_at")

        context = {
            "action_index": action_index,
            "is_object_action": is_object_action,
            "action_slug": action_slug,
            "purchase_orders": pos,
            "inventory_list": inventory_list,
            "select_previous_po": select_previous_po,
            "all_po": all_po,
            "po_ids": po_ids,
            "object_ids": po_ids,
            "update_inventory": update_inventory,
            "selected_inventory": inventory_ids,
            "selected_location": selected_location,
            "is_record_action": request.GET.get("is_record_action"),
        }
        return render(
            request,
            "data/purchase_order/purchase-order-to-inventory-items-form.html",
            context,
        )

    # Workflow related input
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")
    node = None
    page = request.POST.get("page", 1)
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)
    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)
    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    object_source = request.POST.get("object_source" + postfix)
    object_target = request.POST.get("object_target" + postfix)

    action_slug = request.POST.get("action" + postfix)
    is_object_action = request.POST.get("is_object_action", None)
    is_record_action = request.POST.get("is_record_action", None)
    print(request.POST)

    select_previous_po = request.POST.get("select_previous_po" + postfix, False)
    all_po = request.POST.get("all_po" + postfix, False)
    po_ids = None
    inventory_ids = None
    purchase_order_item_id = None
    if not select_previous_po and not all_po:
        try:
            po_ids = request.POST.getlist("po_ids" + postfix)
        except:
            pass
    update_inventory = request.POST.get("update_inventory" + postfix, False)
    selected_location = request.POST.get("location" + postfix, None)
    if update_inventory:
        try:
            inventory_ids = request.POST.getlist("inventory_ids" + postfix)
        except:
            pass
    if not po_ids:
        from utils.action_data_parsing import parse_record_ids

        po_ids = parse_record_ids(request.POST.get("object_ids", []), request, "po_ids")
    select_all_objects = request.POST.get("select_all_objects", "")
    view_id = request.POST.get("view_id", "")
    if not is_valid_uuid(view_id):
        view_id = None

    if submit_option == "save":
        input_data = {}
        node.valid_to_run = True
        if select_previous_po:
            input_data["select_previous_po"] = True
            input_data["all_po"] = False
            input_data["po_ids"] = []
        elif all_po:
            input_data["all_po"] = True
        elif po_ids:
            input_data["po_ids"] = po_ids
        if not select_previous_po and not all_po and not po_ids:
            print("Purchase Orders ID is required.")
            node.valid_to_run = False

        if update_inventory:
            input_data["update_inventory"] = update_inventory
            if inventory_ids:
                input_data["inventory_ids"] = inventory_ids
        elif selected_location:
            try:
                selected_location = InventoryWarehouse.objects.get(
                    workspace=workspace, id=selected_location
                )
            except InventoryWarehouse.DoesNotExist:
                selected_location = None
            if selected_location:
                input_data["selected_location"] = str(selected_location.id)

        if update_inventory and not inventory_ids:
            print("Inventory ID is required.")
            node.valid_to_run = False

        if object_source:
            input_data["object_source"] = object_source
        if object_target:
            input_data["object_target"] = object_target
        input_data["is_convert_record_action"] = True

        node.input_data = input_data
        node.predefined_input = input_data
        node.save()

        print(node.valid_to_run)
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
            )
            .order_by("order", "created_at")
            .first()
        )
        if module:
            module_slug = module.slug
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
            )
        return redirect(reverse("main", host="app"))
    else:
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_PURCHASE_ORDER]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_PURCHASE_ORDER
            )
            .order_by("order", "created_at")
            .first()
        )
        if module:
            module_slug = module.slug
        if submit_option == "run":
            if "select_previous_po" in node.input_data:
                select_previous_po = node.input_data["select_previous_po"]
                if (
                    "purchase_order_item" in at.input_data
                    and "id" in at.input_data["purchase_order_item"]
                ):
                    purchase_order_item_id = at.input_data["purchase_order_item"]["id"]
            elif "all_po" in node.input_data:
                all_po = node.input_data["all_po"]
            elif "po_ids" in node.input_data:
                po_ids = node.input_data["po_ids"]

            update_inventory = False
            if "update_inventory" in node.input_data:
                update_inventory = node.input_data["update_inventory"]
                if "inventory_ids" in node.input_data:
                    inventory_ids = node.input_data["inventory_ids"]
            elif "selected_location" in node.input_data:
                selected_location = node.input_data["selected_location"]

        if not select_previous_po and not all_po and not po_ids:
            print("Purchase Orders ID is required.")
            return HttpResponse(status=400)

        try:
            selected_location = InventoryWarehouse.objects.get(
                workspace=workspace, id=selected_location
            )
        except Exception as e:
            print(e)
            selected_location = None

        if is_object_action or is_record_action:
            action = Action.objects.filter(slug=action_slug).first()
            history = ActionHistory.objects.create(
                workspace=workspace,
                action=action,
                status="initialized",
                created_by=request.user,
            )
        if is_record_action:
            history.object_id = po_ids[0]
            history.object_type = TYPE_OBJECT_PURCHASE_ORDER
            history.save()

        po_conditions = Q(
            purchase_order__workspace=workspace, purchase_order__usage_status="active"
        )
        if select_previous_po:
            po_conditions &= Q(id=purchase_order_item_id)
        else:
            if not all_po:
                po_conditions &= Q(purchase_order__id__in=po_ids)
        pois = PurchaseOrdersItem.objects.filter(po_conditions)
        if not pois:
            if is_object_action:
                history.status = "failed"
                history.completed_at = timezone.now()
                history.save()
            print(
                "Purchase Order does not exist or Purchase Order does not have any item."
            )
            msg = "Purchase Order does not exist or Purchase Order does not have any item."
            if lang == "ja":
                msg = "発注が存在しないか、発注に項目がありません。"
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message=msg,
                type="error",
            )
            if module_slug:
                if page and page != "None":
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                        + f"?page={page}&target={TYPE_OBJECT_ITEM}&id={po_ids[0]}"
                    )
                else:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                        + f"?target={TYPE_OBJECT_ITEM}&id={po_ids[0]}"
                    )
            return redirect(reverse("main", host="app"))

        inventory_module_slug = None
        if is_record_action:
            inventory_module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_INVENTORY]
            inventory_module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_INVENTORY
                )
                .order_by("order", "created_at")
                .first()
            )
            if inventory_module:
                inventory_module_slug = inventory_module.slug
        output_map = {"items": [], "inventories": []}
        for poi in pois:
            item = poi.item
            currency = poi.purchase_order.currency
            price = poi.amount_price
            if item:
                item_name = item.name
                shopturbo_item = item
                create_new_item = False
            else:
                item_name = poi.item_name
                shopturbo_item = None
                create_new_item = True
            tax = poi.tax_rate
            if not tax:
                tax = 0

            create_new_inventory = True
            if update_inventory:
                create_new_inventory = False
                if not shopturbo_item:
                    continue

            if is_object_action or is_record_action:
                input_data = dict(request.POST)
                keys = ["update_inventory", "inventory_ids", "selected_location"]
                input_data = {
                    key: value[0] if len(value) == 1 else value
                    for key, value in input_data.items()
                    if key in keys
                }
                transfer_history = ActionTaskHistory.objects.create(
                    workspace=history.workspace,
                    action_history=history,
                    status="initialized",
                    input_data=input_data,
                )
            if create_new_item and not has_quota(workspace, ITEM_USAGE_CATEGORY):
                if is_object_action or is_record_action:
                    transfer_history.status = "failed"
                    transfer_history.error_message = "Can not create new item: Item limit exceeded. Upgrade your plan or archive old records."
                    transfer_history.error_message_ja = "新しいアイテムを作成できません: アイテムの制限を超えました。プランをアップグレードするか、古いレコードをアーカイブしてください。"
                    transfer_history.save()
                continue

            if create_new_item:
                shopturbo_item = ShopTurboItems.objects.create(
                    workspace=workspace,
                    status="active",
                    platform="sanka",
                    currency=currency,
                    name=item_name,
                )
            item_price = shopturbo_item.purchase_prices.filter(
                currency=currency, price=price, tax=tax
            ).first()
            if not item_price:
                ItemPurchasePrice.objects.create(
                    item=shopturbo_item,
                    currency=currency,
                    price=float(price),
                    tax=tax,
                    default=True,
                )
                shopturbo_item.purchase_price = price
                shopturbo_item.save()

            # Inventory
            if create_new_inventory and not has_quota(
                workspace, INVENTORY_USAGE_CATEGORY
            ):
                if is_object_action or is_record_action:
                    transfer_history.status = "failed"
                    transfer_history.error_message = "Can not create new inventory: Inventory limit exceeded. Upgrade your plan or archive old records."
                    transfer_history.error_message_ja = "新しい在庫を作成できません: 在庫制限を超えました。プランをアップグレードするか、古いレコードをアーカイブしてください。"
                    transfer_history.save()
                continue

            if not create_new_inventory:
                inventory = ShopTurboInventory.objects.filter(
                    workspace=workspace,
                    item=shopturbo_item,
                    id__in=inventory_ids,
                    status="active",
                ).first()
                if not inventory:
                    create_new_inventory = True

            if create_new_inventory:
                inventory = ShopTurboInventory.objects.create(
                    workspace=workspace,
                    status="active",
                    currency=currency,
                    initial_value=int(poi.amount_item) if poi.amount_item else 0,
                    inventory_status="available",
                    warehouse=selected_location,
                    unit_price=float(price),
                )
                if poi.purchase_order.date:
                    inventory.date = datetime.combine(
                        poi.purchase_order.date,
                        datetime.min.time(),
                        pytz.timezone(workspace.timezone),
                    )
                else:
                    inventory.date = timezone.now()
                inventory.item.add(shopturbo_item)
                inventory.save(log_data={"status": "create", "workspace": workspace})

                create_inventory_transaction_helper(inventory, request.user, lang=lang)

            else:
                if poi.purchase_order.date:
                    transaction_date = datetime.combine(
                        poi.purchase_order.date,
                        datetime.min.time(),
                        pytz.timezone(workspace.timezone),
                    )
                else:
                    transaction_date = timezone.now()
                if transaction_date < inventory.date:
                    transaction_date = inventory.date
                transaction = InventoryTransaction.objects.create(
                    workspace=inventory.workspace,
                    inventory=inventory,
                    transaction_type="in",
                    amount=poi.amount_item,
                    transaction_date=transaction_date,
                )

                if inventory.unit_price:
                    transaction.price = inventory.unit_price
                else:
                    latest_transaction = (
                        InventoryTransaction.objects.exclude(
                            pk=transaction.id, usage_status="archived"
                        )
                        .filter(
                            inventory=inventory,
                            transaction_date__lte=transaction.transaction_date,
                        )
                        .order_by("-transaction_date", "-created_at")
                        .first()
                    )
                    transaction.price = latest_transaction.average_price
                if transaction.price:
                    transaction.total_price = transaction.price * poi.amount_item
                else:
                    transaction.total_price = 0
                transaction.save()

                workflows = get_workflows_by_first_action_trigger(
                    "record-created",
                    inventory.workspace,
                    TYPE_OBJECT_INVENTORY_TRANSACTION,
                    additional_conditions={
                        "input_data__transaction_type": transaction.transaction_type
                    },
                )
                for workflow in workflows:
                    param = {
                        "function": "trigger_workflow_when_inventory_transaction_created",
                        "job_id": str(uuid.uuid4()),
                        "workspace_id": str(inventory.workspace.id),
                        "user_id": None,
                        "args": [
                            f"--inventory_transaction_id={transaction.id}",
                            f"--lang={lang}",
                            f"--workflow_id={workflow.id}",
                        ],
                    }
                    trigger_bg_job(param)
                recalculate_transactions_after(inventory, transaction, request)
                inventory.refresh_from_db()
                update_inventory_stock_price(inventory, request)
                inventory.refresh_from_db()
                re_calculate_inventory_stock(inventory)
                inventory.refresh_from_db()
                inventory.total_inventory += poi.amount_item
                inventory.save()
                trigger_workflow_by_inventory_less_than(inventory)

            output_map["items"].append(f"#{shopturbo_item.item_id}")
            output_map["inventories"].append(f"#{inventory.inventory_id}")

            if is_object_action or is_record_action:
                transfer_history.completed_at = timezone.now()
                transfer_history.status = "success"
                if inventory_module_slug:
                    transfer_history.result_link = (
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": inventory_module_slug,
                                "object_slug": inventory_module_object_slug,
                            },
                        )
                        + f"?inventory_id={inventory.id}"
                    )
                transfer_history.save()

        if is_object_action or is_record_action:
            history.status = "success"
            history.completed_at = timezone.now()
            history.save()

        if submit_option == "run":
            at.status = "success"
            at.completed_at = timezone.now()
            at.save()

            next_node = None
            if node:
                next_node = node.next_node
                at = ActionTracker.objects.filter(id=at.id).first()
                if at:
                    id_pos = list(set([f"#{poi.purchase_order.id_po}" for poi in pois]))
                    at.input_data = {
                        f"{'発注ID' if lang == 'ja' else 'Purchase Order ID'}": ",".join(
                            id_pos
                        ),
                    }
                    at.output_data = {
                        f"{'商品ID' if lang == 'ja' else 'Item IDs'}": ",".join(
                            output_map["items"]
                        ),
                        f"{'在庫ID' if lang == 'ja' else 'Inventory IDs'}": ",".join(
                            output_map["inventories"]
                        ),
                    }
                    at.save()
            if next_node:
                next_at = ActionTracker.objects.get(
                    node=next_node, workflow_action_tracker=wat
                )
                transfer_output_to_target_input(at, next_at)
                trigger_next_action(
                    current_action_tracker=at,
                    workflow_action_tracker=wat,
                    current_node=node,
                    user_id=str(request.user.id),
                    lang=lang,
                )
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
                )
                .order_by("order", "created_at")
                .first()
            )
            if module:
                module_slug = module.slug
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
                )
            return redirect(reverse("main", host="app"))

        if module:
            if is_record_action:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?id={po_ids[0]}&target={TYPE_OBJECT_PURCHASE_ORDER}&sidedrawer=action-history"
                )
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?id={po_ids[0]}&open_drawer=action_drawer_history"
            )
        return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def when_purchase_item_updated(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        app_setting, _ = AppSetting.objects.get_or_create(
            workspace=get_workspace(request.user), app_target=PROCUREMENT_APP_TARGET
        )
        if not app_setting.purchase_order_is_status:
            msg = "Please enable Manage Purchase Item Status in workspace settings."
            if lang == "ja":
                msg = (
                    "ワークスペース設定で仕入品のステータスの管理を有効にしてください。"
                )
            return HttpResponse(msg)

        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")
        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        if not action_slug:
            return HttpResponse(status=400)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        try:
            choice_property = PurchaseOrdersNameCustomField.objects.get(
                workspace=workspace,
                name__isnull=True,
                type="choice",
                choice_value__isnull=False,
            )
        except PurchaseOrdersNameCustomField.DoesNotExist:
            choice_property = None
        choice_value = None
        if node and node.input_data:
            if choice_property and "choice_value" in node.input_data:
                choice_value = node.input_data["choice_value"]

        context = {
            "action_index": action_index,
            "action_node_id": action_node_id,
            "action_slug": action_slug,
            "choice_property": choice_property,
            "choice_value": choice_value,
        }
        return render(
            request,
            "data/purchase_order/when-purchase-item-status-updated.html",
            context,
        )

    # POST
    # Workflow related input
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    action_slug = request.POST.get("action_slug")
    at_id = request.POST.get("action_tracker_id")
    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)

    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    choice_value = request.POST.get("choice_value" + postfix, False)
    action_name = request.POST.get("action_name" + postfix)

    if submit_option == "save":
        input_data = {}
        node.valid_to_run = True
        try:
            choice_property = PurchaseOrdersNameCustomField.objects.get(
                workspace=workspace,
                name__isnull=True,
                type="choice",
                choice_value__isnull=False,
            )
        except PurchaseOrdersNameCustomField.DoesNotExist:
            choice_property = None
        if choice_property and choice_value in choice_property.choice_value:
            input_data["choice_value"] = choice_value
        else:
            print("Choice property and choice value are required.")
            node.valid_to_run = False

        if action_name:
            input_data["action_name"] = action_name

        node.input_data = input_data
        node.predefined_input = input_data
        node.save()
        return HttpResponse()

    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
    module = (
        Module.objects.filter(
            workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
        )
        .order_by("order", "created_at")
        .first()
    )
    if module:
        module_slug = module.slug

        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
        )
    return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def purchase_orders_options(request):
    workspace = get_workspace(request.user)
    q = request.GET.get("q")
    page = request.GET.get("page", 1)

    filter_conditions = Q(workspace=workspace, usage_status="active")
    if q:
        q_id = q

        try:
            if q[0] == "#" or q[0] == "0":
                q_id = int(q[1:].replace(" ", ""))
        except Exception as e:
            print(e)

        filter_conditions &= Q(id_po__icontains=q_id)

    purchase_order = PurchaseOrders.objects.filter(filter_conditions).order_by("-id_po")
    print(purchase_order)

    res = []
    ITEMS_PER_PAGE = 30
    po_paginator = Paginator(purchase_order, ITEMS_PER_PAGE)
    paginated_po = []
    more_pagination = False
    if page:
        try:
            po_page_content = po_paginator.page(page if page else 1)
            paginated_po = po_page_content.object_list
            more_pagination = po_page_content.has_next()
        except EmptyPage:
            pass

    for purchase_order in paginated_po:
        try:
            text = f"#{purchase_order.id_po:04d} | {str(purchase_order.currency).upper()} {purchase_order.total_price}"
        except Exception:
            pass

        res.append(
            {
                "id": str(purchase_order.id),
                "text": text,
            }
        )

    context = {"results": res, "pagination": {"more": more_pagination}}

    return JsonResponse(context)


@login_or_hubspot_required
def get_purchase_order_events(request, id):
    workspace = get_workspace(request.user)
    try:
        purchaseorders = PurchaseOrders.objects.get(id=id, workspace=workspace)
    except PurchaseOrders.DoesNotExist:
        return HttpResponse(status=404)

    app_logs = AppLog.objects.filter(
        workspace=workspace, purchaseorders=purchaseorders
    ).first()

    context = {
        "app_logs": app_logs,
        "purchaseorders": purchaseorders,
    }
    return render(request, "data/purchase_order/purchase-order-events.html", context)


@login_or_hubspot_required
def purchase_order_bot(request):
    workspace = get_workspace(request.user)

    if request.method == "GET":
        object_type = request.GET.get("object_type")
        context = {
            "object_type": object_type,
        }

        return render(request, "data/purchase_order/bot-pdf.html", context)
    elif request.method == "POST":
        lang = request.LANGUAGE_CODE
        file = request.FILES.get("file")
        object_type = request.POST.get("object_type")

        if file:
            file.name = str(uuid.uuid4()) + "." + file.name.split(".")[-1]
            res = BotPdfUpload.objects.create(
                workspace=workspace,
                object_type=object_type,
                file=file,
                status="uploaded",
            )

            return JsonResponse({"file_id": res.id})

        body = json.loads(request.body)
        submitted_file = body.get("submitted_files")
        deleted_file = body.get("deleted_files")

        if deleted_file:
            BotPdfUpload.objects.filter(id__in=deleted_file).delete()

        if submitted_file:
            BotPdfUpload.objects.filter(id__in=submitted_file).update(status="queue")

        for pdf_id in submitted_file:
            file_obj = BotPdfUpload.objects.get(id=pdf_id)
            file = file_obj.file
            object_type = body.get("object_type")

            file_name = str(uuid.uuid4()) + "." + file.name.split(".")[-1]
            with file.open("rb") as f:
                file_content = BytesIO(f.read())

            if object_type == "billing":
                temp_object = Bill.objects.create(
                    workspace=workspace,
                    description="処理中..." if lang == "ja" else "Processing ...",
                    file=File(file_content, name=file_name),
                )
            elif object_type == "expense":
                temp_object = Expense.objects.create(
                    workspace=workspace,
                    description="処理中..." if lang == "ja" else "Processing ...",
                )
                ExpenseFile.objects.create(
                    expense=temp_object, file=File(file_content, name=file_name)
                )

            param = {
                "function": "bot_pdf",
                "job_id": str(uuid.uuid4()),
                "workspace_id": str(workspace.id),
                "user_id": str(request.user.id),
                "args": [
                    f"--workspace_id={workspace.id}",
                    f"--user_id={request.user.id}",
                    f"--upload_pdf_id={pdf_id}",
                    f"--temp_object_id={temp_object.id}",
                    f"--lang={lang}",
                ],
            }

            is_running = trigger_bg_job(param)

        if is_running:
            if lang == "ja":
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message="一括アップロードが実行されました。",
                    type="success",
                )
            else:
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message="Bulk Upload is triggered.",
                    type="success",
                )
        else:
            if lang == "ja":
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message="一括アップロードに失敗しました。",
                    type="error",
                )
            else:
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message="Bulk Upload is fail to trigger.",
                    type="error",
                )

        return JsonResponse({"status": True})


@login_or_hubspot_required
def create_new_purchase_order(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        # Required
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        input_data = {
            "status": None,
            "date": None,
            "contact_company": None,
            "tax_rate": None,
            "currency": None,
            "send_from": None,
            "notes": None,
            "tax_option": None,
            "items_show": None,
            "condition": None,
            "inventory_level_buffer": None,
            "item_company_property": None,
        }

        if node:
            input_data = node.input_data

        page_obj = get_page_object(TYPE_OBJECT_PURCHASE_ORDER, lang)

        base_model = page_obj["base_model"]
        custom_field = CustomProperty.objects.filter(
            workspace=workspace, model=base_model._meta.db_table, name="status"
        )
        if custom_field:
            custom_field = custom_field.first()
            choices = ast.literal_eval(custom_field.value)
            choices = [{"label": choices[value], "value": value} for value in choices]
        else:
            choices = [
                {"label": PURCHASE_ORDER_STATUS_DISPLAY[value][lang], "value": value}
                for value, label in PURCHASE_ORDER_STATUS
            ]

        context = {
            "action_index": action_index,
            "action_slug": action_slug,
            "input_data": input_data,
        }
        if choices:
            context["choices"] = choices

        return render(
            request,
            "data/purchase_order/action-form/create-new-purchase-order.html",
            context,
        )

    if request.method == "POST":
        print("[DEBUG] POST", request.POST)

        submit_option = request.POST.get("submit-option")
        action_index = request.POST.get("action_index")
        action_node_id = request.POST.get("action_node_id")
        action_slug = request.POST.get("action_slug")
        at_id = request.POST.get("action_tracker_id")
        wat_id = request.POST.get("workflow_action_tracker_id")

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except ActionNode.DoesNotExist:
                print("ActionNode does not exist")
                return HttpResponse(status=404)
        at = None
        if at_id:
            try:
                at = ActionTracker.objects.get(id=at_id)
                if at.workspace:
                    workspace = at.workspace
            except ActionTracker.DoesNotExist:
                print("ActionTracker does not exist")
                return HttpResponse(status=404)
        wat = None
        if wat_id:
            try:
                wat = WorkflowActionTracker.objects.get(id=wat_id)
            except WorkflowActionTracker.DoesNotExist:
                print("WorkflowActionTracker does not exist")
                return HttpResponse(status=404)

        # Text Field
        postfix = ""
        if action_index:
            postfix = "-" + action_index

        object_type = request.POST.get("object_type" + postfix)
        object_name = request.POST.get("object_name" + postfix)

        status = request.POST.get("status" + postfix, None)
        date = request.POST.get("date" + postfix, None)
        contact_company = request.POST.get("contact_and_company" + postfix, None)
        currency = request.POST.get("currency" + postfix, None)

        # Differentiate between tax_rate(from original form) and tax_input(from inventory level)
        tax_rate = request.POST.get("tax_rate" + postfix, None)
        tax_input = request.POST.get("tax_input" + postfix, None)

        send_from = request.POST.get("send_from" + postfix, None)
        notes = request.POST.get("notes" + postfix, None)
        tax_option = request.POST.get("tax_option" + postfix, None)

        condition = request.POST.get("condition" + postfix, None)
        inventory_level_buffer = request.POST.get(
            "inventory_level_buffer" + postfix, None
        )
        item_company_property = request.POST.get(
            "item_company_property" + postfix, None
        )

        items_company = {}

        try:
            # Items Information
            items = request.POST.getlist("item" + postfix, [])
            tax_item_rate = request.POST.getlist("tax_item_rate" + postfix, [])
            item_amount = request.POST.getlist("item_amount" + postfix, [])
            amount_price = request.POST.getlist("amount" + postfix, [])
            items_status = request.POST.getlist("item_status" + postfix, [])

            dummy_poi_ids = []
            item = []
            item_status = []
            amount = []
            item_id = []
            price = []
            tax = []
            items_show = None

            if items:
                try:
                    for idx, item_data in enumerate(items):
                        if "|" in item_data:
                            item.append(str(item_data.split("|")[0]))
                            item_id.append(str(item_data.split("|")[2]))
                        else:
                            item.append(item_data)
                            item_id.append("")

                        if item_amount:
                            amount.append(float(item_amount[idx]))
                        else:
                            amount.append(float(0))
                        if amount_price:
                            price.append(float(amount_price[idx]))
                        else:
                            price.append(float(0))
                        if tax_item_rate:
                            tax.append(float(tax_item_rate[idx]))
                        else:
                            tax.append(None)

                        if items_status:
                            item_status.append(items_status[idx])
                        else:
                            item_status.append("")
                        dummy_poi_ids.append("")
                    items_show = list(
                        zip(dummy_poi_ids, item_id, item, amount, price, tax, status)
                    )
                except Exception as e:
                    print(e)
        except Exception:
            print(
                "[DEBUG] Skip this input as it is read from html, Workflow do not have this input"
            )
            pass

        if submit_option == "save":
            node.valid_to_run = True

            required_fields = [status, condition]
            if not all(required_fields):
                print("Required fields are missing.")
                node.valid_to_run = False

            if condition == "create":
                print("[DEBUG] Tax Rate", tax_rate)
                input_data = {
                    "status": status,
                    "date": date,
                    "contact_company": contact_company,
                    "tax_rate": tax_rate,
                    "currency": currency,
                    "send_from": send_from,
                    "notes": notes,
                    "tax_option": tax_option,
                    "items_show": items_show,
                    "condition": condition,
                }
            elif condition == "inventory_level":
                print("[DEBUG] Tax Rate", tax_rate)
                input_data = {
                    "status": status,
                    "tax_rate": tax_rate,
                    "send_from": send_from,
                    "notes": notes,
                    "tax_option": tax_option,
                    "condition": condition,
                }

            if condition == "inventory_level":
                input_data["inventory_level_buffer"] = inventory_level_buffer
                input_data["item_company_property"] = item_company_property
                input_data["tax_rate"] = tax_input
            else:
                input_data["inventory_level_buffer"] = None
                input_data["item_company_property"] = None
                input_data["tax_rate"] = tax_rate

            if object_type:
                input_data["object_type"] = object_type
            if object_name:
                input_data["object_name"] = object_name

            node.input_data = input_data
            node.predefined_input = input_data
            node.save()

            return HttpResponse(status=200)
        else:
            if submit_option == "run":
                status = node.input_data.get("status", None)
                date = node.input_data.get("date", None)
                contact_company = node.input_data.get("contact_company", None)
                tax_rate = node.input_data.get("tax_rate", None)
                currency = node.input_data.get("currency", None)
                send_from = node.input_data.get("send_from", None)
                notes = node.input_data.get("notes", None)
                tax_option = node.input_data.get("tax_option", None)
                items_show = node.input_data.get("items_show", None)
                condition = node.input_data.get("condition", None)
                inventory_level_buffer = node.input_data.get(
                    "inventory_level_buffer", None
                )
                item_company_property = node.input_data.get(
                    "item_company_property", None
                )

                # To remove postfix from tax_option
                if tax_option:
                    tax_option = tax_option.split("-")[0]
                print("[DEBUG] tax_option", tax_option)
            else:
                required_fields = [status, condition]
                if not all(required_fields):
                    print("Required fields are missing.")
                    return HttpResponse(status=400)

            if condition == "inventory_level":
                if not inventory_level_buffer:
                    print("Inventory Level Buffer is required.")
                    return HttpResponse(status=400)
                else:
                    inventory_level_buffer = int(inventory_level_buffer)

                tax_option = "unified_tax"
                date = timezone.now().strftime("%Y-%m-%d")

                # Get All Inventory Transaction
                seven_days_ago = timezone.now() - timezone.timedelta(days=7)

                try:
                    inventory_data = (
                        InventoryTransaction.objects.filter(
                            workspace=workspace,
                            transaction_type="out",
                            transaction_date__gte=seven_days_ago,
                        )
                        .values("inventory")
                        .annotate(
                            total_out=Sum("amount"),
                            avg_daily_stockout=ExpressionWrapper(
                                Sum("amount") / 7.0, output_field=FloatField()
                            ),
                            expected_stockout=ExpressionWrapper(
                                Sum("amount") / 7.0 * inventory_level_buffer,
                                output_field=FloatField(),
                            ),
                            inventory_stock=F("inventory__available"),
                            stock_difference=ExpressionWrapper(
                                F("inventory__available")
                                - (Sum("amount") / 7.0 * inventory_level_buffer),
                                output_field=FloatField(),
                            ),
                            is_restock=Case(
                                When(stock_difference__lt=0, then=Value(True)),
                                default=Value(False),
                                output_field=BooleanField(),
                            ),
                        )
                        .values(
                            "inventory__id",
                            "avg_daily_stockout",
                            "expected_stockout",
                            "inventory_stock",
                            "stock_difference",
                            "is_restock",
                        )
                    )
                except Exception as e:
                    print("[DEBUG] Error in inventory_data", e)
                    # If error in inventory_data, send message there is an error while fetching inventory data
                    message = (
                        "在庫データの取得中にエラーが発生しました。"
                        if lang == "ja"
                        else "An error occurred while fetching inventory data."
                    )
                    at.output_data = {"status": "error", "message": message}
                    return_fn = get_redirect_workflow(
                        submit_option, node, workspace, at, wat, status="failed"
                    )
                    next_node = None
                    if node:
                        next_node = node.next_node
                        at = ActionTracker.objects.filter(id=at.id).first()
                    if next_node:
                        trigger_next_action(
                            current_action_tracker=at,
                            workflow_action_tracker=wat,
                            current_node=node,
                            user_id=str(request.user.id),
                            lang=lang,
                        )
                    return redirect(return_fn)

                if not any(item.get("is_restock") for item in inventory_data):
                    message = (
                        "在庫が不足していません。"
                        if lang == "ja"
                        else "No inventories need restocking."
                    )
                    at.output_data = {"status": "pass", "message": message}
                    return_fn = get_redirect_workflow(
                        submit_option, node, workspace, at, wat, status="success"
                    )
                    next_node = None
                    if node:
                        next_node = node.next_node
                        at = ActionTracker.objects.filter(id=at.id).first()
                    if next_node:
                        trigger_next_action(
                            current_action_tracker=at,
                            workflow_action_tracker=wat,
                            current_node=node,
                            user_id=str(request.user.id),
                            lang=lang,
                        )
                    return redirect(return_fn)
                else:
                    yesterday = timezone.now() - timezone.timedelta(days=1)
                    yesterday_str = yesterday.strftime("%Y-%m-%d")
                    yesterday = datetime.strptime(yesterday_str, "%Y-%m-%d")

                    workflow = wat.workflow
                    completed_action_trackers = ActionTracker.objects.filter(
                        workflow_action_tracker__workflow=workflow,
                        completed_at__gt=yesterday,
                        status="success",
                    ).values("output_data")

                    tracker_inventory_ids = []
                    for tracker in completed_action_trackers:
                        if (
                            tracker["output_data"]
                            and tracker["output_data"].get("status")
                            == "create_purchase_order"
                        ):
                            tracker_inventory_ids += tracker["output_data"].get(
                                "inventory_ids", []
                            )
                        else:
                            pass

                    inventories = []
                    for inventory in inventory_data:
                        if (
                            inventory.get("is_restock")
                            and str(inventory.get("inventory__id", ""))
                            not in tracker_inventory_ids
                        ):
                            inventories.append(inventory)

                    if not inventories:
                        message = (
                            "在庫が不足しています。発注は既に作成されています。"
                            if lang == "ja"
                            else "Inventories need restocking. Purchase Order Already Created."
                        )
                        at.output_data = {"status": "pass", "message": message}
                        return_fn = get_redirect_workflow(
                            submit_option, node, workspace, at, wat, status="success"
                        )
                        next_node = None
                        if node:
                            next_node = node.next_node
                            at = ActionTracker.objects.filter(id=at.id).first()
                        if next_node:
                            trigger_next_action(
                                current_action_tracker=at,
                                workflow_action_tracker=wat,
                                current_node=node,
                                user_id=str(request.user.id),
                                lang=lang,
                            )
                        return redirect(return_fn)
                    else:
                        message = (
                            "在庫が不足しています。発注を作成してください。"
                            if lang == "ja"
                            else "Inventories need restocking. Purchase Order created."
                        )
                        at.output_data = {
                            "status": "create_purchase_order",
                            "message": message,
                            "inventory_ids": [
                                str(inventory.get("inventory__id", ""))
                                for inventory in inventories
                            ],
                        }

                items_company = {}

                try:
                    cf_name = ShopTurboItemsNameCustomField.objects.filter(
                        workspace=workspace,
                        type="company",
                        id=str(item_company_property),
                    ).first()
                    if not cf_name:
                        cf_name = None
                except:
                    # When item Company Property is not found or Not Valid UUID
                    cf_name = None

                # Convert Inventories to items_show (item_id, item_name, item_amount, item_price, tax_item_rate, item_status)
                for inventory in inventories:
                    inventory_id = str(inventory.get("inventory__id"))
                    items = []
                    # Get Item
                    try:
                        inventory_item = ShopTurboInventory.objects.filter(
                            workspace=workspace, id=inventory_id
                        ).first()
                        if inventory_item:
                            items = inventory_item.item.all()
                    except Exception as e:
                        print(
                            f"[DEBUG] Inventory ID: {inventory_id}, Error in inventory_item",
                            e,
                        )
                        continue

                    # Set Item Default
                    item_name = ""
                    tax_item_rate = None
                    item_status = ""

                    # Amount Update
                    try:
                        item_amount = float(inventory.get("stock_difference", 0))
                        item_amount = math.ceil(item_amount)
                        # Make item_amount absolute (+)
                        item_amount = abs(item_amount)
                    except Exception as e:
                        print(
                            f"[DEBUG] Inventory ID: {inventory_id}, Error in item_amount",
                            e,
                        )
                        continue

                    if items:
                        for item in items:
                            try:
                                item_id = str(item.id)
                                item_name = item.name
                                if item.purchase_price:
                                    item_price = float(item.purchase_price)
                                else:
                                    item_price = float(0)

                                company = ""
                                if cf_name:
                                    cf_value = (
                                        ShopTurboItemsValueCustomField.objects.filter(
                                            items=item, field_name=cf_name
                                        ).first()
                                    )
                                    if cf_value:
                                        company = cf_value.value
                                if company not in items_company:
                                    items_company[company] = []
                                items_company[company].append(
                                    [
                                        item_id,
                                        item_name,
                                        item_amount,
                                        item_price,
                                        tax_item_rate,
                                        item_status,
                                    ]
                                )

                                # Set The Last Item Currency
                                currency = item.currency

                            except Exception as e:
                                print(
                                    f"[DEBUG] Inventory ID: {inventory_id}, Error in item",
                                    e,
                                )
                                continue
                    else:
                        print(f"[DEBUG] Inventory ID: {inventory_id}, No item found")

            else:
                items_company = {}
                if contact_company:
                    items_company[contact_company] = items_show
                else:
                    items_company[""] = items_show

            sync_usage(workspace, MODELS_TO_STORAGE_USAGE[PurchaseOrders])
            if not has_quota(workspace, PURCHASE_ORDER_USAGE_CATEGORY):
                entries_name = USAGE_CATEGORIES_TITLE[PURCHASE_ORDER_USAGE_CATEGORY][
                    "en"
                ]
                msg = f"{entries_name}, could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some existing {entries_name} to free up space."
                if lang == "ja":
                    entries_name = USAGE_CATEGORIES_TITLE[
                        PURCHASE_ORDER_USAGE_CATEGORY
                    ]["ja"]
                    msg = f"{entries_name},制限を超えたため作成できませんでした。サブスクリプション プランをアップグレードしてクォータを増やすか、既存の {entries_name} の一部をアーカイブしてスペースを解放します。"
                Notification.objects.create(
                    workspace=workspace, user=request.user, message=msg, type="error"
                )
                return_fn = get_redirect_workflow(
                    submit_option, node, workspace, at, wat, status="failed"
                )
                next_node = None
                if node:
                    next_node = node.next_node
                    at = ActionTracker.objects.filter(id=at.id).first()
                if next_node:
                    trigger_next_action(
                        current_action_tracker=at,
                        workflow_action_tracker=wat,
                        current_node=node,
                        user_id=str(request.user.id),
                        lang=lang,
                    )
                return redirect(return_fn)

            # Loop For Each Contact/Company
            purchase_order_ids = []
            for contact_company, items_show in items_company.items():
                purchase = PurchaseOrders.objects.create(workspace=workspace)
                purchase.save(
                    log_data={
                        "user": request.user,
                        "status": "create",
                        "workspace": workspace,
                    }
                )

                # FARIS CODE: [OLD POI STUFF] Let maintain this code to make sure the position of the item (if dragged to another position) and update the information (cc@ Khanh)

                old_pois = PurchaseOrdersItem.objects.filter(
                    purchase_order=purchase, created_at__lt=timezone.now()
                )

                for item_data in items_show:
                    (
                        poi_id,
                        item_id,
                        item_name,
                        item_amount,
                        item_price,
                        tax_item_rate,
                        item_status,
                    ) = item_data

                    if item_id:
                        item = ShopTurboItems.objects.filter(
                            workspace=workspace, id=item_id
                        ).first()
                        if not item:
                            item = None
                    else:
                        item = None

                    item_price = float(item_price or 0)
                    item_amount = float(item_amount or 0)
                    if "unified_tax" in tax_option or "non_tax" in tax_option:
                        tax_item_rate = None

                    poi = PurchaseOrdersItem.objects.create(
                        purchase_order=purchase,
                        item=item,
                        item_name=item_name,
                        item_status=item_status,
                        amount_price=item_price,
                        amount_item=item_amount,
                        tax_rate=tax_item_rate,
                    )

                    if tax_item_rate:
                        poi.total_price_without_tax = item_price * item_amount
                        poi.total_price = poi.total_price_without_tax + (
                            poi.total_price_without_tax * tax_item_rate / 100
                        )
                        poi.save()

                    # FARIS CODE: [OLD POI STUFF] Maintain this code
                    if item_id:
                        old_poi = (
                            old_pois.filter(purchase_order=purchase, item_id=item_id)
                            .exclude(id=poi.id)
                            .first()
                        )
                    else:
                        old_poi = (
                            old_pois.filter(purchase_order=purchase)
                            .exclude(id=poi.id)
                            .first()
                        )

                    # Check if updating existing purchase item choice property
                    if old_poi:
                        print(old_poi.item_status, item_status)
                    if (
                        old_poi
                        and old_poi.item_status
                        and old_poi.item_status != item_status
                    ):
                        trigger_workflow_by_purchase_order_item_status_event(
                            poi, request.user.username, lang=request.LANGUAGE_CODE
                        )

                # FARIS CODE: [OLD POI STUFF] Let maintain this code to make sure the position of the item (if dragged to another position) and update the information (cc@ Khanh)
                if old_pois:
                    old_pois.delete()

                # Save Supplier
                if contact_company:
                    contact = Contact.objects.filter(
                        workspace=workspace, id=contact_company
                    ).first()
                    if contact:
                        purchase.contact = contact
                        purchase.save()

                    company = Company.objects.filter(
                        workspace=workspace, id=contact_company
                    ).first()
                    if company:
                        purchase.company = company
                        purchase.save()

                purchase.tax_option = tax_option
                purchase.notes = notes
                purchase.send_from = send_from
                purchase.status = status
                purchase.currency = currency
                purchase.date = date
                purchase.tax_rate = tax_rate
                purchase.save(log_data={"user": request.user, "workspace": workspace})
                purchase = handling_items(purchase)

                # Save association labels
                save_association_label(request, purchase, TYPE_OBJECT_PURCHASE_ORDER)

                purchase_order_ids.append(str(purchase.id))

            return_fn = get_redirect_workflow(
                submit_option, node, workspace, at, wat, status="success"
            )
            next_node = None
            at.input_data["purchase_order_ids"] = purchase_order_ids
            at.save()
            if node:
                next_node = node.next_node
                at = ActionTracker.objects.filter(id=at.id).first()
            if next_node:
                trigger_next_action(
                    current_action_tracker=at,
                    workflow_action_tracker=wat,
                    current_node=node,
                    user_id=str(request.user.id),
                    lang=lang,
                    with_previous_data=True,
                )
            return redirect(return_fn)

    return HttpResponse(status=200)


@login_or_hubspot_required
def export_purchase_order(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        # Getting Info if have action_index, it means from workflow
        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        channels = Channel.objects.filter(
            workspace=workspace, integration__slug="smaregi"
        )
        selected_channel = None
        use_previous_order = False
        if postfix:
            if node and node.input_data:
                if "selected_channel" in node.input_data:
                    selected_channel = node.input_data["selected_channel"]
                if "use_previous_order" in node.input_data:
                    use_previous_order = node.input_data["use_previous_order"]

        context = {
            "action_index": action_index,
            "selected_channel": selected_channel,
            "channels": channels,
            "use_previous_order": use_previous_order,
        }
        return render(
            request, "data/shopturbo/export-purchase-order-action.html", context
        )

    # Workflow related input
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")
    node = None

    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)
    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)
    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    selected_channel = request.POST.get("selected_channel" + postfix, "")
    use_previous_order = request.POST.get("use_previous_order" + postfix, False)
    action_name = request.POST.get("action_name" + postfix, "")
    purchase_order_ids = []
    if at:
        purchase_order_ids = at.input_data.get("purchase_order_ids", [])

    # POST
    print(request.POST)
    print("Running Export Purchase Order")
    try:
        if submit_option == "save":
            input_data = {}
            node.valid_to_run = True
            input_data["use_previous_order"] = use_previous_order

            if selected_channel:
                input_data["selected_channel"] = selected_channel

            if not selected_channel:
                print("Selected Channel need to be defined.")
                node.valid_to_run = False

            input_data["action_name"] = action_name
            node.input_data = input_data
            node.predefined_input = input_data
            node.save()

            print(node.valid_to_run)
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = None
            if module_slug:
                module_slug = module.slug
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))
        else:
            if "selected_channel" in node.input_data:
                selected_channel = node.input_data["selected_channel"]
            if "use_previous_order" in node.input_data:
                use_previous_order = node.input_data["use_previous_order"]

            channel = Channel.objects.get(id=selected_channel)

            if not use_previous_order:
                purchase_orders = PurchaseOrders.objects.filter(workspace=workspace)
            else:
                purchase_orders = PurchaseOrders.objects.filter(
                    id__in=purchase_order_ids
                )
            if not purchase_orders:
                # use the latest:
                purchase_orders = [
                    PurchaseOrders.objects.filter(workspace=workspace)
                    .order_by("-created_at")
                    .first()
                ]
            print("logit", channel, use_previous_order, purchase_orders)

            for order in purchase_orders:
                if channel.integration.slug == "smaregi":
                    contract_id = channel.account_id
                    access_token = channel.access_token
                    headers = {
                        "Content-Type": "application/x-www-form-urlencoded",
                        "Accept": "application/json",
                        "X-contract-id": contract_id,
                        "X-access-token": access_token,
                    }
                    order_items = PurchaseOrdersItem.objects.filter(
                        purchase_order=order
                    )
                    product_ids = []
                    product_details = []
                    for item in order_items:
                        if item.item:
                            name = item.item.name
                        else:
                            name = item.item_name

                        if item.item_price:
                            item_price = item.item_price.price
                        else:
                            item_price = item.amount_price

                        data = {
                            "proc_name": "product_ref",
                            "params": json.dumps(
                                {
                                    "conditions": [{"productName": name}],
                                    "table_name": "Product",
                                }
                            ),
                        }
                        response = requests.post(
                            "https://webapi.smaregi.jp/access/",
                            headers=headers,
                            data=data,
                        )
                        print(response.json())
                        if response.status_code == 200:
                            if response.json().get("total_count") != "0":
                                product_ids.append(
                                    {
                                        "productId": response.json()
                                        .get("result")[0]
                                        .get("productId"),
                                        "cost": "0",
                                    }
                                )
                                product_details.append(
                                    {
                                        "storeId": "1",
                                        "productId": response.json()
                                        .get("result")[0]
                                        .get("productId"),
                                        "quantity": item.amount_item,
                                    }
                                )
                                continue

                        new_id = 1
                        data = {
                            "proc_name": "product_ref",
                            "params": json.dumps(
                                {
                                    "conditions": [{"productId": str(new_id)}],
                                    "table_name": "Product",
                                }
                            ),
                        }
                        response = requests.post(
                            "https://webapi.smaregi.jp/access/",
                            headers=headers,
                            data=data,
                        )
                        latest_response = response.json()  # Determine New ID to create
                        while latest_response.get("total_count") != "0":
                            new_id += 1
                            latest_data = {
                                "proc_name": "product_ref",
                                "params": json.dumps(
                                    {
                                        "conditions": [{"productId": str(new_id)}],
                                        "table_name": "Product",
                                    }
                                ),
                            }
                            latest_response = requests.post(
                                "https://webapi.smaregi.jp/access/",
                                headers=headers,
                                data=latest_data,
                            )
                            latest_response = latest_response.json()
                            print("latest:", new_id, latest_response)
                        print("new id:", new_id)

                        # If Product does not exist
                        if latest_response.get("total_count") == "0":
                            params_data = {
                                "proc_info": {"proc_division": "U"},
                                "data": [
                                    {
                                        "table_name": "Product",
                                        "rows": [
                                            {
                                                "productId": str(new_id),
                                                "categoryId": "1",
                                                "productName": name,
                                                "price": str(int(item_price)),
                                                "taxDivision": "0",
                                                "taxFreeDivision": "0",
                                                "cost": "0",
                                            }
                                        ],
                                    },
                                ],
                            }

                            data = {
                                "proc_name": "product_upd",
                                "params": json.dumps(params_data),
                            }
                            response = requests.post(
                                "https://webapi.smaregi.jp/access/",
                                headers=headers,
                                data=data,
                            )
                            print(response.json())
                            if response.status_code == 200:
                                product_ids.append({"productId": new_id, "cost": "0"})
                                product_details.append(
                                    {
                                        "storeId": "1",
                                        "productId": new_id,
                                        "quantity": item.amount_item,
                                    }
                                )

                    print("product_ids:", product_ids)
                    ordered_date = order.date.strftime("%Y-%m-%d")
                    token = int(order.date.strftime("%Y%m%d") + "10000001")
                    token_used = True
                    while token_used:
                        params_data = {
                            "proc_info": {"proc_division": "U"},
                            "data": [
                                {
                                    "table_name": "StorageInfo",
                                    "rows": [
                                        {
                                            "orderedDate": ordered_date,
                                            "status": "2",
                                            "identificationNo": "StorageInfo1",
                                            "token": str(token),
                                        }
                                    ],
                                },
                                {
                                    "table_name": "StorageInfoDelivery",
                                    "rows": [
                                        {
                                            "storageStoreId": "1",
                                            "storageExpectedDateFrom": ordered_date,
                                            "storageExpectedDateTo": ordered_date,
                                        },
                                    ],
                                },
                                {
                                    "table_name": "StorageInfoProduct",
                                    "rows": product_ids,
                                },
                                {
                                    "table_name": "StorageInfoDeliveryProduct",
                                    "rows": product_details,
                                },
                            ],
                        }

                        # Prepare the payload. Notice that we are URL-encoding the data.
                        # The `params` field must be a string containing the JSON, so we use json.dumps.
                        data = {
                            "proc_name": "storage_info_upd",
                            "params": json.dumps(params_data),
                        }
                        response = requests.post(
                            "https://webapi.smaregi.jp/access/",
                            headers=headers,
                            data=data,
                        )
                        print(response.json())
                        if (
                            response.json().get("result").get("mesasge")
                            == "重複するトークンでのリクエストが存在するためリクエストをスルーします"
                        ):
                            token_used = True
                            token += 1
                        else:
                            token_used = False

    except Exception:
        traceback.print_exc()
        return HttpResponse(status=500)

    if submit_option == "run":
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
            )
            .order_by("order", "created_at")
            .first()
        )
        module_slug = None
        if module:
            module_slug = module.slug
        at.status = "success"
        at.completed_at = timezone.now()
        at.save()

        next_node = None
        if node:
            next_node = node.next_node
            at = ActionTracker.objects.filter(id=at.id).first()
        if next_node:
            trigger_next_action(
                current_action_tracker=at,
                workflow_action_tracker=wat,
                current_node=node,
                user_id=str(request.user.id),
                lang=lang,
            )

            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
            )
        return redirect(reverse("main", host="app"))
    return redirect(reverse("main", host="app"))


def autocomplete_purchase_order(request):
    workspace = get_workspace(request.user)
    search = request.GET.get("search", "")
    ids = request.GET.getlist("ids[]", "")
    view_id = request.GET.get("view_id", "")

    # Filter out invalid IDs (empty strings, "None", etc.) and validate UUIDs
    valid_ids = []
    if ids:
        for id_value in ids:
            if id_value and id_value != "None" and is_valid_uuid(id_value):
                valid_ids.append(id_value)

    filter_condition = Q(workspace=workspace) & ~Q(status="archived")
    page = int(request.GET.get("page", 1))

    results_per_page = 10 if len(valid_ids) <= 0 else len(valid_ids)
    start = (page - 1) * results_per_page
    end = start + results_per_page

    if search:
        app_setting = AppSetting.objects.filter(
            workspace=workspace, app_target=PROCUREMENT_APP_TARGET
        ).first()
        get_ordered_views(
            workspace=workspace,
            object_target=TYPE_OBJECT_PURCHASE_ORDER,
            user=request.user,
        )
        try:
            if view_id:
                view = View.objects.filter(id=view_id).first()
                if not view or (
                    view.is_private and (not request.user or view.user != request.user)
                ):
                    view = View.objects.filter(
                        workspace=workspace,
                        title="main",
                        target=TYPE_OBJECT_PURCHASE_ORDER,
                    ).first()
                    view_id = view.id
            else:
                view = View.objects.filter(
                    workspace=workspace, title="main", target=TYPE_OBJECT_PURCHASE_ORDER
                ).first()
            view_id = view.id
        except Exception as e:
            print(f"ERROR === procurement.py -- 2650: {e}")

        if not view:
            view = View.objects.filter(
                title="main", target=TYPE_OBJECT_PURCHASE_ORDER
            ).first()

        view_filter = view.viewfilter_set.first()
        search_filters = Q()
        if app_setting:
            search_setting_values = getattr(
                app_setting, "search_setting_purchase_order"
            )
            if search_setting_values:
                search_fields = search_setting_values.split(",")
                for s_field in search_fields:
                    search_filters |= apply_search_setting(
                        TYPE_OBJECT_PURCHASE_ORDER,
                        view_filter,
                        s_field,
                        search,
                        force=True,
                    )
        if search_filters:
            filter_condition &= search_filters

    if valid_ids:
        filter_condition &= Q(id__in=valid_ids)

    obj, created = ObjectManager.objects.get_or_create(
        workspace=workspace, page_group_type=TYPE_OBJECT_PURCHASE_ORDER
    )
    if created:
        try:
            col_display = ",".join(DEFAULT_OBJECT_DISPLAY[TYPE_OBJECT_PURCHASE_ORDER])
        except:
            col_display = "id_journal"
        obj.column_display = col_display
        obj.save()
    else:
        col_display = obj.column_display if obj.column_display else "id_po"

    columns = col_display.split(",")
    results = PurchaseOrders.objects.filter(filter_condition).order_by("-created_at")[
        start:end
    ]
    data = [
        {
            "id": item.id,
            "text": get_object_display_based_columns(
                TYPE_OBJECT_PURCHASE_ORDER,
                item,
                columns,
                workspace.timezone,
                request.LANGUAGE_CODE,
            ),
        }
        for item in results
    ]

    return JsonResponse({"results": data, "pagination": {"more": results.exists()}})


@login_or_hubspot_required
def row_detail(request, id, object_type=None):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    page_obj = get_page_object(object_type, lang)
    base_model = page_obj["base_model"]
    manage_obj_link = page_obj["manage_obj_link"]
    print(manage_obj_link)
    id_field = page_obj["id_field"]  # id_inv, id_est, id_rcp, id_ds etc

    object_ = get_object_or_404(base_model, id=id)

    view_id = request.GET.get("view_id", None)
    if view_id:
        view = View.objects.filter(id=view_id).first()
    else:
        # Create Main View
        view, _ = View.objects.get_or_create(
            workspace=workspace, title__isnull=True, page_group_type=object_type
        )
        view_id = view.id

    view_filter, _ = ViewFilter.objects.get_or_create(view=view)
    columns = ast.literal_eval(view_filter.column)

    # association label
    association_labels_sanka_true = AssociationLabel.objects.filter(
        workspace=workspace, object_source=object_type, created_by_sanka=True
    )
    association_label_list_sanka_true = [
        association_label.label for association_label in association_labels_sanka_true
    ]
    association_labels_sanka_false = AssociationLabel.objects.filter(
        workspace=workspace, object_source=object_type, created_by_sanka=False
    )
    association_label_list_sanka_false = [
        str(association_label.id)
        for association_label in association_labels_sanka_false
    ]
    association_label_list = (
        association_label_list_sanka_true + association_label_list_sanka_false
    )
    # association label relation
    related_association_labels = AssociationLabel.objects.filter(
        created_by_sanka=False,
        workspace=workspace,
        object_target__icontains=object_type,
    ).order_by("created_at")
    for related_association_label in related_association_labels:
        association_label_list.append(str(related_association_label.id))

    context = {
        "manage_obj_link": manage_obj_link,
        "object": object_,
        "obj_id": getattr(object_, id_field, None),
        "columns": columns,
        "view_id": view_id,
        "selected_id": request.GET.get("selected_id"),
        "page": request.GET.get("page", 1),
        "object_type": object_type,
        "association_label_list": association_label_list,
    }
    row_target = "data/purchase_order/purchase-order-row.html"
    return render(request, row_target, context)


def select_item(request, object_type):
    item_id = request.GET.get("item_id")
    currency = request.GET.get("currency")

    item_object = ShopTurboItems.objects.filter(id=item_id).first()

    if not item_object:
        return HttpResponse(status=404)

    item_prices = ItemPurchasePrice.objects.filter(
        item=item_object, currency=currency
    ).order_by("-default")

    if not item_prices.exists():
        # Create fake ItemPurchasePrice object
        fake_price = ItemPurchasePrice(
            item=item_object, price=0, default=True, currency=currency
        )
        # If you need it as a queryset
        item_prices = [fake_price]

    item_prices_list = []
    for price in item_prices:
        symbol = get_currency_symbol(currency)
        formatted_price = use_thousand_separator_string_with_currency(
            price.price, currency
        )
        display_text = f"{symbol} {formatted_price}"

        item_prices_list.append({"price": price.price, "text": display_text})

    return JsonResponse(item_prices_list, safe=False)


@login_or_hubspot_required
@require_GET
def purchase_orders_association_drawer(request):
    source = request.GET.get("source", None)
    object_id = request.GET.get("object_id", None)
    page = request.GET.get("page", 1)
    view_id = request.GET.get("view_id", None)
    module = request.GET.get("module", None)
    object_type = request.GET.get("object_type", None)

    obj = None

    try:
        if source == TYPE_OBJECT_INVENTORY_TRANSACTION:
            obj = InventoryTransaction.objects.get(id=object_id)
    except:
        pass

    context = {
        "source": source,
        "object_id": object_id,
        "page": page,
        "view_id": view_id,
        "obj": obj,
        "module": module,
        "object_type": object_type,
        "form_id": uuid.uuid4(),
    }

    return render(
        request, "data/association/default-create-add/purchase-orders.html", context
    )


@login_or_hubspot_required
@require_GET
def get_purchase_order_object_options(request):
    workspace = get_workspace(request.user)
    q = request.GET.get("q")
    page = request.GET.get("page", 1)

    filter_conditions = Q(workspace=workspace, usage_status="active")
    if q:
        q_id = q
        try:
            if q[0] == "#" or q[0] == "0":
                q_id = int(q[1:].replace(" ", ""))
        except Exception as e:
            print(e)
        filter_conditions &= Q(id_po__icontains=q_id) | Q(
            purchase_order_object__item_name__icontains=q
        )
    po_objects = PurchaseOrders.objects.filter(filter_conditions).order_by("-id_po")

    res = []
    ITEMS_PER_PAGE = 30
    po_paginator = Paginator(po_objects, ITEMS_PER_PAGE)
    paginated_po = []
    more_pagination = False
    if page:
        try:
            po_page_content = po_paginator.page(page if page else 1)
            paginated_po = po_page_content.object_list
            more_pagination = po_page_content.has_next()
        except EmptyPage:
            pass

    for po in paginated_po:
        po_dict = {"id": str(po.id), "text": f"#{po.id_po:04d}"}

        res.append(po_dict)
    raise Exception(res)

    context = {"results": res, "pagination": {"more": more_pagination}}

    return JsonResponse(context)
