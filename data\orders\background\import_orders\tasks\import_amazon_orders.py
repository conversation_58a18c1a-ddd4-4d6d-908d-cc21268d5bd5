import ast
from datetime import timedelta
import json
import traceback

from asgiref.sync import sync_to_async
from hatchet_sdk import Context

from data.models import TransferHistory
from utils.amazon import import_amazon_orders as amazon_import_util
from utils.logger import logger

from ..models import ImportOrdersAmazonPayload
from ..workflows import import_amazon_orders
from utils.bgjobs.handler import (
    aio_set_bg_job_completed,
    aio_set_bg_job_failed,
    aio_set_bg_job_running,
)


@import_amazon_orders.task(
    name="ImportAmazonOrdersTask",
    execution_timeout=timedelta(hours=2),
    schedule_timeout=timedelta(hours=1),
)
async def import_amazon_orders_task(
    input: ImportOrdersAmazonPayload, ctx: Context
) -> dict:
    """
    Child task for importing Amazon orders
    """
    logger.info("Run Amazon orders import child task")
    logger.info(f"Input: {input}")

    history_id = input.history_id
    task = None

    try:
        task = await TransferHistory.objects.select_related("workspace", "user").aget(
            id=history_id
        )
        task.keep_alive = True
        await task.asave()
    except TransferHistory.DoesNotExist:
        logger.warning("TransferHistory does not exist")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])

    if input.background_job_id:
        await aio_set_bg_job_running(input.background_job_id)

    try:
        # Process mapping fields
        mapping_custom_fields = (
            input.mapping_custom_fields
            if input.mapping_custom_fields and input.mapping_custom_fields != "None"
            else {}
        )

        # Parse string mappings if needed
        if isinstance(mapping_custom_fields, str):
            mapping_custom_fields = ast.literal_eval(mapping_custom_fields)

        # Process import_filter parameter - parse JSON string to dict
        import_filter = input.import_filter
        if import_filter and isinstance(import_filter, str):
            try:
                import_filter = json.loads(import_filter)
                logger.info(f"Parsed import_filter from JSON string: {import_filter}")
            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(
                    f"Failed to parse import_filter JSON: {e}. Using empty dict."
                )
                import_filter = {}
        elif not import_filter:
            import_filter = {}

        # Refresh timeout to handle potentially long-running Amazon import
        ctx.refresh_timeout("1h")

        # Call the Amazon import utility
        await sync_to_async(amazon_import_util)(
            input.channel_id,
            mapping_custom_fields=mapping_custom_fields,
            default_customer=input.default_customer,
            key_item_field=input.key_item_field,
            how_to_import=input.how_to_import,
            import_filter=import_filter,
            task=task,
            ctx=ctx,
            user=input.user,
        )

        logger.info("Successfully imported Amazon orders")

        if task:
            await task.arefresh_from_db()
            task.status = "completed"
            await task.asave(update_fields=["status"])

        if input.background_job_id:
            await aio_set_bg_job_completed(input.background_job_id)

    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error importing Amazon orders: {str(e)}")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])
