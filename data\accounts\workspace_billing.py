from datetime import timed<PERSON><PERSON>
import stripe
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.contrib import messages
from django.shortcuts import redirect, render, HttpResponse
from django_hosts.resolvers import reverse


from data.constants.properties_constant import *
from data.constants.constant import USAGE_CATEGORIES
from data.constants.custom_pdf_constant import (
    DEFAULT_LINE_ITEM,
    DEFAULT_SEND_FROM,
    DEFAULT_SEND_TO,
    DEFAULT_NOTES,
    DEFAULT_FOOTER,
    DEFAULT_HEADER_BY_SETTING_TYPE,
)
from data.models import *

from utils.billing import credit_card_direct_charge, manual_workspace_charges
from utils.decorator import login_or_hubspot_required
from utils.meter import archive_objects_by_limit, sync_usage
from utils.utility import get_next_month_first_day_timestamp, get_workspace, get_icon_by_name
from utils.workspace import get_permission


from data.accounts.utils import workspace_all

stripe.api_key = settings.STRIPE_SECRET_KEY


@login_or_hubspot_required
def workspace_billing(request):
    page_title = "Workspace Billing"
    menu_key = "workspace"
    page_group_type = "workspace_billing"
    workspace = get_workspace(request.user)
    workspaces, invitations, teammates = workspace_all(request)

    Log.objects.create(
        workspace=workspace,
        user=request.user,
        page_name="workspace",
        sub_page_name="billing",
    )

    # Sync all usage categories to ensure accurate counts
    for category, _ in USAGE_CATEGORIES:
        sync_usage(workspace, category)

    subscription_plan = (
        SubscriptionPlan.objects.filter(
            workspace=workspace,
            subscription_period_started__lt=timezone.now(),
            subscription_period_ended=None,
        )
        .order_by("-created_at")
        .first()
    )
    next_renewal_date = None
    if workspace.subscription_period_ended:
        next_renewal_date = workspace.subscription_period_ended + timedelta(days=1)
    if subscription_plan and subscription_plan.stripe_subscription_id != None:
        try:
            payment = stripe.PaymentMethod.list(
                customer=workspace.stripe_customer_id, type="card"
            )
            payment = payment.data

            # Loop through payment, and where payment[i].id == workspace.stripe_payment_method_id, set as first index
            if len(payment) > 1:
                for i in range(len(payment)):
                    if payment[i].id == workspace.stripe_default_payment_method:
                        payment[0], payment[i] = payment[i], payment[0]
        except:
            payment = None

        status = None
    elif workspace.stripe_customer_id != None:
        try:
            payment = stripe.PaymentMethod.list(
                customer=workspace.stripe_customer_id, type="card"
            )
            payment = payment.data
            # Loop through payment, and where payment[i].id == workspace.stripe_payment_method_id, set as first index
            if len(payment) > 1:
                for i in range(len(payment)):
                    if payment[i].id == workspace.stripe_default_payment_method:
                        payment[0], payment[i] = payment[i], payment[0]
        except Exception:
            # Just in case the stripe customer is deleted from the stripe dashboard
            payment = None
            workspace.stripe_customer_id = None
            workspace.save()

        status = None
    else:
        payment = None
        status = None

    payment_history = PaymentHistory.objects.filter(workspace=workspace).order_by(
        "-created_at"
    )
    base_pricings = BasePricing.objects.filter(
        is_base_plan=False,
        tier=workspace.subscription
        if workspace.subscription != "partner"
        else "standard",
        payment_frequency=MONTHLY,
    ).order_by("order")
    user_role = UserManagement.objects.filter(
        workspace=workspace, user=request.user
    ).first()
    user_role = user_role.type

    permission = get_permission(object_type="workspace_billing", user=request.user)
    if not permission:
        permission = "hide"

    context = {
        "menu_key": menu_key,
        "page_title": page_title,
        "workspace": workspace,
        "workspaces": workspaces,
        "page_group_type": page_group_type,
        "object_type": USER_USAGE_CATEGORY,
        "payment_history": payment_history,
        "status": status,
        "user_role": user_role,
        "payment": payment,
        "page_type": "usage",
        "subscription_plan": subscription_plan,
        "end_of_month": (
            timezone.now() + timedelta(days=32 - timezone.now().day)
        ).replace(day=1)
        - timedelta(days=1),
        "base_pricings": base_pricings,
        "next_renewal_date": next_renewal_date,
        "permission": permission,
    }

    return render(request, "data/account/workspace/workspace_template.html", context)


@login_or_hubspot_required
def workspace_set_default_payment_method(request, payment_id):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    if workspace:
        if not workspace.stripe_customer_id:
            customer = stripe.Customer.create(name=workspace.name)
            workspace.stripe_customer_id = customer.id
            workspace.save()
        stripe.Customer.modify(
            workspace.stripe_customer_id,
            invoice_settings={"default_payment_method": payment_id},
        )
        workspace.stripe_default_payment_method = payment_id
        workspace.save()

        if lang == "ja":
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="あなたの支払い方法は正常に変更されました。",
                type="success",
            )
            return redirect(reverse("workspace_billing", host="app"))
        else:
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="Your payment method has been successfully changed.",
                type="success",
            )
            return redirect(reverse("workspace_billing", host="app"))
    return redirect(reverse("workspace_billing", host="app"))


def sync_workspace_usage(request):
    workspace = get_workspace(request.user)
    
    # Sync all categories regardless of subscription type
    for val in USAGE_CATEGORIES:
        sync_usage(workspace, val[0])
    return HttpResponse()


@login_or_hubspot_required
def billing_confirmation(request):
    workspace = get_workspace(request.user)

    purchase_amount = request.GET.get("purchase_amount", None)

    context = {
        "workspace": workspace,
        "purchase_amount": purchase_amount,
    }

    return render(request, "data/account/billing_confirmation_screen.html", context)


@login_or_hubspot_required
def workspace_starter_tier(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    if not workspace:
        return redirect(reverse("workspace_billing", host="app"))

    # Main Onboarding with Free account
    if workspace.subscription_status == "preactive":
        redirect_url = redirect(reverse("main", host="app") + "?onboard_start=true")

        if request.method != "POST":
            return redirect_url

        # solutions = request.POST.getlist('solutions
        # Haegwan updated - no more solution selection

        solutions = Solution.objects.filter(module=True)

        country = request.POST.get("country")
        if not (solutions and country):
            return redirect_url

        for solution in solutions:
            if solution.menu_ja:
                name_ja = solution.menu_ja
            else:
                name_ja = solution.keyword_ja

            if solution.menu:
                name = solution.menu
            else:
                name = solution.keyword

            icon = get_icon_by_name(name, name_ja)
            Module.objects.get_or_create(
                workspace=workspace,
                name_ja=name_ja,
                name=name,
                icon=icon,
                slug=solution.slug,
                object_values=solution.module_values,
                order=solution.order,
            )

        if country.lower() not in [val[0].lower() for val in HOLIDAY_COUNTRY_CODE]:
            return redirect_url

        workspace.country_code = country.upper()
        currency = "USD"
        if country.lower() == "jp":
            currency = "JPY"
        workspace.currencies = str([currency])
        workspace.payment_currency = currency
        workspace.save()

        Widget.objects.get_or_create(
            workspace=workspace, special_type="onboarding", order=0
        )

        templates = MasterPdfTemplate.objects.all()
        master_templates = {}
        for template in templates:
            if template.setting_type not in master_templates:
                master_templates[template.setting_type] = []

            master_templates[template.setting_type].append(template)

        currencies = workspace.currencies.upper() if workspace.currencies else ""
        country_code = workspace.country_code.upper() if workspace.country_code else ""
        app_setting = AppSetting.objects.filter(
            workspace=workspace, app_target="shopturbo"
        ).first()
        if not app_setting:
            app_setting = AppSetting.objects.create(
                workspace=workspace, app_target="shopturbo"
            )

        app_setting = AppSetting.objects.filter(
            workspace=workspace, app_target="shopturbo"
        ).first()
        if not app_setting:
            app_setting = AppSetting.objects.create(
                workspace=workspace, app_target="shopturbo"
            )

        master_pdfs = MasterPdf.objects.all()
        default_pdf = {
            "estimates": None,
            "invoices": None,
            "receipts": None,
            "slips": None,
            "delivery_slips": None,
            "purchaseorder": None,
            "commerce_orders": None,
        }

        for master_pdf in master_pdfs:
            default_header = DEFAULT_HEADER_BY_SETTING_TYPE.get(
                master_pdf.object_type, DEFAULT_LINE_ITEM
            )
            template = PdfTemplate.objects.create(
                workspace=workspace,
                master_pdf=master_pdf,
                line_item_table=DEFAULT_LINE_ITEM,
                line_item_table_display="",
                line_item_table_bg_color="#ffffff",
                line_item_table_font_color="#000000",
                line_item_table_font_size="12",
                font_type="",
                header_block=default_header,
                header_block_display="",
                payment_block="",
                payment_block_display="",
                send_from_block=DEFAULT_SEND_FROM,
                send_from_block_display="",
                send_to_block=DEFAULT_SEND_TO,
                send_to_block_display="",
                ship_to_block="",
                ship_to_block_display="",
                user_block="",
                user_block_display="",
                notes_block=DEFAULT_NOTES,
                notes_block_display="",
                information_block="",
                information_block_display="",
                send_from_adds_block="",
                send_from_adds_block_display="",
                send_to_adds_block="",
                send_to_adds_block_display="",
                sign_1_block="",
                sign_2_block="",
                pdf_header_template="",
                pdf_footer_template=DEFAULT_FOOTER,
            )

            if "pattern-1" in master_pdf.path:
                default_pdf[master_pdf.object_type] = template.id

        app_setting.purchase_order_pdf_template = default_pdf["purchaseorder"]
        app_setting.purchase_order_pdf_line_item_table = DEFAULT_LINE_ITEM
        app_setting.purchase_order_pdf_header_block = "id_po,date"
        app_setting.purchase_order_pdf_send_from_block = DEFAULT_SEND_FROM
        app_setting.purchase_order_pdf_send_to_block = DEFAULT_SEND_TO
        app_setting.purchase_order_pdf_notes_block = DEFAULT_NOTES
        app_setting.purchase_order_pdf_footer_template = DEFAULT_FOOTER

        app_setting.order_pdf_template = default_pdf["commerce_orders"]
        app_setting.order_pdf_line_item_table = DEFAULT_LINE_ITEM
        app_setting.order_pdf_header_block = "order_id,order_at"
        app_setting.order_pdf_send_from_block = ""
        app_setting.order_pdf_send_to_block = DEFAULT_SEND_TO
        app_setting.order_pdf_notes_block = "memo"
        app_setting.order_pdf_footer_template = DEFAULT_FOOTER

        # shopturbo
        for st_ in ["delivery_slip", "estimate", "invoice", "receipt", "slip"]:
            app_child_setting = AppSettingChild.objects.filter(
                app_setting=app_setting, target_name=f"{st_}_pdf_template"
            ).first()
            if not app_child_setting:
                app_child_setting = AppSettingChild.objects.create(
                    app_setting=app_setting, target_name=f"{st_}_pdf_template"
                )

            tmp = f"{st_}s"

            app_child_setting.value = default_pdf[tmp]
            app_child_setting.save()

        app_setting.save()

        updated_target = [
            "_pdf_line_item_table",
            "_pdf_header_block",
            "_pdf_send_from_block",
            "_pdf_send_to_block",
            "_pdf_notes_block",
            "_pdf_footer_template",
        ]
        for child in [
            TYPE_OBJECT_INVOICE,
            TYPE_OBJECT_ESTIMATE,
            TYPE_OBJECT_DELIVERY_NOTE,
            TYPE_OBJECT_RECEIPT,
        ]:
            for target in updated_target:
                app_setting_child = AppSettingChild.objects.filter(
                    app_setting=app_setting, target_name=f"{child[:-1]}{target}"
                ).first()
                if not app_setting_child:
                    app_setting_child = AppSettingChild.objects.create(
                        app_setting=app_setting, target_name=f"{child[:-1]}{target}"
                    )

                if target == "_pdf_line_item_table":
                    app_setting_child.value = DEFAULT_LINE_ITEM
                elif target == "_pdf_header_block":
                    if child == TYPE_OBJECT_INVOICE:
                        app_setting_child.value = "id_inv,start_date"
                    elif child == TYPE_OBJECT_ESTIMATE:
                        app_setting_child.value = "id_est,start_date"
                    elif child == TYPE_OBJECT_DELIVERY_NOTE:
                        app_setting_child.value = "id_ds,start_date"
                    elif child == TYPE_OBJECT_RECEIPT:
                        app_setting_child.value = "id_rcp,start_date"
                elif target == "_pdf_send_from_block":
                    app_setting_child.value = DEFAULT_SEND_FROM
                elif target == "_pdf_send_to_block":
                    app_setting_child.value = DEFAULT_SEND_TO
                elif target == "_pdf_notes_block":
                    app_setting_child.value = DEFAULT_NOTES
                elif target == "_pdf_footer_template":
                    app_setting_child.value = DEFAULT_FOOTER

                app_setting_child.save()

        now = timezone.now().replace(hour=0, minute=0, second=0, microsecond=0)
        workspace.subscription_period_started = now
        workspace.subscription_period_ended = now + relativedelta(months=1)
        workspace.subscription = STARTER_PRICING_TIER
        workspace.subscription_status = "active"
        workspace.custom_price = 0
        if lang == "ja":
            workspace.timezone = "Asia/Tokyo"
        else:
            workspace.timezone = "UTC"
        workspace.save()

        # Create Usage Limit
        for val in USAGE_CATEGORIES:
            # Use .first() instead of .get() to handle potential duplicates gracefully
            # The unique constraint will prevent new duplicates, but this handles existing ones
            base_pricing = (
                BasePricing.objects.filter(
                    tier=STARTER_PRICING_TIER,
                    category=val[0],
                    payment_frequency=MONTHLY,
                    is_base_plan=False,
                )
                .order_by("created_at")
                .first()
            )

            if not base_pricing:
                # Log error and skip this category if no BasePricing record found
                print(f"Warning: No BasePricing record found for category {val[0]}")
                continue

            usage_limit, _ = UsageLimit.objects.get_or_create(
                workspace=workspace, category=val[0]
            )
            usage_limit.value = base_pricing.limit
            usage_limit.save()

            meter, _ = Meter.objects.get_or_create(workspace=workspace, category=val[0])
            meter.count = 0
            meter.save()

        if lang == "ja":
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="ワークスペースが正常に有効化されました。",
                type="success",
            )
            return redirect_url
        else:
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="You have successfully activated your workspace.",
                type="success",
            )
            return redirect_url

    # From other subscription
    else:
        redirect_url = redirect(reverse("main", host="app"))

        for val in USAGE_CATEGORIES:
            # Use .first() instead of .get() to handle potential duplicates gracefully
            base_pricing = (
                BasePricing.objects.filter(
                    tier=STARTER_PRICING_TIER,
                    category=val[0],
                    payment_frequency=MONTHLY,
                    is_base_plan=False,
                )
                .order_by("created_at")
                .first()
            )

            if not base_pricing:
                # Log error and skip this category if no BasePricing record found
                print(f"Warning: No BasePricing record found for category {val[0]}")
                continue

            usage_limit, _ = UsageLimit.objects.get_or_create(
                workspace=workspace, category=val[0]
            )
            usage_limit.value = base_pricing.limit
            usage_limit.save()

            meter, _ = Meter.objects.get_or_create(workspace=workspace, category=val[0])
            if meter.count is None:
                meter.count = 0
                meter.save()
            if meter.count > usage_limit.value:
                meter.count = usage_limit.value
                meter.save()

            # Set the total active storages based on usage limit.
            archive_objects_by_limit(workspace, val[0])

        workspace.subscription_period_started = None
        workspace.subscription_period_ended = None
        workspace.subscription = STARTER_PRICING_TIER
        workspace.subscription_status = "active"
        workspace.save()

        return redirect_url


@login_or_hubspot_required
def workspace_billing_change_plan(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)

    if request.method == "GET":
        base_plans = BasePricing.objects.filter(
            is_base_plan=True, payment_frequency=MONTHLY
        ).order_by("order")
        base_pricing_items = BasePricing.objects.filter(
            is_base_plan=False, payment_frequency=MONTHLY
        ).order_by("order")
        usage_limits = UsageLimit.objects.filter(workspace=workspace)
        currency_dict = {
            code: text_symbol
            for code, text_symbol, _ in CURRENCY_MODEL
            if code in ["JPY", "USD"]
        }

        # Get default currency from workspace payment_currency
        default_currency = None
        if workspace.payment_currency:
            default_currency = workspace.payment_currency
            print(f"Using workspace payment_currency as default: {default_currency}")

        context = {
            "base_plans": base_plans,
            "base_pricing_items": base_pricing_items,
            "usage_limits": usage_limits,
            "LANGUAGE_CODE": lang,
            "currencies": currency_dict,
            "default_currency": default_currency,
        }
        return render(request, "data/account/drawer_change_subscription.html", context)

    elif request.method == "POST":
        print(request.POST)
        plan_id = request.POST.get("plan_id")
        currency = request.POST.get("currency")
        proration_date = request.POST.get("proration_date")
        if not (plan_id and proration_date and currency):
            print("No plan id or proration date")
            if lang == "ja":
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message="サブスクリプションの変更に失敗しました: 入力の形式が正しくありません。",
                    type="error",
                )
            else:
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message="Change subscription failed: Input was malformed.",
                    type="error",
                )
            return redirect("workspace_billing")

        try:
            plan = SubscriptionPlan.objects.get(id=plan_id, workspace=workspace)
        except SubscriptionPlan.DoesNotExist:
            print("Subscription plan does not exist")
            if lang == "ja":
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message="サブスクリプションの変更に失敗しました: 入力の形式が正しくありません。",
                    type="error",
                )
            else:
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message="Change subscription failed: Input was malformed.",
                    type="error",
                )
            return redirect("workspace_billing")

        # Delete other existing workspace Subscription Plan
        try:
            SubscriptionPlan.objects.filter(
                workspace=workspace,
                subscription_period_started=None,
                subscription_period_ended=None,
            ).exclude(id=plan_id).delete()
        except Exception as e:
            print(f"Error deleting other subscription plans: {e}")
            pass

        workspace.payment_currency = currency
        workspace.save()

        print("stripe_items", plan.stripe_items)

        if plan.stripe_items:
            if plan.stripe_subscription_id:
                invoice = stripe.Invoice.create_preview(
                    customer=workspace.stripe_customer_id,
                    subscription=plan.stripe_subscription_id,
                    subscription_details={
                        "items": plan.stripe_items,
                        "proration_date": proration_date,
                        "billing_cycle_anchor": "unchanged",
                        "proration_behavior": "always_invoice",
                    },
                )

                prorate_cost = (
                    invoice.total
                    if ast.literal_eval(workspace.currencies)[0].lower() == "jpy"
                    else invoice.total / 100
                )
                if prorate_cost != plan.prorate_cost:
                    print(prorate_cost, plan.prorate_cost)
                    if lang == "ja":
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="サブスクリプションの変更に失敗しました: 入力の形式が正しくありません。",
                            type="error",
                        )
                    else:
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="Change subscription failed: Input was malformed.",
                            type="error",
                        )
                    return redirect("workspace_billing")

                subscription = stripe.Subscription.modify(
                    plan.stripe_subscription_id,
                    items=plan.stripe_items,
                    proration_date=proration_date,
                    proration_behavior="always_invoice",
                )
                print("subscription updated:", subscription.id)
            else:
                if not workspace.stripe_customer_id:
                    customer = stripe.Customer.create(name=workspace.name)
                    workspace.stripe_customer_id = customer.id
                    workspace.save()

                invoice = stripe.Invoice.create_preview(
                    customer=workspace.stripe_customer_id,
                    subscription_details={
                        "items": plan.stripe_items,
                        "start_date": proration_date,
                        "billing_cycle_anchor": get_next_month_first_day_timestamp(),
                    },
                )
                prorate_cost = (
                    invoice.total
                    if ast.literal_eval(workspace.currencies)[0].lower() == "jpy"
                    else invoice.total / 100
                )
                if prorate_cost != plan.prorate_cost:
                    print(prorate_cost, plan.prorate_cost)
                    if lang == "ja":
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="サブスクリプションの変更に失敗しました: 入力の形式が正しくありません。",
                            type="error",
                        )
                    else:
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="Change subscription failed: Input was malformed.",
                            type="error",
                        )
                    return redirect("workspace_billing")

                subscription = stripe.Subscription.create(
                    customer=workspace.stripe_customer_id,
                    items=plan.stripe_items,
                    billing_cycle_anchor=get_next_month_first_day_timestamp(),
                    payment_settings={"payment_method_types": ["card"]},
                )
                print("subscription cerated:", subscription.id)

            # Update the workspace subscription details directly
            workspace.subscription = (
                plan.base_plan.tier if plan.base_plan else STARTER_PRICING_TIER
            )
            workspace.subscription_status = "active"
            workspace.save()

            plan.stripe_subscription_id = subscription.id
            plan.subscription_period_started = timezone.now()
            plan.save()

            # Update usage limits based on the plan
            if plan.base_plan:
                for val in USAGE_CATEGORIES:
                    # Use .first() instead of .get() to handle potential duplicates gracefully
                    base_pricing = (
                        BasePricing.objects.filter(
                            tier=plan.base_plan.tier,
                            category=val[0],
                            payment_frequency=MONTHLY,
                            is_base_plan=False,
                        )
                        .order_by("created_at")
                        .first()
                    )

                    if not base_pricing:
                        # Log error and skip this category if no BasePricing record found
                        print(
                            f"Warning: No BasePricing record found for tier {plan.base_plan.tier}, category {val[0]}"
                        )
                        continue

                    usage_limit, _ = UsageLimit.objects.get_or_create(
                        workspace=workspace, category=val[0]
                    )
                    usage_limit.value = base_pricing.limit
                    usage_limit.save()

            if lang == "ja":
                msg = "プランは正常にアップグレードされました。"
            else:
                msg = "Your plan was upgraded successfully."
            Notification.objects.create(
                workspace=workspace, user=request.user, message=msg, type="success"
            )
            messages.success(request, msg)
        else:
            # Use starter plan (all free)

            if plan and plan.stripe_subscription_id:
                # Cancel the subscription at the end of the period
                stripe.Subscription.modify(
                    plan.stripe_subscription_id, cancel_at_period_end=True
                )
                print("Subscription canceled:", plan.stripe_subscription_id)

                # But also update the workspace immediately for local development
                workspace.subscription = STARTER_PRICING_TIER
                workspace.subscription_status = "active"
                workspace.save()

                # Set the subscription period for the plan
                plan.subscription_period_started = timezone.now()
                plan.save()
            else:
                workspace.subscription = STARTER_PRICING_TIER
                workspace.subscription_status = "active"
                workspace.save()

            # Update usage limits for the starter plan
            for val in USAGE_CATEGORIES:
                category = val[0]
                # Use .first() instead of .get() to handle potential duplicates gracefully
                base_pricing = (
                    BasePricing.objects.filter(
                        tier=STARTER_PRICING_TIER,
                        category=category,
                        payment_frequency=MONTHLY,
                        is_base_plan=False,
                    )
                    .order_by("created_at")
                    .first()
                )

                if not base_pricing:
                    # Log error and skip this category if no BasePricing record found
                    print(
                        f"Warning: No BasePricing record found for category {category}"
                    )
                    continue

                usage_limit, _ = UsageLimit.objects.get_or_create(
                    workspace=workspace, category=category
                )
                usage_limit.value = base_pricing.limit
                usage_limit.save()

            if lang == "ja":
                msg = "プランのダウングレードが完了しました"
            else:
                msg = "Your plan was downgraded successfully."
            Notification.objects.create(
                workspace=workspace, user=request.user, message=msg, type="success"
            )
            messages.success(request, msg)
        return redirect(reverse("workspace_billing", host="app"))


def subscription_plan_price(request):
    # Get currency from request or use workspace payment_currency
    currency = request.GET.get("currency")
    id = request.GET.get("id")

    if not id:
        return HttpResponse(status=400)

    workspace = get_workspace(request.user)

    # If currency not provided, use workspace payment_currency
    if not currency:
        if workspace.payment_currency:
            currency = workspace.payment_currency
            print(f"Using workspace payment_currency: {currency}")
        else:
            # Default to JPY for Japanese language, USD otherwise
            lang = request.LANGUAGE_CODE
            currency = "jpy" if lang == "ja" else "usd"
            print(f"Using default currency based on language: {currency}")

    currency = currency.lower()
    print(f"Final currency being used in subscription_plan_price: {currency}")

    lang = request.LANGUAGE_CODE
    base_plans = BasePricing.objects.filter(
        is_base_plan=True, payment_frequency=MONTHLY
    ).order_by("order")
    base_pricing_items = BasePricing.objects.filter(
        is_base_plan=False, payment_frequency=MONTHLY
    ).order_by("order")
    usage_limits = UsageLimit.objects.filter(workspace=workspace)
    context = {
        "base_plans": base_plans,
        "base_pricing_items": base_pricing_items,
        "usage_limits": usage_limits,
        "LANGUAGE_CODE": lang,
        "currency": currency,
        "id": id,
        "default_tier": workspace.subscription,
    }
    return render(request, "data/account/subscription-price-estimation.html", context)


@login_or_hubspot_required
def change_plan_summary(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    # Prioritize workspace payment_currency if available
    if workspace.payment_currency:
        currency = workspace.payment_currency
        print(f"Using workspace payment_currency: {currency}")
    else:
        # Get currency from request
        currency = request.GET.get("currency")
        if not currency:
            # Try to get currency from workspace currencies
            if workspace.currencies:
                try:
                    import ast

                    currencies = ast.literal_eval(workspace.currencies)
                    if currencies and len(currencies) > 0:
                        currency = currencies[0]
                        print(f"Using workspace currency: {currency}")
                except Exception as e:
                    print(f"Error getting workspace currency: {e}")

            # If still no currency, default based on language
            if not currency:
                currency = "jpy" if lang == "ja" else "usd"
                print(f"Using default currency based on language: {currency}")

    currency = currency.lower()
    print(f"Final currency being used: {currency}")

    redirect_url = reverse("workspace_billing", host="app")
    response = HttpResponse()
    response["HX-Redirect"] = redirect_url

    tier = request.GET.get("tier")
    if tier not in [val[0] for val in PRICING_TIER_OPTIONS]:
        return response

    try:
        SubscriptionPlan.objects.filter(
            workspace=workspace,
            subscription_period_started=None,
            subscription_period_ended=None,
        ).delete()
    except Exception as e:
        print(e)
        pass  # if no subscription plans at all

    plan = (
        SubscriptionPlan.objects.filter(
            workspace=workspace,
            subscription_period_started__isnull=False,
            subscription_period_ended=None,
        )
        .order_by("-created_at")
        .first()
    )

    is_subscription_changed = False
    if workspace.subscription != tier:
        is_subscription_changed = True

    # Use .first() instead of .get() to handle potential duplicates gracefully
    base_plan = (
        BasePricing.objects.filter(
            tier=tier, payment_frequency=MONTHLY, is_base_plan=True
        )
        .order_by("created_at")
        .first()
    )

    if not base_plan:
        # Log error and return error response if no base plan found
        print(f"Error: No base plan found for tier {tier}")
        return response

    total_subscription_cost = 0
    total_prorate_cost = 0

    # Add Base plan pricing
    if currency == "jpy":
        total_subscription_cost += base_plan.base_price_jpy
        print(f"Using JPY base price: {base_plan.base_price_jpy}")
    else:
        total_subscription_cost += base_plan.base_price_usd
        print(f"Using USD base price: {base_plan.base_price_usd}")

    target_storages = []
    items = []
    proration_date = int(timezone.now().timestamp())
    existing_stripe_prices = []
    if plan and plan.stripe_subscription_id:
        subscription = stripe.Subscription.retrieve(plan.stripe_subscription_id)
        existing_stripe_prices = [
            {"id": val.id, "price_id": val.price.id}
            for val in subscription["items"].data
        ]

    print("existing_stripe_prices", existing_stripe_prices)

    # Add more or reduce storage(s) on the same base plan
    if base_plan.tier == workspace.subscription:
        print("# Add more or reduce storage(s) on the same base plan")
        next_subscription_plan = SubscriptionPlan.objects.create(
            workspace=workspace,
            base_plan=base_plan,
            stripe_items=items,
            cost=total_subscription_cost,
            prorate_cost=total_prorate_cost,
            stripe_subscription_id=plan.stripe_subscription_id if plan else None,
        )

        # Check selected storage plans and calculate the cost
        category = "users"
        target_storage = {}

        try:
            amount = int(request.GET.get(f"{tier}-{category}", 0))
        except:
            return response

        # Use .first() instead of .get() to handle potential duplicates gracefully
        base_pricing = (
            BasePricing.objects.filter(
                tier=base_plan.tier,
                category=category,
                payment_frequency=MONTHLY,
                is_base_plan=False,
            )
            .order_by("created_at")
            .first()
        )

        if not base_pricing:
            # Log error and return error response if no BasePricing record found
            print(
                f"Error: No BasePricing record found for tier {base_plan.tier}, category {category}"
            )
            return response
        price_id = (
            base_pricing.stripe_price_id_jpy
            if currency == "jpy"
            else base_pricing.stripe_price_id_usd
        )
        target_storage["category"] = category
        target_storage["amount"] = amount
        target_storage["title"] = (
            base_pricing.title_ja if lang == "ja" else base_pricing.title
        )

        # Validate stroage amount input
        if category != "users":
            if amount % base_pricing.limit != 0:
                return response

        current_storage_limit, created = UsageLimit.objects.get_or_create(
            workspace=workspace, category=category
        )
        if created:
            current_storage_limit.value = base_pricing.limit
            current_storage_limit.save()
        target_storage["current_amount"] = current_storage_limit.value

        # Compare target storage with current storage
        if amount != current_storage_limit.value:
            is_subscription_changed = True

        # User reduced storage to the free storage.
        if amount == base_pricing.limit:
            target_storage["cost"] = 0
            target_storage["quantity"] = 0
            target_storages.append(target_storage)

            # Remove the old additional storage from subscription
            for val in existing_stripe_prices:
                if price_id == val["price_id"]:
                    items.append({"id": val["id"], "deleted": True})
                    print("c", items)
        else:
            # Count target storage cost
            quantity = int((amount - base_pricing.limit))
            target_storage["quantity"] = quantity
            if currency == "jpy":
                storage_cost = base_pricing.base_price_jpy * quantity
                total_subscription_cost += storage_cost
                target_storage["cost"] = storage_cost
                print(
                    f"Using JPY storage price: {base_pricing.base_price_jpy} x {quantity} = {storage_cost}"
                )
            else:
                storage_cost = base_pricing.base_price_usd * quantity
                total_subscription_cost += storage_cost
                target_storage["cost"] = storage_cost
                print(
                    f"Using USD storage price: {base_pricing.base_price_usd} x {quantity} = {storage_cost}"
                )

            is_items_updated = False
            # Update existing storage quantity on the subscription
            for val in existing_stripe_prices:
                if price_id == val["price_id"]:
                    is_items_updated = True
                    items.append(
                        {
                            "id": val["id"],
                            "price": price_id,
                            "quantity": quantity,
                        }
                    )
                    print("d", items)

            # if new storage is not registered yet to the subscription
            if not is_items_updated:
                # print(stripe.Price.retrieve(price_id))
                items.append({"price": price_id, "quantity": quantity})
                print("e", items)
            target_storages.append(target_storage)

            StoragePlan.objects.create(
                subscription=next_subscription_plan,
                category=base_pricing,
                amount=amount,
            )

        print(items)
        if not is_subscription_changed:
            if lang == "ja":
                return HttpResponse("サブスクリプションに変更はありません。")
            else:
                return HttpResponse("You have no changes to your subscription.")

        # if removed all of the items with no base plan
        cancel_subscription = True
        for target_storage in target_storages:
            if "quantity" in target_storage and target_storage["quantity"] > 0:
                cancel_subscription = False
                break

        if cancel_subscription:
            items = []
        else:
            if plan and plan.stripe_subscription_id:
                invoice = stripe.Invoice.create_preview(
                    customer=workspace.stripe_customer_id,
                    subscription=plan.stripe_subscription_id,
                    subscription_details={
                        "items": items,
                        "proration_date": proration_date,
                        "billing_cycle_anchor": "unchanged",
                        "proration_behavior": "always_invoice",
                    },
                )
            else:
                invoice = stripe.Invoice.create_preview(
                    customer=workspace.stripe_customer_id,
                    subscription_details={
                        "items": items,
                        "start_date": proration_date,
                        "billing_cycle_anchor": get_next_month_first_day_timestamp(),
                    },
                )

            # print(invoice)
            total_prorate_cost = (
                invoice.total if currency == "jpy" else invoice.total / 100
            )

        next_subscription_plan.stripe_items = items
        next_subscription_plan.cost = total_subscription_cost
        next_subscription_plan.prorate_cost = total_prorate_cost
        next_subscription_plan.save()

    # User upgrade base plan from tier
    elif (
        workspace.subscription == STARTER_PRICING_TIER and tier == STANDARD_PRICING_TIER
    ):
        print("# User upgrade base plan")
        is_subscription_changed = True
        if plan and plan.stripe_subscription_id:
            # Delete all price items from the subscription
            try:
                subscription = stripe.Subscription.retrieve(plan.stripe_subscription_id)
                for val in subscription["items"].data:
                    items.append({"id": val.id, "deleted": True})
            except:
                pass

        # Add the base plan price
        items.append(
            {
                "price": base_plan.stripe_price_id_jpy
                if currency == "jpy"
                else base_plan.stripe_price_id_usd,
                "quantity": 1,
            }
        )

        next_subscription_plan = SubscriptionPlan.objects.create(
            workspace=workspace,
            base_plan=base_plan,
            stripe_items=items,
            cost=total_subscription_cost,
        )

        # Check selected storage plans and calculate the cost
        category = "users"
        target_storage = {}

        try:
            amount = int(request.GET.get(f"{tier}-{category}", 0))
        except:
            return response

        # Use .first() instead of .get() to handle potential duplicates gracefully
        base_pricing = (
            BasePricing.objects.filter(
                tier=base_plan.tier,
                category=category,
                payment_frequency=MONTHLY,
                is_base_plan=False,
            )
            .order_by("created_at")
            .first()
        )

        if not base_pricing:
            # Log error and return error response if no BasePricing record found
            print(
                f"Error: No BasePricing record found for tier {base_plan.tier}, category {category}"
            )
            return response
        price_id = (
            base_pricing.stripe_price_id_jpy
            if currency == "jpy"
            else base_pricing.stripe_price_id_usd
        )
        print(price_id)
        target_storage["category"] = category
        target_storage["amount"] = amount
        target_storage["title"] = (
            base_pricing.title_ja if lang == "ja" else base_pricing.title
        )

        # Validate stroage amount input. Also if no change, skip it
        if amount - base_pricing.limit > 0:
            current_storage_limit, created = UsageLimit.objects.get_or_create(
                workspace=workspace, category=category
            )
            if created:
                current_storage_limit.value = base_pricing.limit
                current_storage_limit.save()
            target_storage["current_amount"] = current_storage_limit.value

            quantity = int(amount - base_pricing.limit)
            target_storage["quantity"] = quantity
            if currency == "jpy":
                storage_cost = base_pricing.base_price_jpy * quantity
                total_subscription_cost += storage_cost
                target_storage["cost"] = storage_cost
                print(
                    f"Upgrade: Using JPY storage price: {base_pricing.base_price_jpy} x {quantity} = {storage_cost}"
                )
            else:
                storage_cost = base_pricing.base_price_usd * quantity
                total_subscription_cost += storage_cost
                target_storage["cost"] = storage_cost
                print(
                    f"Upgrade: Using USD storage price: {base_pricing.base_price_usd} x {quantity} = {storage_cost}"
                )

            # print(stripe.Price.retrieve(price_id))
            items.append({"price": price_id, "quantity": quantity})
            print("e", items)
            target_storages.append(target_storage)

            StoragePlan.objects.create(
                subscription=next_subscription_plan,
                category=base_pricing,
                amount=amount,
            )

        if plan and plan.stripe_subscription_id:
            invoice = stripe.Invoice.create_preview(
                customer=workspace.stripe_customer_id,
                subscription=plan.stripe_subscription_id,
                subscription_details={
                    "items": items,
                    "proration_date": proration_date,
                    "billing_cycle_anchor": "unchanged",
                    "proration_behavior": "always_invoice",
                },
            )
            print(invoice)
        else:
            invoice = stripe.Invoice.create_preview(
                customer=workspace.stripe_customer_id,
                subscription_details={
                    "items": items,
                    "start_date": proration_date,
                    "billing_cycle_anchor": get_next_month_first_day_timestamp(),
                },
            )

        total_prorate_cost = invoice.total if currency == "jpy" else invoice.total / 100
        next_subscription_plan.prorate_cost = total_prorate_cost
        next_subscription_plan.save()

    # User downgrade the base plan
    elif (
        workspace.subscription == STANDARD_PRICING_TIER and tier == STARTER_PRICING_TIER
    ):
        print("# User downgrade the base plan")
        # No need to create stripe items list

        is_subscription_changed = True
        total_prorate_cost = 0
        total_subscription_cost = 0

        # Populate target storages with base pricing
        for base_pricing in BasePricing.objects.filter(
            tier=tier, payment_frequency=MONTHLY, is_base_plan=False
        ):
            target_storage = {}
            target_storage["title"] = (
                base_pricing.title_ja if lang == "ja" else base_pricing.title
            )
            current_storage, created = UsageLimit.objects.get_or_create(
                workspace=workspace, category=base_pricing.category
            )
            if created:
                current_storage.value = base_pricing.limit
                current_storage.save()
            target_storage["current_amount"] = current_storage.value
            target_storage["amount"] = base_pricing.limit
            target_storages.append(target_storage)

        next_subscription_plan = SubscriptionPlan.objects.create(
            workspace=workspace,
            stripe_items=items,
            cost=total_subscription_cost,
            prorate_cost=total_prorate_cost,
        )

    if not is_subscription_changed:
        return response

    # Populate target storages with base pricing
    base_pricings = BasePricing.objects.filter(
        tier=tier, payment_frequency=MONTHLY, is_base_plan=False
    )
    if target_storages:
        base_pricings = base_pricings.exclude(category="users")
    for base_pricing in base_pricings:
        target_storage = {}
        target_storage["title"] = (
            base_pricing.title_ja if lang == "ja" else base_pricing.title
        )
        current_storage, created = UsageLimit.objects.get_or_create(
            workspace=workspace, category=base_pricing.category
        )
        if created:
            current_storage.value = base_pricing.limit
            current_storage.save()
        if current_storage.value != base_pricing.limit:
            target_storage["current_amount"] = current_storage.value
            target_storage["amount"] = base_pricing.limit
            target_storages.append(target_storage)

    context = {
        "current_base_plan": BasePricing.objects.get(
            tier=workspace.subscription, payment_frequency=MONTHLY, is_base_plan=True
        ),
        "plan_id": next_subscription_plan.id,
        "base_plan": base_plan,
        "base_pricings": BasePricing.objects.filter(
            tier=workspace.subscription, payment_frequency=MONTHLY, is_base_plan=False
        ),
        "target_storages": target_storages,
        "total_cost": total_subscription_cost,
        "prorated_cost": total_prorate_cost,
        "payment_methods": BillingMethod.objects.filter(workspace=workspace),
        "proration_date": proration_date,
        "currency": currency,
    }
    return render(request, "data/account/change-plan-summary.html", context)


@login_or_hubspot_required
def workspace_billing_success(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)

    if workspace:
        redirect_url = redirect(reverse("workspace_billing", host="app"))
        stripe_checkout_session_id = request.GET.get("session_id")
        setup_payment = request.GET.get("setup_payment")
        tier = request.GET.get("tier")
        mode = request.GET.get("mode")

        # Validate stripe checkout session
        try:
            checkout_session = stripe.checkout.Session.retrieve(
                stripe_checkout_session_id
            )
        except:
            return redirect(reverse("main", host="app"))

        if workspace.subscription_status == "preactive":
            redirect_url = redirect(reverse("main", host="app"))

            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message="Your account is now ready. You can learn how to use Sanka anytime on our help center.",
                message_ja="アカウントのセットアップが完了しました。Sankaの使い方については、いつでもヘルプセンターでも学べます。",
                cta_text="Visit Help Center",
                cta_text_ja="ヘルプセンターを見る",
                cta_target=reverse("help", host="static"),
                type="success",
            )

        # Choose Default Payment
        payment_methods = stripe.PaymentMethod.list(
            customer=workspace.stripe_customer_id, type="card"
        )
        default_payment_method = payment_methods.data[0]
        print(payment_methods)

        if not workspace.stripe_default_payment_method:
            stripe.Customer.modify(
                workspace.stripe_customer_id,
                invoice_settings={"default_payment_method": default_payment_method.id},
            )

        workspace.stripe_checkout_session = stripe_checkout_session_id
        workspace.subscription_status = "active"
        workspace.stripe_default_payment_method = default_payment_method.id
        workspace.save()

        BillingMethod.objects.get_or_create(
            workspace=workspace,
            payment_method_id=workspace.stripe_default_payment_method,
            customer_id=workspace.stripe_customer_id,
        )

        if workspace.stripe_customer_id != None:
            if setup_payment == "true":
                if lang == "ja":
                    Notification.objects.create(
                        workspace=get_workspace(request.user),
                        user=request.user,
                        message="新しい支払い方法がワークスペースに追加されました。",
                        type="success",
                    )
                    return redirect_url
                else:
                    Notification.objects.create(
                        workspace=get_workspace(request.user),
                        user=request.user,
                        message="New payment method has been added to your workspace.",
                        type="success",
                    )
                    return redirect_url
            else:
                if lang == "ja":
                    Notification.objects.create(
                        workspace=get_workspace(request.user),
                        user=request.user,
                        message="ワークスペースが正常に有効化されました。",
                        type="success",
                    )
                    return redirect_url
                else:
                    Notification.objects.create(
                        workspace=get_workspace(request.user),
                        user=request.user,
                        message="You have successfully activated your workspace.",
                        type="success",
                    )
                    return redirect_url

        else:
            if lang == "ja":
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="申し訳ございません。数分後にもう一度お試しください。\n ご不明な点がございましたら、お問い合わせください。",
                    type="error",
                )
            else:
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="Sorry. Please try again in a few minutes.\n Contact us if you have any questions.",
                    type="error",
                )
            return redirect_url

    else:
        return redirect(reverse("workspace_billing", host="app"))


@login_or_hubspot_required
def workspace_delete_payment_method(request, payment_id):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    if workspace:
        stripe.PaymentMethod.detach(
            payment_id,
        )

        if workspace.stripe_default_payment_method == payment_id:
            workspace.stripe_default_payment_method = None
            workspace.save()

        BillingMethod.objects.filter(
            workspace=workspace, payment_method_id=payment_id
        ).delete()

        if lang == "ja":
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="あなたの支払い方法は正常に削除されました。",
                type="success",
            )
            return redirect(reverse("workspace_billing", host="app"))
        else:
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="Your payment method has been successfully deleted.",
                type="success",
            )
            return redirect(reverse("workspace_billing", host="app"))
    return redirect(reverse("workspace_billing", host="app"))


@login_or_hubspot_required
def credit_purchase(request):
    workspace = get_workspace(request.user)
    credit = request.POST.get("credit")

    if credit:
        credit = float(credit)
        resp = credit_card_direct_charge(
            workspace, credit, payment_type="top_up_credit"
        )
        if resp:
            workspace.credit += credit
            workspace.save()

    return redirect(reverse("workspace_billing", host="app"))


@login_or_hubspot_required
def workspace_subscription(request):
    workspace = get_workspace(request.user)
    if request.method == "POST":
        if "cancel" in request.POST:
            workspace.subscription_status = "canceled"
            workspace.save()

        elif "active" in request.POST:
            workspace.subscription_status = "active"
            workspace.save()

    return redirect(reverse("workspace_billing", host="app"))


@login_or_hubspot_required
def workspace_manual_billing(request):
    workspace = get_workspace(request.user)
    if request.method == "POST":
        if "pay" in request.POST:
            # NEXT SUBSCRIPTIONS
            start_datetime = (
                workspace.subscription_period_ended + timedelta(days=1)
            ).replace(hour=0, minute=0, second=0, microsecond=0)
            workspace.subscription_period_started = start_datetime
            workspace.subscription_period_ended = start_datetime + relativedelta(day=31)
            manual_workspace_charges(workspace)

    return redirect(reverse("workspace_billing", host="app"))
