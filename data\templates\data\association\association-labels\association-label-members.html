{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

<div class="accordion mt-5" id="accordion-association-label-members">
    <div class="accordion-item">
        <h2 class="accordion-header" id="heading-association-label-members">
            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-association-label-members" aria-expanded="true" aria-controls="collapse-association-label-members">
                {% if LANGUAGE_CODE == 'ja'%}
                アソシエーションメンバー
                {% else %}
                Association Members
                {% endif %}
            </button>
        </h2>
        <div id="collapse-association-label-members" class="accordion-collapse collapse show" aria-labelledby="heading-association-label-members" data-bs-parent="#accordion-association-label-members">
            <div class="accordion-body">
                {% if page_objects %}
                {% for association in page_objects %}
                    <div class="d-flex">
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6 w-100">
                            <div class="mb-1 d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    
                                    {# Source Object #}
                                    <div class="mb-2">
                                        <span class="badge badge-light-secondary small me-2">
                                            {% if LANGUAGE_CODE == 'ja' %}ソース{% else %}SOURCE{% endif %}
                                        </span>
                                        {% if association.source_object %}
                                            <button class="py-0 pe-0 btn justify-content-start text-start bg-transparent cursor-pointer text-primary manage_full_wizard_button-being-used view-settings-button" type="button"
                                                {% if association.source_content_type.model == constant.TYPE_OBJECT_COMPANY %}
                                                hx-get="{% host_url 'load_explore_company' association.source_object.id host 'app' %}"
                                                hx-vals='js:{"source_url": getDrawerURL(), "view_id": "{{view_filter.view.id}}"}'
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_ITEM %}
                                                hx-get="{% url 'shopturbo_load_drawer' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "drawer_type":"item-manage", "item_id":"{{association.source_object.id}}", "view_id":"{{view_id}}", "page": "{{page}}", "module": "{{menu_key}}" }'    
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_CONTACT %}
                                                hx-get="{% host_url 'load_explore_profile' association.source_object.id host 'app' %}"
                                                hx-vals='js:{"source_url": getDrawerURL(), "view_id":"{{view_id}}"}'
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_CASE %}
                                                hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "drawer_type":"manage-deal", "deal_id":"{{association.source_object.id}}" ,"view_id":"{{view_filter.view.id}}","module":"{{menu_key}}"}'
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_ORDER %}
                                                hx-get="{% host_url 'load_manage_order_drawer' host 'app' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "drawer_type":"manage-orders", "order_id":"{{association.source_object.id}}" ,"view_id":"{{view_id}}", "page": "{{page}}", "module": "{{module}}"}'
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_INVENTORY %}
                                                hx-get="{% url 'shopturbo_load_drawer' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "drawer_type":"inventory-manage", "inventory_id":"{{association.source_object.id}}" ,"view_id":"{{view_id}}", "page": "{{page}}", "module": "{{module}}"}'
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_INVENTORY_WAREHOUSE %}
                                                hx-get="{% url 'shopturbo_load_drawer' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "drawer_type":"inventory-warehouse-manage", "warehouse_id":"{{association.source_object.id}}", "module": "{{menu_key}}", "view_id":"{{view_id}}" }'
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_INVENTORY_TRANSACTION %}
                                                hx-get="{% url 'shopturbo_load_drawer' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "drawer_type":"inventory-transaction-manage", "transaction_id":"{{association.source_object.id}}", "module":"{{menu_key}}","view_id":"{{view_id}}","set_id":"{{set_id}}" }'
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_PURCHASE_ORDER %}
                                                hx-get="{% host_url 'purchase_manage' association.source_object.id host 'app' %}?view_id={{view_id}}&page={{page}}&module={{menu_key}}&set_id={{set_id}}"  
                                                hx-vals = 'js:{"source_url": getDrawerURL()}'
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_BILL %}
                                                hx-get="{% host_url 'bill_manage' association.source_object.id host 'app' %}?module={{menu_key}}"
                                                hx-vals = 'js:{"source_url": getDrawerURL()}'
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_ESTIMATE %}
                                                hx-get="{% host_url 'estimate_edit' association.source_object.id host 'app' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL()}'                                
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_INVOICE %}
                                                hx-get="{% host_url 'invoice_edit' association.source_object.id host 'app' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "set_id": "{{set_id}}","module":"{{module}}", "view_id":"{{view_id}}"}'
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_RECEIPT %}
                                                hx-get="{% host_url 'receipt_edit' association.source_object.id host 'app' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "set_id": "{{set_id}}","module":"{{module}}", "view_id":"{{view_id}}"}'
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_DELIVERY_NOTE %}
                                                hx-get="{% host_url 'delivery_slip_edit' association.source_object.id host 'app' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "set_id": "{{set_id}}","module":"{{module}}", "view_id":"{{view_id}}"}'
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_SUBSCRIPTION %}
                                                hx-get="{% url 'load_manage_subscriptions_drawer' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "drawer_type":"subscriptions-manage", "subscription_id":"{{association.source_object.id}}","view_id":"{{view_id}}", "page": "{{page}}", "module": "{{menu_key}}" }'
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_EXPENSE %}
                                                hx-get="{% host_url 'expense_drawer' association.source_object.id host 'app' %}?module={{menu_key}}" 
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "expense_id":"{{association.source_object.id}}"}'
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_TASK or association.source_content_type.model == 'taskflow' %}
                                                hx-get="{% host_url 'task_form' host 'app' %}"
                                                hx-vals='js:{"source_url": getDrawerURL(), "task_id":"{{association.source_object.id}}", "view_id":"{{view_id}}", "from":"{{from}}", "p_id": "{{p_id}}", "type": "update", "module": "{{module}}"}'
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_CONTRACT %}
                                                hx-get="{% host_url 'document_form' host 'app' %}" 
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "contract_id":"{{association.source_object.id}}", "module": "{{menu_key}}", "view_id": "{{view_id}}", "page": "{{page}}" }'
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_SLIP %}
                                                hx-get="{% host_url 'slip_edit' association.source_object.id host 'app' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "set_id": "{{set_id}}","module":"{{module}}", "view_id":"{{view_id}}"}'
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_JOURNAL %}
                                                hx-get="{% host_url 'journal_manage_drawer' association.source_object.id host 'app' %}"  
                                                hx-vals = 'js:{"source_url": getDrawerURL() }'
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_PANEL %}
                                                hx-get="{% host_url 'panel_view' panel_id=association.source_object.id host 'app' %}?module={{menu_key}}"
                                                hx-vals = 'js:{"source_url": getDrawerURL() }'
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_DASHBOARD %}
                                                hx-get="{% host_url 'report_view' report_id=association.source_object.id host 'app' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL() }'
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_USER_MANAGEMENT %}
                                                hx-get="{% host_url 'user_management_drawer' association.source_object.id host 'app' %}" 
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "drawer_type": "manage-object","view_id":"{{view_id}}"}' 
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_JOBS %}
                                                hx-get="{% host_url 'get_job' id=association.source_object.id host 'app' %}"
                                                hx-vals='js:{"source_url": getDrawerURL() }'
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_WORKFLOW %}
                                                hx-get="{% host_url 'create_workflow_form' host 'app' %}"
                                                hx-vals='js:{"source_url": getDrawerURL(), "workflow_id": "{{association.source_object.id}}" }'
                                                {% elif association.source_content_type.model == constant.TYPE_OBJECT_CUSTOM_OBJECT %}
                                                hx-get="{% url 'custom_object_drawer' custom_object_id %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "drawer-type":"manage", "row_id":"{{association.source_object.id}}", "view_id":"{{view_id}}", "page": "{{page}}", "module": "{{menu_key}}" }'
                                                {% endif %}
                                                hx-target="#manage-full-drawer-content-being-used"
                                                hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content-being-used,#manage-full-drawer-content"
                                                hx-trigger="click">
                                                <strong>{% get_object_display association.source_object association.source_content_type|content_type_to_page_group_type %}</strong>
                                            </button>
                                        {% else %}
                                            <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="text-center my-2">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor" class="text-muted">
                                            <path d="M8 12l-4-4h8l-4 4z"/>
                                        </svg>
                                    </div>
                                    
                                    {# Target Object #}
                                    <div class="mb-2">
                                        <span class="badge badge-light-primary small me-2">
                                            {% if LANGUAGE_CODE == 'ja' %}ターゲット{% else %}TARGET{% endif %}
                                        </span>
                                        {% if association.target_object %}
                                            <button class="py-0 pe-0 btn justify-content-start text-start bg-transparent cursor-pointer text-primary manage_full_wizard_button-being-used view-settings-button" type="button"
                                                {% if association.target_content_type.model == constant.TYPE_OBJECT_COMPANY %}
                                                hx-get="{% host_url 'load_explore_company' association.target_object.id host 'app' %}"
                                                hx-vals='js:{"source_url": getDrawerURL(), "view_id": "{{view_filter.view.id}}"}'
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_ITEM %}
                                                hx-get="{% url 'shopturbo_load_drawer' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "drawer_type":"item-manage", "item_id":"{{association.target_object.id}}", "view_id":"{{view_id}}", "page": "{{page}}", "module": "{{menu_key}}" }'    
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_CONTACT %}
                                                hx-get="{% host_url 'load_explore_profile' association.target_object.id host 'app' %}"
                                                hx-vals='js:{"source_url": getDrawerURL(), "view_id":"{{view_id}}"}'
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_CASE %}
                                                hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "drawer_type":"manage-deal", "deal_id":"{{association.target_object.id}}" ,"view_id":"{{view_filter.view.id}}","module":"{{menu_key}}"}'
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_ORDER %}
                                                hx-get="{% host_url 'load_manage_order_drawer' host 'app' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "drawer_type":"manage-orders", "order_id":"{{association.target_object.id}}" ,"view_id":"{{view_id}}", "page": "{{page}}", "module": "{{module}}"}'
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_INVENTORY %}
                                                hx-get="{% url 'shopturbo_load_drawer' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "drawer_type":"inventory-manage", "inventory_id":"{{association.target_object.id}}" ,"view_id":"{{view_id}}", "page": "{{page}}", "module": "{{module}}"}'
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_INVENTORY_WAREHOUSE %}
                                                hx-get="{% url 'shopturbo_load_drawer' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "drawer_type":"inventory-warehouse-manage", "warehouse_id":"{{association.target_object.id}}", "module": "{{menu_key}}", "view_id":"{{view_id}}" }'
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_INVENTORY_TRANSACTION %}
                                                hx-get="{% url 'shopturbo_load_drawer' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "drawer_type":"inventory-transaction-manage", "transaction_id":"{{association.target_object.id}}", "module":"{{menu_key}}","view_id":"{{view_id}}","set_id":"{{set_id}}" }'
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_PURCHASE_ORDER %}
                                                hx-get="{% host_url 'purchase_manage' association.target_object.id host 'app' %}?view_id={{view_id}}&page={{page}}&module={{menu_key}}&set_id={{set_id}}"  
                                                hx-vals = 'js:{"source_url": getDrawerURL()}'
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_BILL %}
                                                hx-get="{% host_url 'bill_manage' association.target_object.id host 'app' %}?module={{menu_key}}"
                                                hx-vals = 'js:{"source_url": getDrawerURL()}'
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_ESTIMATE %}
                                                hx-get="{% host_url 'estimate_edit' association.target_object.id host 'app' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL()}'                                
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_INVOICE %}
                                                hx-get="{% host_url 'invoice_edit' association.target_object.id host 'app' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "set_id": "{{set_id}}","module":"{{module}}", "view_id":"{{view_id}}"}'
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_RECEIPT %}
                                                hx-get="{% host_url 'receipt_edit' association.target_object.id host 'app' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "set_id": "{{set_id}}","module":"{{module}}", "view_id":"{{view_id}}"}'
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_DELIVERY_NOTE %}
                                                hx-get="{% host_url 'delivery_slip_edit' association.target_object.id host 'app' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "set_id": "{{set_id}}","module":"{{module}}", "view_id":"{{view_id}}"}'
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_SUBSCRIPTION %}
                                                hx-get="{% url 'load_manage_subscriptions_drawer' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "drawer_type":"subscriptions-manage", "subscription_id":"{{association.target_object.id}}","view_id":"{{view_id}}", "page": "{{page}}", "module": "{{menu_key}}" }'
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_EXPENSE %}
                                                hx-get="{% host_url 'expense_drawer' association.target_object.id host 'app' %}?module={{menu_key}}" 
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "expense_id":"{{association.target_object.id}}"}'
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_TASK or association.target_content_type.model == 'taskflow' %}
                                                hx-get="{% host_url 'task_form' host 'app' %}"
                                                hx-vals='js:{"source_url": getDrawerURL(), "task_id":"{{association.target_object.id}}", "view_id":"{{view_id}}", "from":"{{from}}", "p_id": "{{p_id}}", "type": "update", "module": "{{module}}"}'
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_CONTRACT %}
                                                hx-get="{% host_url 'document_form' host 'app' %}" 
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "contract_id":"{{association.target_object.id}}", "module": "{{menu_key}}", "view_id": "{{view_id}}", "page": "{{page}}" }'
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_SLIP %}
                                                hx-get="{% host_url 'slip_edit' association.target_object.id host 'app' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "set_id": "{{set_id}}","module":"{{module}}", "view_id":"{{view_id}}"}'
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_JOURNAL %}
                                                hx-get="{% host_url 'journal_manage_drawer' association.target_object.id host 'app' %}"  
                                                hx-vals = 'js:{"source_url": getDrawerURL() }'
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_PANEL %}
                                                hx-get="{% host_url 'panel_view' panel_id=association.target_object.id host 'app' %}?module={{menu_key}}"
                                                hx-vals = 'js:{"source_url": getDrawerURL() }'
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_DASHBOARD %}
                                                hx-get="{% host_url 'report_view' report_id=association.target_object.id host 'app' %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL() }'
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_USER_MANAGEMENT %}
                                                hx-get="{% host_url 'user_management_drawer' association.target_object.id host 'app' %}" 
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "drawer_type": "manage-object","view_id":"{{view_id}}"}' 
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_JOBS %}
                                                hx-get="{% host_url 'get_job' id=association.target_object.id host 'app' %}"
                                                hx-vals='js:{"source_url": getDrawerURL() }'
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_WORKFLOW %}
                                                hx-get="{% host_url 'create_workflow_form' host 'app' %}"
                                                hx-vals='js:{"source_url": getDrawerURL(), "workflow_id": "{{association.target_object.id}}" }'
                                                {% elif association.target_content_type.model == constant.TYPE_OBJECT_CUSTOM_OBJECT %}
                                                hx-get="{% url 'custom_object_drawer' custom_object_id %}"
                                                hx-vals = 'js:{"source_url": getDrawerURL(), "drawer-type":"manage", "row_id":"{{association.target_object.id}}", "view_id":"{{view_id}}", "page": "{{page}}", "module": "{{menu_key}}" }'
                                                {% endif %}
                                                hx-target="#manage-full-drawer-content-being-used"
                                                hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content-being-used,#manage-full-drawer-content"
                                                hx-trigger="click">
                                                <strong>{% get_object_display association.target_object association.target_content_type|content_type_to_page_group_type %} </strong>
                                            </button>
                                        {% else %}
                                            <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </div>
                                    
                                </div>
                    
                            </div>
                        </div>
                    </div>
                {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <div class="text-muted">
                            {% if LANGUAGE_CODE == 'ja'%}
                                関連付けられたメンバーがありません
                            {% else %}
                                No associated members found
                            {% endif %}
                        </div>
                    </div>
                {% endif %}

                {% comment %} Pagination Controls for association label members {% endcomment %}
                {% if pagination.count > 0 %}
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div class="text-muted">
                        {% if LANGUAGE_CODE == 'ja'%}
                            {{paginator_item_begin}}–{{paginator_item_end}} の {{pagination.count}} 件
                        {% else %}
                            Viewing {{paginator_item_begin}}–{{paginator_item_end}} of {{pagination.count}} results
                        {% endif %}
                    </div>

                    
                    <div class="d-flex gap-2 align-items-center">
                        <div>

                            <select class="bg-white border min-h-40px form-select form-select-solid select2-this" onchange="changePaginationAssociationLabelMembers(this)" data-control="select2" placeholder="{% if LANGUAGE_CODE == 'ja' %}ページネーション{% else %}Pagination{% endif %}">
                                <option value="10" {% if per_page == 10 %} selected {% endif %} > 10 </option>
                                <option value="20" {% if per_page == 20 %} selected {% endif %} > 20 </option>
                                <option value="50" {% if per_page == 50 %} selected {% endif %} > 50 </option>
                                <option value="100" {% if per_page == 100 %} selected {% endif %} > 100 </option>
                            </select>
                    
                            <script>
                                $('.select2-this').select2();
                                function changePaginationAssociationLabelMembers(selectElement) {
                                    const selectedValue = selectElement.value;
                                    const associationLabelMembersContent = document.getElementById('association-label-members-content');
                                    const associationLabelMembersLoader = associationLabelMembersContent.parentElement;
                                    var currentHxVals = associationLabelMembersLoader.getAttribute('hx-vals');
                                    var currentHxValsObject = JSON.parse(currentHxVals);
                                    currentHxValsObject['per_page'] = selectedValue;
                                    associationLabelMembersLoader.setAttribute('hx-vals', JSON.stringify(currentHxValsObject));
                                    htmx.trigger(associationLabelMembersLoader, 'change');
                                }
                            </script>
                    
                        </div>
                        <nav aria-label="Association members pagination">
                            <ul class="pagination pagination-sm mb-0 overflow-auto text-nowrap flex-nowrap">
                                
                                {% if current_page != 1 %}
                                    <li class="page-item flex-shrink-0">
                                        <button class="page-link"
                                            hx-get="{% host_url 'get_association_label_members' host 'app' %}"
                                            hx-vals='{"association_label_id": "{{association_label.id}}", "object_source": "{{ object_source }}", "page": 1, "per_page": {{ per_page }} }'
                                            hx-target="#association-label-members-content"
                                            hx-swap="innerHTML"
                                            hx-trigger="click"
                                            type="button">
                                            {% if LANGUAGE_CODE == 'ja' %}最初{% else %}First{% endif %}
                                        </button>
                                    </li>
                                {% endif %}
                                
                                {% if current_page > 1 %}
                                    <li class="page-item flex-shrink-0">
                                        <button class="page-link"
                                            hx-get="{% host_url 'get_association_label_members' host 'app' %}"
                                            hx-vals='{"association_label_id": "{{association_label.id}}", "object_source": "{{ object_source }}", "page": {{ current_page|add:"-1" }}, "per_page": {{ per_page }} }'
                                            hx-target="#association-label-members-content"
                                            hx-swap="innerHTML"
                                            hx-trigger="click"
                                            type="button">
                                            {% if LANGUAGE_CODE == 'ja' %}前{% else %}Previous{% endif %}
                                        </button>
                                    </li>
                                {% endif %}

                                {% for page_num in pagination.page_range %}
                                    {% if page_num >= start_page and page_num <= end_page %}
                                    <li class="page-item flex-shrink-0 {% if page_num|stringify == current_page|stringify %}active{% endif %}">
                                        <button class="page-link"
                                            hx-get="{% host_url 'get_association_label_members' host 'app' %}"
                                            hx-vals='{"association_label_id": "{{association_label.id}}", "object_source": "{{ object_source }}", "page": {{ page_num }}, "per_page": {{ per_page }} }'
                                            hx-target="#association-label-members-content"
                                            hx-swap="innerHTML"
                                            hx-trigger="click"
                                            type="button">
                                            {{ page_num }}  
                                        </button>
                                    </li>
                                    {% endif %}
                                {% endfor %}

                                {% if current_page < page_objects.paginator.num_pages %}
                                    <li class="page-item flex-shrink-0">
                                        <button class="page-link"
                                            hx-get="{% host_url 'get_association_label_members' host 'app' %}"
                                            hx-vals='{"association_label_id": "{{association_label.id}}", "object_source": "{{ object_source }}", "page": {{ current_page|add:1 }}, "per_page": {{ per_page }} }'
                                            hx-target="#association-label-members-content"
                                            hx-swap="innerHTML"
                                            hx-trigger="click"
                                            type="button">
                                            {% if LANGUAGE_CODE == 'ja' %}次{% else %}Next{% endif %}
                                        </button>
                                    </li>
                                {% endif %}

                                {% if current_page != page_objects.paginator.num_pages %}
                                    <li class="page-item flex-shrink-0">
                                        <button class="page-link"
                                            hx-get="{% host_url 'get_association_label_members' host 'app' %}"
                                            hx-vals='{"association_label_id": "{{association_label.id}}", "object_source": "{{ object_source }}", "page": {{ page_objects.paginator.num_pages }}, "per_page": {{ per_page }} }'
                                            hx-target="#association-label-members-content"
                                            hx-swap="innerHTML"
                                            hx-trigger="click"
                                            type="button">
                                            {% if LANGUAGE_CODE == 'ja' %}最後{% else %}Last{% endif %}
                                        </button>
                                    </li>
                                {% endif %}

                            </ul>
                        </nav>

                    </div>
                </div>
                {% endif %}
            </div>   
        </div>
    </div>
</div>

<script>
    {% comment %} // show warning message if association label has members
    $(document).ready(function() {
       const deleteAssociationLabelButton = document.querySelector('[name="delete-association-label"]');
       const associationLabelMembersWarning = document.getElementById('association-label-members-warning');
       if (deleteAssociationLabelButton && associationLabelMembersWarning) {
           deleteAssociationLabelButton.disabled = true;
           associationLabelMembersWarning.classList.remove('d-none');
       }
    }); {% endcomment %}
</script>