import ast
import traceback
from datetime import datetime
from django.conf import settings
from django.http import HttpResponse
from django.shortcuts import redirect
from django.views.generic import View
from django_hosts.resolvers import reverse
from data.constants.associate_constant import *
from data.constants.properties_constant import (
    TYPE_OBJECT_COMPANY,
    TYPE_OBJECT_DELIVERY_NOTE,
    TYPE_OBJECT_INVOICE,
    TYPE_OBJECT_RECEIPT,
)
from data.models import Module, Notification, ShopTurboShippingCost
from utils.decorator import login_or_hubspot_required
from utils.freee_bg_jobs.freee import *
from utils.items_util import handling_items
from utils.meter import MODELS_TO_STORAGE_USAGE, has_quota, sync_usage
from utils.properties.properties import get_page_object
from utils.serializer import *
from utils.smtp import *
from utils.utility import (
    get_workspace,
    is_valid_uuid,
    save_custom_property,
    update_query_params_url,
    assign_object_owner,
)
from data.commerce.commerce_functions import commerce_send_mail
from data.commerce.bulk_csv_commerce import bulk_csv_commerce
from data.accounts.association_labels import save_association_label

type_http = settings.SITE_URL.split("//")[0]


@login_or_hubspot_required
def delivery_note_create_and_update(
    request, id=None, object_type=TYPE_OBJECT_DELIVERY_NOTE
):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    page_obj = get_page_object(object_type, lang)

    module_object_slug = OBJECT_TYPE_TO_SLUG[object_type]
    module = Module.objects.filter(
        workspace=workspace, object_values__contains=object_type
    ).order_by("order", "created_at")

    module_slug = request.POST.get("module", "")
    if module_slug:
        module = module.filter(slug=module_slug)

    if module:
        module = module.first()
        module_slug = module.slug

    id_field = page_obj["id_field"]
    page_title = page_obj["page_title"]
    default_columns = page_obj["default_columns"]
    if id_field and id_field not in default_columns:
        default_columns.insert(0, id_field)
    base_model = page_obj["base_model"]
    custom_model = page_obj["custom_model"]
    # InvoiceNameCustomField etc
    custom_value_model = page_obj["custom_value_model"]
    custom_value_file_model = page_obj["custom_value_file_model"]
    custom_value_file_relation = page_obj["custom_value_file_relation"]

    custom_item_model = page_obj["custom_item_model"]
    custom_item_value_model = page_obj["custom_item_value_model"]

    item_model = page_obj["item_model"]
    try:
        purchase_item_model = page_obj["purchase_item_model"]
    except:
        purchase_item_model = ""
    field_item_name = page_obj["field_item_name"]
    additional_filter_fields = page_obj["additional_filter_fields"]

    if request.method != "POST":
        return HttpResponse(405)

    if "csv_upload" in request.POST:
        try:
            result = bulk_csv_commerce(request, object_type)
            if result != "Done":
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message="Wrong format uploaded, Please use this format",
                    message_ja="アップロードされた形式が間違っています。この形式を使用してください",
                    cta_text="Template",
                    cta_text_ja="テンプレート",
                    cta_target=TEMPLATE_FILE[object_type][lang],
                    type="error",
                )

        except:
            traceback.print_exc()
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message="Wrong format uploaded, Please use this format",
                message_ja="アップロードされた形式が間違っています。この形式を使用してください",
                cta_text="Template",
                cta_text_ja="テンプレート",
                cta_target=TEMPLATE_FILE[object_type][lang],
                type="error",
            )

        if module_slug:
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
            )
        return redirect(reverse("main", host="app"))

    if request.POST.get("submit_action") == "archive":
        obj = base_model.objects.get(id=id)
        obj.usage_status = "archived"
        obj.save(log_data={"user": request.user, "workspace": workspace})

        if request.POST.get("view_id"):
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?view_id={str(request.POST.get('view_id'))}"
            )
        else:
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
            )

    if request.POST.get("submit_action") == "activate":
        obj = base_model.objects.get(id=id)
        if obj.usage_status == "archived":
            if object_type == TYPE_OBJECT_SLIP or has_quota(workspace, object_type):
                obj.usage_status = "active"
                obj.save(log_data={"user": request.user, "workspace": workspace})
        if request.POST.get("view_id"):
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?view_id={str(request.POST.get('view_id'))}"
            )
        else:
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
            )

    # Input Steps from form ==========================
    amount = []
    amounts_input = request.POST.getlist("amount")
    for am in amounts_input:
        am = am.replace(",", "")
        amount.append(am)

    section_position = request.POST.getlist("section_position")
    section_type = request.POST.getlist("section_type")
    currency = request.POST.get("currency")
    section = request.POST.getlist("section")
    amounts_input = request.POST.getlist("amount")
    amount_item = request.POST.getlist("item_amount")
    contact_and_company = request.POST.getlist("contact_and_company", None)
    start_date = request.POST.get("start-date", "")
    due_date = request.POST.get("freq-due", "")
    email = request.POST.get("email", "")
    cc_list = request.POST.get("cc_list", "")
    tax_rate = request.POST.get("tax_rate", 0)
    status = request.POST.get("status", "")
    slip_type = request.POST.get("slip_type", "")
    stamp_value = request.POST.get("sw_stamp", "")
    item_request = request.POST.getlist("item", "")

    # Quick Entry Check
    billing_type = request.POST.get("billing_type", "item")

    # Cost Table
    purchase_item_request = request.POST.getlist("purchase-item", "")
    purchase_amounts_input = request.POST.getlist("purchase-amount", "")
    purchase_amount_item = request.POST.getlist("purchase-item_amount", "")
    purchase_tax_item_rate = request.POST.getlist("purchase-tax_item_rate", "")

    notes = request.POST.get("notes", "")
    send_from = request.POST.get("send_from", "")
    tax_item_rate = request.POST.getlist("tax_item_rate", None)
    tax_option = request.POST.get("tax_option", None)
    tax_inclusive = request.POST.get("tax_inclusive", None)
    discount_toggle = request.POST.get("discount_toggle", None)
    cost_toggle = request.POST.get("cost_toggle", None)
    owner = request.POST.get("owner", None)
    shippibg_checkbox = request.POST.get("shipping_checkbox", None)
    shipping = request.POST.get("shipping", 0)

    if cc_list:
        cc_list = cc_list.split(",")

    if tax_rate == "":
        tax_rate = 0

    item = []

    for it in item_request:
        item.append(it.split("|")[0])

    if discount_toggle:
        discount_option = request.POST.get("discount_option")
        discount_tax_option = request.POST.get("discount_tax_option")
        discount_value = request.POST.get("discount")
    else:
        discount_tax_option = ""
        discount_option = ""
        discount_value = ""

    if cost_toggle:
        cost_option = True
    else:
        cost_option = ""

    if not tax_inclusive:
        tax_inclusive = False
    else:
        if tax_option != "item_based_tax":
            tax_inclusive = "False"

    if shippibg_checkbox and shipping:
        shipping = ShopTurboShippingCost.objects.filter(id=shipping).first()
    else:
        shipping = None

    if not stamp_value:
        stamp_value = False

    purchase_amount = []
    for pam in purchase_amounts_input:
        pam = pam.replace(",", "")
        purchase_amount.append(pam)

    purchase_item = []
    for pit in purchase_item_request:
        purchase_item.append(pit.split("|")[0])

    if id:
        try:
            obj = base_model.objects.get(id=id)
        except base_model.DoesNotExist:
            return HttpResponse(status=404)
    else:
        if object_type != TYPE_OBJECT_SLIP:
            sync_usage(workspace, MODELS_TO_STORAGE_USAGE[base_model])
            if object_type != TYPE_OBJECT_SLIP and not has_quota(
                workspace, object_type
            ):
                msg = f"{page_title}, could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some existing {page_title} to free up space."
                if lang == "ja":
                    msg = f"{page_title},制限を超えたため作成できませんでした。サブスクリプション プランをアップグレードしてクォータを増やすか、既存の {page_title} の一部をアーカイブしてスペースを解放します。"
                Notification.objects.create(
                    workspace=workspace, user=request.user, message=msg, type="error"
                )
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
        obj = base_model.objects.create(workspace=workspace, updated_at=timezone.now)

    if type(item) != list:
        item = ast.literal_eval(item)
    if type(amount_item) != list:
        amount_item = ast.literal_eval(amount_item)
    if type(amount) != list:
        amount = ast.literal_eval(amount)
    if type(tax_item_rate) != list:
        tax_item_rate = ast.literal_eval(tax_item_rate)
    if type(section) != list:
        section = ast.literal_eval(section)
    if type(section_type) != list:
        section_type = ast.literal_eval(section_type)
    if type(section_position) != list:
        section_position = ast.literal_eval(section_position)

    if len(purchase_item) > 0:
        if type(purchase_item) != list:
            purchase_item = ast.literal_eval(purchase_item)
        if type(purchase_amount_item) != list:
            purchase_amount_item = ast.literal_eval(purchase_amount_item)
        if type(purchase_amount) != list:
            purchase_amount = ast.literal_eval(purchase_amount)
        if type(purchase_tax_item_rate) != list:
            purchase_tax_item_rate = ast.literal_eval(purchase_tax_item_rate)

    # Redirect after updating the associated object
    if "update_orders" in request.POST:
        source = request.POST.get("source")
        if source == TYPE_OBJECT_INVOICE:
            order_ids = request.POST.getlist("orders", [])
            invoice = obj
            if invoice and order_ids != [""]:
                invoice.orders.clear()
                order_objs = ShopTurboOrders.objects.filter(id__in=order_ids)
                invoice.orders.add(*order_objs)
            else:
                invoice.orders.clear()
        if source == TYPE_OBJECT_ESTIMATE:
            order_ids = request.POST.getlist("orders", [])
            estimate = obj
            if estimate and order_ids != [""]:
                estimate.shopturboorders_set.clear()
                order_objs = ShopTurboOrders.objects.filter(id__in=order_ids)
                estimate.shopturboorders_set.add(*order_objs)
            else:
                estimate.orders.clear()

    if "update_cases" in request.POST:
        source = request.POST.get("source")
        if source == TYPE_OBJECT_INVOICE:
            case_ids = request.POST.getlist("cases", [])
            invoice = obj
            if invoice and case_ids != [""]:
                invoice.deals.clear()
                case_objs = Deals.objects.filter(id__in=case_ids)
                invoice.deals.add(*case_objs)
            else:
                invoice.deals.clear()
        elif source == TYPE_OBJECT_ESTIMATE:
            case_ids = request.POST.getlist("cases", [])
            estimate = obj
            if estimate and case_ids != [""]:
                estimate.deals.clear()
                case_objs = Deals.objects.filter(id__in=case_ids)
                estimate.deals.add(*case_objs)
            else:
                estimate.deals.clear()

    if "update_estimates" in request.POST:
        source = request.POST.get("source")
        if source == TYPE_OBJECT_INVOICE:
            estimate_ids = request.POST.getlist("estimates", [])
            invoice = obj
            if invoice and estimate_ids != [""]:
                invoice.estimate_set.clear()
                estimate_objs = Estimate.objects.filter(id__in=estimate_ids)
                invoice.estimate_set.add(*estimate_objs)
            else:
                invoice.estimate_set.clear()

    if "update_subscriptions" in request.POST:
        source = request.POST.get("source")
        if source == TYPE_OBJECT_INVOICE:
            subscription_ids = request.POST.getlist("subscriptions", [])
            invoice = obj
            if invoice and subscription_ids != [""]:
                invoice.subscriptions.clear()
                subscription_objs = ShopTurboSubscriptions.objects.filter(
                    id__in=subscription_ids
                )
                invoice.subscriptions.add(*subscription_objs)
            else:
                invoice.subscriptions.clear()

    if "update_invoices" in request.POST:
        source = request.POST.get("source")
        if source == TYPE_OBJECT_RECEIPT:
            invoice_ids = request.POST.getlist("invoices", [])
            receipt = obj
            if receipt and invoice_ids != [""]:
                receipt.invoices.clear()
                invoice_objs = Invoice.objects.filter(id__in=invoice_ids)
                receipt.invoices.add(*invoice_objs)
            else:
                receipt.invoices.clear()
        elif source == TYPE_OBJECT_ESTIMATE:
            invoice_ids = request.POST.getlist("invoices", [])
            estimate = obj
            if estimate and invoice_ids != [""]:
                estimate.invoice.clear()
                invoice_objs = Invoice.objects.filter(id__in=invoice_ids)
                estimate.invoice.add(*invoice_objs)
            else:
                estimate.invoice.clear()

    if (
        "update_orders" in request.POST
        or "update_cases" in request.POST
        or "update_estimates" in request.POST
        or "update_subscriptions" in request.POST
        or "update_invoices" in request.POST
    ):
        if request.POST.get("source_url"):
            source_url = request.POST.get("source_url", None)
            if source_url:
                source_url = update_query_params_url(
                    source_url, {"target": object_type, "id": [str(obj.id)]}
                )
                return redirect(source_url)
        if request.POST.get("view_id"):
            view = View.objects.filter(id=request.POST.get("view_id"))
            if view:
                object_type = view[0].target
                page_obj = get_page_object(object_type, lang)

                if obj:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                        + f"?view_id={str(request.POST.get('view_id'))}&id={obj.id}"
                    )
                else:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                        + f"?view_id={str(request.POST.get('view_id'))}"
                    )

    try:
        print("logit!", request.POST)
        obj_item = item_model.objects.filter(**{field_item_name: obj}).order_by(
            "created_at"
        )
        # Clear all billing item data
        if obj_item:
            for obj__item in obj_item:
                obj__item.delete(
                    log_data={"user": request.user, "workspace": workspace}
                )
        for index, (item, amount_item, amount_price, tax_item_rate) in enumerate(
            zip(item, amount_item, amount, tax_item_rate)
        ):
            if not tax_item_rate:
                tax_item_rate = "0"
            if amount_item == "":
                amount_item = 0.0
            if amount_price == "":
                amount_price = 0.0

            obj_item = item_model.objects.create(**{field_item_name: obj})

            if is_valid_uuid(item):
                item = ShopTurboItems.objects.filter(id=item)
                if item:
                    obj_item.item_link = item.last()
            else:
                obj_item.item_name = item
            obj_item.save(
                log_data={
                    "user": request.user,
                    "status": "create",
                    "workspace": workspace,
                }
            )

            obj_item.amount_price = amount_price
            obj_item.amount_item = amount_item

            if obj.tax_inclusive:
                obj_item.total_price_without_tax = (
                    float(amount_price)
                    * float(amount_item)
                    / (1 + (float(tax_item_rate) / 100.0))
                )
                obj_item.total_price = float(amount_price) * float(amount_item)
            else:
                if tax_item_rate != "0":
                    obj_item.total_price_without_tax = float(amount_price) * float(
                        amount_item
                    )
                    obj_item.total_price = (
                        float(amount_price) * float(tax_item_rate) + float(amount_price)
                    ) * float(amount_item)
                else:
                    obj_item.total_price_without_tax = float(amount_price) * float(
                        amount_item
                    )
                    obj_item.total_price = float(amount_price) * float(amount_item)
            obj_item.tax_rate = tax_item_rate
            obj_item.save(log_data={"user": request.user, "workspace": workspace})

            line_item_properties = {
                key: request.POST.getlist(key)
                for key in request.POST
                if key.startswith("line_item_property")
            }

            for key, value in line_item_properties.items():
                custom_field_keys = key.split("|")
                custom_field_id = key.split("|")[-1]

                custom_field = custom_item_model.objects.get(id=custom_field_id)
                custom_field_value, _ = custom_item_value_model.objects.get_or_create(
                    field_name=custom_field, item_order=obj_item
                )

                if len(custom_field_keys) == 3:  # discount
                    custom_field_value.value_number_format = value[index]
                else:
                    custom_field_value.value = value[index]
                custom_field_value.save()

        if object_type == TYPE_OBJECT_ESTIMATE and cost_toggle:  # Cost Table Items
            obj_item = purchase_item_model.objects.filter(
                **{field_item_name: obj}
            ).order_by("created_at")
            # Clear all billing item data
            if obj_item:
                for obj__item in obj_item:
                    obj__item.delete(
                        log_data={"user": request.user, "workspace": workspace}
                    )

            for item, amount_item, amount_price, tax_item_rate in zip(
                purchase_item,
                purchase_amount_item,
                purchase_amount,
                purchase_tax_item_rate,
            ):
                if not tax_item_rate:
                    tax_item_rate = "0"
                if amount_item == "":
                    amount_item = 0.0
                if amount_price == "":
                    amount_price = 0.0

                obj_item = purchase_item_model.objects.create(**{field_item_name: obj})

                if is_valid_uuid(item):
                    item = ShopTurboItems.objects.filter(id=item)
                    if item:
                        obj_item.item_link = item.last()
                else:
                    obj_item.item_name = item
                obj_item.save(
                    log_data={
                        "user": request.user,
                        "status": "create",
                        "workspace": workspace,
                    }
                )

                obj_item.amount_price = amount_price
                obj_item.amount_item = amount_item

                if obj.tax_inclusive:
                    obj_item.total_price_without_tax = (
                        float(amount_price)
                        * float(amount_item)
                        / (1 + (float(tax_item_rate) / 100.0))
                    )
                    obj_item.total_price = float(amount_price) * float(amount_item)
                else:
                    if tax_item_rate != "0":
                        obj_item.total_price_without_tax = float(amount_price) * float(
                            amount_item
                        )
                        obj_item.total_price = (
                            float(amount_price) * float(tax_item_rate)
                            + float(amount_price)
                        ) * float(amount_item)
                    else:
                        obj_item.total_price_without_tax = float(amount_price) * float(
                            amount_item
                        )
                        obj_item.total_price = float(amount_price) * float(amount_item)
                obj_item.tax_rate = tax_item_rate
                obj_item.save(log_data={"user": request.user, "workspace": workspace})
    except Exception as e:
        print("[Error saving items]: ", e)

    try:
        if object_type == TYPE_OBJECT_ESTIMATE:
            SectionItemEstimate.objects.filter(
                workspace=workspace, estimate=obj
            ).delete()
            for section, section_type, section_position in zip(
                section, section_type, section_position
            ):
                section_object, _ = SectionItemEstimate.objects.get_or_create(
                    workspace=workspace,
                    section_type=section_type,
                    estimate=obj,
                    position=int(section_position),
                    value=section,
                )

        elif object_type == TYPE_OBJECT_DELIVERY_NOTE:
            SectionItemDeliverySlip.objects.filter(
                workspace=workspace, deliveryslip=obj
            ).delete()
            for section, section_type, section_position in zip(
                section, section_type, section_position
            ):
                section_object, _ = SectionItemDeliverySlip.objects.get_or_create(
                    workspace=workspace,
                    section_type=section_type,
                    deliveryslip=obj,
                    position=int(section_position),
                    value=section,
                )

        elif object_type == TYPE_OBJECT_INVOICE:
            SectionItemInvoice.objects.filter(workspace=workspace, invoice=obj).delete()
            for section, section_type, section_position in zip(
                section, section_type, section_position
            ):
                section_object, _ = SectionItemInvoice.objects.get_or_create(
                    workspace=workspace,
                    section_type=section_type,
                    invoice=obj,
                    position=int(section_position),
                    value=section,
                )
        elif object_type == TYPE_OBJECT_RECEIPT:
            SectionItemReceipt.objects.filter(workspace=workspace, receipt=obj).delete()
            for section, section_type, section_position in zip(
                section, section_type, section_position
            ):
                section_object, _ = SectionItemReceipt.objects.get_or_create(
                    workspace=workspace,
                    section_type=section_type,
                    receipt=obj,
                    position=int(section_position),
                    value=section,
                )
    except Exception as e:
        print("[Error saving sections]: ", e)

    # IF NEED DELETE OBJECT
    if "delete" in request.POST:
        obj = base_model.objects.get(id=id)
        obj.delete()
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
        )

    obj.workspace = workspace
    obj.email = email
    obj.notes = notes
    obj.status = status
    obj.tax_rate = float(tax_rate)
    obj.send_from = send_from
    obj.is_stamp = stamp_value
    obj.tax_list = tax_item_rate
    obj.tax_inclusive = tax_inclusive
    obj.discount_option = discount_option
    obj.cost_option = cost_option
    obj.discount_tax_option = discount_tax_option
    obj.discount = discount_value
    obj.currency = currency
    obj.updated_at = timezone.now
    obj.shipping_cost = shipping

    assign_object_owner(obj, owner, request, object_type)

    if object_type == TYPE_OBJECT_RECEIPT and hasattr(obj, "billing_type"):
        obj.billing_type = billing_type
        tax_option = "unified_tax"
        if billing_type == "manual":
            obj.manual_price = float(
                request.POST.get("manual_price", 0.0).replace(",", "")
            )
            item_model.objects.filter(**{field_item_name: obj}).delete()

            SectionItemReceipt.objects.filter(workspace=workspace, receipt=obj).delete()
    obj.tax_option = tax_option

    if slip_type:
        obj.slip_type = slip_type

    if start_date:
        if isinstance(start_date, tuple):
            start_date = start_date[0]
        try:
            if lang == "ja":
                start_date = datetime.strptime(start_date, "%Y年%m月%d日").date()
            else:
                start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
            obj.start_date = start_date
        except:  # Format date is invalid , skip to save it
            pass
    if due_date:
        try:
            if lang == "ja":
                due_date = datetime.strptime(due_date, "%Y年%m月%d日").date()
            else:
                due_date = datetime.strptime(due_date, "%Y-%m-%d").date()
            obj.due_date = due_date
        except:  # Format date is invalid , skip to save it
            pass

    #association label
    association_label = AssociationLabel.objects.filter(
        workspace=workspace, object_source=object_type, label__iexact="customer"
    ).first()
    AssociationLabelObject.reset_associations_for_object(
        obj, workspace, association_label
    )
    for contact_and_company_id in contact_and_company:
        if Contact.objects.filter(id=contact_and_company_id):
            obj.contact = Contact.objects.get(id=contact_and_company_id)
            obj.company = None
            AssociationLabelObject.create_association(
                        obj,
                        obj.contact,
                        workspace,
                        association_label,
                    )

        elif Company.objects.filter(id=contact_and_company_id):
            obj.company = Company.objects.get(id=contact_and_company_id)
            obj.contact = None
            AssociationLabelObject.create_association(
                        obj,
                        obj.company,
                        workspace,
                        association_label,
                    )

    if object_type in [
        TYPE_OBJECT_INVOICE,
        TYPE_OBJECT_ESTIMATE,
        TYPE_OBJECT_RECEIPT,
        TYPE_OBJECT_DELIVERY_NOTE,
        TYPE_OBJECT_SLIP,
    ]:
        obj.save(log_data={"user": request.user, "workspace": workspace})
    else:
        obj.save()
    obj = handling_items(obj)
    print("Obj Handled 1: ")
    print(obj)

    save_association_label(request, obj, TYPE_OBJECT_DELIVERY_NOTE)

    # == Object Properties handler
    save_custom_property(request, obj, page_obj)

    if request.POST.get("submit_action") == "duplicate" or "duplicate" in request.POST:
        obj_item = item_model.objects.filter(**{field_item_name: obj}).order_by(
            "created_at"
        )
        customFields = custom_value_model.objects.filter(**{field_item_name: obj})

        if object_type == TYPE_OBJECT_SLIP or has_quota(workspace, object_type):
            obj = base_model.objects.get(id=id)
            obj.id = None
            obj.created_at = timezone.now()
            setattr(obj, id_field, None)
            obj.save()

            for field in customFields:
                field.id = None
                setattr(field, field_item_name, obj)
                field.save()
                # custom_value_model.objects.create(**{field_item_name:obj, 'value':field.value,'field_name':field.field_name})

            for item in obj_item:
                item.id = None
                setattr(item, field_item_name, obj)
                item.save()

    if request.POST.get("submit_action") == "send_email":
        commerce_send_mail(request, obj.id, object_type=object_type)

    if request.POST.get("source_url"):
        source_url = request.POST.get("source_url", None)
        if source_url:
            source_url = update_query_params_url(
                source_url, {"target": object_type, "id": [str(obj.id)]}
            )
            return redirect(source_url)
    if request.POST.get("view_id"):
        view = View.objects.filter(id=request.POST.get("view_id"))
        if view:
            object_type = view[0].target
            page_obj = get_page_object(object_type, lang)

            if obj:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?view_id={str(request.POST.get('view_id'))}&id={obj.id}"
                )
            else:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?view_id={str(request.POST.get('view_id'))}"
                )

    # association related
    if request.POST.get("type", "") == "create-association":
        if "object_type" in request.POST:
            object_type = request.POST.get("object_type")
        source = request.POST.get("source")
        module_object_slug = OBJECT_TYPE_TO_SLUG[source]
        module = (
            Module.objects.filter(workspace=workspace, object_values__contains=source)
            .order_by("order", "created_at")
            .first()
        )
        module_slug = module.slug
        if source == TYPE_OBJECT_CASE:
            object_id = request.POST.get("source_object_id")
            case = Deals.objects.filter(id=object_id).first()
            if object_type == TYPE_OBJECT_INVOICE:
                case.invoices.add(obj)
            elif object_type == TYPE_OBJECT_ESTIMATE:
                case.estimates.add(obj)
            case.save()
        elif source == TYPE_OBJECT_INVOICE and object_type == TYPE_OBJECT_RECEIPT:
            object_id = request.POST.get("source_object_id")
            invoice = Invoice.objects.filter(id=object_id).first()
            if "object_id" in request.POST:
                receipt_id = request.POST.get("object_id")
            else:
                receipt_id = obj.id
            receipt = Receipt.objects.filter(id=receipt_id).first()
            if invoice and receipt:
                invoice.receipts.add(receipt)
                invoice.save()
            if "object_id" in request.POST:
                return HttpResponse(status=200)
            else:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?id={object_id}"
                )

        elif source == TYPE_OBJECT_RECEIPT:
            object_id = request.POST.get("source_object_id")
            receipt = Receipt.objects.filter(id=object_id).first()
            if object_type == TYPE_OBJECT_INVOICE:
                if receipt:
                    receipt.invoices.add(obj)
                    receipt.save()

            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?id={object_id}"
            )

        elif source == TYPE_OBJECT_DELIVERY_NOTE:
            object_id = request.POST.get("source_object_id")
            delivery_slip = DeliverySlip.objects.filter(id=object_id).first()
            if delivery_slip and object_type == TYPE_OBJECT_INVOICE:
                delivery_slip.invoices.add(obj)
                delivery_slip.save()

            if "object_id" in request.POST:
                return HttpResponse(status=200)
            else:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?id={object_id}"
                )
        elif source == TYPE_OBJECT_ORDER:
            object_id = request.POST.get("source_object_id")
            order = ShopTurboOrders.objects.filter(id=object_id).first()
            if object_type == TYPE_OBJECT_INVOICE:
                order.invoice.add(obj)
            if object_type == TYPE_OBJECT_ESTIMATE:
                order.estimate.add(obj)
            if object_type == TYPE_OBJECT_DELIVERY_NOTE:
                order.deliveryslip.add(obj)
            order.save()

            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?target={source}&id={order.id}"
            )

        elif source == TYPE_OBJECT_SUBSCRIPTION:
            object_id = request.POST.get("source_object_id")
            subs = ShopTurboSubscriptions.objects.filter(id=object_id).first()
            if object_type == TYPE_OBJECT_INVOICE:
                subs.invoices.add(obj)
                if not subs.upcoming_invoice_date:
                    subs.upcoming_invoice_date = obj.due_date
            subs.save()

            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?target={source}&id={subs.id}"
            )

        elif source == TYPE_OBJECT_INVOICE and object_type == TYPE_OBJECT_ESTIMATE:
            object_id = request.POST.get("source_object_id")
            invoice = Invoice.objects.filter(id=object_id).first()
            invoice.estimate_set.add(obj)

            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?id={str(invoice.id)}"
            )

        elif source == TYPE_OBJECT_ESTIMATE:
            object_id = request.POST.get("source_object_id")
            estimate = Estimate.objects.filter(id=object_id).first()
            if object_type == TYPE_OBJECT_INVOICE:
                estimate.invoice.add(obj)

            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?id={str(estimate.id)}"
            )

        elif source == TYPE_OBJECT_COMPANY:
            object_id = request.POST.get("source_object_id")
            company = Company.objects.filter(id=object_id).first()
            obj.company = company
            obj.save()

            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?target={source}&id={company.id}"
            )

        elif source == TYPE_OBJECT_JOURNAL:
            object_id = request.POST.get("source_object_id")
            journal = JournalEntry.objects.filter(id=object_id).first()
            if object_type == TYPE_OBJECT_INVOICE:
                journal.invoice = obj
                journal.save()

            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?target={source}&id={journal.id}"
            )

    else:
        if obj:
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?id={obj.id}"
            )
    return redirect(
        reverse(
            "load_object_page",
            host="app",
            kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
        )
    )
