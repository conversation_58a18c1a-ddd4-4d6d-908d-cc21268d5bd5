from datetime import timed<PERSON><PERSON>
import uuid

from django.db import transaction
from django.utils import timezone

from data.models import BackgroundJob, TransferHistory, WorkflowHistory
from utils.logger import logger

STALE_JOB_THRESHOLD_HOURS = 6


def create_bg_job(
    workspace,
    user,
    function_name,
    transfer_history=None,
    payload=None,
):
    if not isinstance(transfer_history, TransferHistory):
        transfer_history = None

    job_id = str(uuid.uuid4())

    if payload and isinstance(payload, dict):
        payload["background_job_id"] = job_id

    BackgroundJob.objects.get_or_create(
        job_id=job_id,
        defaults={
            "workspace": workspace,
            "user": user,
            "transfer_history": transfer_history,
            "name": f"[Background Job] {function_name}",
            "payload": payload,
            "status": BackgroundJob.BackgroundJobStatus.PENDING,
            "log": [],
        },
    )

    logger.info(f"BackgroundJob created: {job_id}")
    return job_id


def add_hatchet_run_id(
    job_id,
    hatchet_run_id,
):
    bg_job = BackgroundJob.objects.filter(job_id=job_id).first()
    if not bg_job:
        logger.warning("BackgroundJob not found")
        return

    bg_job.hatchet_run_id = hatchet_run_id
    bg_job.save()

    logger.info(f"BackgroundJob updated: {bg_job.id}")

    return bg_job.id


def add_payload_bg_job(job_id, payload):
    bg_job = BackgroundJob.objects.filter(job_id=job_id).first()
    if not bg_job:
        logger.warning("BackgroundJob not found")
        return

    bg_job.payload = payload
    bg_job.save()

    logger.info(f"BackgroundJob updated: {bg_job.id}")


def set_bg_job_running(
    job_id,
):
    bg_job = BackgroundJob.objects.filter(job_id=job_id).first()
    if not bg_job:
        logger.warning("BackgroundJob not found")
        return

    bg_job.status = BackgroundJob.BackgroundJobStatus.RUNNING
    bg_job.save()

    logger.info(f"BackgroundJob updated: {bg_job.id}")


async def aio_set_bg_job_running(job_id):
    bg_job = await BackgroundJob.objects.filter(job_id=job_id).afirst()
    if not bg_job:
        logger.warning("BackgroundJob not found")
        return

    bg_job.status = BackgroundJob.BackgroundJobStatus.RUNNING
    await bg_job.asave()

    logger.info(f"BackgroundJob updated: {bg_job.id}")


def set_bg_job_completed(
    job_id,
):
    bg_job = BackgroundJob.objects.filter(job_id=job_id).first()
    if not bg_job:
        logger.warning("BackgroundJob not found")
        return

    bg_job.status = BackgroundJob.BackgroundJobStatus.COMPLETED
    bg_job.save()

    logger.info(f"BackgroundJob updated: {bg_job.id}")


async def aio_set_bg_job_completed(
    job_id,
):
    bg_job = await BackgroundJob.objects.filter(job_id=job_id).afirst()
    if not bg_job:
        logger.warning("BackgroundJob not found")
        return

    bg_job.status = BackgroundJob.BackgroundJobStatus.COMPLETED
    await bg_job.asave()

    logger.info(f"BackgroundJob updated: {bg_job.id}")


def set_bg_job_failed(
    job_id,
    error_message=None,
):
    bg_job = BackgroundJob.objects.filter(job_id=job_id).first()
    if not bg_job:
        logger.warning("BackgroundJob not found")
        return

    bg_job.status = BackgroundJob.BackgroundJobStatus.FAILED

    # Add error message to log if provided
    if error_message:
        bg_job.log = (bg_job.log or []) + [
            {
                "job_id": bg_job.job_id or bg_job.id,
                "status": "failed",
                "message": error_message,
            }
        ]

    bg_job.save()

    logger.info(f"BackgroundJob updated: {bg_job.id}")


async def aio_set_bg_job_failed(
    job_id,
    error_message=None,
):
    bg_job = await BackgroundJob.objects.filter(job_id=job_id).afirst()
    if not bg_job:
        logger.warning("BackgroundJob not found")
        return

    bg_job.status = BackgroundJob.BackgroundJobStatus.FAILED

    # Add error message to log if provided
    if error_message:
        bg_job.log = (bg_job.log or []) + [
            {
                "job_id": bg_job.job_id or bg_job.id,
                "status": "failed",
                "message": error_message,
            }
        ]

    await bg_job.asave()

    logger.info(f"BackgroundJob updated: {bg_job.id}")


def set_bg_job_canceled(
    job_id,
    cancel_message=None,
):
    bg_job = BackgroundJob.objects.filter(job_id=job_id).first()
    if not bg_job:
        logger.warning("BackgroundJob not found")
        return

    bg_job.status = BackgroundJob.BackgroundJobStatus.CANCELED

    # Add cancel message to log if provided
    if cancel_message:
        bg_job.log = (bg_job.log or []) + [
            {
                "job_id": bg_job.job_id or bg_job.id,
                "status": "canceled",
                "message": cancel_message,
            }
        ]

    bg_job.save()

    logger.info(f"BackgroundJob canceled: {bg_job.id}")


def add_bgjob_log_by_workflow_history(
    workflow_history,
    log_status,
    log_message,
    bg_job_status=None,
):
    if not isinstance(workflow_history, WorkflowHistory):
        logger.warning("WorkflowHistory is not an instance of WorkflowHistory")
        return

    bg_job = BackgroundJob.objects.filter(workflow_history=workflow_history).first()
    if not bg_job:
        logger.warning(
            f"BackgroundJob not found for WorkflowHistory {workflow_history.id}. Available BackgroundJobs for this workspace: {list(BackgroundJob.objects.filter(workspace=workflow_history.workspace).values_list('id', 'workflow_history_id', 'job_id'))}"
        )
        return

    bg_job.log = (bg_job.log or []) + [
        {
            "job_id": bg_job.job_id or bg_job.id,
            "status": log_status,
            "message": log_message,
        }
    ]
    if bg_job_status:
        bg_job.status = bg_job_status

    bg_job.save()

    logger.info(f"BackgroundJob updated: {bg_job.id}")


def add_bgjob_log_by_bg_job_id(
    bg_job_id,
    log_status,
    log_message,
    bg_job_status=None,
):
    bg_job = BackgroundJob.objects.filter(job_id=bg_job_id).first()
    if not bg_job:
        logger.warning("BackgroundJob not found")
        return

    bg_job.log = (bg_job.log or []) + [
        {
            "job_id": bg_job.job_id or bg_job.id,
            "status": log_status,
            "message": log_message,
        }
    ]

    if bg_job_status:
        bg_job.status = bg_job_status

    bg_job.save()

    logger.info(f"BackgroundJob updated: {bg_job.id}")


def is_run_bg_job(
    function_name,
    workspace,
):
    with transaction.atomic():
        # First, handle stale jobs if they exist
        latest_job = (
            BackgroundJob.objects.filter(name="function_name", workspace=workspace)
            .order_by("-created_at")
            .first()
        )

        if latest_job:
            threshold_time_ago = timezone.now() - timedelta(
                hours=STALE_JOB_THRESHOLD_HOURS
            )
            if latest_job.created_at < threshold_time_ago:
                # Set all Pending or Running jobs to Failed
                failed_jobs_count = BackgroundJob.objects.filter(
                    name=f"[Background Job] {function_name}",
                    workspace=workspace,
                    status__in=[
                        BackgroundJob.BackgroundJobStatus.PENDING,
                        BackgroundJob.BackgroundJobStatus.RUNNING,
                    ],
                ).update(status=BackgroundJob.BackgroundJobStatus.FAILED)

                if failed_jobs_count > 0:
                    logger.info(
                        f"Latest job for {function_name} is older than {STALE_JOB_THRESHOLD_HOURS} hours. Set {failed_jobs_count} jobs to Failed status."
                    )

        # Always check for existing pending/running jobs with locking to prevent race conditions
        existing_job = (
            BackgroundJob.objects.select_for_update()
            .filter(
                name=f"[Background Job] {function_name}",
                workspace=workspace,
                status__in=[
                    BackgroundJob.BackgroundJobStatus.PENDING,
                    BackgroundJob.BackgroundJobStatus.RUNNING,
                ],
            )
            .first()
        )

        if existing_job:
            logger.info(
                f"Job already exists for function {function_name} and still running. Skipping job creation."
            )
            return False

    return True
