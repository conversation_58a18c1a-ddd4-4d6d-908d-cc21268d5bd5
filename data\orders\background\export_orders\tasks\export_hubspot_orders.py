import ast
import asyncio
from datetime import timedelta
from hatchet_sdk import Context
from utils.logger import logger
from data.models import <PERSON><PERSON><PERSON><PERSON>, User, BackgroundJob
from asgiref.sync import sync_to_async

from ..models import ExportOrdersHubspotPayload, ExportOrdersHubspotPayloadV2, ExportOrdersResultPayload
from ..workflows import export_hubspot_orders

# Import the new v2 export function
from utils.hubspot.orders import export_hubspot_orders_v2


@export_hubspot_orders.task(
    name="ExportHubspotOrdersTask",
    execution_timeout=timedelta(hours=5),
    schedule_timeout=timedelta(hours=1),
)
async def export_hubspot_orders_task(
    input: ExportOrdersHubspotPayloadV2, _ctx: Context
) -> dict:
    """
    Child task for exporting orders to HubSpot
    Supports both legacy and new CSV-based export approaches
    """
    logger.info("Run HubSpot orders export child task")
    # logger.info(f"Input: {input}")
    
    background_job_id = input.background_job_id
    
    # Retrieve BackgroundJob
    bg_job = await BackgroundJob.objects.aget(id=background_job_id)
    if not bg_job:
        logger.error(f"BackgroundJob {background_job_id} does not exist")
        raise Exception(f"BackgroundJob {background_job_id} does not exist")
    
    payload_dict = bg_job.payload
    
    # Validate Payload with ImportOrdersSalesforcePayload
    try:
        payload = await asyncio.to_thread(ExportOrdersHubspotPayload, **payload_dict)
    except Exception as e:
        logger.error(f"Invalid payload: {str(e)}")
        raise Exception(f"Invalid payload: {str(e)}")

    history_id = payload.history_id
    task = None

    try:
        task = await sync_to_async(TransferHistory.objects.get)(id=history_id)
        task.keep_alive = True
        await sync_to_async(task.save)()
    except TransferHistory.DoesNotExist:
        logger.warning("TransferHistory does not exist")
        return ExportOrdersResultPayload(
            success=False, platform="hubspot", error="TransferHistory does not exist"
        ).model_dump(mode="json")

    try:
        # Get user object
        await sync_to_async(User.objects.get)(id=payload.user)

        # Process mapping fields
        mapping_custom_fields = None
        if payload.mapping_custom_fields:
            if isinstance(payload.mapping_custom_fields, str):
                mapping_custom_fields = ast.literal_eval(payload.mapping_custom_fields)
            else:
                mapping_custom_fields = payload.mapping_custom_fields

        mapping_association_custom_fields = None
        if payload.mapping_association_custom_fields:
            if isinstance(payload.mapping_association_custom_fields, str):
                mapping_association_custom_fields = ast.literal_eval(
                    payload.mapping_association_custom_fields
                )
            else:
                mapping_association_custom_fields = (
                    payload.mapping_association_custom_fields
                )

        mapping_status_custom_fields = None
        if payload.mapping_status_custom_fields:
            if isinstance(payload.mapping_status_custom_fields, str):
                mapping_status_custom_fields = ast.literal_eval(
                    payload.mapping_status_custom_fields
                )
            else:
                mapping_status_custom_fields = (
                    payload.mapping_status_custom_fields
                )

        # Using the new CSV-based export method
        logger.info("Using new CSV-based export method for orders")

        # Call the async export function directly
        result = await export_hubspot_orders_v2(
            channel_id=payload.channel_id,
            order_ids=payload.order_ids,
            mapping_custom_fields=mapping_custom_fields,
            mapping_custom_fields_association=mapping_association_custom_fields,
            mapping_status_custom_fields=mapping_status_custom_fields,
            lang=payload.lang,
            user_id=payload.user,
            task=task,
            use_csv_import=True,
        )

        # Check result and return appropriate response
        if result.successful_exports > 0:
            logger.info(
                f"Successfully exported {result.successful_exports}/{result.total_orders} orders"
            )
            return ExportOrdersResultPayload(
                success=True,
                platform="hubspot",
                message=f"Successfully exported {result.successful_exports}/{result.total_orders} orders to HubSpot",
                exported_count=result.successful_exports,
            ).model_dump(mode="json")
        else:
            error_msg = "Export completed but no orders were successfully exported"
            if result.errors:
                error_msg = (
                    f"{error_msg}: {result.errors[0].get('error', 'Unknown error')}"
                )

            logger.warning(error_msg)
            return ExportOrdersResultPayload(
                success=False, platform="hubspot", error=error_msg
            ).model_dump(mode="json")

    except Exception as e:
        logger.error(f"Error exporting orders to HubSpot: {str(e)}")

        # Update task status if available
        if task:
            task.status = "failed"
            task.error_message = str(e)
            await sync_to_async(task.save)()

        return ExportOrdersResultPayload(
            success=False, platform="hubspot", error=str(e)
        ).model_dump(mode="json")
