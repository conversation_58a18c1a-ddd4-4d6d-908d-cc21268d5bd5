# OBJECT_TYPE CONSTANT
TYPE_OBJECT_ITEM = "commerce_items"
TYPE_OBJECT_INVENTORY = "commerce_inventory"
TYPE_OBJECT_INVENTORY_TRANSACTION = "commerce_inventory_transaction"
TYPE_OBJECT_INVENTORY_WAREHOUSE = "commerce_inventory_warehouse"
TYPE_OBJECT_WORKER = "worker"
TYPE_OBJECT_WORKER_REVIEW = "worker_review"
TYPE_OBJECT_WORKER_ABSENCE = "absence"
TYPE_OBJECT_TIMEGENIE = "timegenie"
TYPE_OBJECT_PURCHASE_ORDER = "purchaseorder"
TYPE_OBJECT_BILL = "bill"
TYPE_OBJECT_EXPENSE = "expense"

# Tools
TYPE_OBJECT_SUBSCRIPTION = "commerce_subscription"
TYPE_OBJECT_SUBSCRIPTION_PLATFORM = "subscription_platform"

# Commerce
TYPE_OBJECT_ESTIMATE = "estimates"
TYPE_OBJECT_ORDER = "commerce_orders"
TYPE_OBJECT_DELIVERY_NOTE = "delivery_slips"
TYPE_OBJECT_INVOICE = "invoices"
TYPE_OBJECT_RECEIPT = "receipts"
TYPE_OBJECT_SLIP = "slips"
TYPE_OBJECT_WORKFLOW = "workflow"
TYPE_OBJECT_COMMERCE_METER = "commerce_meter"


# Companies
TYPE_OBJECT_CONTACT = "contacts"
TYPE_OBJECT_COMPANY = "company"
TYPE_OBJECT_CASE = "customer_case"
TYPE_OBJECT_CAMPAIGN = "campaigns"
TYPE_OBJECT_CONVERSATION = "conversation"
# TYPE_OBJECT_EMAIL = 'emails'

TYPE_OBJECT_TASK = "task"
TYPE_OBJECT_JOURNAL = "journal"


TYPE_OBJECT_PANEL = "panels"
TYPE_OBJECT_DASHBOARD = "dashboards"

TYPE_OBJECT_SPENDPOCKET = "spendpocket"

TYPE_OBJECT_GOAL = "goal"
TYPE_OBJECT_FORM = "forms"
TYPE_OBJECT_PORTAL = "portal"

TYPE_OBJECT_SESSION_EVENT = "session_event"
TYPE_OBJECT_JOBS = "jobs"
TYPE_OBJECT_JOBS_APPLICANT = "jobs_applicant"
TYPE_OBJECT_IT = "it"

TYPE_OBJECT_CONTRACT = "contract"

TYPE_OBJECT_CUSTOM_OBJECT = "custom_object"

# Line item objects
TYPE_OBJECT_CASE_LINE_ITEM = "case_line_item"
TYPE_OBJECT_ORDER_LINE_ITEM = "order_line_item"

# USER OBJECT
TYPE_OBJECT_USER_MANAGEMENT = "user_management"

# TYPE_OBJECT_POST = 'post'
TYPE_OBJECTS = [
    TYPE_OBJECT_ITEM,
    TYPE_OBJECT_INVENTORY,
    TYPE_OBJECT_INVENTORY_TRANSACTION,
    TYPE_OBJECT_INVENTORY_WAREHOUSE,
    TYPE_OBJECT_PURCHASE_ORDER,
    TYPE_OBJECT_BILL,
    TYPE_OBJECT_EXPENSE,
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_SUBSCRIPTION,
    TYPE_OBJECT_SUBSCRIPTION_PLATFORM,
    TYPE_OBJECT_ESTIMATE,
    TYPE_OBJECT_DELIVERY_NOTE,
    TYPE_OBJECT_INVOICE,
    TYPE_OBJECT_RECEIPT,
    TYPE_OBJECT_SLIP,
    TYPE_OBJECT_CONTACT,
    TYPE_OBJECT_COMPANY,
    TYPE_OBJECT_CASE,
    TYPE_OBJECT_CAMPAIGN,
    TYPE_OBJECT_CONVERSATION,
    TYPE_OBJECT_TASK,
    TYPE_OBJECT_WORKFLOW,
    TYPE_OBJECT_TIMEGENIE,
    TYPE_OBJECT_PANEL,
    TYPE_OBJECT_DASHBOARD,
    TYPE_OBJECT_FORM,
    TYPE_OBJECT_WORKER,
    TYPE_OBJECT_WORKER_REVIEW,
    TYPE_OBJECT_WORKER_ABSENCE,
    TYPE_OBJECT_JOBS,
    TYPE_OBJECT_JOBS_APPLICANT,
    TYPE_OBJECT_JOURNAL,
    TYPE_OBJECT_CONTRACT,
    TYPE_OBJECT_COMMERCE_METER,
]

DEFAULT_OBJECT_DISPLAY = {
    TYPE_OBJECT_ITEM: ["item_id", "name"],
    TYPE_OBJECT_INVENTORY: ["inventory_id", "commerce_items|name"],
    TYPE_OBJECT_INVENTORY_TRANSACTION: ["transaction_id", "commerce_items|name"],
    TYPE_OBJECT_ORDER: ["order_id"],
    TYPE_OBJECT_SUBSCRIPTION: ["subscriptions_id"],
    TYPE_OBJECT_INVENTORY_WAREHOUSE: ["id_iw"],
    TYPE_OBJECT_CASE: ["deal_id"],
    TYPE_OBJECT_CONTACT: ["contact_id"],
    TYPE_OBJECT_COMPANY: ["company_id"],
    TYPE_OBJECT_PURCHASE_ORDER: ["id_po"],
    TYPE_OBJECT_BILL: ["id_bill"],
    TYPE_OBJECT_EXPENSE: ["id_pm"],
    TYPE_OBJECT_DELIVERY_NOTE: ["id_ds"],
    TYPE_OBJECT_RECEIPT: ["id_rcp"],
    TYPE_OBJECT_ESTIMATE: ["id_est"],
    TYPE_OBJECT_INVOICE: ["id_inv"],
    TYPE_OBJECT_SLIP: ["id_slip"],
    TYPE_OBJECT_TASK: ["title"],
    TYPE_OBJECT_JOBS: ["job_id"],
    TYPE_OBJECT_JOBS_APPLICANT: ["job_applicant_id"],
    TYPE_OBJECT_CONTRACT: ["contract_id", "name"],
    TYPE_OBJECT_CUSTOM_OBJECT: ["row_id"],
    TYPE_OBJECT_COMMERCE_METER: ["meter_id"],
    TYPE_OBJECT_WORKFLOW: ["workflow_id"],
}

OBJECT_TYPE_TO_URL_NAME = {
    TYPE_OBJECT_ITEM: "shopturbo_items",
    TYPE_OBJECT_INVENTORY: "shopturbo_inventory",
    TYPE_OBJECT_INVENTORY_TRANSACTION: "shopturbo_inventory_transactions",
    TYPE_OBJECT_INVENTORY_WAREHOUSE: "shopturbo_inventory_warehouse",
    TYPE_OBJECT_PURCHASE_ORDER: "purchaseorder",
    TYPE_OBJECT_BILL: "bill",
    TYPE_OBJECT_EXPENSE: "expense",
    TYPE_OBJECT_ORDER: "shopturbo",
    TYPE_OBJECT_SUBSCRIPTION: "shopturbo_subscriptions",
    TYPE_OBJECT_SUBSCRIPTION_PLATFORM: "shopturbo_subscription_platforms",
    TYPE_OBJECT_ESTIMATE: "estimates",
    TYPE_OBJECT_DELIVERY_NOTE: "delivery_slips",
    TYPE_OBJECT_INVOICE: "invoices",
    TYPE_OBJECT_RECEIPT: "receipts",
    TYPE_OBJECT_SLIP: "slips",
    TYPE_OBJECT_CONTACT: "contacts",
    TYPE_OBJECT_COMPANY: "companies",
    TYPE_OBJECT_CASE: "service_deals",
    TYPE_OBJECT_CAMPAIGN: "campaigns",
    TYPE_OBJECT_CONVERSATION: "contactline",
    TYPE_OBJECT_TASK: "taskflow",
    TYPE_OBJECT_WORKFLOW: "main_workflows",
    TYPE_OBJECT_TIMEGENIE: "timegenie",
    TYPE_OBJECT_PANEL: "report_reports",
    TYPE_OBJECT_DASHBOARD: "dashboards",
    TYPE_OBJECT_FORM: "formroom",
    TYPE_OBJECT_WORKER: "worker",
    TYPE_OBJECT_WORKER_REVIEW: "worker_review",
    TYPE_OBJECT_WORKER_ABSENCE: "absence",
    TYPE_OBJECT_JOURNAL: "journal",
    TYPE_OBJECT_SESSION_EVENT: "event",
    TYPE_OBJECT_JOBS: "jobs",
    TYPE_OBJECT_JOBS_APPLICANT: "jobs_applicant",
    TYPE_OBJECT_CONTRACT: "contractwise",
    TYPE_OBJECT_COMMERCE_METER: "commerce_meter",
}

OBJECT_SLUG_TO_OBJECT_TYPE = {
    "items": TYPE_OBJECT_ITEM,
    "inventory": TYPE_OBJECT_INVENTORY,
    "inventory_transactions": TYPE_OBJECT_INVENTORY_TRANSACTION,
    "locations": TYPE_OBJECT_INVENTORY_WAREHOUSE,
    "purchase_orders": TYPE_OBJECT_PURCHASE_ORDER,
    "bills": TYPE_OBJECT_BILL,
    "expenses": TYPE_OBJECT_EXPENSE,
    "orders": TYPE_OBJECT_ORDER,
    "subscriptions": TYPE_OBJECT_SUBSCRIPTION,
    "estimates": TYPE_OBJECT_ESTIMATE,
    "delivery_notes": TYPE_OBJECT_DELIVERY_NOTE,
    "invoices": TYPE_OBJECT_INVOICE,
    "receipts": TYPE_OBJECT_RECEIPT,
    "payments": TYPE_OBJECT_RECEIPT,  # payments URL also maps to receipts object
    "slips": TYPE_OBJECT_SLIP,
    "contacts": TYPE_OBJECT_CONTACT,
    "companies": TYPE_OBJECT_COMPANY,
    "cases": TYPE_OBJECT_CASE,
    "campaign": TYPE_OBJECT_CAMPAIGN,
    "conversation": TYPE_OBJECT_CONVERSATION,
    "projects": TYPE_OBJECT_TASK,
    "workflows": TYPE_OBJECT_WORKFLOW,
    "attendance": TYPE_OBJECT_TIMEGENIE,
    "panels": TYPE_OBJECT_PANEL,
    "dashboards": TYPE_OBJECT_DASHBOARD,
    "formroom": TYPE_OBJECT_FORM,
    "worker": TYPE_OBJECT_WORKER,
    "worker_review": TYPE_OBJECT_WORKER_REVIEW,
    "absence": TYPE_OBJECT_WORKER_ABSENCE,
    "jobs": TYPE_OBJECT_JOBS,
    "jobs_applicant": TYPE_OBJECT_JOBS_APPLICANT,
    "journal": TYPE_OBJECT_JOURNAL,
    "session_event": TYPE_OBJECT_SESSION_EVENT,
    "contract": TYPE_OBJECT_CONTRACT,
    "commerce_meter": TYPE_OBJECT_COMMERCE_METER,
}

OBJECT_TYPE_TO_SLUG = {
    TYPE_OBJECT_ITEM: "items",
    TYPE_OBJECT_INVENTORY: "inventory",
    TYPE_OBJECT_INVENTORY_TRANSACTION: "inventory_transactions",
    TYPE_OBJECT_INVENTORY_WAREHOUSE: "locations",
    TYPE_OBJECT_PURCHASE_ORDER: "purchase_orders",
    TYPE_OBJECT_BILL: "bills",
    TYPE_OBJECT_EXPENSE: "expenses",
    TYPE_OBJECT_ORDER: "orders",
    TYPE_OBJECT_SUBSCRIPTION: "subscriptions",
    TYPE_OBJECT_SUBSCRIPTION_PLATFORM: "subscription_platforms",
    TYPE_OBJECT_ESTIMATE: "estimates",
    TYPE_OBJECT_DELIVERY_NOTE: "delivery_notes",
    TYPE_OBJECT_INVOICE: "invoices",
    TYPE_OBJECT_RECEIPT: "payments",  # Changed to payments to match URL
    TYPE_OBJECT_SLIP: "slips",
    TYPE_OBJECT_CONTACT: "contacts",
    TYPE_OBJECT_COMPANY: "companies",
    TYPE_OBJECT_CASE: "cases",
    TYPE_OBJECT_CAMPAIGN: "campaign",
    TYPE_OBJECT_CONVERSATION: "conversation",
    TYPE_OBJECT_TASK: "projects",
    TYPE_OBJECT_WORKFLOW: "workflows",
    TYPE_OBJECT_TIMEGENIE: "attendance",
    TYPE_OBJECT_PANEL: "panels",
    TYPE_OBJECT_DASHBOARD: "dashboards",
    TYPE_OBJECT_FORM: "formroom",
    TYPE_OBJECT_WORKER: "worker",
    TYPE_OBJECT_WORKER_REVIEW: "worker_review",
    TYPE_OBJECT_WORKER_ABSENCE: "absence",
    TYPE_OBJECT_JOBS: "jobs",
    TYPE_OBJECT_JOBS_APPLICANT: "jobs_applicant",
    TYPE_OBJECT_JOURNAL: "journal",
    TYPE_OBJECT_SESSION_EVENT: "session_event",
    TYPE_OBJECT_CONTRACT: "contract",
    TYPE_OBJECT_COMMERCE_METER: "commerce_meter",
}

# Dynamically create LIST_OBJECTS from all TYPE_OBJECT_ string constants
# This will capture all variables in the current scope (globals of the module)
# that start with 'TYPE_OBJECT_' and are strings.
_g = globals().copy()  # Use .copy() to iterate over a snapshot
OBJECT_TYPE_LIST = {
    name: value
    for name, value in _g.items()
    if name.startswith("TYPE_OBJECT_") and isinstance(value, str)
}

LIST_COMMERCE_OBJECT_TYPES = [
    TYPE_OBJECT_ESTIMATE,
    TYPE_OBJECT_DELIVERY_NOTE,
    TYPE_OBJECT_INVOICE,
    TYPE_OBJECT_RECEIPT,
    TYPE_OBJECT_SLIP,
]


DEFAULT_ASSOCIATION_OBJECT_MEMBER = {
    TYPE_OBJECT_CASE: [
        "customer",
        TYPE_OBJECT_CONTACT,
        TYPE_OBJECT_COMPANY,
        TYPE_OBJECT_ORDER,
        TYPE_OBJECT_INVOICE,
        TYPE_OBJECT_ESTIMATE,
        TYPE_OBJECT_TASK,
        TYPE_OBJECT_PURCHASE_ORDER,
        TYPE_OBJECT_CASE,
        TYPE_OBJECT_ITEM,
    ],
    TYPE_OBJECT_ORDER: [
        "customer",
        TYPE_OBJECT_CONTACT,
        TYPE_OBJECT_COMPANY,
        TYPE_OBJECT_SUBSCRIPTION,
        TYPE_OBJECT_INVOICE,
        TYPE_OBJECT_ESTIMATE,
        TYPE_OBJECT_INVENTORY_TRANSACTION,
        TYPE_OBJECT_CASE,
        TYPE_OBJECT_PURCHASE_ORDER,
        TYPE_OBJECT_TASK,
        TYPE_OBJECT_ITEM,
        TYPE_OBJECT_INVENTORY,
    ],
    TYPE_OBJECT_CUSTOM_OBJECT: [
        TYPE_OBJECT_CASE,
        TYPE_OBJECT_CONTACT,
        TYPE_OBJECT_COMPANY,
        TYPE_OBJECT_ORDER,
        TYPE_OBJECT_SUBSCRIPTION,
        TYPE_OBJECT_INVOICE,
        TYPE_OBJECT_ITEM,
    ],
    TYPE_OBJECT_CONTACT: [
        TYPE_OBJECT_COMPANY,
        TYPE_OBJECT_CASE,
        TYPE_OBJECT_ORDER,
        TYPE_OBJECT_ESTIMATE,
        TYPE_OBJECT_DELIVERY_NOTE,
        TYPE_OBJECT_INVOICE,
        TYPE_OBJECT_RECEIPT,
        TYPE_OBJECT_SUBSCRIPTION,
        TYPE_OBJECT_EXPENSE,
        TYPE_OBJECT_BILL,
        TYPE_OBJECT_PURCHASE_ORDER,
        TYPE_OBJECT_ITEM,
    ],
    TYPE_OBJECT_INVENTORY_TRANSACTION: [
        TYPE_OBJECT_INVENTORY,
        TYPE_OBJECT_ORDER,
        TYPE_OBJECT_PURCHASE_ORDER,
    ],
    TYPE_OBJECT_INVENTORY: [
        TYPE_OBJECT_ITEM,
        TYPE_OBJECT_INVENTORY_TRANSACTION,
        TYPE_OBJECT_INVENTORY_WAREHOUSE,
        TYPE_OBJECT_ORDER,
        TYPE_OBJECT_SUBSCRIPTION
    ],
    TYPE_OBJECT_ITEM: [
        "supplier",
        TYPE_OBJECT_CONTACT,
        TYPE_OBJECT_COMPANY,
        TYPE_OBJECT_INVENTORY,
        TYPE_OBJECT_ORDER,
        TYPE_OBJECT_SUBSCRIPTION,
        TYPE_OBJECT_PURCHASE_ORDER,
        TYPE_OBJECT_CASE,
        TYPE_OBJECT_ESTIMATE,
        TYPE_OBJECT_INVOICE,
        TYPE_OBJECT_RECEIPT,
        TYPE_OBJECT_DELIVERY_NOTE,
    ],
    TYPE_OBJECT_COMPANY: [
        TYPE_OBJECT_CONTACT,
        TYPE_OBJECT_CASE,
        TYPE_OBJECT_ORDER,
        TYPE_OBJECT_ESTIMATE,
        TYPE_OBJECT_DELIVERY_NOTE,
        TYPE_OBJECT_INVOICE,
        TYPE_OBJECT_RECEIPT,
        TYPE_OBJECT_SUBSCRIPTION,
        TYPE_OBJECT_EXPENSE,
        TYPE_OBJECT_BILL,
        TYPE_OBJECT_PURCHASE_ORDER,
        TYPE_OBJECT_ITEM,
    ],
    TYPE_OBJECT_SUBSCRIPTION: [
        "customer",
        TYPE_OBJECT_CONTACT,
        TYPE_OBJECT_COMPANY,
        TYPE_OBJECT_ITEM,
        TYPE_OBJECT_INVOICE,
        TYPE_OBJECT_JOURNAL,
        TYPE_OBJECT_ORDER,
        TYPE_OBJECT_CASE,
        TYPE_OBJECT_INVENTORY
    ],
    TYPE_OBJECT_ESTIMATE: [
        "customer",
        TYPE_OBJECT_CONTACT,
        TYPE_OBJECT_COMPANY,
        TYPE_OBJECT_ORDER,
        TYPE_OBJECT_INVOICE,
        TYPE_OBJECT_CASE,
        TYPE_OBJECT_ITEM,
    ],
    TYPE_OBJECT_INVOICE: [
        "customer",
        TYPE_OBJECT_CONTACT,
        TYPE_OBJECT_COMPANY,
        TYPE_OBJECT_ORDER,
        TYPE_OBJECT_ESTIMATE,
        TYPE_OBJECT_CASE,
        TYPE_OBJECT_SUBSCRIPTION,
        TYPE_OBJECT_RECEIPT,
        TYPE_OBJECT_ITEM,
    ],
    TYPE_OBJECT_RECEIPT: [
        "customer",
        TYPE_OBJECT_CONTACT,
        TYPE_OBJECT_COMPANY,
        TYPE_OBJECT_INVOICE,
        TYPE_OBJECT_ITEM,
    ],
    TYPE_OBJECT_PURCHASE_ORDER: [
        "supplier",
        TYPE_OBJECT_CONTACT,
        TYPE_OBJECT_COMPANY,
        TYPE_OBJECT_ORDER,
        TYPE_OBJECT_INVENTORY_TRANSACTION,
        TYPE_OBJECT_CASE,
        TYPE_OBJECT_ITEM,
    ],
    TYPE_OBJECT_DELIVERY_NOTE: [
        "customer",
        TYPE_OBJECT_CONTACT,
        TYPE_OBJECT_COMPANY,
        TYPE_OBJECT_INVOICE,
        TYPE_OBJECT_ORDER,
        TYPE_OBJECT_ITEM,
    ],
    TYPE_OBJECT_BILL: [
        "partner",
        TYPE_OBJECT_CONTACT,
        TYPE_OBJECT_COMPANY,
        TYPE_OBJECT_JOURNAL,
    ],
}


DEAFULT_ASSOCIATION_OBJECT_TARGET = {
    "customer": [TYPE_OBJECT_COMPANY, TYPE_OBJECT_CONTACT],
    "supplier": [TYPE_OBJECT_COMPANY, TYPE_OBJECT_CONTACT],
    "partner" : [TYPE_OBJECT_COMPANY, TYPE_OBJECT_CONTACT],
}

TYPE_OBJECT_TO_DB_MODEL = {
    TYPE_OBJECT_ITEM: "shopturboitems",
    TYPE_OBJECT_INVENTORY: "shopturboinventory",
    TYPE_OBJECT_INVENTORY_TRANSACTION: "inventorytransaction",
    TYPE_OBJECT_INVENTORY_WAREHOUSE: "inventorywarehouse",
    TYPE_OBJECT_PURCHASE_ORDER: "purchaseorders",
    TYPE_OBJECT_BILL: "bill",
    TYPE_OBJECT_EXPENSE: "expense",
    TYPE_OBJECT_ORDER: "shopturboorders",
    TYPE_OBJECT_SUBSCRIPTION: "subscriptions",
    TYPE_OBJECT_SUBSCRIPTION_PLATFORM: "shopturbosubscriptionplatforms",
    TYPE_OBJECT_ESTIMATE: "estimate",
    TYPE_OBJECT_DELIVERY_NOTE: "deliveryslip",
    TYPE_OBJECT_INVOICE: "invoice",
    TYPE_OBJECT_RECEIPT: "receipt",
    TYPE_OBJECT_SLIP: "slip",
    TYPE_OBJECT_CONTACT: "contact",
    TYPE_OBJECT_COMPANY: "company",
    TYPE_OBJECT_CASE: "deals",
    TYPE_OBJECT_CAMPAIGN: "campaigns",
    TYPE_OBJECT_CONVERSATION: "conversation",
    TYPE_OBJECT_TASK: "task",
    TYPE_OBJECT_WORKFLOW: "workflow",
    TYPE_OBJECT_TIMEGENIE: "timegenie",
    TYPE_OBJECT_PANEL: "panels",
    TYPE_OBJECT_DASHBOARD: "dashboards",
    TYPE_OBJECT_FORM: "formroom",
    TYPE_OBJECT_WORKER: "worker",
    TYPE_OBJECT_WORKER_REVIEW: "worker_review",
    TYPE_OBJECT_WORKER_ABSENCE: "absence",
    TYPE_OBJECT_JOBS: "jobs",
    TYPE_OBJECT_JOBS_APPLICANT: "jobsapplicant",
    TYPE_OBJECT_JOURNAL: "journalentry",
    TYPE_OBJECT_SESSION_EVENT: "sessionevent",
    TYPE_OBJECT_CONTRACT: "contract",
    TYPE_OBJECT_COMMERCE_METER: "commerce_meter",
    TYPE_OBJECT_CUSTOM_OBJECT: "customobjectpropertyrow",
}

OBJ_CUSTOM_FIELD_MAP_PAGE_GROUP_TYPE = {
    "components": TYPE_OBJECT_ITEM,
    "commerce_items": TYPE_OBJECT_ITEM,  # Add mapping for commerce_items
    "bill_objects": TYPE_OBJECT_BILL,
    "invoice_objects": TYPE_OBJECT_INVOICE,
    "contact": TYPE_OBJECT_CONTACT,
    "company": TYPE_OBJECT_COMPANY,
    "deal": TYPE_OBJECT_CASE,
    "purchase_order": TYPE_OBJECT_PURCHASE_ORDER,
    "subscription": TYPE_OBJECT_SUBSCRIPTION,
    "warehouse_objects": TYPE_OBJECT_INVENTORY_WAREHOUSE,
}

SIMPLE_TYPE_MAPPING = {
    "text": "string",
    "text-area": "string",
    "choice": "string",
    "formula": "string",
    "number": "numeric",
    "date": "datetime",
    "date_time": "datetime",
}

ALIAS_TABLE = {
    "cases": "deals",
    "items": "shopturboitems",
    "inventory": "shopturboinventory",
    "inventory_transaction": "inventorytransaction",
    "inventory_warehouse": "inventorywarehouse",
    "worker": "worker",
    "journal": "journalentry",
    "purchaseorder": "purchaseorders",
    "billing": "bill",
    "bill": "bill",
    "expense": "expense",
    "subscription": "shopturbosubscriptions",
    "estimates": "estimate",
    "orders": "shopturboorders",
    "delivery_slips": "deliveryslip",
    "invoices": "invoice",
    "receipts": "receipt",
    "slips": "slip",
    "contacts": "contacts",
    "company": "company",
    "campaigns": "post",
    "conversation": "message",
    "task": "taskcustomfield",
    "production": "production",
    "panels": "reportpanel",
    "dashboards": "report",
    "contract": "contractdocument",
}
