from data.models import (
    Workspace, 
    AssociationLabel,
    TYPE_OBJECT_INVENTORY,
    TYPE_OBJECT_INVENTORY_TRANSACTION,
    TYPE_OBJECT_CONTACT,
    TYPE_OBJECT_COMPANY,
    TYPE_OBJECT_CASE,
    TYPE_OBJECT_ITEM,
    TYPE_OBJECT_INVOICE,
    TYPE_OBJECT_RECEIPT,
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_SUBSCRIPTION,
    TYPE_OBJECT_PURCHASE_ORDER,
    TYPE_OBJECT_ESTIMATE,
    TYPE_OBJECT_DELIVERY_NOTE,
    TYPE_OBJECT_BILL,
    TYPE_OBJECT_JOURNAL
)
from data.models.inventory import ShopTurboInventory, InventoryTransaction
from data.models.customer import Contact, Company
from data.models.order import ShopTurboOrders
from data.models.subscription import ShopTurboSubscriptions
from data.models.deals import Deals
from data.models.expensebill import PurchaseOrders
from data.models.item import ShopTurboItems
from data.models import AssociationLabelObject,Estimate
from data.models.expensebill import Invoice, Receipt, Bill, DeliverySlip
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = 'Populate association labels for inventory, inventory transaction, contact, company, case, order, subscription, purchase order, and delivery note objects with related data.'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--delete',
            action='store_true',
            help='Delete existing association labels before recreating (default: False)',
        )
        parser.add_argument(
            '--object-types',
            nargs='+',
            choices=['order','subscription','estimate','invoice','payment','purchase_order','item','bill','delivery_note', 'all'],
            default=['order', 'subscription','estimate','invoice','payment','purchase_order','item','bill','delivery_note'],
            help='Object types to process (default: all). Options: order, subscription,estimate,invoice,payment,purchase_order,item,bill,delivery_note, all',
        )
        parser.add_argument(
            '--workspace',
            type=str,
            help='Process only specific workspace by name (optional)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without actually updating the database (default: False)',
        )

    def handle(self, *args, **options):
        delete_existing = options['delete']
        object_types = options['object_types']
        workspace_name = options['workspace']
        dry_run = options['dry_run']
        
        if 'payment' in object_types:
            object_types.remove('payment')
            object_types.append('receipt')

        # Handle 'all' option
        if 'all' in object_types:
            object_types = ['order','subscription','estimate','invoice','receipt','purchase_order','item','bill','delivery_note', 'all']
            
        
        self.stdout.write("Starting association label population script...")
        self.stdout.write(f"Delete existing: {delete_existing}")
        self.stdout.write(f"Dry run: {dry_run}")
        self.stdout.write(f"Object types: {object_types}")
        
        # Filter workspaces if specific workspace is requested
        workspaces = Workspace.objects.all()
        if workspace_name:
            workspaces = workspaces.filter(name__icontains=workspace_name)
            if not workspaces.exists():
                self.stdout.write(f"No workspace found matching '{workspace_name}'")
                return
        
        for workspace in workspaces:
            self.stdout.write(f"\nProcessing workspace: {workspace.name}")
            
            if delete_existing:
                if 'inventory' in object_types:
                    deleted_count = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_INVENTORY
                    ).count()
                    AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_INVENTORY
                    ).delete()
                    self.stdout.write(f"    Deleted {deleted_count} existing inventory association labels")
                    
                if 'inventory_transaction' in object_types:
                    deleted_count = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_INVENTORY_TRANSACTION
                    ).count()
                    AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_INVENTORY_TRANSACTION
                    ).delete()
                    self.stdout.write(f"    Deleted {deleted_count} existing inventory transaction association labels")
                    
                if 'contact' in object_types:
                    deleted_count = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_CONTACT
                    ).count()
                    AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_CONTACT
                    ).delete()
                    self.stdout.write(f"    Deleted {deleted_count} existing contact association labels")
                    
                if 'company' in object_types:
                    deleted_count = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_COMPANY
                    ).count()
                    AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_COMPANY
                    ).delete()
                    self.stdout.write(f"    Deleted {deleted_count} existing company association labels")
                    
                if 'order' in object_types:
                    deleted_count = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_ORDER
                    ).count()
                    AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_ORDER
                    ).delete()
                    self.stdout.write(f"    Deleted {deleted_count} existing order association labels")
                    
                if 'subscription' in object_types:
                    deleted_count = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_SUBSCRIPTION
                    ).count()
                    AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_SUBSCRIPTION
                    ).delete()
                    self.stdout.write(f"    Deleted {deleted_count} existing subscription association labels")
                    
                if 'case' in object_types:
                    deleted_count = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_CASE
                    ).count()
                    AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_CASE
                    ).delete()
                    self.stdout.write(f"    Deleted {deleted_count} existing case association labels")

                if 'estimate' in object_types:
                    deleted_count = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_ESTIMATE
                    ).count()
                    AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_ESTIMATE
                    ).delete()
                    self.stdout.write(f"    Deleted {deleted_count} existing estimate association labels")
                    
                if 'invoice' in object_types:
                    deleted_count = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_INVOICE
                    ).count()
                    AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_INVOICE
                    ).delete()
                    self.stdout.write(f"    Deleted {deleted_count} existing invoice association labels")
                    
                if 'receipt' in object_types:
                    deleted_count = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_RECEIPT
                    ).count()
                    AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_RECEIPT
                    ).delete()
                    self.stdout.write(f"    Deleted {deleted_count} existing receipt association labels")
                    
                if 'purchase_order' in object_types:
                    deleted_count = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_PURCHASE_ORDER
                    ).count()
                    AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_PURCHASE_ORDER
                    ).delete()
                    self.stdout.write(f"    Deleted {deleted_count} existing purchase order association labels")

                if 'item' in object_types:
                    deleted_count = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_ITEM
                    ).count()
                    AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_ITEM
                    ).delete()
                    self.stdout.write(f"    Deleted {deleted_count} existing item association labels")

                if 'bill' in object_types:
                    deleted_count = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_BILL
                    ).count()
                    AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_BILL
                    ).delete()
                    self.stdout.write(f"    Deleted {deleted_count} existing bill association labels")
                    
                if 'delivery_note' in object_types:
                    deleted_count = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_DELIVERY_NOTE
                    ).count()
                    AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_DELIVERY_NOTE
                    ).delete()
                    self.stdout.write(f"    Deleted {deleted_count} existing delivery note association labels")
            
            if 'inventory' in object_types:
                self.populate_inventory_association_labels(workspace, delete_existing, dry_run)
            
            if 'inventory_transaction' in object_types:
                self.populate_inventory_transaction_association_labels(workspace, delete_existing, dry_run)
            
            if 'contact' in object_types:
                self.populate_contact_association_labels(workspace, delete_existing, dry_run)
                
            if 'company' in object_types:
                self.populate_company_association_labels(workspace, delete_existing, dry_run)
                
            if 'order' in object_types:
                self.populate_order_association_labels(workspace, delete_existing, dry_run)
                
            if 'subscription' in object_types:
                self.populate_subscription_association_labels(workspace, delete_existing, dry_run)
                
            if 'case' in object_types:
                self.populate_case_association_labels(workspace, delete_existing, dry_run)
            
            if 'estimate' in object_types:
                self.populate_estimate_association_labels(workspace, delete_existing, dry_run)
            
            if 'invoice' in object_types:
                self.populate_invoice_association_labels(workspace, delete_existing, dry_run)
            
            if 'receipt' in object_types:
                self.populate_receipt_association_labels(workspace, delete_existing, dry_run)
            
            if 'purchase_order' in object_types:
                self.populate_purchase_order_association_labels(workspace, delete_existing, dry_run)
            
            if 'item' in object_types:
                self.populate_item_association_labels(workspace, delete_existing, dry_run)
            
            if 'bill' in object_types:
                self.populate_bill_association_labels(workspace, delete_existing, dry_run)
            
            if 'delivery_note' in object_types:
                self.populate_delivery_note_association_labels(workspace, delete_existing, dry_run)
            
            self.stdout.write("=" * 50)
            self.stdout.write("POPULATION COMPLETED!")
            self.stdout.write(f"Workspace: {workspace.name}")
            self.stdout.write("=" * 50)
    def populate_estimate_association_labels(self, workspace, delete_existing, dry_run):
        """
        Populate association labels for estimates
        """
        self.stdout.write("  Processing estimate associations...")
        
        # Get all estimates in workspace
        estimates = Estimate.objects.filter(workspace=workspace).order_by("-id_est")
        
        updated_count = 0
        
        for estimate in estimates:
            if not estimate.is_latest():continue
            self.stdout.write(f"Process latest version {estimate.id_est} - {estimate.version}")
            if dry_run:
                self.stdout.write(f"    [DRY RUN] Estimate {estimate.id_est}")
            else:    
                # Add contact associations
                if estimate.contact:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_ESTIMATE, 
                        label='customer'
                    ).first()
                    if association_label:
                        print("association_label: ",association_label.label," ", estimate.contact)
                        AssociationLabelObject.reset_associations_for_object(
                            estimate, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            estimate, 
                            estimate.contact, 
                            workspace, 
                            association_label
                        )

                # Add company associations
                if estimate.company:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_ESTIMATE, 
                        label='customer'
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            estimate, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            estimate, 
                            estimate.company, 
                            workspace, 
                            association_label
                        )
                
                #invoice
                if estimate.invoice.exists():
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_ESTIMATE, 
                        label__iexact=TYPE_OBJECT_INVOICE
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            estimate, 
                            workspace, 
                            association_label
                        )
                        for transaction in estimate.invoice.all():
                            AssociationLabelObject.create_association(
                                estimate, 
                                transaction, 
                                workspace, 
                                association_label
                            )

                # Add order associations
                orders = ShopTurboOrders.objects.filter(workspace=workspace,estimate=estimate).order_by('order_id')
                for order in orders:
                    if order.estimate.exists():
                        association_label = AssociationLabel.objects.filter(
                            workspace=workspace, 
                            object_source=TYPE_OBJECT_ORDER, 
                            label__iexact=TYPE_OBJECT_ESTIMATE
                        ).first()
                        if association_label:
                            AssociationLabelObject.reset_associations_for_object(
                                order, 
                                workspace, 
                                association_label
                            )
                            for transaction in order.estimate.all():
                                AssociationLabelObject.create_association(
                                    order, 
                                    transaction, 
                                    workspace, 
                                    association_label
                                )

            updated_count += 1
        
        self.stdout.write(f"    Processed {updated_count} estimates")

    def populate_inventory_association_labels(self, workspace, delete_existing, dry_run):
        """
        Populate association labels for inventory
        """
        self.stdout.write("  Processing inventory associations...")
        
        # Get all inventory in workspace
        inventories = ShopTurboInventory.objects.filter(workspace=workspace).order_by("-inventory_id")
        
        updated_count = 0
        
        for inventory in inventories:
            if dry_run:
                self.stdout.write(f"    [DRY RUN] Inventory {inventory.inventory_id}")
            else:    
                # Add item associations
                if inventory.item:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_INVENTORY, 
                        label__iexact=TYPE_OBJECT_ITEM
                    ).first()
                    if association_label:
                        print("association_label: ",association_label.label," ", inventory.item)
                        AssociationLabelObject.reset_associations_for_object(
                            inventory, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            inventory, 
                            inventory.item, 
                            workspace, 
                            association_label
                        )

                # Add contact associations
                if hasattr(inventory, 'contact') and inventory.contact:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_INVENTORY, 
                        label='customer'
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            inventory, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            inventory, 
                            inventory.contact, 
                            workspace, 
                            association_label
                        )

                # Add company associations
                if hasattr(inventory, 'company') and inventory.company:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_INVENTORY, 
                        label='customer'
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            inventory, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            inventory, 
                            inventory.company, 
                            workspace, 
                            association_label
                        )

            updated_count += 1
        
        self.stdout.write(f"    Processed {updated_count} inventories")

    def populate_inventory_transaction_association_labels(self, workspace, delete_existing, dry_run):
        """
        Populate association labels for inventory transactions
        """
        self.stdout.write("  Processing inventory transaction associations...")
        
        # Get all inventory transactions in workspace
        transactions = InventoryTransaction.objects.filter(workspace=workspace).order_by("-transaction_id")
        
        updated_count = 0
        
        for transaction in transactions:
            if dry_run:
                self.stdout.write(f"    [DRY RUN] Transaction {transaction.transaction_id}")
            else:    
                # Add inventory associations
                if transaction.inventory:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_INVENTORY_TRANSACTION, 
                        label__iexact=TYPE_OBJECT_INVENTORY
                    ).first()
                    if association_label:
                        print("association_label: ",association_label.label," ", transaction.inventory)
                        AssociationLabelObject.reset_associations_for_object(
                            transaction, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            transaction, 
                            transaction.inventory, 
                            workspace, 
                            association_label
                        )

                # Add item associations (through inventory)
                if transaction.inventory and transaction.inventory.item:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_INVENTORY_TRANSACTION, 
                        label__iexact=TYPE_OBJECT_ITEM
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            transaction, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            transaction, 
                            transaction.inventory.item, 
                            workspace, 
                            association_label
                        )

                # Add contact associations
                if hasattr(transaction, 'contact') and transaction.contact:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_INVENTORY_TRANSACTION, 
                        label='customer'
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            transaction, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            transaction, 
                            transaction.contact, 
                            workspace, 
                            association_label
                        )

                # Add company associations
                if hasattr(transaction, 'company') and transaction.company:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_INVENTORY_TRANSACTION, 
                        label='customer'
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            transaction, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            transaction, 
                            transaction.company, 
                            workspace, 
                            association_label
                        )

            updated_count += 1
        
        self.stdout.write(f"    Processed {updated_count} inventory transactions")

    def populate_contact_association_labels(self, workspace, delete_existing, dry_run):
        """
        Populate association labels for contacts
        """
        self.stdout.write("  Processing contact associations...")
        
        # Get all contacts in workspace
        contacts = Contact.objects.filter(workspace=workspace).order_by("-contact_id")
        
        updated_count = 0
        
        for contact in contacts:
            if dry_run:
                self.stdout.write(f"    [DRY RUN] Contact {contact.contact_id}")
            else:    
                # Add company associations
                if contact.company:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_CONTACT, 
                        label__iexact=TYPE_OBJECT_COMPANY
                    ).first()
                    if association_label:
                        print("association_label: ",association_label.label," ", contact.company)
                        AssociationLabelObject.reset_associations_for_object(
                            contact, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            contact, 
                            contact.company, 
                            workspace, 
                            association_label
                        )

                # Add order associations
                if hasattr(contact, 'orders') and contact.orders.exists():
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_CONTACT, 
                        label__iexact=TYPE_OBJECT_ORDER
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            contact, 
                            workspace, 
                            association_label
                        )
                        for order in contact.orders.all():
                            AssociationLabelObject.create_association(
                                contact, 
                                order, 
                                workspace, 
                                association_label
                            )

                # Add subscription associations
                if hasattr(contact, 'subscriptions') and contact.subscriptions.exists():
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_CONTACT, 
                        label__iexact=TYPE_OBJECT_SUBSCRIPTION
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            contact, 
                            workspace, 
                            association_label
                        )
                        for subscription in contact.subscriptions.all():
                            AssociationLabelObject.create_association(
                                contact, 
                                subscription, 
                                workspace, 
                                association_label
                            )

                # Add invoice associations
                if hasattr(contact, 'invoices') and contact.invoices.exists():
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_CONTACT, 
                        label__iexact=TYPE_OBJECT_INVOICE
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            contact, 
                            workspace, 
                            association_label
                        )
                        for invoice in contact.invoices.all():
                            AssociationLabelObject.create_association(
                                contact, 
                                invoice, 
                                workspace, 
                                association_label
                            )

            updated_count += 1
        
        self.stdout.write(f"    Processed {updated_count} contacts")

    def populate_company_association_labels(self, workspace, delete_existing, dry_run):
        """
        Populate association labels for companies
        """
        self.stdout.write("  Processing company associations...")
        
        # Get all companies in workspace
        companies = Company.objects.filter(workspace=workspace).order_by("-company_id")
        
        updated_count = 0
        
        for company in companies:
            if dry_run:
                self.stdout.write(f"    [DRY RUN] Company {company.company_id}")
            else:    
                # Add contact associations
                if hasattr(company, 'contacts') and company.contacts.exists():
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_COMPANY, 
                        label__iexact=TYPE_OBJECT_CONTACT
                    ).first()
                    if association_label:
                        print("association_label: ",association_label.label," ", company.contacts.count())
                        AssociationLabelObject.reset_associations_for_object(
                            company, 
                            workspace, 
                            association_label
                        )
                        for contact in company.contacts.all():
                            AssociationLabelObject.create_association(
                                company, 
                                contact, 
                                workspace, 
                                association_label
                            )

                # Add order associations
                if hasattr(company, 'orders') and company.orders.exists():
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_COMPANY, 
                        label__iexact=TYPE_OBJECT_ORDER
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            company, 
                            workspace, 
                            association_label
                        )
                        for order in company.orders.all():
                            AssociationLabelObject.create_association(
                                company, 
                                order, 
                                workspace, 
                                association_label
                            )

                # Add subscription associations
                if hasattr(company, 'subscriptions') and company.subscriptions.exists():
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_COMPANY, 
                        label__iexact=TYPE_OBJECT_SUBSCRIPTION
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            company, 
                            workspace, 
                            association_label
                        )
                        for subscription in company.subscriptions.all():
                            AssociationLabelObject.create_association(
                                company, 
                                subscription, 
                                workspace, 
                                association_label
                            )

                # Add invoice associations
                if hasattr(company, 'invoices') and company.invoices.exists():
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_COMPANY, 
                        label__iexact=TYPE_OBJECT_INVOICE
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            company, 
                            workspace, 
                            association_label
                        )
                        for invoice in company.invoices.all():
                            AssociationLabelObject.create_association(
                                company, 
                                invoice, 
                                workspace, 
                                association_label
                            )

            updated_count += 1
        
        self.stdout.write(f"    Processed {updated_count} companies")

    def populate_order_association_labels(self, workspace, delete_existing, dry_run):
        """
        Populate association labels for orders
        """
        self.stdout.write("  Processing order associations...")
        
        # Get all orders in workspace
        orders = ShopTurboOrders.objects.filter(workspace=workspace).order_by("-order_id")
        
        updated_count = 0
        
        for order in orders:
            if dry_run:
                self.stdout.write(f"    [DRY RUN] Order {order.order_id}")
            else:    
                # Add contact associations
                if order.contact:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_ORDER, 
                        label='customer'
                    ).first()
                    if association_label:
                        print("association_label: ",association_label.label," ", order.contact)
                        AssociationLabelObject.reset_associations_for_object(
                            order, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            order, 
                            order.contact, 
                            workspace, 
                            association_label
                        )

                # Add company associations
                if order.company:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_ORDER, 
                        label='customer'
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            order, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            order, 
                            order.company, 
                            workspace, 
                            association_label
                        )

                if order.subscription:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_ORDER, 
                        label__iexact=TYPE_OBJECT_SUBSCRIPTION
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            order, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            order, 
                            order.subscription, 
                            workspace, 
                            association_label
                        )
                
            #inventory transaction
            if order.inventory_transactions.exists():
                association_label = AssociationLabel.objects.filter(
                    workspace=workspace, 
                    object_source=TYPE_OBJECT_ORDER, 
                    label__iexact=TYPE_OBJECT_INVENTORY_TRANSACTION
                ).first()
                if association_label:
                    AssociationLabelObject.reset_associations_for_object(
                        order, 
                        workspace, 
                        association_label
                    )
                    for transaction in order.inventory_transactions.all():
                        AssociationLabelObject.create_association(
                            order, 
                            transaction, 
                            workspace, 
                            association_label
                        )

            #invoice, estimate, purchase_order, cases, deliveryslip, inventory transaction
            if order.invoice.exists():
                association_label = AssociationLabel.objects.filter(
                    workspace=workspace, 
                    object_source=TYPE_OBJECT_ORDER, 
                    label__iexact=TYPE_OBJECT_INVOICE
                ).first()
                if association_label:
                    AssociationLabelObject.reset_associations_for_object(
                        order, 
                        workspace, 
                        association_label
                    )
                    for invoice in order.invoice.all():
                        AssociationLabelObject.create_association(
                            order, 
                            invoice, 
                            workspace, 
                            association_label
                        )
            if order.estimate.exists():
                association_label = AssociationLabel.objects.filter(
                    workspace=workspace, 
                    object_source=TYPE_OBJECT_ORDER, 
                    label__iexact=TYPE_OBJECT_ESTIMATE
                ).first()
                if association_label:
                    AssociationLabelObject.reset_associations_for_object(
                        order, 
                        workspace, 
                        association_label
                    )
                    for estimate in order.estimate.all():
                        AssociationLabelObject.create_association(
                            order, 
                            estimate, 
                            workspace, 
                            association_label
                        )
            if order.purchase_orders.exists():
                association_label = AssociationLabel.objects.filter(
                    workspace=workspace, 
                    object_source=TYPE_OBJECT_ORDER, 
                    label__iexact=TYPE_OBJECT_PURCHASE_ORDER
                ).first()
                if association_label:
                    AssociationLabelObject.reset_associations_for_object(
                        order, 
                        workspace, 
                        association_label
                    )
                    for purchase_order in order.purchase_orders.all():
                        AssociationLabelObject.create_association(
                            order, 
                            purchase_order, 
                            workspace, 
                            association_label
                        )
            if order.cases.exists():
                association_label = AssociationLabel.objects.filter(
                    workspace=workspace, 
                    object_source=TYPE_OBJECT_ORDER, 
                    label__iexact=TYPE_OBJECT_CASE
                ).first()
                if association_label:
                    AssociationLabelObject.reset_associations_for_object(
                        order, 
                        workspace, 
                        association_label
                    )
                    for case in order.cases.all():
                        AssociationLabelObject.create_association(
                            order, 
                            case, 
                            workspace, 
                            association_label
                        )
            if order.deliveryslip.exists():
                association_label = AssociationLabel.objects.filter(
                    workspace=workspace, 
                    object_source=TYPE_OBJECT_ORDER, 
                    label__iexact=TYPE_OBJECT_DELIVERY_NOTE
                ).first()
                if association_label:
                    AssociationLabelObject.reset_associations_for_object(
                        order, 
                        workspace, 
                        association_label
                    )
                    for deliveryslip in order.deliveryslip.all():
                        AssociationLabelObject.create_association(
                            order, 
                            deliveryslip, 
                            workspace, 
                            association_label
                        )   

            updated_count += 1
        
        self.stdout.write(f"    Processed {updated_count} orders")

    def populate_subscription_association_labels(self, workspace, delete_existing, dry_run):
        """
        Populate association labels for subscriptions
        """
        self.stdout.write("  Processing subscription associations...")
        
        # Get all subscriptions in workspace
        subscriptions = ShopTurboSubscriptions.objects.filter(workspace=workspace).order_by("-subscriptions_id")
        
        updated_count = 0
        
        for subscription in subscriptions:
            if dry_run:
                self.stdout.write(f"    [DRY RUN] Subscription {subscription.subscriptions_id}")
            else:    
                # Add contact associations
                if subscription.contact:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_SUBSCRIPTION, 
                        label='customer'
                    ).first()
                    if association_label:
                        print("association_label: ",association_label.label," ", subscription.contact)
                        AssociationLabelObject.reset_associations_for_object(
                            subscription, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            subscription, 
                            subscription.contact, 
                            workspace, 
                            association_label
                        )

                # Add company associations
                if subscription.company:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_SUBSCRIPTION, 
                        label='customer'
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            subscription, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            subscription, 
                            subscription.company, 
                            workspace, 
                            association_label
                        )

                # Add item associations
                if subscription.item:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_SUBSCRIPTION, 
                        label__iexact=TYPE_OBJECT_ITEM
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            subscription, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            subscription, 
                            subscription.item, 
                            workspace, 
                            association_label
                        )

                # Add invoice associations
                if subscription.invoices.exists():
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_SUBSCRIPTION, 
                        label__iexact=TYPE_OBJECT_INVOICE
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            subscription, 
                            workspace, 
                            association_label
                        )
                        for invoice in subscription.invoices.all():
                            AssociationLabelObject.create_association(
                                subscription, 
                                invoice, 
                                workspace, 
                                association_label
                            )

                # Add order associations
                if subscription.orders.exists():
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_SUBSCRIPTION, 
                        label__iexact=TYPE_OBJECT_ORDER
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            subscription, 
                            workspace, 
                            association_label
                        )
                        for order in subscription.orders.all():
                            AssociationLabelObject.create_association(
                                subscription, 
                                order, 
                                workspace, 
                                association_label
                            )

            updated_count += 1
        
        self.stdout.write(f"    Processed {updated_count} subscriptions")

    def populate_case_association_labels(self, workspace, delete_existing, dry_run):
        """
        Populate association labels for cases/deals
        """
        self.stdout.write("  Processing case associations...")
        
        # Get all cases in workspace
        cases = Deals.objects.filter(workspace=workspace).order_by("-deal_id")
        
        updated_count = 0
        
        for case in cases:
            if dry_run:
                self.stdout.write(f"    [DRY RUN] Case {case.deal_id}")
            else:    
                # Add contact associations
                if case.contact:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_CASE, 
                        label='customer'
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            case, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            case, 
                            case.contact, 
                            workspace, 
                            association_label
                        )

                # Add company associations
                if case.company:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_CASE, 
                        label='customer'
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            case, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            case, 
                            case.company, 
                            workspace, 
                            association_label
                        )
                
                # Add order associations
                if hasattr(case, 'orders') and case.orders.exists():
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_CASE, 
                        label__iexact=TYPE_OBJECT_ORDER
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            case, 
                            workspace, 
                            association_label
                        )
                        for order in case.orders.all():
                            AssociationLabelObject.create_association(
                                case, 
                                order, 
                                workspace, 
                                association_label
                            )
                
                # Add subscription associations
                if hasattr(case, 'subscriptions') and case.subscriptions.exists():
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_CASE, 
                        label__iexact=TYPE_OBJECT_SUBSCRIPTION
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            case, 
                            workspace, 
                            association_label
                        )
                        for subscription in case.subscriptions.all():
                            AssociationLabelObject.create_association(
                                case, 
                                subscription, 
                                workspace, 
                                association_label
                            )

                # Add invoices associations
                


            updated_count += 1
        
        self.stdout.write(f"    Processed {updated_count} cases")

    def populate_invoice_association_labels(self, workspace, delete_existing, dry_run):
        """
        Populate association labels for invoices
        """
        self.stdout.write("  Processing invoice associations...")
        
        # Get all invoices in workspace
        invoices = Invoice.objects.filter(workspace=workspace).order_by("-id")
        
        updated_count = 0
        
        for invoice in invoices:
            if dry_run:
                self.stdout.write(f"    [DRY RUN] Invoice {invoice.id}")
            else:    
                # Add contact associations
                if invoice.contact:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_INVOICE, 
                        label__iexact=TYPE_OBJECT_CONTACT
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            invoice, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            invoice, 
                            invoice.contact, 
                            workspace, 
                            association_label
                        )
                
                # Add company associations
                if invoice.company:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_INVOICE, 
                        label__iexact=TYPE_OBJECT_COMPANY
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            invoice, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            invoice, 
                            invoice.company, 
                            workspace, 
                            association_label
                        )
                
                # add orders 
                if invoice.orders.exists():
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_INVOICE, 
                        label__iexact=TYPE_OBJECT_ORDER
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            invoice, 
                            workspace, 
                            association_label
                        )
                        for order in invoice.orders.all():
                            AssociationLabelObject.create_association(
                                invoice, 
                                order, 
                                workspace, 
                                association_label
                            )
                            
                # add receipt
                if invoice.receipts.exists():
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_INVOICE, 
                        label__iexact=TYPE_OBJECT_RECEIPT
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            invoice, 
                            workspace, 
                            association_label
                        )
                        for receipt in invoice.receipts.all():
                            AssociationLabelObject.create_association(
                                invoice, 
                                receipt, 
                                workspace, 
                                association_label
                            )


                updated_count += 1
        
        self.stdout.write(f"    Processed {updated_count} invoices")

    def populate_receipt_association_labels(self, workspace, delete_existing, dry_run):
        """
        Populate association labels for receipts
        """
        self.stdout.write("  Processing receipt associations...")
        
        # Get all receipts in workspace
        receipts = Receipt.objects.filter(workspace=workspace).order_by("-id")
        
        updated_count = 0
        
        for receipt in receipts:
            if dry_run:
                self.stdout.write(f"    [DRY RUN] Receipt {receipt.id}")
            else:    
                # Add contact associations
                if receipt.contact:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_RECEIPT, 
                        label__iexact=TYPE_OBJECT_CONTACT
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            receipt, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            receipt, 
                            receipt.contact, 
                            workspace, 
                            association_label
                        )
                
                # Add company associations
                if receipt.company:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_RECEIPT, 
                        label__iexact=TYPE_OBJECT_COMPANY
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            receipt, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            receipt, 
                            receipt.company, 
                            workspace, 
                            association_label
                        )

                #add invoice associations
                if receipt.invoices.exists():
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_RECEIPT, 
                        label__iexact=TYPE_OBJECT_INVOICE
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            receipt, 
                            workspace, 
                            association_label
                        )
                        for invoice in receipt.invoices.all():
                            AssociationLabelObject.create_association(
                                receipt, 
                                invoice, 
                                workspace, 
                                association_label
                            )
                
                updated_count += 1
        
        self.stdout.write(f"    Processed {updated_count} receipts")

    def populate_purchase_order_association_labels(self, workspace, delete_existing, dry_run):
        """
        Populate association labels for purchase orders
        """
        self.stdout.write("  Processing purchase order associations...")
        
        # Get all purchase orders in workspace
        purchase_orders = PurchaseOrders.objects.filter(workspace=workspace).order_by("-id_po")
        
        updated_count = 0
        
        for purchase_order in purchase_orders:
            if dry_run:
                self.stdout.write(f"    [DRY RUN] Purchase Order {purchase_order.id_po}")
            else:    
                # Add contact associations (supplier)
                if purchase_order.contact:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_PURCHASE_ORDER, 
                        label='supplier'
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            purchase_order, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            purchase_order, 
                            purchase_order.contact, 
                            workspace, 
                            association_label
                        )

                # Add company associations (supplier)
                if purchase_order.company:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_PURCHASE_ORDER, 
                        label='supplier'
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            purchase_order, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            purchase_order, 
                            purchase_order.company, 
                            workspace, 
                            association_label
                        )

                # Add inventory transaction associations
                if hasattr(purchase_order, 'inventory_transactions') and purchase_order.inventory_transactions.exists():
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_PURCHASE_ORDER, 
                        label__iexact=TYPE_OBJECT_INVENTORY_TRANSACTION
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            purchase_order, 
                            workspace, 
                            association_label
                        )
                        for transaction in purchase_order.inventory_transactions.all():
                            AssociationLabelObject.create_association(
                                purchase_order, 
                                transaction, 
                                workspace, 
                                association_label
                            )
        

            updated_count += 1
        
        self.stdout.write(f"    Processed {updated_count} purchase orders")

    def populate_item_association_labels(self, workspace, delete_existing, dry_run):
        """
        Populate association labels for Item
        """
        self.stdout.write("  Processing Item associations...")
        
        # Get all purchase orders in workspace
        items = ShopTurboItems.objects.filter(workspace=workspace).order_by("-item_id")
        
        updated_count = 0
        
        for item in items:
            if dry_run:
                self.stdout.write(f"    [DRY RUN] Purchase Order {item.item_id}")
            else:
                # Add company associations (supplier)
                if item.company:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_ITEM, 
                        label='supplier'
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            item, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            item, 
                            item.company, 
                            workspace, 
                            association_label
                        )

                if item.contact:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_ITEM, 
                        label='supplier'
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            item, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            item, 
                            item.contact, 
                            workspace, 
                            association_label
                        )   

            updated_count += 1
        
        self.stdout.write(f"    Processed {updated_count} items")

    def populate_bill_association_labels(self, workspace, delete_existing, dry_run):
        """
        Populate association labels for bills
        """
        self.stdout.write("  Processing bill associations...")
        
        # Get all bills in workspace
        bills = Bill.objects.filter(workspace=workspace).order_by("-id_bill")
        
        updated_count = 0
        
        for bill in bills:
            if dry_run:
                self.stdout.write(f"    [DRY RUN] Bill {bill.id_bill}")
            else:    
                # Add contact associations (partner)
                if bill.contact:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_BILL, 
                        label='partner'
                    ).first()
                    if association_label:
                        print("association_label: ",association_label.label," ", bill.contact)
                        AssociationLabelObject.reset_associations_for_object(
                            bill, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            bill, 
                            bill.contact, 
                            workspace, 
                            association_label
                        )

                # Add company associations (partner)
                if bill.company:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_BILL, 
                        label='partner'
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            bill, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            bill, 
                            bill.company, 
                            workspace, 
                            association_label
                        )

                # Add journal entry associations
                if bill.journal_entry.exists():
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_BILL, 
                        label__iexact=TYPE_OBJECT_JOURNAL
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            bill, 
                            workspace, 
                            association_label
                        )
                        for journal_entry in bill.journal_entry.all():
                            AssociationLabelObject.create_association(
                                bill, 
                                journal_entry, 
                                workspace, 
                                association_label
                            )

            updated_count += 1
        
        self.stdout.write(f"    Processed {updated_count} bills")

    def populate_delivery_note_association_labels(self, workspace, delete_existing, dry_run):
        """
        Populate association labels for delivery notes
        """
        self.stdout.write("  Processing delivery note associations...")
        
        # Get all delivery notes in workspace
        delivery_notes = DeliverySlip.objects.filter(workspace=workspace).order_by("-id_ds")
        
        updated_count = 0
        
        for delivery_note in delivery_notes:
            if dry_run:
                self.stdout.write(f"    [DRY RUN] Delivery Note {delivery_note.id_ds}")
            else:    
                # Add contact associations
                if delivery_note.contact:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_DELIVERY_NOTE, 
                        label='customer'
                    ).first()
                    if association_label:
                        print("association_label: ",association_label.label," ", delivery_note.contact)
                        AssociationLabelObject.reset_associations_for_object(
                            delivery_note, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            delivery_note, 
                            delivery_note.contact, 
                            workspace, 
                            association_label
                        )

                # Add company associations
                if delivery_note.company:
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_DELIVERY_NOTE, 
                        label='customer'
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            delivery_note, 
                            workspace, 
                            association_label
                        )
                        AssociationLabelObject.create_association(
                            delivery_note, 
                            delivery_note.company, 
                            workspace, 
                            association_label
                        )

                # Add invoice associations
                if delivery_note.invoices.exists():
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_DELIVERY_NOTE, 
                        label__iexact=TYPE_OBJECT_INVOICE
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            delivery_note, 
                            workspace, 
                            association_label
                        )
                        for invoice in delivery_note.invoices.all():
                            AssociationLabelObject.create_association(
                                delivery_note, 
                                invoice, 
                                workspace, 
                                association_label
                            )

                # Add order associations - orders that reference this delivery note
                orders_with_delivery_note = ShopTurboOrders.objects.filter(
                    workspace=workspace, 
                    deliveryslip=delivery_note
                )
                if orders_with_delivery_note.exists():
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=TYPE_OBJECT_DELIVERY_NOTE, 
                        label__iexact=TYPE_OBJECT_ORDER
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            delivery_note, 
                            workspace, 
                            association_label
                        )
                        for order in orders_with_delivery_note:
                            AssociationLabelObject.create_association(
                                delivery_note, 
                                order, 
                                workspace, 
                                association_label
                            )

            updated_count += 1
        
        self.stdout.write(f"    Processed {updated_count} delivery notes")