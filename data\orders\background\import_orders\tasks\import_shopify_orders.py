import ast
from datetime import timedelta
import json
import traceback

from hatchet_sdk import Context

from data.models import Channel, TransferHistory
from utils.bgjobs.handler import (
    aio_set_bg_job_completed,
    aio_set_bg_job_failed,
    aio_set_bg_job_running,
)
from utils.error_logger.import_export_logger import (
    ImportExportLogger as ShopifyErrorLogger,
)
from utils.logger import logger
from utils.shopify_bg_job.graphql.import_orders.get_shopify_customers import (
    pull_shopify_customer,
)
from utils.shopify_bg_job.graphql.import_orders.import_shopify_orders import (
    get_shopify_orders,
)

from ..models import (
    ImportOrdersShopifyPayload,
    ImportShopifyOrdersSubprocessPayload,
)
from ..workflows import import_shopify_orders, import_shopify_orders_subprocess


@import_shopify_orders.task(
    name="ImportShopifyOrdersTask",
    execution_timeout=timedelta(hours=5),
    schedule_timeout=timedelta(hours=1),
)
async def import_shopify_orders_task(
    input: ImportOrdersShopifyPayload, ctx: Context
) -> dict:
    """
    Child task for importing Shopify orders
    """
    logger.info("Run Shopify orders import child task")
    logger.info(f"Input: {input}")

    history_id = input.history_id
    task = None

    try:
        task = await TransferHistory.objects.select_related("workspace", "user").aget(
            id=history_id
        )
        task.keep_alive = True
        await task.asave()
    except TransferHistory.DoesNotExist:
        logger.warning("TransferHistory does not exist")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])

    if input.background_job_id:
        await aio_set_bg_job_running(input.background_job_id)

    try:
        # Process mapping fields
        mapping_contact_custom_fields = (
            input.mapping_contact_custom_fields
            if input.mapping_contact_custom_fields
            and input.mapping_contact_custom_fields != "None"
            else None
        )

        mapping_custom_fields = (
            input.mapping_custom_fields
            if input.mapping_custom_fields and input.mapping_custom_fields != "None"
            else None
        )

        if isinstance(mapping_contact_custom_fields, str):
            mapping_contact_custom_fields = ast.literal_eval(
                mapping_contact_custom_fields
            )

        if isinstance(mapping_custom_fields, str):
            mapping_custom_fields = ast.literal_eval(mapping_custom_fields)

        # Call the Shopify import utility
        await shopify_pagination_async(
            user=input.user,
            channel_id=input.channel_id,
            mapping_custom_fields=mapping_custom_fields,
            mapping_contact_custom_fields=mapping_contact_custom_fields,
            default_customer=input.default_customer,
            key_item_field=input.key_item_field,
            how_to_import=input.how_to_import,
            sync_method=input.sync_method,
            history_id=history_id,
            lang=input.lang,
        )
        # await shopify_import_util(
        #     user=input.user,
        #     channel_id=input.channel_id,
        #     mapping_custom_fields=mapping_custom_fields,
        #     mapping_contact_custom_fields=mapping_contact_custom_fields,
        #     default_customer=input.default_customer,
        #     key_item_field=input.key_item_field,
        #     how_to_import=input.how_to_import,
        #     sync_method=input.sync_method,
        #     history_id=history_id,
        #     lang=input.lang
        # )

        logger.info("Successfully imported Shopify orders")

        logger.info("Successfully completed import for platform: shopify")
        if task:
            await task.arefresh_from_db()
            task.status = "completed"
            await task.asave(update_fields=["status"])

        if input.background_job_id:
            await aio_set_bg_job_completed(input.background_job_id)

    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error importing Shopify orders: {str(e)}")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])


async def shopify_pagination_async(
    user,
    channel_id,
    mapping_custom_fields,
    mapping_contact_custom_fields,
    default_customer,
    key_item_field,
    how_to_import,
    sync_method,
    history_id,
    lang,
) -> bool:
    """
    Handle Shopify pagination by spawning child workflows for each page (async version)
    """

    def chunk_list(data, chunk_size):
        for i in range(0, len(data), chunk_size):
            yield data[i : i + chunk_size]

    logger.info("Starting shopify pagination")

    task = await (
        TransferHistory.objects.filter(id=history_id).order_by("-created_at").afirst()
    )
    if not task:
        logger.error(f"TransferHistory {history_id} does not exist")
        return False
    try:
        channel = await Channel.objects.select_related("workspace", "integration").aget(
            id=channel_id
        )
    except Channel.DoesNotExist:
        logger.error(f"Channel {channel_id} does not exist")
        return False
    try:
        shopify_task_result = []

        access_token = channel.access_token
        shop_name = channel.account_id
        platform = channel.integration.slug
        workspace = channel.workspace

        error_logger = ShopifyErrorLogger(lang=lang)

        with error_logger.function_context("import_shopify_orders"):
            try:
                error_logger.log_info(
                    "import_shopify_orders",
                    f"Starting GraphQL import for platform: {platform}, shop_name: {shop_name}",
                )
                error_logger.log_info(
                    "import_shopify_orders", "Starting Shopify Order import"
                )

                try:
                    if history_id:
                        task = await TransferHistory.objects.filter(
                            workspace=workspace, id=history_id
                        ).afirst()
                    else:
                        task = (
                            await TransferHistory.objects.filter(
                                workspace=workspace,
                                type="import_order",
                                channel=channel,
                            )
                            .order_by("-created_at")
                            .afirst()
                        )

                    error_logger.log_info(
                        "import_shopify_orders",
                        f"TransferHistory task initialized: {task.id}",
                    )
                except Exception as e:
                    error_logger.log_database_error(
                        "import_shopify_orders",
                        f"Failed to update TransferHistory: {str(e)}",
                        e,
                    )
                    raise

                if not mapping_custom_fields:
                    mapping_custom_fields = {}
                    try:
                        mapping_custom_fields = {
                            k: v
                            for k, v in mapping_contact_custom_fields.items()
                            if "|order" in v
                        }
                        for k in mapping_custom_fields:
                            mapping_contact_custom_fields.pop(k)
                    except:
                        error_logger.log_info(
                            "import_shopify_orders", "Error at Order mapping"
                        )
                        traceback.print_exc()
                if not mapping_contact_custom_fields:
                    mapping_contact_custom_fields = {"en"}
                    try:
                        mapping_contact_custom_fields = {
                            k: v
                            for k, v in mapping_custom_fields.items()
                            if "|contact" in v
                        }
                        for k in mapping_contact_custom_fields:
                            mapping_custom_fields.pop(k)
                    except:
                        error_logger.log_info(
                            "import_shopify_orders", "Error at Contact mapping"
                        )
                        traceback.print_exc()

                error_logger.log_info(
                    "import_shopify_orders", f"Order mapping: {mapping_custom_fields}"
                )
                error_logger.log_info(
                    "import_shopify_orders",
                    f"Contact mapping: {mapping_contact_custom_fields}",
                )

                try:
                    channel = await Channel.objects.select_related(
                        "workspace", "integration"
                    ).aget(id=channel_id)
                    workspace = channel.workspace

                    # Fetch orders using GraphQL (still sync)
                    all_orders = await get_shopify_orders(access_token, shop_name)
                    all_orders = all_orders[::-1]
                    error_logger.log_info(
                        "import_shopify_orders",
                        f"Successfully connected to Shopify Orders: {channel.account_id}",
                    )
                except Exception as e:
                    error_logger.log_api_error(
                        "import_shopify_orders",
                        f"Failed to connect to Shopify: {str(e)}",
                        e,
                    )
                    raise

                if task:
                    await task.arefresh_from_db()
                    task.total_number = len(all_orders)
                    await task.asave()

                for order_data in chunk_list(all_orders, 50):
                    # Create payload for subprocess
                    order_datas = [order.to_dict() for order in order_data]
                    sub_history = await TransferHistory.objects.acreate(
                        workspace=workspace,
                        user=task.user if task and task.user else None,
                        status="pending",
                        type="import_orders",
                        name=f"Import Shopify Deals. Subprocess of {task.id if task else 'unknown'}",
                        parent=task,
                    )

                    subprocess_payload = ImportShopifyOrdersSubprocessPayload(
                        user=user,
                        channel_id=channel_id,
                        mapping_custom_fields=json.dumps(mapping_custom_fields),
                        default_customer=default_customer,
                        key_item_field=key_item_field,
                        how_to_import=how_to_import,
                        sync_method=sync_method,
                        history_id=str(history_id),
                        sub_history_id=str(sub_history.id),
                        order_datas=json.dumps(order_datas),
                    )

                    # Call the subprocess task directly
                    hatchet_task = (
                        await import_shopify_orders_subprocess.aio_run_no_wait(
                            subprocess_payload
                        )
                    )

                    logger.info(
                        f"[DEBUG] New shopify subprocess is running with id: {hatchet_task.workflow_run_id}"
                    )

                    shopify_task_result.append(hatchet_task)

                # Customer sync - exact same logic preserved
                if (
                    mapping_contact_custom_fields
                    and not default_customer
                    and not channel.ms_refresh_token
                ):
                    await pull_shopify_customer(
                        channel_id, mapping_contact_custom_fields
                    )

                return True

            except Exception as e:
                logger.error(f"Error in pagination loop: {str(e)}")
                logger.error(
                    f"Error in pagination loop traceback: {traceback.format_exc()}"
                )
                return False

    except Exception:
        logger.error(f"Error in shopify_pagination_async: {traceback.format_exc()}")
        return False
