{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}


<div class="border-0 card shadow-none rounded-0 w-100 h-100 bg-white">

    <div>
        <span class="fs-5 fw-bolder text-active-primary ms-0 mb-3 py-0 border-0">
            {% if LANGUAGE_CODE == 'ja' %}
            ステータスマッピング
            {% else %}
            Status Mapping
            {% endif %}
        </span>
        <script>
            function showLoadingIndicator() {
                document.getElementById('hubspot-loading').classList.remove('d-none');
            }
        </script>
    
        <input hidden name="platform" value="{{platform}}">
        <input hidden name="function_type" value="{{function_type}}">

        <div>

            <table class="{% include "data/utility/table.html" %} px-5">
                <thead class="{% include "data/utility/table-header.html" %}">
                    <tr>
                        <th class="min-w-50px">
                            <span>
                                {% if LANGUAGE_CODE == 'ja' %}
                                プラットフォームステータス
                                {% else %}
                                Platform Status
                                {% endif %}
                            </span>
                        </th>
                        <th class="min-w-50px">
                            <span>
                                {% if LANGUAGE_CODE == 'ja' %}
                                Sankaステータス
                                {% else %}
                                Sanka Status
                                {% endif %}
                            </span>
                        </th>
                    </tr>
                </thead>
                <tbody id="body-section" class="fs-6">
                    {% for header in header_list %}
                    <tr>
                        <td>
                            <input name="order-status-file-column" type="hidden" value="{{header|parse_header_item}}"/>
                            {% if LANGUAGE_CODE == 'ja' %}
                                <input name="order-status-file-column-name" type="hidden" value="{{header|parse_header_item:'name_ja'}}"/>
                                <span style="display: inline-block; max-width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                                {% if header|parse_header_item:'hubspot_default' == True %}{% translate_lang header|parse_header_item:'name_ja' LANGUAGE_CODE %}{% else %}{{header|parse_header_item:'name_ja'}}{% endif %}
                                </span>
                            {% else %}
                                <input name="order-status-file-column-name" type="hidden" value="{{header|parse_header_item:'name'}}"/>
                                <span style="display: inline-block; max-width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                                {{header|parse_header_item:'name'}}
                                </span>
                            {% endif %}
                        </td>
                        
                        <td>
                            <div>
                                <select name="order-status-sanka-properties" class="bg-white form-select form-select-solid border {% if is_multi_object %}w-300px{% endif %} h-40px select2-this select-sanka-properties-{{header|parse_header_item}}"
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    data-placeholder="ヘッダーの選択"
                                    {% else %}
                                    data-placeholder="Select Header"
                                    {% endif %}
                                    >  
                                        {% for delivery_status in status_choices %}
                                            {% if header|parse_header_item:'default' ==  delivery_status.value %}
                                            <option value="{{delivery_status.value}}" selected>
                                                {% if LANGUAGE_CODE == 'ja' %}
                                                    {{ delivery_status.name_ja }}
                                                {% else %}
                                                    {{ delivery_status.name }}
                                                {% endif %}
                                            </option>
                                            {% else %}
                                            <option value="{{delivery_status.value}}">
                                                {% if LANGUAGE_CODE == 'ja' %}
                                                    {{ delivery_status.name_ja }}
                                                {% else %}
                                                    {{ delivery_status.name }}
                                                {% endif %}
                                            </option>
                                            {% endif %}
                                        {% endfor %}
                                    
                                </select>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <div id="loading-spinner-header" class="text-center mb-3 d-none">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div>
                {% if LANGUAGE_CODE == 'ja' %}
                さらにプロパティを読み込んでいます...
                {% else %}
                Loading More Properties...
                {% endif %}
            </div>
        </div>


        <button type="button" class="btn btn-light-primary" name="save_mapping"
                hx-post="{% host_url 'shopturbo' host 'app' %}"
                hx-swap="outerHTML"
                hx-vals='{"save_mapping":"", "header_list":"{{header_list}}", "channel_id":"{{channel_id}}"}'
                hx-indicator="#mappingLoadingIndicator"
                id="saveMappingButton"
                onclick="statusCheckerChannelChanged(); showSaveMessage()">
            {% if LANGUAGE_CODE == 'ja'%}
            マッピングの保存
            {% else %}
            Save Mapping
            {% endif %}
        </button>

        <script>
            function disableButtons() {
                const importBtn = document.getElementById('import-btn');
                const exportBtn = document.getElementById('export-btn');
                if (importBtn) importBtn.disabled = true;
                if (exportBtn) exportBtn.disabled = true;
            }

            function enableButtons() {
                const importBtn = document.getElementById('import-btn');
                const exportBtn = document.getElementById('export-btn');
                if (importBtn) importBtn.disabled = false;
                if (exportBtn) exportBtn.disabled = false;
            }

            function toggleConditionalMapping() {
                const checkbox = document.getElementById('conditional-export-mapping');
                const section = document.getElementById('conditional-export-mapping-section');
                
                if (checkbox.checked) {
                    section.classList.remove('d-none');
                } else {
                    section.classList.add('d-none');
                }
            }
        </script>

        <div id="mappingLoadingIndicator" class="d-none">
            {% if LANGUAGE_CODE == 'ja' %}保存中...{% else %}Saving...{% endif %}
        </div>

        <script>
            function statusCheckerChannelChanged() {
                setTimeout(() => {
                    if (document.getElementById('status-checker')) {
                        htmx.trigger('#status-checker', 'channelChanged');
                    }
                }, 4000);
            }

            function showSaveMessage() {
                const saveMessage = document.getElementById('saveMappingMessage');
                if (saveMessage) {
                    saveMessage.classList.remove('d-none'); // Show the message
                    setTimeout(() => {
                        saveMessage.classList.add('d-none'); // Hide it after 5 seconds
                    }, 5000);
                }
            }
        </script>
    
    </div>

    {% if platform == 'hubspot' %}
        <div class="fs-4 fw-bolder mt-5">
            {% if LANGUAGE_CODE == 'ja' %}フィルター{% else %}Filters{% endif %}
        </div>
        <select class="bg-white border min-h-40px form-select form-select-solid w-100 select2-this select2-this-filter"
            hx-get="{% host_url 'sync_hubspot_filter_selector' host 'app' %}"
            hx-vals='{"header_list": "{{header_list}}"}'
            hx-target="#data-selector-content"
            hx-trigger="htmx-change"
            name="data_filter"
            hx-swap="beforeend" 
            {% if LANGUAGE_CODE == 'ja'%}
            data-placeholder="フィルターを選択"
            {% else %}
            data-placeholder="Select Filter"
            {% endif %}
            onchange="onchangeFilterSelect(this)"
            >
            <option></option>
            <option value='last_order'>{% if LANGUAGE_CODE == 'ja' %}最新 X 件の注文{% else %}Last X Orders{% endif %}</option>
            <option value='order_status'>{% if LANGUAGE_CODE == 'ja' %}取引ステージ{% else %}Deal Stage{% endif %}</option>
        </select>

        <div class="w-100">
            <div id="data-selector-content" class="mt-3">
            </div>
        </div> 

        <script>
            function onchangeFilterSelect(element) {
                htmx.trigger(element, 'htmx-change');
            }
        </script>

        {% if predefined_hubspot_filter_import %}
            {% for filter_import, filter_import_value in predefined_hubspot_filter_import.items %}
                <div 
                    class="data-selector-content-loader"        
                    hx-get="{% host_url 'sync_hubspot_filter_selector' host 'app' %}"
                    hx-vals='{"header_list": "{{header_list}}", "predefined_hubspot_filter_import": "{{filter_import}}", "predefined_hubspot_filter_value": "{{filter_import_value}}"}'
                    hx-target="#data-selector-content"
                    hx-swap="beforeend" 
                    hx-trigger="load"
                >
                </div>
            {% endfor %}
        {% endif %}
    {% endif %}
    
    <script>
        $(document).ready(function() {
            // Hide content and show spinner at the very beginning
            if (document.getElementById('statusMappingContainer')) {
                document.getElementById('statusMappingContainer').classList.add('d-none');
                document.getElementById('loading-spinner-status').classList.remove('d-none');
            }
            
            // Step 1: Use setTimeout to allow browser to render pagination
            // before moving to the next heavy operation
            setTimeout(function() {
                // Initialize Select2 controls in smaller batches
                initializeSelect2Controls();
                
                // Step 2: After Select2 initialization is complete, set up events
                setTimeout(function() {
                    setupSelect2Events();
                    
                    // Show content and hide spinner when everything is done
                    if (document.getElementById('statusMappingContainer')) {
                        document.getElementById('loading-spinner-status').classList.add('d-none');
                        document.getElementById('statusMappingContainer').classList.remove('d-none');
                    }
                }, 50);
            }, 100);
        });

        // Helper functions to break up the work
        function initializeSelect2Controls() {
            // Initialize Select2 in batches to prevent browser freeze
            const selects = $('.select2-this').toArray();
            const batchSize = 50;
            let currentBatch = 0;
            
            function initializeBatch() {
                // Process current batch
                const startIndex = currentBatch * batchSize;
                const endIndex = Math.min(startIndex + batchSize, selects.length);
                
                if (startIndex < selects.length) {
                    // Process this batch
                    const batch = selects.slice(startIndex, endIndex);
                    $(batch).select2();
                    
                    // Update progress indicator if needed
                    if (document.getElementById('loading-spinner-progress')) {
                        const progress = Math.round((endIndex / selects.length) * 100);
                        document.getElementById('loading-spinner-progress').style.width = progress + '%';
                        document.getElementById('loading-spinner-text').textContent = 
                            `Initializing... ${progress}%`;
                    }
                    
                    // Schedule next batch
                    currentBatch++;
                    setTimeout(initializeBatch, 10);
                } else {
                    // All batches processed
                    // Handle item_id selects
                    $('.select2-this-order').each(function() {
                        let selectElement = $(this).closest('select').get(0);
                        if (selectElement && selectElement.value === 'item_id') {
                            selectElement.dispatchEvent(new Event('htmx-change'));
                        }
                    });
                }
            }
            
            // Start the batch processing
            initializeBatch();
        }

        function setupSelect2Events() {
            // Track previous values more efficiently
            var previousValues = {};
            $(`.select2-this-order`).on('select2:opening', function(e) {
                // Store the current value before change
                var selectElement = $(this).closest('select').get(0);
                if (selectElement) {
                    previousValues[selectElement.id || $(this).index()] = selectElement.value;
                }
            }).on('select2:select', function(e) {
                var selectElement = $(this).closest('select').get(0);
                if (!selectElement) return;
                
                let id = selectElement.id || $(this).index();
                let oldValue = previousValues[id] || '';
                let currentValue = selectElement.value;

                if (['order', 'inventory'].includes(import_as)) {
                    if ((currentValue === 'item_id') || (oldValue === 'item_id')) {
                        selectElement.dispatchEvent(new Event('htmx-change'));
                    }
                } else {
                    selectElement.dispatchEvent(new Event('htmx-change'));
                }

                // Update the previous value after change
                previousValues[id] = currentValue;
            });
        }
    </script>
    
</div>
