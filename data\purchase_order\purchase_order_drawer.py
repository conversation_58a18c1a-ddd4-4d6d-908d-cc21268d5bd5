import os
from django.db.models import (<PERSON><PERSON><PERSON><PERSON><PERSON>, Case, ExpressionWrapper, F,
                              <PERSON>loat<PERSON>ield, OuterRef, Q, Subquery, Sum, Value,
                              When)
from django.http import HttpResponse
from django.shortcuts import get_object_or_404, redirect, render
from data.constants.procurement_constant import (
    DIRECTORY, NEXT_DIRECTORY, PROCUREMENT_APP_SETTING_MANAGE_APP,
    PROCUREMENT_APP_TARGET)
from data.constants.properties_constant import TYPE_OBJECT_PURCHASE_ORDER
from data.custom_pdf import generate_pdf_bytes
from data.models import (
    Module, PropertySet, CustomProperty, AppSetting, AppSettingChild, PurchaseOrders, PurchaseOrdersItem,
    PurchaseOrdersNameCustomField, AppLog, PdfTemplate, View, Association, AssociationLabel,
    PURCHASE_ORDER_STATUS, PURCHASE_ORDER_STATUS_DISPLAY
)
from django.db.models import Q
from django.http import HttpResponse
from data.constants.constant import DEFAULT_PERMISSION
from data.constants.commerce_constant import APP_SETTING_CHILD_LIST
import ast
import os
from data.property import get_default_property_set, get_properties_with_details
from data.property import properties as forward_properties
from data.templatetags.custom_tags import (
    get_currency_symbol, use_thousand_separator_string_with_currency)
from utils.decorator import login_or_hubspot_required
from utils.inventory import *
from utils.properties.properties import (get_object_display_based_columns,
                                         get_page_object)
from utils.utility import (build_redirect_url, get_redirect_workflow,
                           get_workspace, is_valid_uuid, natural_sort_key,
                           save_custom_property)
from utils.workspace import get_permission


# =========== Purchase Order

@login_or_hubspot_required
def purchase_order_drawer(request, id=None):
    view_id = request.GET.get('view_id', None)
    set_id = request.GET.get('set_id', None)
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    items_show = None

    # Get module slug from request or find default module for purchase orders
    module_slug = request.GET.get('module')
    if not module_slug:
        # Try to find a module that contains purchase orders
        module = Module.objects.filter(
            workspace=workspace,
            object_values__contains=TYPE_OBJECT_PURCHASE_ORDER
        ).order_by('order', 'created_at').first()
        if module:
            module_slug = module.slug
        else:
            # If no module found, use a default value that won't cause URL reversal errors
            module_slug = 'purchase_orders'

    object_type = request.GET.get('object_type')

    print("====set_id: ", request.GET)

    selected_order_id = request.GET.get('selected_order_id', '')
    drawer_type = request.GET.get('drawer_type', None)
    type_association = request.GET.get('type_association', None)
    source = request.GET.get('source', TYPE_OBJECT_PURCHASE_ORDER)
    object_id = request.GET.get('object_id')
    if drawer_type == 'purchaseorder':
        context = {
            "view_id": view_id,
            "set_id": set_id,
            'PURCHASE_ORDER_STATUS': PURCHASE_ORDER_STATUS,
            'module': module_slug,
            'side_drawer': request.GET.get('sidedrawer', '')
        }
        if type_association:
            context['source'] = source
            context['type_association'] = type_association
            context['object_id'] = object_id

        property_sets = PropertySet.objects.filter(
            workspace=workspace, target=TYPE_OBJECT_PURCHASE_ORDER).order_by('created_at')
        context['property_sets'] = property_sets
        context['set_id'] = set_id

        # related associate label
        related_association_labels = AssociationLabel.objects.filter(created_by_sanka=False, workspace=workspace, object_target__icontains=TYPE_OBJECT_PURCHASE_ORDER).order_by("created_at")

        context['related_association_labels'] = related_association_labels

        # association label list for forms
        association_labels = AssociationLabel.objects.filter(
            workspace=workspace, object_source=TYPE_OBJECT_PURCHASE_ORDER
        ).order_by("created_at")
        association_labels_sanka_true = AssociationLabel.objects.filter(workspace=workspace, object_source=TYPE_OBJECT_PURCHASE_ORDER, created_by_sanka=True)
        association_label_list_sanka_true = [association_label.label for association_label in association_labels_sanka_true]
        association_labels_sanka_false = AssociationLabel.objects.filter(workspace=workspace, object_source=TYPE_OBJECT_PURCHASE_ORDER, created_by_sanka=False)
        association_label_list_sanka_false = [str(association_label.id) for association_label in association_labels_sanka_false]
        association_label_list = association_label_list_sanka_true + association_label_list_sanka_false

        related_association_labels = AssociationLabel.objects.filter(created_by_sanka=False,workspace=workspace, object_target__icontains=TYPE_OBJECT_PURCHASE_ORDER).order_by('created_at')
        context['association_labels'] = association_labels
        context['association_label_list'] = association_label_list
        if request.GET.get('source_hx_target'):
            context['source_hx_target'] = request.GET.get('source_hx_target')
        context['related_association_labels'] = related_association_labels

        return render(request, 'data/purchase_order/purchase-order-form.html', context)

    page_obj = get_page_object(TYPE_OBJECT_PURCHASE_ORDER, lang)

    base_model = page_obj['base_model']
    custom_model = page_obj['custom_model']
    page_title = page_obj['page_title']

    custom_field = CustomProperty.objects.filter(
        workspace=workspace, model=base_model._meta.db_table, name='status')
    if custom_field:
        custom_field = custom_field.first()
        choices = ast.literal_eval(custom_field.value)

        choices = [{'label': choices[value], 'value': value}
                   for value in choices]

    else:
        choices = [{'label': PURCHASE_ORDER_STATUS_DISPLAY[value][lang],
                    'value': value} for value, label in PURCHASE_ORDER_STATUS]

    purchase = None
    choice_status = None
    section = None
    exclude_item_list = []
    exclude_item = []
    action_index = None
    try:
        app_setting = AppSetting.objects.get(
            workspace=workspace, app_target=PROCUREMENT_APP_TARGET)
        value_app_setting_tax = app_setting.purchase_order_tax
        value_app_setting_is_status = app_setting.purchase_order_is_status
    except AppSetting.DoesNotExist:
        app_setting = None

    supplier_lock = False
    if app_setting and app_setting.purchase_order_supplier_lock:
        supplier_lock = True

    current_index = ''
    permission = get_permission(
        object_type=TYPE_OBJECT_PURCHASE_ORDER, user=request.user)
    if not permission:
        permission = DEFAULT_PERMISSION

    if request.method == 'GET':
        section = request.GET.get('section')
        tab = request.GET.get('tab')
        if 'available-items' in request.GET:
            if 'add_type' in request.GET:
                action_index = request.GET.get('action_index')
                if action_index:
                    exclude_item_list = request.GET.getlist(
                        f'selected-item-id-{action_index}', [])
                else:
                    exclude_item_list = request.GET.getlist(
                        'selected-item-id', [])

                i = 0
                for item in exclude_item_list:
                    if item:
                        exclude_item.append(item)
                    else:
                        i += 1
                # If everything is empty string, make it empty list
                if i == len(exclude_item_list):
                    exclude_item_list = ['']

            if 'id' in request.GET:
                id_purchase = request.GET.get("id")
                purchase = PurchaseOrders.objects.get(id=id_purchase)

    if purchase and purchase.owner and purchase.owner.user:
        permission += f'|{purchase.owner.user.id}#{request.user.id}'

    context = {
        "selected_order_id": selected_order_id,
        "source": source,
        'module': module_slug,
        'PURCHASE_ORDER_STATUS': PURCHASE_ORDER_STATUS,
        'view_id':  view_id,
        'page_title':  page_title,
        'app_setting':  app_setting,
        'value_app_setting_tax': value_app_setting_tax,
        'value_app_setting_is_status': value_app_setting_is_status,
        'purchase':  purchase,
        'obj': purchase,
        'PurchaseOrdersNameCustomField': PurchaseOrdersNameCustomField.objects.filter(workspace=workspace, name__isnull=False).order_by("order"),
        'permission': permission,
        'object_type': TYPE_OBJECT_PURCHASE_ORDER,
        'tab': tab,
        'choices': choices,
        'supplier_lock': supplier_lock,
        'side_drawer': request.GET.get('sidedrawer', ''),
        "form_id": uuid.uuid4()
    }

    for setting_ in APP_SETTING_CHILD_LIST:
        app_setting_child = AppSettingChild.objects.filter(
            app_setting=app_setting, target_name=TYPE_OBJECT_PURCHASE_ORDER + setting_).first()
        if not app_setting_child:
            app_setting_child = AppSettingChild.objects.create(
                app_setting=app_setting, target_name=TYPE_OBJECT_PURCHASE_ORDER + setting_)
        setting_ = 'value_app_setting' + setting_
        context[setting_] = app_setting_child.value
        
    property_sets = PropertySet.objects.filter(
        workspace=workspace, target=TYPE_OBJECT_PURCHASE_ORDER).order_by('created_at')
    context['property_sets'] = property_sets

    # association label list for forms
    association_labels = AssociationLabel.objects.filter(workspace=workspace, object_source=TYPE_OBJECT_PURCHASE_ORDER).order_by("created_at")
    association_labels_sanka_true = AssociationLabel.objects.filter(workspace=workspace, object_source=TYPE_OBJECT_PURCHASE_ORDER, created_by_sanka=True)
    association_label_list_sanka_true = [association_label.label for association_label in association_labels_sanka_true]
    association_labels_sanka_false = AssociationLabel.objects.filter(workspace=workspace, object_source=TYPE_OBJECT_PURCHASE_ORDER, created_by_sanka=False)
    association_label_list_sanka_false = [str(association_label.id) for association_label in association_labels_sanka_false]
    association_label_list = association_label_list_sanka_true + association_label_list_sanka_false
    related_association_labels = AssociationLabel.objects.filter(created_by_sanka=False,workspace=workspace, object_target__icontains=TYPE_OBJECT_PURCHASE_ORDER).order_by('created_at')
    context['association_labels'] = association_labels
    context['association_label_list'] = association_label_list
    context['related_association_labels'] = related_association_labels

    PurchaseOrdersCustomFieldMap = {}
    PurchaseOrdersCustomFieldName = PurchaseOrdersNameCustomField.objects.filter(
        workspace=workspace)
    for ctf in PurchaseOrdersCustomFieldName:
        PurchaseOrdersCustomFieldMap[str(ctf.id)] = ctf
    context['PurchaseOrdersCustomFieldMap'] = PurchaseOrdersCustomFieldMap

    if set_id == 'None':
        set_id = None
    if set_id:
        property_set = PropertySet.objects.filter(id=set_id).first()
        print(property_set)
    else:
        if view_id == 'None':
            view_id = None
        if view_id:
            view = View.objects.filter(
                id=view_id, target=TYPE_OBJECT_PURCHASE_ORDER).first()
            if view:
                if view.form:
                    property_set = view.form
                else:
                    condition_filter = Q(
                        workspace=workspace, target=TYPE_OBJECT_PURCHASE_ORDER)
                    condition_filter &= (
                        Q(as_default=True) | Q(name__isnull=True))
                    property_set = PropertySet.objects.filter(
                        condition_filter).first()
            else:
                condition_filter = Q(workspace=workspace,
                                     target=TYPE_OBJECT_PURCHASE_ORDER)
                condition_filter &= (Q(as_default=True) | Q(name__isnull=True))
                property_set = PropertySet.objects.filter(
                    condition_filter).first()
        else:
            condition_filter = Q(workspace=workspace,
                                 target=TYPE_OBJECT_PURCHASE_ORDER)
            condition_filter &= (Q(as_default=True) | Q(name__isnull=True))
            property_set = PropertySet.objects.filter(condition_filter).first()

    if property_set:
        if property_set.name is None:
            property_set = get_default_property_set(
                TYPE_OBJECT_PURCHASE_ORDER, workspace, lang)

    choice_status = None
    properties = {'list_all': []}
    line_item_properties = None
    if property_set:
        properties['list_all'] = property_set.children
        # if property_set.custom_property:
        #     choice_status = property_set.custom_property.value
        line_item_properties = property_set.purchase_order_line_items.all()


    context["properties"] = properties
    context["property_set"] = property_set
    context["line_item_properties"] = line_item_properties

    page = 1
    if id:
        page = request.GET.get('page', 1)
        purchase = PurchaseOrders.objects.get(id=id)
        pois = PurchaseOrdersItem.objects.filter(
            purchase_order=purchase).order_by("created_at")
        conditions = {
            'currency': purchase.currency
        }
        item = []
        status = []
        amount = []
        item_id = []
        price = []
        tax = []
        poi_id = []
        items_show = None
        if pois:
            try:
                for poi in pois:
                    # Check if item_name looks like a UUID and try to get the actual item name
                    actual_item_name = poi.item_name
                    if poi.item_name and is_valid_uuid(poi.item_name):
                        try:
                            from data.models import ShopTurboItems
                            actual_item = ShopTurboItems.objects.filter(workspace=workspace, id=poi.item_name).first()
                            if actual_item:
                                actual_item_name = actual_item.name
                        except Exception as e:
                            pass  # If lookup fails, use the original item_name

                    poi_id.append(poi.id)
                    item.append(actual_item_name)  # Use the actual item name instead of UUID
                    status.append(poi.item_status)
                    amount.append(poi.amount_item)
                    tax.append(poi.tax_rate)
                    price.append(poi.amount_price)
                    if poi.item:
                        item_id.append(str(poi.item.id))
                    else:
                        item_id.append('')
                items_show = list(
                    zip(poi_id, item_id, item, amount, price, tax, status))
            except Exception as e:
                print("Error item show: ", e)
                pass

        if purchase.date:
            if lang == 'en':
                purchase.date = str(purchase.date)[:10]
        else:
            purchase.date = ""

        app_logs = AppLog.objects.filter(
            workspace=workspace, purchaseorders=purchase).order_by('-created_at')

        # customize_template = CustomizePdfTemplate.objects.filter(
        #     setting_type=TYPE_OBJECT_PURCHASE_ORDER, workspace=workspace)
        customize_template = []

        list_template = []
        for filename in os.listdir(DIRECTORY):
            # Check if the file is an HTML file and contains 'pdf_pattern' in its name
            if filename.endswith('.html') and page_obj['pdf_pattern'] in filename:
                list_template.append(NEXT_DIRECTORY + filename)

        list_template = sorted(list_template, key=natural_sort_key)
        customize_template = PdfTemplate.objects.filter(workspace=workspace, master_pdf__object_type=TYPE_OBJECT_PURCHASE_ORDER).order_by('master_pdf__name_en', 'master_pdf__name_ja')

        try:
            hide_associated_data = bool(
                request.GET.get('hide_associated_data', False))
        except:
            hide_associated_data = False

        if purchase and purchase.owner and purchase.owner.user:
            permission += f'|{purchase.owner.user.id}#{request.user.id}'

        context = {
            "selected_order_id": selected_order_id,
            "source": source,
            'module': module_slug,
            'PURCHASE_ORDER_STATUS': PURCHASE_ORDER_STATUS,
            'view_id': view_id,
            'app_setting': app_setting,
            'page_title': page_title,
            'items_show': items_show,
            'purchase': purchase,
            'obj': purchase,
            'PurchaseOrdersNameCustomField': PurchaseOrdersNameCustomField.objects.filter(workspace=workspace, name__isnull=False).order_by("order"),
            'value_app_setting_tax': value_app_setting_tax,
            'value_app_setting_is_status': value_app_setting_is_status,
            'supplier_lock': supplier_lock,

            'permission': permission,
            'app_logs': app_logs,
            'object_type': TYPE_OBJECT_PURCHASE_ORDER,
            'tab': tab,
            'choices': choices,
            'list_template': list_template,
            'list_customize_template': customize_template,
            'page': page,
            'side_drawer': request.GET.get('sidedrawer', ''),
            'hide_associated_data': hide_associated_data,
            'form_id': uuid.uuid4(),
            'object_action': {
                'additional_params': {
                    'po_ids': str(purchase.id)
                }
            }
        }
        property_sets = PropertySet.objects.filter(
            workspace=workspace, target=TYPE_OBJECT_PURCHASE_ORDER).order_by('created_at')
        context['property_sets'] = property_sets


    elif section:
        if choice_status:
            context['choice_status'] = choice_status
        if choices:
            context['choices'] = choices
        if set_id:
            context['set_id'] = set_id
        context['section'] = section
        context['view_id'] = view_id
        context['associate_id'] = request.GET.get('associate_id', '')
        context['object_id'] = request.GET.get('object_id', '')
        context['page'] = request.GET.get('page', 1)
        context['associate_view_id'] = request.GET.get('associate_view_id', '')
        context['purchase'] = {
            'model': base_model,
            'workspace': workspace}
        context['type_association'] = type_association
        context['source'] = source
        context['source_hx_target'] = request.GET.get('source_hx_target')
        return render(request, 'data/purchase_order/purchase-order-create-form.html', context)

    if choice_status:
        context['choice_status'] = choice_status
    if choices:
        context['choices'] = choices
    if set_id:
        context['set_id'] = set_id
    if line_item_properties:
        context['line_item_properties'] = line_item_properties

    if 'available-items' in request.GET:
        context['count_item'] = int(request.GET.get('available-items')) + 1
        context['type_data'] = "purchase"
        context['drawer_type'] = "purchaseorder"
        context['poi_id'] = None
        if current_index:
            context['action_index'] = current_index
        if exclude_item:
            context['exclude_item'] = exclude_item
        if 'add_type' in request.GET:
            context['add_type'] = request.GET.get('add_type')
            if request.GET.get('add_type') == 'select_item' or request.GET.get('add_type') == 'manual_item':
                action_index = request.GET.get('action_index')
                if action_index:
                    context['action_index'] = action_index
                return render(request, 'data/purchase_order/purchase-order-manual-item.html', context)
            return HttpResponse('')
        else:
            return render(request, 'data/purchase_order/purchase-order-partial-add-items.html', context)
    else:
        context["properties"] = properties
        context['PurchaseOrdersCustomFieldMap'] = PurchaseOrdersCustomFieldMap
        context["po_associates"] = Association.objects.filter(
            workspace=workspace, purchase_order_associate__isnull=False)
        context["associates"] = Association.objects.filter(
            workspace=workspace, associate_type=TYPE_OBJECT_PURCHASE_ORDER)

        context['view_id'] = view_id
        context['page'] = request.GET.get('page', 1)
        if not id:
            context['purchase'] = {'model': 'purchaseorders',
                                   'workspace': workspace}

        # related associate label
        related_association_labels = AssociationLabel.objects.filter(created_by_sanka=False, workspace=workspace, object_target__icontains=TYPE_OBJECT_PURCHASE_ORDER).order_by("created_at")

        context['related_association_labels'] = related_association_labels

        # association label list for forms
        association_labels = AssociationLabel.objects.filter(
            workspace=workspace, object_source=TYPE_OBJECT_PURCHASE_ORDER
        ).order_by("created_at")

        association_labels_sanka_true = AssociationLabel.objects.filter(workspace=workspace, object_source=TYPE_OBJECT_PURCHASE_ORDER, created_by_sanka=True)
        association_label_list_sanka_true = [association_label.label for association_label in association_labels_sanka_true]
        association_labels_sanka_false = AssociationLabel.objects.filter(workspace=workspace, object_source=TYPE_OBJECT_PURCHASE_ORDER, created_by_sanka=False)
        association_label_list_sanka_false = [str(association_label.id) for association_label in association_labels_sanka_false]
        association_label_list = association_label_list_sanka_true + association_label_list_sanka_false
        related_association_labels = AssociationLabel.objects.filter(created_by_sanka=False,workspace=workspace, object_target__icontains=TYPE_OBJECT_PURCHASE_ORDER).order_by('created_at')
        context['association_labels'] = association_labels
        context['association_label_list'] = association_label_list
        context['related_association_labels'] = related_association_labels
        
        return render(request, 'data/purchase_order/purchase-order-form.html', context)
