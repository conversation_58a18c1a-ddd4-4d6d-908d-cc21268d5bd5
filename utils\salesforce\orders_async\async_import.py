"""
Main orchestration for Salesforce Orders import using Bulk API 2.0.
Implements checkpointing, batching, streaming, async execution, and structured logging.
"""

import traceback
from typing import Dict, Optional, List, TYPE_CHECKING

if TYPE_CHECKING:
    from hatchet_sdk import Context

from data.models import BackgroundJob, Channel, TransferHistory
from utils.error_logger.import_export_logger import ImportExportLogger

from .fetcher import SalesforceOrdersFetcher
from .processor import OrdersDataProcessor
from .batch_handler import OrdersBatchHandler
from .checkpoint_manager import CheckpointManager
from .progress import OrdersProgressTracker
from .custom_fields import CustomFieldsManager
from .config import ORDER_BATCH_SIZE, LINE_ITEM_BATCH_SIZE


async def import_salesforce_orders(
    channel_id: str,
    field_mapping: Optional[Dict[str, str]] = None,
    user_id: Optional[str] = None,
    mapping_custom_fields_association: Optional[Dict[str, str]] = None,
    mapping_status_custom_fields: Optional[Dict[str, str]] = None,
    lang: str = "en",
    transfer_history_id: Optional[str] = None,
    checkpoint: Optional[int] = None,
    ctx: Optional["Context"] = None,
) -> bool:
    """
    Import Salesforce opportunities as orders using async batch processing.

    Args:
        channel_id: Channel ID for Salesforce connection
        field_mapping: Salesforce to Sanka field mapping
        user_id: User ID for tracking
        mapping_custom_fields_association: Association field mappings
        mapping_status_custom_fields: Status field mappings
        lang: Language for error messages
        transfer_history_id: Existing TransferHistory ID for resume
        checkpoint: Legacy checkpoint parameter (unused, using URL-based checkpoints)
        ctx: Hatchet context for cancellation support

    Returns:
        True if import successful, False otherwise
    """
    error_logger = ImportExportLogger(lang=lang)
    progress_tracker = None
    checkpoint_manager = None
    fetcher = None
    opportunity_job_id = None
    line_item_job_id = None
    background_job = None

    try:
        # Get workspace from channel
        channel = await Channel.objects.select_related("workspace").aget(id=channel_id)
        workspace_id = str(channel.workspace_id)

        # Initialize or get transfer history
        if transfer_history_id:
            transfer_history = await TransferHistory.objects.aget(
                id=transfer_history_id
            )
            # Try to get background_job from transfer_history if it exists
            if hasattr(transfer_history, "background_job"):
                try:
                    background_job = await BackgroundJob.objects.aget(
                        transfer_history=transfer_history
                    )
                except BackgroundJob.DoesNotExist:
                    pass
        else:
            transfer_history = await TransferHistory.objects.acreate(
                workspace_id=workspace_id,
                user_id=user_id,
                status="running",
                type="import",
                channel_id=channel_id,
                start_date=None,  # Will be set when processing starts
            )

        # Initialize components
        checkpoint_manager = CheckpointManager(transfer_history, error_logger)
        progress_tracker = OrdersProgressTracker(
            transfer_history, background_job, error_logger
        )

        # Initialize progress tracking
        await progress_tracker.initialize()

        # Check if we should resume
        if checkpoint_manager.should_resume():
            await error_logger.alog_info(
                "AsyncImport",
                f"Resuming import from checkpoint state: {checkpoint_manager.get_state()}",
            )

            # Restore progress from checkpoint
            resume_info = checkpoint_manager.get_resume_info()
            await progress_tracker.update_from_checkpoint(
                resume_info.get("opportunity_processed", 0),
                resume_info.get(
                    "opportunity_processed", 0
                ),  # Approximate success count
                0,  # Failed count not tracked in checkpoint yet
            )

        # Initialize fetcher and connect
        fetcher = SalesforceOrdersFetcher(channel_id, error_logger, progress_tracker)
        await fetcher.connect()

        # Initialize batch handler (don't initialize caches yet)
        batch_handler = OrdersBatchHandler(
            workspace_id,
            channel_id,
            error_logger,
        )

        # Initialize processor
        processor = OrdersDataProcessor(
            workspace_id,
            channel_id,
            error_logger,
        )

        # Initialize custom fields manager
        custom_fields_manager = CustomFieldsManager(workspace_id, error_logger)
        await custom_fields_manager.initialize()

        # Use field mapping from checkpoint if resuming
        if checkpoint_manager.should_resume():
            existing_mapping = checkpoint_manager.get_field_mapping()
            if existing_mapping:
                field_mapping = existing_mapping

        await processor.initialize_field_mapping(field_mapping)

        # Save field mapping to checkpoint
        await checkpoint_manager.set_field_mapping(field_mapping)

        # Save field mappings to database
        await batch_handler.process_field_mappings(field_mapping)

        # Collect owner IDs and initialize caches before importing
        owner_ids = await _collect_owner_ids_from_opportunities(
            fetcher,
            field_mapping,
            checkpoint_manager,
            error_logger,
        )
        
        # Initialize caches with owner IDs from Salesforce
        await batch_handler.initialize_caches(owner_ids)

        # Import Opportunity records
        success = await _import_opportunities(
            fetcher,
            processor,
            batch_handler,
            checkpoint_manager,
            progress_tracker,
            custom_fields_manager,
            field_mapping,
            mapping_status_custom_fields,
            False,  # enable_salesforce_filter
            None,  # salesforce_field
            None,  # salesforce_filter
            None,  # salesforce_filters
            ctx,
        )

        if not success:
            await error_logger.alog_error(
                "IMPORT_ERROR", "AsyncImport", "Failed to import Opportunity records"
            )
            await progress_tracker.mark_failed("Opportunity import failed")
            return False

        # Get the job_ids for cleanup
        opp_job_info = checkpoint_manager.get_job_info("Opportunity")
        if opp_job_info:
            opportunity_job_id = opp_job_info.get("job_id")

        line_item_job_info = checkpoint_manager.get_job_info("OpportunityLineItem")
        if line_item_job_info:
            line_item_job_id = line_item_job_info.get("job_id")

        # Mark as completed
        await checkpoint_manager.mark_completed()
        await progress_tracker.mark_completed()

        await error_logger.alog_info(
            "AsyncImport", "Successfully completed Salesforce orders import"
        )

        return True

    except Exception as e:
        await error_logger.alog_error(
            "IMPORT_ERROR",
            "AsyncImport",
            f"Import failed: {str(e)}\n{traceback.format_exc()}",
            e,
        )

        if checkpoint_manager:
            await checkpoint_manager.mark_failed(str(e))

        if progress_tracker:
            await progress_tracker.mark_failed(str(e))

        return False

    finally:
        # Clean up
        if fetcher:
            await fetcher.disconnect()
            await error_logger.alog_info(
                "AsyncImport", "Successfully cleaned up Salesforce orders import"
            )
            # Clean up temp files if job_ids exist
            if opportunity_job_id:
                await fetcher.cleanup_temp_files(opportunity_job_id)
            if line_item_job_id:
                await fetcher.cleanup_temp_files(line_item_job_id)

        # Clean up checkpoint temp files
        if checkpoint_manager:
            await checkpoint_manager.cleanup_temp_files()

        # Save error log to transfer history
        if transfer_history and error_logger.has_errors():
            await error_logger.asave_to_transfer_history(transfer_history)


async def _collect_owner_ids_from_opportunities(
    fetcher: SalesforceOrdersFetcher,
    field_mapping: Dict[str, Dict],
    checkpoint_manager: CheckpointManager,
    error_logger: ImportExportLogger,
) -> List[str]:
    """
    Collect all unique owner IDs from opportunities before processing.
    
    Args:
        fetcher: Salesforce fetcher instance
        field_mapping: Field mapping configuration
        checkpoint_manager: Checkpoint manager for tracking
        error_logger: Error logger
        
    Returns:
        List of unique owner IDs found in the opportunities
    """
    owner_ids = set()
    
    try:
        # Check for existing job in checkpoint
        opp_job_info = checkpoint_manager.get_job_info("Opportunity")
        opp_job_id = opp_job_info.get("job_id") if opp_job_info else None
        opp_temp_file = checkpoint_manager.get_temp_file("Opportunity")
        
        # Build SOQL query if not in checkpoint
        if not opp_job_info:
            # Build query with OwnerId field
            opp_soql_query = fetcher.build_opportunity_query(
                field_mapping,
                False,  # enable_filter
                None,  # filter_field
                None,  # filter_value
                None,  # additional_filters
            )
            
            await error_logger.alog_info(
                "CollectOwnerIds",
                "Collecting owner IDs"
            )
        else:
            opp_soql_query = opp_job_info.get("soql_query", "")
        
        # Fetch opportunities to collect owner IDs
        batch_size = ORDER_BATCH_SIZE
        
        # Get generator from fetcher
        _, _, opp_generator = await fetcher.fetch_opportunities_streaming(
            opp_soql_query,
            opp_job_id,
            opp_temp_file,
            batch_size=batch_size,
            start_index=0,  # Start from beginning to collect all owner IDs
        )
        
        # Iterate through all opportunities to collect owner IDs
        batch_count = 0
        async for opp_batch in opp_generator:
            batch_count += 1
            
            # Extract owner IDs from this batch
            for opportunity in opp_batch:
                owner_id = opportunity.get("OwnerId")
                if owner_id:
                    owner_ids.add(owner_id)
            
            # Log progress every 10 batches
            if batch_count % 10 == 0:
                await error_logger.alog_info(
                    "CollectOwnerIds",
                    f"Processed {batch_count} batches, found {len(owner_ids)} unique owner IDs so far"
                )
        
        await error_logger.alog_info(
            "CollectOwnerIds",
            f"Collected {len(owner_ids)} unique owner IDs from opportunities"
        )
        
    except Exception as e:
        await error_logger.alog_warning(
            "CollectOwnerIds",
            f"Error collecting owner IDs: {str(e)}"
        )
    
    return list(owner_ids)


async def _import_opportunities(
    fetcher: SalesforceOrdersFetcher,
    processor: OrdersDataProcessor,
    batch_handler: OrdersBatchHandler,
    checkpoint_manager: CheckpointManager,
    progress_tracker: OrdersProgressTracker,
    custom_fields_manager: CustomFieldsManager,
    field_mapping: Dict[str, Dict],
    mapping_status_custom_fields: Dict[str, str],
    enable_filter: bool,
    filter_field: Optional[str],
    filter_value: Optional[str],
    additional_filters: Optional[List[Dict]],
    ctx: Optional["Context"],
) -> bool:
    """
    Import Opportunity and OpportunityLineItem records from Salesforce.

    Returns:
        True if successful, False otherwise
    """
    try:
        # Note: Total count will be set after CSV download to avoid Bulk API aggregate query issues
        # Check for existing jobs in checkpoint
        opp_job_info = checkpoint_manager.get_job_info("Opportunity")
        opp_job_id = opp_job_info.get("job_id") if opp_job_info else None
        opp_temp_file = checkpoint_manager.get_temp_file("Opportunity")

        line_item_job_info = checkpoint_manager.get_job_info("OpportunityLineItem")
        line_item_job_id = (
            line_item_job_info.get("job_id") if line_item_job_info else None
        )
        line_item_temp_file = checkpoint_manager.get_temp_file("OpportunityLineItem")

        # Build SOQL queries
        if not opp_job_info:
            opp_soql_query = fetcher.build_opportunity_query(
                field_mapping,
                enable_filter,
                filter_field,
                filter_value,
                additional_filters,
            )

            # Log any fields that were skipped
            await fetcher.log_skipped_fields()

            await checkpoint_manager.error_logger.alog_info(
                "OpportunityImport", f"Opportunity SOQL Query: {opp_soql_query}"
            )
        else:
            opp_soql_query = opp_job_info.get("soql_query", "")

        if not line_item_job_info:
            line_item_soql_query = fetcher.build_line_item_query(field_mapping)

            await checkpoint_manager.error_logger.alog_info(
                "OpportunityImport", f"LineItem SOQL Query: {line_item_soql_query}"
            )
        else:
            line_item_soql_query = line_item_job_info.get("soql_query", "")

        # Fetch Opportunity records
        batch_size = ORDER_BATCH_SIZE
        current_batch = 0
        total_processed = checkpoint_manager.get_processed_count("Opportunity")

        # Get job_id, temp_file, and generator from fetcher for Opportunities
        (
            opp_job_id,
            opp_temp_file,
            opp_generator,
        ) = await fetcher.fetch_opportunities_streaming(
            opp_soql_query,
            opp_job_id,
            opp_temp_file,
            batch_size=batch_size,
            start_index=total_processed,
        )

        # Save Opportunity job info to checkpoint
        if not opp_job_info:
            await checkpoint_manager.set_job_info(
                opp_job_id, "Opportunity", opp_soql_query
            )
            await checkpoint_manager.set_temp_file("Opportunity", opp_temp_file)

        # Get job_id, temp_file, and generator from fetcher for LineItems
        (
            line_item_job_id,
            line_item_temp_file,
            line_item_generator,
        ) = await fetcher.fetch_line_items_streaming(
            line_item_soql_query,
            line_item_job_id,
            line_item_temp_file,
            batch_size=LINE_ITEM_BATCH_SIZE,
            start_index=checkpoint_manager.get_processed_count("OpportunityLineItem"),
        )

        # Save LineItem job info to checkpoint
        if not line_item_job_info:
            await checkpoint_manager.set_job_info(
                line_item_job_id, "OpportunityLineItem", line_item_soql_query
            )
            await checkpoint_manager.set_temp_file(
                "OpportunityLineItem", line_item_temp_file
            )

        # First, collect all line items into memory (they should be smaller)
        all_line_items = []
        async for line_item_batch in line_item_generator:
            processed_line_items = await processor.batch_process_line_items(
                line_item_batch, field_mapping
            )
            all_line_items.extend(processed_line_items)

        await checkpoint_manager.error_logger.alog_info(
            "OpportunityImport", f"Loaded {len(all_line_items)} line items into memory"
        )

        # Now process opportunities with their line items
        async for opp_batch in opp_generator:
            # Check for cancellation
            if ctx and ctx.exit_flag:
                await checkpoint_manager.error_logger.alog_info(
                    "OpportunityImport", "Import canceled by user"
                )
                await checkpoint_manager.mark_cancelled()
                await progress_tracker.mark_canceled()
                return False

            # Process batch of opportunities
            processed_opportunities = await processor.batch_process_opportunities(
                opp_batch, field_mapping, mapping_status_custom_fields, batch_handler
            )

            # Join with line items
            processed_opportunities = (
                await processor.join_opportunities_with_line_items(
                    processed_opportunities, all_line_items
                )
            )

            # Save batch to database
            batch_result = await batch_handler.save_orders_batch(
                processed_opportunities
            )
            total_processed += len(processed_opportunities)

            # Fix order_id for newly inserted records
            await batch_handler.fix_order_ids()

            # Process custom fields for this batch
            opportunity_order_map = batch_handler.get_opportunity_to_order_map()
            line_item_map = batch_handler.get_line_item_to_item_order_map()

            await custom_fields_manager.process_mixed_custom_fields(
                processed_opportunities,
                opportunity_order_map,
                line_item_map,
            )

            # Update progress
            progress_tracker.add_batch_results(
                batch_result.total_success, batch_result.failed_count
            )

            await progress_tracker.update_progress(
                len(processed_opportunities),
                batch_result.total_success,
                batch_result.failed_count,
                f"Processed {total_processed} Opportunity records",
            )

            # Update checkpoint
            await checkpoint_manager.update_batch_progress(
                "Opportunity",
                current_batch,
                total_processed,
                progress_tracker.success_count,
                progress_tracker.failed_count,
            )

            current_batch += 1

        # Save the complete mappings
        opportunity_order_map = batch_handler.get_opportunity_to_order_map()
        if opportunity_order_map:
            await checkpoint_manager.set_opportunity_order_mapping(
                opportunity_order_map
            )

        line_item_map = batch_handler.get_line_item_to_item_order_map()
        if line_item_map:
            await checkpoint_manager.set_line_item_mapping(line_item_map)

        await checkpoint_manager.error_logger.alog_info(
            "OpportunityImport",
            f"Successfully imported {total_processed} Opportunity records with line items",
        )

        return True

    except Exception as e:
        await checkpoint_manager.error_logger.alog_error(
            "IMPORT_ERROR",
            "OpportunityImport",
            f"Error importing Opportunities: {str(e)}",
            e,
        )
        return False
