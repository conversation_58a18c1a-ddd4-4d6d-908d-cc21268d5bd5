from datetime import datetime
from datetime import time as datetime_time
import json
import uuid

from asgiref.sync import sync_to_async
from django.contrib.auth.models import User
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ValidationError
from django.db import models
from django.utils import timezone

from data.constants.constant import *
from data.models.constant import *


class CustomQuerySet(models.QuerySet):
    def paginate(self, page_number, page_size):
        start_index = (page_number - 1) * page_size
        end_index = start_index + page_size
        return self[start_index:end_index]

    def delete(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self.model._log_data = log_data
        return super().delete(*args, **kwargs)


class ObjectManager(models.Manager):
    def get_queryset(self):
        return CustomQuerySet(self.model, using=self._db)

    def paginate(self, page_number, page_size):
        return self.get_queryset().paginate(page_number, page_size)


class BrowserActionResult(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    page_source = models.TextField(blank=True, null=True)
    url = models.TextField(blank=True, null=True)
    screenshot = models.FileField(
        verbose_name="Browser Action Screenshot", upload_to="browser-action"
    )
    created_at = models.DateTimeField(default=timezone.now)


class PdfFile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    file = models.FileField(verbose_name="Pdf File", upload_to="pdf-files")
    created_at = models.DateTimeField(default=timezone.now)


class WhisperAudio(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    file = models.FileField(
        verbose_name="Whisper Audio File", upload_to="whisper-audio"
    )
    created_at = models.DateTimeField(default=timezone.now)


class ActionsFiles(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    action = models.ForeignKey(
        "Action", on_delete=models.CASCADE, null=True, blank=True
    )
    file = models.FileField(verbose_name="Action File", upload_to="action-file")
    output_file = models.FileField(
        verbose_name="Action Output File",
        upload_to="action-file",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)


class TaskFile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    task = models.ForeignKey("Task", on_delete=models.CASCADE, null=True, blank=True)
    file = models.FileField(verbose_name="Task File", upload_to="task-files")
    created_at = models.DateTimeField(default=timezone.now)


class GeneratedImage(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    file = models.FileField(verbose_name="Generated Image", upload_to="generated-image")
    created_at = models.DateTimeField(default=timezone.now)


class GeneratedPresentation(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    file = models.FileField(
        verbose_name="Generated Presentation", upload_to="generated-pptx"
    )
    created_at = models.DateTimeField(default=timezone.now)


GeneralFileType = [
    ("image", "Image"),
    ("video", "Video"),
    ("document", "Document"),  # csv, excel sheets, txt, json etc.
]


class GeneralStorage(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    file = models.FileField(
        verbose_name="General Storage Files", upload_to="general-files"
    )
    file_type = models.CharField(
        max_length=20, choices=GeneralFileType, null=True, blank=True
    )
    created_at = models.DateTimeField(default=timezone.now)


class TwoFactorAuthenticator(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    recovery = models.TextField(null=True, blank=True)
    secret = models.CharField(max_length=256, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)


class SubscriptionPlan(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    # only for base pricing which is_base_plan = True
    base_plan = models.ForeignKey(
        "BasePricing", on_delete=models.CASCADE, null=True, blank=True
    )
    cost = models.FloatField(null=True, blank=True)
    prorate_cost = models.FloatField(null=True, blank=True)
    stripe_subscription_id = models.CharField(max_length=255, null=True, blank=True)
    stripe_items = models.JSONField(null=True, blank=True)
    subscription_period_started = models.DateTimeField(null=True, blank=True)
    subscription_period_ended = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)


class StoragePlan(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    subscription = models.ForeignKey(
        SubscriptionPlan,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="storage_plans",
    )
    # only for base pricing which is_base_plan = False
    category = models.ForeignKey(
        "BasePricing", on_delete=models.CASCADE, null=True, blank=True
    )
    amount = models.IntegerField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)


class CustomProperty(models.Model):
    """To Extend Property choices"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    model = models.CharField(max_length=500, null=True, blank=True)  # Table
    name = models.CharField(max_length=500, null=True, blank=True)  # Column in table
    # New column's value
    value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class PropertySet(models.Model):
    """
    To manage record form properties.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    target = models.CharField(max_length=500, null=True, blank=True)  # Page group type
    name = models.CharField(max_length=500, null=True, blank=True)
    children = models.JSONField(null=True, blank=True)
    sub_children = models.CharField(
        max_length=500, null=True, blank=True
    )  # Additional value in Form
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    as_default = models.BooleanField(default=False)
    custom_property = models.ForeignKey(
        "CustomProperty", on_delete=models.SET_NULL, null=True, blank=True
    )
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    order_line_items = models.ManyToManyField(
        "ShopTurboItemsOrdersNameCustomField", blank=True
    )
    purchase_order_line_items = models.ManyToManyField(
        "PurchaseOrdersItemNameCustomField", blank=True
    )
    deal_line_items = models.ManyToManyField("DealsItemsNameCustomField", blank=True)

    estimate_line_items = models.ManyToManyField(
        "EstimateItemsNameCustomField", blank=True
    )
    receipt_line_items = models.ManyToManyField(
        "ReceiptItemsNameCustomField", blank=True
    )
    invoice_line_items = models.ManyToManyField(
        "InvoiceItemsNameCustomField", blank=True
    )
    delivery_slip_line_items = models.ManyToManyField(
        "DeliverySlipItemsNameCustomField", blank=True
    )

    default_properties = models.TextField(null=True, blank=True)
    is_custom_form = models.BooleanField(default=False, null=True, blank=True)


def validate_value_custom_field(self, *args, **kwargs):
    if self.field_name and (
        self.field_name.type == "number" or self.field_name.type == "formula"
    ):
        if self.value:
            try:
                # Remove commas from the value for proper number parsing
                cleaned_value = (
                    self.value.replace(",", "")
                    if isinstance(self.value, str)
                    else self.value
                )
                self.value_number = float(cleaned_value)
            except (ValueError, TypeError):
                pass
        else:
            self.value_number = None

    elif self.field_name and self.field_name.type == "date_time":
        from dateutil.parser import parse as date_parse

        if self.value:
            try:
                # Parse datetime string and store in value_time field
                # Support various datetime formats
                parsed_datetime = date_parse(self.value)
                self.value_time = parsed_datetime
            except Exception as e:
                print(f"... ERROR === models.py -- date_time parsing: {e}")
                self.value_time = None
        else:
            self.value_time = None

    elif self.field_name and self.field_name.type == "date_range":
        from utils.date import parse_date

        if self.value:
            try:
                date_parts = self.value.split(" - ")
                start_date = parse_date(date_parts[0])
                self.value_time = start_date

                if "value_time_end" in [
                    field.name for field in self._meta.get_fields()
                ]:
                    end_date = parse_date(date_parts[1])
                    if end_date is not None:
                        end_date = datetime.combine(end_date, datetime_time(23, 59, 59))
                    self.value_time_end = end_date

            except Exception as e:
                print(f"... ERROR === models.py -- 6591: {e}")
        else:
            self.value_time = None

    super(self.__class__, self).save(*args, **kwargs)


def save_tags_values(self, *args, **kwargs):
    if self.value:
        # Get All Available Tag
        all_tags = set()
        for val_cf in self.__class__.objects.filter(field_name=self.field_name).exclude(
            id=self.id
        ):
            if val_cf.value:
                val_json = json.loads(val_cf.value)
                for tag in val_json:
                    all_tags.add(tag["value"])

        # Get Current Tag
        val_json = json.loads(self.value)
        for tag in val_json:
            all_tags.add(tag["value"])

        all_tags_str = ",".join(all_tags)
        self.field_name.tag_value = all_tags_str
        self.field_name.save()

    super(self.__class__, self).save(*args, **kwargs)


BARCODE_TYPES = [
    ("code_128_reader", "Code 128"),
    ("ean_reader", "JAN/EAN 13"),
    ("ean_8_reader", "JAN/EAN 8"),
    ("code_39_reader", "Code 39"),
    ("upc_reader", "UPC-A"),
    ("upc_e_reader", "UPC-E"),
]

QRCODE = [("qrcode", "QR Code")]


class DefaultBarcodeProperty(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    target = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(choices=BARCODE_TYPES, max_length=20, null=True, blank=True)
    column_field = models.CharField(max_length=500, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


# To Save Multi-Value of default Setting in app
class AppSettingChild(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    target_name = models.CharField(max_length=500, null=True, blank=True)
    app_setting = models.ForeignKey(
        "AppSetting",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="app_setting_field_relations",
    )
    value = models.CharField(max_length=500, null=True, blank=True)
    file = models.FileField(
        verbose_name="App Setting - Custom Field file",
        upload_to="appsetting-customfield-files",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)
    property_set = models.ForeignKey(
        PropertySet,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="app_setting_property_set_relations",
    )
    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)


class DateFormat(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    is_workspace_level = models.BooleanField(default=False)
    object_type = models.CharField(max_length=30, blank=True, null=True)
    value = models.CharField(max_length=30, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


class Module(models.Model):
    # Set of objects
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    slug = models.CharField(max_length=256, null=True, blank=True)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    object_values = models.CharField(max_length=2048, null=True, blank=True)
    name = models.CharField(max_length=256, null=True, blank=True)
    name_ja = models.CharField(max_length=256, null=True, blank=True)
    order = models.IntegerField(blank=True, null=True)
    active_module = models.CharField(max_length=256, null=True, blank=True)
    icon = models.TextField(blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


class MasterPdfTemplate(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    setting_type = models.CharField(max_length=50)
    name_en = models.CharField(max_length=50)
    name_ja = models.CharField(max_length=50)
    html = models.TextField()
    css = models.TextField()
    header_template = models.TextField(null=True, blank=True, default="")
    footer_template = models.TextField(null=True, blank=True, default="")
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)


class CustomizePdfTemplate(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    setting_type = models.CharField(max_length=50)
    name = models.CharField(max_length=50)
    name_en = models.CharField(max_length=50, null=True, blank=True)
    name_ja = models.CharField(max_length=50, null=True, blank=True)
    html = models.TextField()
    css = models.TextField()
    header_template = models.TextField(null=True, blank=True, default="")
    footer_template = models.TextField(null=True, blank=True, default="")
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    master_template = models.BooleanField(default=False)


class ExportTemplate(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    target = models.CharField(max_length=500, null=True, blank=True)  # Page group type
    name = models.CharField(max_length=500, null=True, blank=True)
    properties = models.TextField(null=True, blank=True)
    as_default = models.BooleanField(default=False)

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True
    )


class Association(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    associate_type = models.TextField(blank=True, null=True)  # Target of association
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)

    # Source of association (CustomField Source)
    attendance_associate = models.ForeignKey(
        "TrackNameCustomField", on_delete=models.CASCADE, blank=True, null=True
    )
    case_associate = models.ForeignKey(
        "DealsNameCustomField", on_delete=models.CASCADE, blank=True, null=True
    )
    order_associate = models.ForeignKey(
        "ShopTurboOrdersNameCustomField",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
    )
    purchase_order_associate = models.ForeignKey(
        "PurchaseOrdersNameCustomField", on_delete=models.CASCADE, blank=True, null=True
    )
    production_associate = models.ForeignKey(
        "ProductionNameCustomField", on_delete=models.CASCADE, blank=True, null=True
    )
    task_associate = models.ForeignKey(
        "TaskCustomFieldName", on_delete=models.CASCADE, blank=True, null=True
    )

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)


class SamlIntegration(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    saml_slug = models.CharField(max_length=256, null=True, blank=True)
    domain_name = models.TextField(null=True, blank=True)
    sp_entity_id = models.TextField(null=True, blank=True)
    sp_acs_url = models.TextField(null=True, blank=True)
    idp_entity_id = models.TextField(null=True, blank=True)
    idp_sso_url = models.TextField(null=True, blank=True)
    idp_cert = models.TextField(null=True, blank=True)
    email_id_key = models.TextField(null=True, blank=True)


STOCK_MODE = [
    ("in", "In"),
    ("out", "Out"),
]


class QRBotAccess(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    channel = models.ForeignKey("Channel", on_delete=models.CASCADE)

    inventory_transaction_user_property = models.TextField(null=True, blank=True)
    inventory_transaction_case_property = models.TextField(
        null=True, blank=True
    )  # This is using Case Property
    user_id_property = models.TextField(null=True, blank=True)
    qr_code_property = models.TextField(null=True, blank=True)

    # QR Bot State
    current_user = models.TextField(null=True, blank=True)
    current_case = models.TextField(null=True, blank=True)
    stock_mode = models.TextField(choices=STOCK_MODE, null=True, blank=True)

    scan_log = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)


SCAN_TYPE = [
    ("employee", "Employee"),
    ("mode", "Mode"),
    ("case", "Case"),
    ("product", "Product"),
]


class QRBotAccessLog(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    qrbot_access = models.ForeignKey(QRBotAccess, on_delete=models.CASCADE)
    scan_type = models.TextField(choices=SCAN_TYPE, null=True, blank=True)
    content = models.TextField(null=True, blank=True)
    error = models.BooleanField(default=False)
    error_message = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)


ADVANCE_FILTER_TYPE = [("default", "Default"), ("template", "Template")]


class AdvanceSearchFilter(models.Model):
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    custom_object = models.ForeignKey(
        "CustomObject", on_delete=models.CASCADE, null=True, blank=True
    )
    object_type = models.CharField(max_length=200, null=True, blank=True)

    type = models.CharField(
        max_length=200, choices=ADVANCE_FILTER_TYPE, default="default"
    )
    template_name = models.CharField(max_length=200, null=True, blank=True)

    is_active = models.BooleanField(default=False)

    search_settings = models.TextField(null=True, blank=True)
    search_filter = models.JSONField(null=True, blank=True)
    search_filter_status = models.JSONField(null=True, blank=True)

    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


class HubspotUser(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    channel = models.ForeignKey("Channel", on_delete=models.CASCADE)
    hubspot_id = models.TextField(null=True, blank=True)
    hubspot_email = models.TextField(null=True, blank=True)
    hubspot_first_name = models.TextField(null=True, blank=True)
    hubspot_last_name = models.TextField(null=True, blank=True)
    sanka_user = models.ForeignKey(
        "UserManagement", on_delete=models.CASCADE, null=True, blank=True
    )

    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


class AssociationLabel(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    label = models.CharField(max_length=200, null=True, blank=True)
    label_ja = models.CharField(
        max_length=200, null=True, blank=True
    )  # if created by sanka
    object_source = models.CharField(max_length=200, null=True, blank=True)
    object_target = models.TextField(null=True, blank=True)
    project_target = models.TextField(null=True, blank=True)  # For task - project
    association_type = models.CharField(max_length=200, null=True, blank=True)

    label_pair = models.JSONField(null=True, blank=True)

    created_by_sanka = models.BooleanField(default=False)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        # For user-created custom objects (created_by_sanka=False),
        # automatically set label_ja to the same value as label if label_ja is None
        if not self.created_by_sanka and self.label and not self.label_ja:
            self.label_ja = self.label
        super().save(*args, **kwargs)
    
    def get_association_count(self):
        """Get the total count of associations using this label efficiently"""
        from data.models.base import AssociationLabelObject
        return AssociationLabelObject.objects.filter(label=self).count()


class AssociationLabelImplementation(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    label = models.ForeignKey(AssociationLabel, on_delete=models.CASCADE)
    object_source_id = models.CharField(max_length=200, null=True, blank=True)
    object_target_id = models.CharField(max_length=200, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


class AssociationLabelObject(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Source object (the one "owning" the relationship)
    source_content_type = models.ForeignKey(
        ContentType, on_delete=models.CASCADE, related_name="source_associations"
    )
    source_object_id = (
        models.UUIDField()
    )  # RECORD ID OF SOURCE OBJ: Changed to UUIDField since you use UUIDs
    source_object = GenericForeignKey("source_content_type", "source_object_id")

    # Target object (the one being associated)
    target_content_type = models.ForeignKey(
        ContentType, on_delete=models.CASCADE, related_name="target_associations"
    )
    target_object_id = (
        models.UUIDField()
    )  # RECORD ID OF TARGET OBJ:  Changed to UUIDField
    target_object = GenericForeignKey("target_content_type", "target_object_id")

    # Association metadata
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    label = models.ForeignKey(
        AssociationLabel, on_delete=models.CASCADE, null=True, blank=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    @classmethod
    def create_association(cls, source_obj, target_obj, workspace, label=None):
        """Create association with source -> target relationship"""
        # Validate that both objects are not None
        if source_obj is None:
            raise ValueError("source_obj cannot be None")
        if target_obj is None:
            raise ValueError("target_obj cannot be None")

        # Handle label
        label_instance = None
        if label:
            if isinstance(label, str):
                label_instance = AssociationLabel.objects.get(
                    workspace=workspace, label=label
                )
            else:
                label_instance = label

        # Check for existing association
        existing = cls.objects.filter(
            source_content_type=ContentType.objects.get_for_model(source_obj),
            source_object_id=source_obj.id,
            target_content_type=ContentType.objects.get_for_model(target_obj),
            target_object_id=target_obj.id,
            workspace=workspace,
            label=label_instance,
            is_active=True,
        ).first()

        if existing:
            return existing, False

        # Create new association
        association = cls.objects.create(
            source_object=source_obj,
            target_object=target_obj,
            workspace=workspace,
            label=label_instance,
        )
        return association, True

    @classmethod
    async def aio_create_association(
        cls, source_obj, target_obj, workspace, label=None
    ):
        """Create association with source -> target relationship"""
        # Validate that both objects are not None
        if source_obj is None:
            raise ValueError("source_obj cannot be None")
        if target_obj is None:
            raise ValueError("target_obj cannot be None")

        # Handle label
        label_instance = None
        if label:
            if isinstance(label, str):
                label_instance = await AssociationLabel.objects.aget(
                    workspace=workspace, label=label
                )
            else:
                label_instance = label

        source_content_type = await sync_to_async(ContentType.objects.get_for_model)(
            source_obj
        )
        target_content_type = await sync_to_async(ContentType.objects.get_for_model)(
            target_obj
        )
        # Check for existing association
        existing = await cls.objects.filter(
            source_content_type=source_content_type,
            source_object_id=source_obj.id,
            target_content_type=target_content_type,
            target_object_id=target_obj.id,
            workspace=workspace,
            label=label_instance,
            is_active=True,
        ).afirst()

        if existing:
            return existing, False

        # Create new association
        association = await cls.objects.acreate(
            source_object=source_obj,
            target_object=target_obj,
            workspace=workspace,
            label=label_instance,
        )
        return association, True

    @classmethod
    def get_for_source(cls, source_obj, workspace=None, label=None):
        """Get all associations where obj is the source"""
        content_type = ContentType.objects.get_for_model(source_obj)

        qs = cls.objects.filter(
            source_content_type=content_type,
            source_object_id=source_obj.id,
            is_active=True,
        )

        if workspace:
            qs = qs.filter(workspace=workspace)
        if label:
            if isinstance(label, AssociationLabel):
                qs = qs.filter(label=label)
            else:  # Assume it's a string
                qs = qs.filter(label__label=label)

        return qs

    @classmethod
    def get_for_target(cls, target_obj, workspace=None, label=None):
        """Get all associations where obj is the target"""
        content_type = ContentType.objects.get_for_model(target_obj)

        qs = cls.objects.filter(
            target_content_type=content_type,
            target_object_id=target_obj.id,
            is_active=True,
        )

        if workspace:
            qs = qs.filter(workspace=workspace)
        if label:
            if isinstance(label, AssociationLabel):
                qs = qs.filter(label=label)
            else:  # Assume it's a string
                qs = qs.filter(label__label=label)

        return qs

    @classmethod
    def get_for_object(
        cls, obj, workspace=None, label=None, as_source=None, as_target=None
    ):
        """
        Get associations for an object (as source, target, or both)

        Args:
            obj: Object to find associations for
            workspace: Workspace filter
            label: Label filter
            as_source: True = only where obj is source, False = exclude where obj is source
            as_target: True = only where obj is target, False = exclude where obj is target
        """
        content_type = ContentType.objects.get_for_model(obj)

        q_source = models.Q(source_content_type=content_type, source_object_id=obj.id)
        q_target = models.Q(target_content_type=content_type, target_object_id=obj.id)

        # Build query based on parameters
        if as_source is True and as_target is False:
            query = q_source
        elif as_target is True and as_source is False:
            query = q_target
        elif as_source is False and as_target is True:
            query = q_target
        elif as_source is True and as_target is True:
            query = q_source | q_target
        else:  # Default: both directions
            query = q_source | q_target

        qs = cls.objects.filter(query, is_active=True)

        if workspace:
            qs = qs.filter(workspace=workspace)
        if label:
            if isinstance(label, AssociationLabel):
                qs = qs.filter(label=label)
            else:
                qs = qs.filter(label__label=label)

        return qs

    @classmethod
    def delete_association(cls, source_obj, target_obj, workspace, label=None):
        """Delete association between source and target"""
        label_instance = None
        if label:
            if isinstance(label, str):
                try:
                    label_instance = AssociationLabel.objects.get(
                        workspace=workspace, label=label
                    )
                except AssociationLabel.DoesNotExist:
                    return False
            else:
                label_instance = label

        try:
            association = cls.objects.get(
                source_content_type=ContentType.objects.get_for_model(source_obj),
                source_object_id=source_obj.id,
                target_content_type=ContentType.objects.get_for_model(target_obj),
                target_object_id=target_obj.id,
                workspace=workspace,
                label=label_instance,
                is_active=True,
            )
            association.delete()
            return True
        except cls.DoesNotExist:
            return False

    @classmethod
    def reset_associations_for_object(cls, obj, workspace, label=None):
        """Delete all associations for an object with a specific label"""
        associations = cls.get_for_object(obj, workspace=workspace, label=label)
        count = associations.count()
        associations.delete()
        return count

    class Meta:
        unique_together = [
            [
                "source_content_type",
                "source_object_id",
                "target_content_type",
                "target_object_id",
                "workspace",
                "label",
            ]
        ]
        indexes = [
            models.Index(
                fields=["source_content_type", "source_object_id", "workspace"]
            ),
            models.Index(
                fields=["target_content_type", "target_object_id", "workspace"]
            ),
            models.Index(fields=["label", "workspace"]),
        ]


class WorkspacePropertySet(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)

    object_type = models.CharField(max_length=150)
    property_id = models.CharField(max_length=200)
    order = models.PositiveIntegerField()
