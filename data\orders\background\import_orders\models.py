from pydantic import BaseModel, Field
from typing import Optional


class ImportOrdersShopifyPayload(BaseModel):
    """Payload model for Shopify-specific order import child workflow"""

    user: str
    channel_id: str
    mapping_custom_fields: Optional[str] = None
    mapping_contact_custom_fields: Optional[str] = None
    default_customer: Optional[str] = Field(default="")
    key_item_field: Optional[str] = None
    how_to_import: Optional[str] = None
    sync_method: Optional[str] = Field(default="")
    history_id: str
    background_job_id: Optional[str] = Field(default="")
    lang: Optional[str] = Field(default="ja")


class ImportOrdersHubSpotPayload(BaseModel):
    """Payload model for HubSpot-specific order import child workflow"""

    user: str
    channel_id: str
    mapping_custom_fields: Optional[str] = None
    mapping_status_custom_fields: Optional[str] = None
    history_id: str
    how_to_import: Optional[str] = None
    how_to_import_hubspot: Optional[str] = None
    key_item_field: Optional[str] = None
    as_deal: Optional[bool] = Field(default=False)
    lang: Optional[str] = Field(default="ja")
    last_index: Optional[str] = Field(default="")
    hubspot_filter_import: Optional[str] = Field(default="")
    background_job_id: Optional[str] = Field(default="")


class ImportHubSpotOrdersSubprocessPayload(BaseModel):
    """Payload model for HubSpot orders subprocess task"""

    channel_id: str
    filter_deal_stage: Optional[str] = Field(default="")
    mapping_custom_fields: Optional[str] = Field(default="{}")
    how_to_import: Optional[str] = Field(default="create")
    lang: Optional[str] = Field(default="ja")
    how_to_import_items: Optional[str] = Field(default="create")
    key_item_field: Optional[str] = Field(default="")
    mapping_status_custom_fields: Optional[str] = Field(default="{}")
    last_index: Optional[str] = Field(default="")
    hubspot_filter_import: Optional[str] = Field(default="{}")
    history_id: str
    after: Optional[str] = Field(default="")
    background_job_id: Optional[str] = Field(default="")


class ImportBcartOrdersSubprocessPayload(BaseModel):
    """Payload model for B-Cart orders subprocess task"""

    channel_id: str
    mapping_custom_fields: Optional[str] = None
    key_item_field: Optional[str] = None
    key_customer_field: Optional[str] = None
    how_to_import: Optional[str] = None
    how_to_import_customer: Optional[str] = None
    import_filter: Optional[str] = None
    history_id: str
    sub_history_id: str
    background_job_id: Optional[str] = Field(default="")
    order_datas: Optional[str] = None


class ImportShopifyOrdersSubprocessPayload(BaseModel):
    """Payload model for Shopify-specific order import child workflow"""

    user: str
    channel_id: str
    mapping_custom_fields: Optional[str] = None
    mapping_contact_custom_fields: Optional[str] = None
    default_customer: Optional[str] = Field(default="")
    key_item_field: Optional[str] = None
    how_to_import: Optional[str] = None
    sync_method: Optional[str] = Field(default="")
    history_id: str
    background_job_id: Optional[str] = Field(default="")
    lang: Optional[str] = Field(default="ja")
    sub_history_id: str
    order_datas: Optional[str] = None


class ImportOrdersSquarePayload(BaseModel):
    """Payload model for Square-specific order import child workflow"""

    channel_id: str
    history_id: str
    background_job_id: Optional[str] = Field(default="")


class ImportOrdersAmazonPayload(BaseModel):
    """Payload model for Amazon-specific order import child workflow"""

    channel_id: str
    mapping_custom_fields: Optional[str] = None
    default_customer: Optional[str] = Field(default="")
    history_id: str
    background_job_id: Optional[str] = Field(default="")
    key_item_field: Optional[str] = None
    how_to_import: Optional[str] = None
    import_filter: Optional[str] = None
    user: str


class ImportOrdersEcforcePayload(BaseModel):
    """Payload model for Ecforce-specific order import child workflow"""

    channel_id: str
    workspace_id: Optional[str] = None
    user_id: Optional[str] = None
    mapping_custom_fields: Optional[str] = None
    history_id: str
    background_job_id: Optional[str] = Field(default="")
    lang: Optional[str] = Field(default="ja")


class ImportOrdersRakutenPayload(BaseModel):
    """Payload model for Rakuten-specific order import child workflow"""

    channel_id: str
    mapping_custom_fields: Optional[str] = None
    key_item_field: Optional[str] = None
    how_to_import: Optional[str] = None
    import_filter: Optional[str] = None
    default_customer: Optional[str] = Field(default="")
    history_id: str
    background_job_id: Optional[str] = Field(default="")
    user: str


class ImportOrdersEccubePayload(BaseModel):
    """Payload model for EC-CUBE-specific order import child workflow"""

    channel_id: str
    history_id: str
    background_job_id: Optional[str] = Field(default="")


class ImportOrdersMakeshopPayload(BaseModel):
    """Payload model for Makeshop-specific order import child workflow"""

    channel_id: str
    mapping_custom_fields: Optional[str] = None
    key_item_field: Optional[str] = None
    how_to_import: Optional[str] = None
    import_order_date: Optional[str] = None
    mapping_status_custom_fields: Optional[str] = None
    default_customer: Optional[str] = Field(default="")
    import_filter: Optional[str] = None
    history_id: str
    background_job_id: Optional[str] = Field(default="")


class ImportOrdersYahooShoppingPayload(BaseModel):
    """Payload model for Yahoo Shopping-specific order import child workflow"""

    user: str
    channel_id: str
    mapping_custom_fields: Optional[str] = None
    key_item_field: Optional[str] = None
    how_to_import: Optional[str] = None
    import_filter: Optional[str] = None
    history_id: str
    background_job_id: Optional[str] = Field(default="")


class ImportOrdersBcartPayload(BaseModel):
    """Payload model for B-Cart-specific order import child workflow"""

    channel_id: str
    mapping_custom_fields: Optional[str] = None
    key_item_field: Optional[str] = None
    key_customer_field: Optional[str] = None
    how_to_import: Optional[str] = None
    how_to_import_customer: Optional[str] = None
    import_filter: Optional[str] = None
    history_id: str
    background_job_id: Optional[str] = Field(default="")


class ImportOrdersWoocommercePayload(BaseModel):
    """Payload model for WooCommerce-specific order import child workflow"""

    channel_id: str
    mapping_custom_fields: Optional[str] = None
    history_id: str
    background_job_id: Optional[str] = Field(default="")


class ImportOrdersSalesforcePayload(BaseModel):
    """Payload model for Salesforce-specific order import child workflow"""

    user: str
    channel_id: str
    mapping_custom_fields: Optional[str] = None
    mapping_association_custom_fields: Optional[str] = None
    mapping_status_custom_fields: Optional[str] = None
    history_id: str
    background_job_id: Optional[str] = Field(default="")


class ImportOrdersSalesforcePayloadV2(BaseModel):
    """Payload model for Salesforce-specific order import child workflow"""

    background_job_id: str


class ImportOrdersEbayPayload(BaseModel):
    """Payload model for eBay-specific order import child workflow"""

    channel_id: str
    how_to_import: Optional[str] = None
    history_id: str
    background_job_id: Optional[str] = Field(default="")


class ImportOrdersStripePayload(BaseModel):
    """Payload model for Stripe-specific order import child workflow"""

    channel_id: str
    days_ago_filter: Optional[int] = Field(default=1)
    only_payment_status: Optional[bool] = Field(default=False)
    target_property: Optional[str] = Field(default="")
    target_value: Optional[str] = Field(default="")
    history_id: str
    background_job_id: Optional[str] = Field(default="")


class ImportOrdersNextEnginePayload(BaseModel):
    """Payload model for NextEngine-specific order import child workflow"""

    channel_id: str
    user_id: str
    mapping_custom_fields: Optional[str] = Field(default="{}")
    history_id: str
    background_job_id: Optional[str] = Field(default="")


class ImportOrdersResultPayload(BaseModel):
    """Result payload model for child workflow results"""

    success: bool
    platform: str
    message: Optional[str] = None
    error: Optional[str] = None
