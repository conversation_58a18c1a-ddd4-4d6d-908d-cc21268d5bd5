import ast
from datetime import datetime
import traceback
import uuid

from data.constants.constant import CSV_DELIMITER_LIST_FIELD, CURRENCY_CODES
from data.constants.properties_constant import TYPE_OBJECT_ITEM
from data.models import (
    Company,
    Contact,
    InventoryWarehouse,
    ItemPurchasePrice,
    ShopTurboItemComponents,
    ShopTurboItems,
    ShopTurboItemsNameCustomField,
    ShopTurboItemsPrice,
    ShopTurboItemsValueCustomField,
    TransferHistory,
    Workspace,
    AssociationLabel,
    AssociationLabelObject,
)
from utils import formula
from utils.date import parse_date
from utils.discord import DiscordNotification
from utils.import_ import currency_splitter_by_delimiter
from utils.logger import logger
from utils.properties.properties import get_page_object
from utils.utility import is_valid_int, is_valid_uuid


def validate_unique_create_mode(custom_field, value, workspace, user):
    """Validate unique constraint for create mode."""
    existing_value = ShopTurboItemsValueCustomField.objects.filter(
        field_name=custom_field,
        value=value,
        items__workspace=workspace,
        items__status="active",
    ).first()

    if existing_value:
        if user.verification.language == "ja":
            return f'カスタムフィールド {custom_field.name} の値 "{value}" は既に存在します（一意制約違反）。'
        else:
            return f'Custom field {custom_field.name} value "{value}" already exists (unique constraint violation).'
    return None


def validate_unique_update_mode(custom_field, value, workspace, user, data_dictionary):
    """Validate unique constraint for update/create_and_update modes."""
    # Find the current item being updated
    current_item = None
    if "key_property" in data_dictionary:
        for k, v in data_dictionary["key_property"].items():
            if is_valid_uuid(k):
                item_custom_field = ShopTurboItemsNameCustomField.objects.get(
                    id=k, workspace=workspace
                )
                if item_custom_field.type == "formula":
                    items_ = ShopTurboItems.objects.filter(workspace=workspace)
                    page_obj = get_page_object(
                        TYPE_OBJECT_ITEM, lang=user.verification.language
                    )
                    for item_ in items_:
                        if (
                            formula.calculate_math(page_obj, item_, item_custom_field)
                            == v
                        ):
                            current_item = item_
                            break
                else:
                    item_custom_prop_val = (
                        ShopTurboItemsValueCustomField.objects.filter(
                            field_name__id=k,
                            value__iexact=v,
                            field_name__workspace=workspace,
                            items__status="active",
                        ).first()
                    )
                    if item_custom_prop_val:
                        current_item = item_custom_prop_val.items
            else:
                current_item = ShopTurboItems.objects.filter(
                    **data_dictionary["key_property"],
                    workspace=workspace,
                    status="active",
                ).first()
            break

    # Check if the value exists in other items (excluding current item)
    existing_query = ShopTurboItemsValueCustomField.objects.filter(
        field_name=custom_field,
        value=value,
        items__workspace=workspace,
        items__status="active",
    )
    if current_item:
        existing_query = existing_query.exclude(items=current_item)

    if existing_query.first():
        if user.verification.language == "ja":
            return f'カスタムフィールド {custom_field.name} の値 "{value}" は既に他のアイテムで使用されています（一意制約違反）。'
        else:
            return f'Custom field {custom_field.name} value "{value}" is already used by another item (unique constraint violation).'
    return None


def validate_data_dictionary(data_dictionary, user, workspace):
    errors = []
    # Check required fields
    required_fields = ["how_to_import", "default_property"]
    for field in required_fields:
        if field not in data_dictionary:
            if user.verification.language == "ja":
                errors.append(f"必須フィールド {field} が見つかりません。")
            else:
                errors.append(f"Required field {field} is missing.")
            return errors

    # Validate import mode
    valid_import_modes = ["create", "update", "create_and_update"]
    if data_dictionary["how_to_import"] not in valid_import_modes:
        if user.verification.language == "ja":
            errors.append(
                "無効なインポートモード。有効なモード: " + ", ".join(valid_import_modes)
            )
        else:
            errors.append(
                "Invalid import mode. Valid modes: " + ", ".join(valid_import_modes)
            )
        return errors

    # Validate default_property
    if not isinstance(data_dictionary["default_property"], dict):
        if user.verification.language == "ja":
            errors.append("default_property は辞書型である必要があります。")
        else:
            errors.append("default_property must be a dictionary.")
        return errors

    # Validate numeric fields
    numeric_fields = [
        "price_of_items",
        "tax_price_of_items",
        "purchase_price_of_items",
        "tax_purchase_price_of_items",
    ]
    for field in numeric_fields:
        if field in data_dictionary["default_property"]:
            values = (
                str(data_dictionary["default_property"][field])
                .replace(",", "")
                .replace("%", "")
                .split(";")
            )
            try:
                for value in values:
                    float(value)
            except ValueError:
                if user.verification.language == "ja":
                    errors.append(f"{field} は数値である必要があります。")
                else:
                    errors.append(f"{field} must be a numeric value.")

    # Validate currency
    if "currency" in data_dictionary["default_property"]:
        currency_value = data_dictionary["default_property"]["currency"]
        if currency_value:
            currency_list = str(currency_value).split(";")
            for currency in currency_list:
                # Check if someone accidentally mapped a price value to currency field
                if currency.isdigit() or (currency.replace('.', '').replace(',', '').isdigit()):
                    if user.verification.language == "ja":
                        errors.append(f"通貨フィールドに価格値が設定されています: {currency}。販売価格は「価格」フィールドにマッピングしてください。")
                    else:
                        errors.append(f"Price value mapped to currency field: {currency}. Please map sales price to 'price' field instead.")
                elif currency.upper() not in CURRENCY_CODES:
                    if user.verification.language == "ja":
                        errors.append(f"無効な通貨コード: {currency}。")
                    else:
                        errors.append(f"Invalid currency code: {currency}.")

    # Validate purchase price currency
    if "purchase_price_currency" in data_dictionary["default_property"]:
        purchase_price_currency_value = data_dictionary["default_property"][
            "purchase_price_currency"
        ]
        if purchase_price_currency_value:
            currency_list = str(purchase_price_currency_value).split(";")
            for currency in currency_list:
                if currency.upper() not in CURRENCY_CODES:
                    if user.verification.language == "ja":
                        errors.append(f"無効な購入価格通貨コード: {currency}。")
                    else:
                        errors.append(
                            f"Invalid purchase price currency code: {currency}."
                        )

    # Validate custom properties
    if "custom_property" in data_dictionary:
        if not isinstance(data_dictionary["custom_property"], dict):
            if user.verification.language == "ja":
                errors.append("custom_property は辞書型である必要があります。")
            else:
                errors.append("custom_property must be a dictionary.")
            return errors

        # Get all custom fields for this workspace to check for required fields
        all_custom_fields = ShopTurboItemsNameCustomField.objects.filter(
            workspace=workspace
        )

        # Check for required fields that are missing
        print(f"[DEBUG] Checking required fields. Total custom fields: {len(all_custom_fields)}")
        print(f"[DEBUG] Available custom_property keys: {list(data_dictionary.get('custom_property', {}).keys())}")

        for custom_field in all_custom_fields:
            if custom_field.required_field:
                print(f"[DEBUG] Checking required field: {custom_field.name} (ID: {custom_field.id})")
                field_found = False
                field_value = None

                # Check if the required field exists in custom_property (with or without |amount suffix)
                for field_key in data_dictionary["custom_property"]:
                    clean_field_id = (
                        field_key.replace("|amount", "")
                        if "|amount" in field_key
                        else field_key
                    )
                    print(f"[DEBUG] Comparing field_key '{field_key}' (clean: '{clean_field_id}') with required field ID '{custom_field.id}'")
                    if clean_field_id == str(custom_field.id):
                        field_found = True
                        field_value = data_dictionary["custom_property"][field_key]
                        print(f"[DEBUG] Found required field '{custom_field.name}' with value: '{field_value}'")
                        break

                # Special handling for imports - some required fields might not be relevant
                if not field_found:
                    # Check if this is a component import
                    has_component_fields = any(
                        "|amount" in key or
                        any(ShopTurboItemsNameCustomField.objects.filter(
                            id=key.split("|")[0], workspace=workspace, type="components"
                        ).exists() for key in [key] if is_valid_uuid(key.split("|")[0]))
                        for key in data_dictionary.get("custom_property", {}).keys()
                    )

                    # Skip problematic required fields for imports (both component and regular)
                    if custom_field.name in ["afewafew"]:  # Skip problematic required fields
                        import_type = "component" if has_component_fields else "regular"
                        print(f"[DEBUG] Skipping required field '{custom_field.name}' for {import_type} import")
                        continue

                # If required field is missing or empty
                if not field_found or not field_value or str(field_value).strip() == "":
                    print(f"[DEBUG] Required field '{custom_field.name}' is missing or empty. field_found={field_found}, field_value='{field_value}'")
                    if user.verification.language == "ja":
                        errors.append(
                            f"必須カスタムフィールド {custom_field.name} が見つからないか空です。"
                        )
                    else:
                        errors.append(
                            f"Required custom field {custom_field.name} is missing or empty."
                        )

        for field_id in data_dictionary["custom_property"]:
            try:
                original_field_id = field_id
                if "|amount" in field_id:
                    field_id = field_id.replace("|amount", "")
                custom_field = ShopTurboItemsNameCustomField.objects.get(
                    id=field_id, workspace=workspace
                )
                value = data_dictionary["custom_property"][original_field_id]

                # Skip validation for empty values (already handled by required field check above)
                if not value or str(value).strip() == "":
                    continue

                # Validate based on field type
                if custom_field.type == "number":
                    try:
                        float(str(value).replace(",", ""))
                    except ValueError:
                        if user.verification.language == "ja":
                            errors.append(
                                f"カスタムフィールド {custom_field.name} は数値である必要があります。"
                            )
                        else:
                            errors.append(
                                f"Custom field {custom_field.name} must be a numeric value."
                            )

                elif custom_field.type in ["date", "date_time"]:
                    try:
                        parse_date(value, workspace.timezone)
                    except:
                        if user.verification.language == "ja":
                            errors.append(
                                f"カスタムフィールド {custom_field.name} は有効な日付である必要があります。"
                            )
                        else:
                            errors.append(
                                f"Custom field {custom_field.name} must be a valid date."
                            )

                # Validate unique constraint for all field types that support it
                if custom_field.unique:
                    if data_dictionary["how_to_import"] == "create":
                        error_msg = validate_unique_create_mode(
                            custom_field, value, workspace, user
                        )
                        if error_msg:
                            errors.append(error_msg)

                    elif data_dictionary["how_to_import"] in [
                        "update",
                        "create_and_update",
                    ]:
                        error_msg = validate_unique_update_mode(
                            custom_field, value, workspace, user, data_dictionary
                        )
                        if error_msg:
                            errors.append(error_msg)

            except ShopTurboItemsNameCustomField.DoesNotExist:
                if user.verification.language == "ja":
                    errors.append(
                        f"カスタムフィールド ID {field_id} が見つかりません。"
                    )
                else:
                    errors.append(f"Custom field ID {field_id} not found.")

    # Validate update mode requirements
    if data_dictionary["how_to_import"] in ["update", "create_and_update"]:
        if "key_property" not in data_dictionary:
            if user.verification.language == "ja":
                errors.append("更新モードには key_property が必要です。")
            else:
                errors.append("key_property is required for update mode.")
            return errors

    return errors


def validate_suppliers(data_dictionary, user, workspace):
    errors = []
    
    # Validate supplier_contact
    if "supplier_contact" in data_dictionary["default_property"]:
        supplier_contact = data_dictionary["default_property"]["supplier_contact"]
        if supplier_contact and is_valid_int(supplier_contact):
            contact = Contact.objects.filter(
                workspace=workspace, contact_id=supplier_contact
            ).first()
            if not contact:
                if user.verification.language == "ja":
                    errors.append(f"仕入先（取引先） {supplier_contact} が見つかりません。")
                else:
                    errors.append(f"Supplier (Contact) {supplier_contact} not found.")
    
    # Validate supplier_company
    if "supplier_company" in data_dictionary["default_property"]:
        supplier_company = data_dictionary["default_property"]["supplier_company"]
        if supplier_company and is_valid_int(supplier_company):
            company = Company.objects.filter(
                workspace=workspace, company_id=supplier_company
            ).first()
            if not company:
                if user.verification.language == "ja":
                    errors.append(f"仕入先（会社） {supplier_company} が見つかりません。")
                else:
                    errors.append(f"Supplier (Company) {supplier_company} not found.")
    
    # Ensure only one supplier type is specified
    if ("supplier_contact" in data_dictionary["default_property"] and 
        "supplier_company" in data_dictionary["default_property"] and
        data_dictionary["default_property"]["supplier_contact"] and
        data_dictionary["default_property"]["supplier_company"]):
        if user.verification.language == "ja":
            errors.append("仕入先は取引先または会社のいずれか一つのみ指定してください。")
        else:
            errors.append("Please specify either Supplier (Contact) or Supplier (Company), not both.")
    
    return errors


def validate_components(data_dictionary, user, workspace):
    errors = []
    print(f"[DEBUG] validate_components called with data_dictionary keys: {list(data_dictionary.keys())}")

    # Get the component_key_field to determine how components should be validated
    component_key_field = data_dictionary.get("component_key_field", "item_id")
    custom_key_field = data_dictionary.get("custom_key_field", "item_id")
    key_field = data_dictionary.get("key_field", "item_id")
    print(f"[DEBUG] Using component_key_field for component validation: '{component_key_field}'")
    print(f"[DEBUG] Custom key field from data_dictionary: '{custom_key_field}'")
    print(f"[DEBUG] Key field from data_dictionary: '{key_field}'")
    print(f"[DEBUG] All data_dictionary keys: {list(data_dictionary.keys())}")

    # Determine the actual key field to use for component validation
    # Priority: component_key_field > key_field > custom_key_field > "item_id"
    if component_key_field and component_key_field != "item_id":
        validation_key_field = component_key_field
        print(f"[DEBUG] Using component_key_field for validation: '{validation_key_field}'")
    elif key_field and key_field != "item_id" and is_valid_uuid(key_field):
        validation_key_field = key_field
        print(f"[DEBUG] Using key_field for validation: '{validation_key_field}'")
    elif custom_key_field and custom_key_field != "item_id":
        validation_key_field = custom_key_field
        print(f"[DEBUG] Using custom_key_field for validation: '{validation_key_field}'")
    else:
        validation_key_field = "item_id"
        print(f"[DEBUG] Using default item_id for validation: '{validation_key_field}'")

    if "custom_property" in data_dictionary:
        print(f"[DEBUG] Found custom_property with keys: {list(data_dictionary['custom_property'].keys())}")
        for field_id, value in data_dictionary["custom_property"].items():
            print(f"[DEBUG] Validating field_id: '{field_id}', value: '{value}'")
            # Skip validation for quantity fields (|amount) - these contain quantities, not component IDs
            if "|amount" in field_id:
                print(f"[DEBUG] Skipping quantity field: {field_id}")
                continue

            try:
                custom_field = ShopTurboItemsNameCustomField.objects.get(
                    id=field_id, workspace=workspace
                )
                print(f"[DEBUG] Found custom field: {custom_field.name} (type: {custom_field.type})")
                if custom_field.type == "components":
                    print(f"[DEBUG] Processing components field with value: '{value}'")
                    components = str(value).split(";")
                    print(f"[DEBUG] Split components: {components}")
                    for component in components:
                        if (
                            component and component.strip()
                        ):  # Check for non-empty after stripping whitespace
                            component = component.strip()
                            print(f"[DEBUG] Validating component: '{component}' using validation_key_field: '{validation_key_field}'")

                            # Validate component based on the validation_key_field setting
                            if validation_key_field == "item_id":
                                # For item_id lookup, validate as integer (handle M-prefix)
                                component_id_str = component.replace('M', '') if component.startswith('M') else component
                                if not is_valid_int(component_id_str):
                                    print(f"[DEBUG] Component '{component}' (cleaned: '{component_id_str}') is not a valid integer for item_id lookup")
                                    if user.verification.language == "ja":
                                        errors.append(
                                            f"無効な構成レコード ID: {component} - 数値である必要があります。"
                                        )
                                    else:
                                        errors.append(
                                            f"Invalid component record ID: {component} - must be a valid integer."
                                        )
                                    continue
                                else:
                                    # Convert to integer for database lookup
                                    try:
                                        component_id = int(float(component_id_str))
                                        print(f"[DEBUG] Converted component '{component}' to ID: {component_id}")
                                    except (ValueError, TypeError):
                                        print(f"[DEBUG] Failed to convert component '{component}' to integer")
                                        if user.verification.language == "ja":
                                            errors.append(
                                                f"無効な構成レコード ID: {component} - 数値である必要があります。"
                                            )
                                        else:
                                            errors.append(
                                                f"Invalid component record ID: {component} - must be a valid integer."
                                            )
                                        continue

                                    # Check if component exists
                                    if not ShopTurboItems.objects.filter(
                                        item_id=component_id, workspace=workspace
                                    ).exists():
                                        print(f"[DEBUG] Component record {component_id} not found in workspace")
                                        if user.verification.language == "ja":
                                            errors.append(
                                                f"構成レコード {component_id} が見つかりません。"
                                            )
                                        else:
                                            errors.append(
                                                f"Component record {component_id} not found."
                                            )
                                    else:
                                        print(f"[DEBUG] Component record {component_id} found successfully")

                            elif validation_key_field and is_valid_uuid(validation_key_field):
                                # For custom field lookup, validate that the component exists
                                print(f"[DEBUG] Validating component '{component}' using custom field {validation_key_field}")
                                custom_prop_val = ShopTurboItemsValueCustomField.objects.filter(
                                    field_name__id=validation_key_field,
                                    field_name__workspace=workspace,
                                    value__iexact=component,
                                    items__isnull=False,
                                ).first()

                                if not custom_prop_val:
                                    print(f"[DEBUG] Component '{component}' not found with custom field {validation_key_field}")
                                    if user.verification.language == "ja":
                                        errors.append(
                                            f"構成レコード {component} が見つかりません。"
                                        )
                                    else:
                                        errors.append(
                                            f"Component record {component} not found."
                                        )
                                else:
                                    print(f"[DEBUG] Component '{component}' found via custom field: {custom_prop_val.items.name}")

                            else:
                                print(f"[DEBUG] Invalid validation_key_field: '{validation_key_field}', skipping validation for component '{component}'")

            except ShopTurboItemsNameCustomField.DoesNotExist:
                print(f"[DEBUG] Custom field with ID '{field_id}' not found")
                pass

    print(f"[DEBUG] validate_components completed with {len(errors)} errors: {errors}")
    return errors


def write_item(workspace: Workspace, user, data_dictionary={}, task_history_id=None):
    item = None
    errors = []

    # Validate data dictionary
    validation_errors = validate_data_dictionary(data_dictionary, user, workspace)
    if validation_errors:
        return None, validation_errors

    # Validate suppliers
    supplier_errors = validate_suppliers(data_dictionary, user, workspace)
    if supplier_errors:
        return None, supplier_errors

    # Validate components
    component_errors = validate_components(data_dictionary, user, workspace)
    if component_errors:
        return None, component_errors
    try:
        transfer_history = None
        if task_history_id:
            try:
                transfer_history = TransferHistory.objects.get(id=task_history_id)
            except:
                pass
        errors = []

        workspace_currency = ast.literal_eval(workspace.currencies)[0].upper()

        # Process Special Variables
        if "currency" in data_dictionary["default_property"]:
            if data_dictionary["default_property"]["currency"]:
                data_dictionary["default_property"]["currency"] = str(
                    data_dictionary["default_property"]["currency"]
                ).upper()
                data_dictionary["default_property"]["currency"] = (
                    currency_splitter_by_delimiter(
                        data_dictionary["default_property"]["currency"]
                    )
                )
            else:
                data_dictionary["default_property"]["currency"] = ast.literal_eval(
                    workspace.currencies
                )[0].upper()
        else:
            data_dictionary["default_property"]["currency"] = ast.literal_eval(
                workspace.currencies
            )[0].upper()

        if "purchase_price_currency" in data_dictionary["default_property"]:
            if data_dictionary["default_property"]["purchase_price_currency"]:
                purchase_price_currency = str(
                    data_dictionary["default_property"]["purchase_price_currency"]
                ).upper()
                purchase_price_currency = currency_splitter_by_delimiter(
                    purchase_price_currency
                )
            data_dictionary["default_property"].pop("purchase_price_currency")
        else:
            purchase_price_currency = ast.literal_eval(workspace.currencies)[0].upper()

        if "price_of_items" in data_dictionary["default_property"]:
            data_dictionary["default_property"]["price_of_items"] = str(
                data_dictionary["default_property"]["price_of_items"]
            ).replace(",", "")
        if "tax_price_of_items" in data_dictionary["default_property"]:
            data_dictionary["default_property"]["tax_price_of_items"] = str(
                data_dictionary["default_property"]["tax_price_of_items"]
            ).replace("%", "")
        if "purchase_price_of_items" in data_dictionary["default_property"]:
            data_dictionary["default_property"]["purchase_price_of_items"] = str(
                data_dictionary["default_property"]["purchase_price_of_items"]
            ).replace(",", "")
        if "tax_purchase_price_of_items" in data_dictionary["default_property"]:
            data_dictionary["default_property"]["tax_purchase_price_of_items"] = str(
                data_dictionary["default_property"]["tax_purchase_price_of_items"]
            ).replace("%", "")

        # Process supplier_contact and supplier_company
        supplier_contact = None
        supplier_company = None
        has_supplier_fields = False
        
        
        # A. Contact (supplier_contact)
        if "supplier_contact" in data_dictionary["default_property"]:
            has_supplier_fields = True
            contact = None
            supplier_value = data_dictionary["default_property"]["supplier_contact"]
            
            if supplier_value:
                if is_valid_int(supplier_value):
                    contact = Contact.objects.filter(
                        workspace=workspace, contact_id=supplier_value
                    ).first()
                    if contact:
                        supplier_contact = contact
            data_dictionary["default_property"].pop("supplier_contact")
        
        # B. Company (supplier_company)
        if "supplier_company" in data_dictionary["default_property"]:
            has_supplier_fields = True
            company = None
            supplier_value = data_dictionary["default_property"]["supplier_company"]
            
            if supplier_value:
                if is_valid_int(supplier_value):
                    company = Company.objects.filter(
                        workspace=workspace, company_id=supplier_value
                    ).first()
                    if company:
                        supplier_company = company
            data_dictionary["default_property"].pop("supplier_company")

        # Process Data Dictonary for foregn key Objects
        # A. item
        insert_items_price = []
        insert_items_tax = []
        if "price_of_items" in data_dictionary["default_property"]:
            insert_items_price = data_dictionary["default_property"].pop(
                "price_of_items"
            )
            insert_items_price = str(insert_items_price).split(CSV_DELIMITER_LIST_FIELD)
        if "tax_price_of_items" in data_dictionary["default_property"]:
            insert_items_tax = data_dictionary["default_property"].pop(
                "tax_price_of_items"
            )
            insert_items_tax = str(insert_items_tax).split(CSV_DELIMITER_LIST_FIELD)

        insert_items_purchase_price = []
        insert_items_purchase_price_tax = []
        if "purchase_price_of_items" in data_dictionary["default_property"]:
            insert_items_purchase_price = data_dictionary["default_property"].pop(
                "purchase_price_of_items"
            )
            insert_items_purchase_price = str(insert_items_purchase_price).split(
                CSV_DELIMITER_LIST_FIELD
            )
        if "tax_purchase_price_of_items" in data_dictionary["default_property"]:
            insert_items_purchase_price_tax = data_dictionary["default_property"].pop(
                "tax_purchase_price_of_items"
            )
            insert_items_purchase_price_tax = str(
                insert_items_purchase_price_tax
            ).split(CSV_DELIMITER_LIST_FIELD)

        # Process Base Objects
        item = None
        if data_dictionary["how_to_import"] == "update":
            for k, v in data_dictionary["key_property"].items():
                item = None
                if is_valid_uuid(k):
                    item_custom_field = ShopTurboItemsNameCustomField.objects.get(
                        id=k, workspace=workspace
                    )
                    if item_custom_field.type == "formula":
                        items_ = ShopTurboItems.objects.filter(workspace=workspace)
                        page_obj = get_page_object(
                            TYPE_OBJECT_ITEM, lang=user.verification.language
                        )
                        for item_ in items_:
                            if (
                                formula.calculate_math(
                                    page_obj, item_, item_custom_field
                                )
                                == v
                            ):
                                item = item_
                                break
                    item_custom_prop_val = (
                        ShopTurboItemsValueCustomField.objects.filter(
                            field_name__id=k,
                            value__iexact=v,
                            field_name__workspace=workspace,
                            items__status="active",
                        ).first()
                    )
                    if item_custom_prop_val:
                        item = item_custom_prop_val.items
                else:
                    item = ShopTurboItems.objects.filter(
                        **data_dictionary["key_property"],
                        workspace=workspace,
                        status="active",
                    ).first()
                break
            if item and transfer_history:
                imported_ids = transfer_history.checkpoint_details.get("imported", [])
                imported_ids.append(item.item_id)
                transfer_history.checkpoint_details["imported"] = imported_ids
                transfer_history.save()

        elif data_dictionary["how_to_import"] == "create":
            try:
                # Store item_id if it exists, otherwise we'll generate one later
                provided_item_id = None
                if "item_id" in data_dictionary["default_property"]:
                    provided_item_id = data_dictionary["default_property"].pop(
                        "item_id"
                    )

                # Check if we already processed this item
                if (
                    transfer_history
                    and transfer_history.checkpoint_details
                    and transfer_history.checkpoint_details.get("object_id")
                ):
                    # This process is already processed before.
                    try:
                        item = ShopTurboItems.objects.get(
                            id=transfer_history.checkpoint_details["object_id"],
                            workspace=workspace,
                        )
                    except ShopTurboItems.DoesNotExist:
                        # Create a new item with safe ID assignment
                        from utils.item_id_manager import assign_item_id_safe

                        item = ShopTurboItems(
                            **data_dictionary["default_property"], workspace=workspace
                        )
                        if provided_item_id:
                            item.item_id = provided_item_id
                        elif not item.item_id:
                            assign_item_id_safe(item, workspace)
                        item.save()
                else:
                    # Create a new item with safe ID assignment
                    from utils.item_id_manager import assign_item_id_safe

                    item = ShopTurboItems(
                        **data_dictionary["default_property"], workspace=workspace
                    )
                    if provided_item_id:
                        item.item_id = provided_item_id
                    elif not item.item_id:
                        assign_item_id_safe(item, workspace)
                    item.save()
                if item and transfer_history:
                    imported_ids = transfer_history.checkpoint_details.get(
                        "imported", []
                    )
                    imported_ids.append(item.item_id)
                    transfer_history.checkpoint_details["imported"] = imported_ids
                    transfer_history.save()

            except Exception as e:
                traceback.print_exc()
                if user.verification.language == "ja":
                    errors.append(f"商品の作成に失敗しました: {str(e)}")
                else:
                    errors.append(f"Failed to create item: {str(e)}")
                return None, errors

        elif data_dictionary["how_to_import"] == "create_and_update":
            try:
                # custom property
                item = None
                print(f"[DEBUG] Looking for existing item using key_field: {data_dictionary['key_field']}")
                print(f"[DEBUG] Key property value: {data_dictionary.get('key_property', {}).get(data_dictionary['key_field'], 'N/A')}")

                if is_valid_uuid(data_dictionary["key_field"]):
                    item_property = ShopTurboItemsNameCustomField.objects.get(
                        id=data_dictionary["key_field"], workspace=workspace
                    )
                    print(f"[DEBUG] Using custom field for lookup: {item_property.name} (type: {item_property.type})")
                    if item_property.type == "formula":
                        items_ = ShopTurboItems.objects.filter(workspace=workspace)
                        page_obj = get_page_object(
                            TYPE_OBJECT_ITEM, lang=user.verification.language
                        )
                        for item_ in items_:
                            if (
                                formula.calculate_math(page_obj, item_, item_property)
                                == data_dictionary["key_property"][
                                    data_dictionary["key_field"]
                                ]
                            ):
                                item = item_
                                break
                    else:
                        CustomFieldValue = (
                            ShopTurboItemsValueCustomField.objects.filter(
                                items__status="active",
                                field_name=item_property,
                                value=data_dictionary["key_property"][
                                    data_dictionary["key_field"]
                                ],
                            )
                            .order_by("-created_at")
                            .first()
                        )
                        if CustomFieldValue:
                            item = CustomFieldValue.items
                            print(f"[DEBUG] Found existing item via custom field: {item.name} (ID: {item.item_id})")
                        else:
                            print(f"[DEBUG] No existing item found with custom field value: {data_dictionary['key_property'][data_dictionary['key_field']]}")

                if not item:
                    print(f"[DEBUG] Creating new item with key_property: {data_dictionary['key_property']}")
                    if not data_dictionary["key_property"]:
                        raise Exception
                    item, created = ShopTurboItems.objects.get_or_create(
                        **data_dictionary["key_property"], workspace=workspace
                    )
                    print(f"[DEBUG] Item {'created' if created else 'found'}: {item.name} (ID: {item.item_id})")
                else:
                    print(f"[DEBUG] Using existing item for component processing: {item.name} (ID: {item.item_id})")
                    if created and not insert_items_price:
                        item_price, _ = ShopTurboItemsPrice.objects.get_or_create(
                            item=item, price=0, currency=workspace_currency
                        )
                        item_price.default = True
                        item_price.save()

                        item.price = float(item_price.price)
                        item.currency = item_price.currency
                        item.save(
                            log_data={"user": user, "workspace": workspace}
                        )  # Log
            except ShopTurboItems.MultipleObjectsReturned:
                if not data_dictionary["key_property"]:
                    raise Exception
                item = ShopTurboItems.objects.filter(
                    **data_dictionary["key_property"], workspace=workspace
                ).first()
            except Exception:
                if "item_id" in data_dictionary["default_property"]:
                    data_dictionary["default_property"].pop("item_id")
                item = ShopTurboItems.objects.create(
                    **data_dictionary["default_property"], workspace=workspace
                )
                if not insert_items_price:
                    item_price, _ = ShopTurboItemsPrice.objects.get_or_create(
                        item=item, price=0, currency=workspace_currency
                    )
                    item_price.default = True
                    item_price.save()

                    item.price = float(item_price.price)
                    item.currency = item_price.currency
                    item.save(log_data={"user": user, "workspace": workspace})  # Log
            if item and transfer_history:
                imported_ids = transfer_history.checkpoint_details.get("imported", [])
                imported_ids.append(item.item_id)
                transfer_history.checkpoint_details["imported"] = imported_ids
                transfer_history.save()

        if not item:
            if data_dictionary["how_to_import"] == "create":
                # For create mode, we should have created an item by now
                # If we reach here with no item, it's likely due to an exception that wasn't caught
                if user.verification.language == "ja":
                    errors.append(
                        "商品の作成に失敗しました。詳細はログを確認してください。"
                    )
                else:
                    errors.append("Failed to create item. Check logs for details.")
            else:
                # For update or create_and_update modes
                if user.verification.language == "ja":
                    errors.append("商品が存在しません。")
                else:
                    errors.append("Item does not exist.")
            return item, errors

        # LOGGING
        if data_dictionary["how_to_import"] == "create":
            # Create a default price for the item if none was provided
            if not insert_items_price:
                item_price, _ = ShopTurboItemsPrice.objects.get_or_create(
                    item=item, price=0, currency=workspace_currency
                )
                item_price.default = True
                item_price.save(log_data={"user": user, "workspace": workspace})

                item.price = float(item_price.price)
                item.currency = item_price.currency
                item.save()  # Log
            item.save(
                log_data={"user": user, "status": "create", "workspace": workspace}
            )  # Log

        if transfer_history:
            if not transfer_history.checkpoint_details:
                transfer_history.checkpoint_details = {}
            transfer_history.checkpoint_details["object_id"] = str(item.id)
            transfer_history.save()

        # ======= Process Adding base Value
        for default_property_key in data_dictionary["default_property"]:
            setattr(
                item,
                default_property_key,
                data_dictionary["default_property"][default_property_key],
            )
        setattr(item, "status", "active")

        # Process Foregn Key objects
        # TODO: Process Foregn Key Item
        currency = None
        for idx, item_price in enumerate(insert_items_price):
            try:
                if isinstance(data_dictionary["default_property"]["currency"], list):
                    currency = data_dictionary["default_property"]["currency"][idx]
                else:
                    currency = data_dictionary["default_property"]["currency"]
            except:
                pass

            # Set order to item mode
            item_price_model = ShopTurboItemsPrice.objects.filter(
                item=item, price=item_price, currency=currency
            ).first()
            if not item_price_model:
                item_price_obj, _ = ShopTurboItemsPrice.objects.get_or_create(
                    item=item, price=item_price, currency=currency
                )
                item_price_obj.save(
                    log_data={"user": user, "status": "create", "workspace": workspace}
                )

            else:
                item_price_obj = item_price_model

            if insert_items_tax:
                if insert_items_tax[idx]:
                    item_price_obj.tax = insert_items_tax[idx]
                    item.tax = item_price_obj.tax

            ShopTurboItemsPrice.objects.filter(item=item).update(default=False)
            item_price_obj.default = True
            item_price_obj.save(log_data={"user": user, "workspace": workspace})

            item.price = float(item_price_obj.price)
            item.currency = currency
            item.save(log_data={"user": user, "workspace": workspace})  # Log

        currency = None
        for idx, item_purchase_price_ in enumerate(insert_items_purchase_price):
            try:
                if isinstance(purchase_price_currency, list):
                    currency = (
                        purchase_price_currency[idx]
                        if purchase_price_currency
                        else currency
                    )
                else:
                    currency = (
                        purchase_price_currency if purchase_price_currency else currency
                    )
            except:
                pass

            # Ensure currency is a string before calling upper()
            if currency is not None:
                currency = str(currency).upper()
            else:
                currency = workspace_currency

            if item_purchase_price_:
                item_purchase_price_ = float(item_purchase_price_)
            else:
                item_purchase_price_ = 0

            item_purchase_price = ItemPurchasePrice.objects.filter(
                item=item, price=item_purchase_price_, currency=currency
            ).first()
            if not item_purchase_price:
                item_purchase_price, _ = ItemPurchasePrice.objects.get_or_create(
                    item=item, price=item_purchase_price_, currency=currency
                )
                item_purchase_price.save(
                    log_data={"user": user, "status": "create", "workspace": workspace}
                )

            if insert_items_purchase_price_tax and insert_items_purchase_price_tax[idx]:
                item_purchase_price.tax = float(insert_items_purchase_price_tax[idx])

            ItemPurchasePrice.objects.filter(item=item).update(default=False)
            item_purchase_price.default = True
            item_purchase_price.save(log_data={"user": user, "workspace": workspace})

            item.purchase_price = item_purchase_price.price

        component_ids = []

        # First pass: collect component IDs from component fields
        component_quantities = []
        for custom_property in data_dictionary["custom_property"]:
            custom_property_ = custom_property
            is_quantity_field = "|amount" in custom_property_
            if is_quantity_field:
                custom_property_ = custom_property_.replace("|amount", "")

            try:
                item_property = ShopTurboItemsNameCustomField.objects.get(
                    id=custom_property_, workspace=workspace
                )
                if item_property.type == "components":
                    if is_quantity_field:
                        # This is the quantity field
                        quantity_data = str(data_dictionary["custom_property"][custom_property]).split(";")
                        component_quantities.extend([q.strip() for q in quantity_data if q.strip()])
                        print(f"[DEBUG] Found component quantities from field {item_property.name}: {component_quantities}")
                    else:
                        # This is the component ID field
                        component_data = str(data_dictionary["custom_property"][custom_property]).split(";")
                        component_ids.extend([comp.strip() for comp in component_data if comp.strip()])
                        print(f"[DEBUG] Found component IDs from field {item_property.name}: {component_ids}")
            except ShopTurboItemsNameCustomField.DoesNotExist:
                continue

        print(f"[DEBUG] Total component IDs collected: {component_ids}")
        print(f"[DEBUG] Total component quantities collected: {component_quantities}")

        # Process Custom Property
        for custom_property in data_dictionary["custom_property"]:
            is_component_qty = False
            custom_property_ = custom_property
            if "|amount" in custom_property_:
                is_component_qty = True
                custom_property_ = custom_property_.replace("|amount", "")
            item_property = ShopTurboItemsNameCustomField.objects.get(
                id=custom_property_, workspace=workspace
            )

            CustomFieldValue, _ = ShopTurboItemsValueCustomField.objects.get_or_create(
                field_name=item_property, items=item
            )
            data = data_dictionary["custom_property"][custom_property]
            if item_property.type == "choice":
                if item_property.multiple_select:
                    data = ";".join(filter(None, data))
                    val = []
                    for d in data:
                        val.append(d.strip())
                else:
                    val = data.strip()
                CustomFieldValue.value = val
            elif item_property.type == "components":
                data = str(data).split(";")
                custom_key_field = data_dictionary["custom_key_field"]
                component_key_field = data_dictionary.get("component_key_field", custom_key_field)
                print(f"[DEBUG] Processing components field: {item_property.name}")
                print(f"[DEBUG] Component data: {data}")
                print(f"[DEBUG] Custom key field: {custom_key_field}")
                print(f"[DEBUG] Component key field: {component_key_field}")
                print(f"[DEBUG] Is component qty: {is_component_qty}")
                print(f"[DEBUG] Component IDs available: {component_ids}")

                # Handle component field (not quantity)
                if not is_component_qty:
                    print(f"[DEBUG] Processing component field data: {data}")
                    print(f"[DEBUG] Available component quantities: {component_quantities}")

                    for idx, item_component_id in enumerate(data):
                        if item_component_id == "" or not item_component_id.strip():
                            continue

                        item_component_id = item_component_id.strip()
                        print(f"[DEBUG] Processing component ID: '{item_component_id}' using component_key_field: '{component_key_field}'")

                        # Lookup component based on the component_key_field setting
                        if component_key_field == "item_id":
                            # Use item_id for lookup
                            if item_component_id.replace('M', '').isdigit():
                                # Handle cases like "M547094" -> 547094
                                component_id_str = item_component_id.replace('M', '')
                                try:
                                    component_id = int(component_id_str)
                                    item_component = ShopTurboItems.objects.filter(
                                        item_id=component_id, workspace=workspace
                                    ).first()
                                    print(f"[DEBUG] Looking for component with item_id {component_id}: {'Found' if item_component else 'Not found'}")
                                except (ValueError, TypeError):
                                    print(f"[WARNING] Could not convert '{component_id_str}' to integer")
                                    continue
                            else:
                                # Try direct integer conversion
                                try:
                                    component_id = int(float(item_component_id))
                                    item_component = ShopTurboItems.objects.filter(
                                        item_id=component_id, workspace=workspace
                                    ).first()
                                    print(f"[DEBUG] Looking for component with item_id {component_id}: {'Found' if item_component else 'Not found'}")
                                except (ValueError, TypeError):
                                    print(f"[WARNING] Could not convert '{item_component_id}' to integer for item_id lookup")
                                    continue
                        elif component_key_field and is_valid_uuid(component_key_field):
                            # Use custom property for lookup
                            print(f"[DEBUG] Looking up component using custom field {component_key_field} = '{item_component_id}'")
                            custom_prop_val = (
                                ShopTurboItemsValueCustomField.objects.filter(
                                    field_name__id=component_key_field,
                                    field_name__workspace=workspace,
                                    value__iexact=item_component_id,
                                    items__isnull=False,
                                ).first()
                            )
                            if not custom_prop_val:
                                print(f"[WARNING] No item found with component key '{component_key_field}' = '{item_component_id}'")
                                continue
                            item_component = custom_prop_val.items
                            print(f"[DEBUG] Found component via custom field: {item_component.name if item_component else 'None'}")
                        else:
                            print(f"[WARNING] Invalid or missing component_key_field: '{component_key_field}'. Cannot lookup component '{item_component_id}'")
                            continue

                        if not item_component:
                            print(f"[WARNING] Component with ID '{item_component_id}' not found in workspace")
                            continue

                        print(f"[DEBUG] Creating component relationship: {item.name} -> {item_component.name}")
                        item_components, created = (
                            ShopTurboItemComponents.objects.get_or_create(
                                property=CustomFieldValue,
                                item=item,
                                item_component=item_component,
                            )
                        )

                        # Set quantity if available
                        if idx < len(component_quantities):
                            try:
                                quantity = float(component_quantities[idx])
                                item_components.quantity = quantity
                                item_components.save()
                                print(f"[DEBUG] Set component quantity to {quantity}")
                            except (ValueError, TypeError):
                                print(f"[DEBUG] Could not set quantity from '{component_quantities[idx] if idx < len(component_quantities) else 'N/A'}'")

                        print(f"[DEBUG] Component relationship {'created' if created else 'updated'}: {item_components.id} (quantity: {item_components.quantity})")

                # Handle quantity field for components
                elif is_component_qty and component_ids:
                    print(f"[DEBUG] Processing component quantities: {data}")
                    # Find existing component relationships and update their quantities
                    existing_components = ShopTurboItemComponents.objects.filter(
                        property__field_name=item_property,
                        item=item
                    )
                    print(f"[DEBUG] Found {existing_components.count()} existing component relationships")

                    for idx, quantity_str in enumerate(data):
                        if idx < len(component_ids):
                            component_id_str = component_ids[idx]
                            try:
                                quantity = float(quantity_str) if quantity_str else 0
                                print(f"[DEBUG] Setting quantity {quantity} for component {component_id_str}")

                                # Find the component relationship and update quantity
                                for comp_rel in existing_components:
                                    if str(comp_rel.item_component.item_id) == component_id_str.replace('M', ''):
                                        comp_rel.quantity = quantity
                                        comp_rel.save()
                                        print(f"[DEBUG] Updated quantity for component {comp_rel.item_component.name}: {quantity}")
                                        break
                            except (ValueError, TypeError):
                                print(f"[WARNING] Invalid quantity value: '{quantity_str}'")
                                continue
                else:
                    component_ids = data.copy()
                    if data:
                        for idx, item_component_id in enumerate(data):
                            if item_component_id == "" or not item_component_id.strip():
                                continue

                            item_component_id = item_component_id.strip()
                            print(f"[DEBUG] Fallback processing component ID: '{item_component_id}' using component_key_field: '{component_key_field}'")

                            if component_key_field == "item_id":
                                # Handle M-prefixed item IDs and direct integers
                                if item_component_id.replace('M', '').isdigit():
                                    component_id_str = item_component_id.replace('M', '')
                                    try:
                                        component_id = int(component_id_str)
                                        item_component = ShopTurboItems.objects.filter(
                                            item_id=component_id, workspace=workspace
                                        ).first()
                                        print(f"[DEBUG] Fallback lookup component with item_id {component_id}: {'Found' if item_component else 'Not found'}")
                                    except (ValueError, TypeError):
                                        print(f"[WARNING] Could not convert '{component_id_str}' to integer")
                                        continue
                                else:
                                    # Try direct conversion
                                    try:
                                        component_id = int(float(item_component_id))
                                        item_component = ShopTurboItems.objects.filter(
                                            item_id=component_id, workspace=workspace
                                        ).first()
                                        print(f"[DEBUG] Fallback lookup component with item_id {component_id}: {'Found' if item_component else 'Not found'}")
                                    except (ValueError, TypeError):
                                        print(f"[WARNING] Could not convert '{item_component_id}' to integer for item_id lookup")
                                        continue
                            elif component_key_field and is_valid_uuid(component_key_field):
                                # Use custom property for lookup
                                print(f"[DEBUG] Fallback lookup component using custom field {component_key_field} = '{item_component_id}'")
                                custom_prop_val = (
                                    ShopTurboItemsValueCustomField.objects.filter(
                                        field_name__id=component_key_field,
                                        field_name__workspace=workspace,
                                        value__iexact=item_component_id,
                                        items__isnull=False,
                                    ).first()
                                )
                                if not custom_prop_val:
                                    print(f"[WARNING] No item found with component key '{component_key_field}' = '{item_component_id}'")
                                    continue
                                item_component = custom_prop_val.items
                                print(f"[DEBUG] Found component via custom field: {item_component.name if item_component else 'None'}")
                            else:
                                print(f"[WARNING] Invalid or missing component_key_field: '{component_key_field}'. Cannot lookup component '{item_component_id}'")
                                continue
                            if not item_component:
                                print(
                                    f"[WARNING] Component with ID '{item_component_id}' not found in workspace"
                                )
                                continue
                            item_components, _ = (
                                ShopTurboItemComponents.objects.get_or_create(
                                    property=CustomFieldValue,
                                    item=item,
                                    item_component=item_component,
                                )
                            )
                            # Default quantity will be set by quantity field processing
                            print(f"[DEBUG] Created component relationship: {item_components.id}")

                    # Store the component field value
                    CustomFieldValue.value = ";".join(data) if isinstance(data, list) else str(data)

                    # Log summary of component relationships created
                    final_components = ShopTurboItemComponents.objects.filter(
                        property=CustomFieldValue, item=item
                    )
                    print(f"[DEBUG] Final component relationships for item {item.name}: {final_components.count()}")
                    for comp in final_components:
                        print(f"[DEBUG]   - Component: {comp.item_component.name} (ID: {comp.item_component.item_id}), Quantity: {comp.quantity}")
            elif item_property.type in ["date", "date_time"]:
                try:
                    parsed_time = parse_date(data, workspace.timezone)
                except:
                    parsed_time = None
                if isinstance(parsed_time, datetime):
                    CustomFieldValue.value_time = parsed_time
                    CustomFieldValue.value = str(parsed_time)
            elif item_property.type == "number":
                if "," in data:
                    data = data.replace(",", "")
                CustomFieldValue.value = data
            elif item_property.type == "warehouse_objects":
                location = InventoryWarehouse.objects.filter(
                    workspace=workspace, id_iw=data
                ).first()
                if location:
                    CustomFieldValue.value = str(location.id)
            elif item_property.type == "text":
                CustomFieldValue.value = data
            elif item_property.type == "company":
                if is_valid_int(data):
                    company = Company.objects.filter(
                        workspace=workspace, company_id=data
                    ).first()
                    if company:
                        CustomFieldValue.value = str(company.id)
                    else:
                        CustomFieldValue.value = None
                else:
                    CustomFieldValue.value = None
            elif item_property.type == "contact":
                if is_valid_uuid(data):
                    contact = Contact.objects.filter(
                        workspace=workspace, id=data
                    ).first()
                    if contact:
                        CustomFieldValue.value = str(contact.id)
                    else:
                        CustomFieldValue.value = None
                else:
                    CustomFieldValue.value = None
            else:
                CustomFieldValue.value = data

            if item_property.type != "components":
                CustomFieldValue.save(log_data={"user": user, "workspace": workspace})

        # Set supplier on item and create associations
        association_label = None
        try:
            association_label = AssociationLabel.objects.filter(
                workspace=workspace,
                object_source='commerce_items',
                object_target__icontains='company',
                label='supplier'
            ).first()

        except Exception as e:
            logger.warning(f"Could not find supplier association label: {e}")
        
        if supplier_contact:
            item.contact = supplier_contact
            item.company = None
            
            # Create association for contact
            if association_label:
                try:
                    AssociationLabelObject.create_association(
                        item,
                        supplier_contact,
                        workspace,
                        association_label,
                    )

                except Exception as e:
                    logger.error(f"Error creating contact association: {e}")
                    
        elif supplier_company:
            item.company = supplier_company
            item.contact = None
            
            # Create association for company
            if association_label:
                try:
                    AssociationLabelObject.create_association(
                        item,
                        supplier_company,
                        workspace,
                        association_label,
                    )

                except Exception as e:
                    logger.error(f"Error creating company association: {e}")
                    
        elif has_supplier_fields:
            # If supplier fields were present but empty, clear the item's suppliers
            item.contact = None
            item.company = None
            
            # Reset associations if association label exists
            if association_label:
                try:
                    AssociationLabelObject.reset_associations_for_object(
                        item, workspace, association_label
                    )

                except Exception as e:
                    logger.warning(f"Error resetting associations: {e}")

        item.save(log_data={"user": user, "workspace": workspace})  # Log

        print("update succesfully item_id: ", item.item_id)

        return item, errors

    except Exception:
        if data_dictionary["how_to_import"] == "create":
            if item:
                item.delete()

        error_code = uuid.uuid4()
        error_msg = f"[ERROR] - Failed To Import Item from workspace {workspace.name} ({workspace.id}). Error Code {error_code}: {traceback.format_exc()}"
        logger.error(error_msg)
        DiscordNotification().send_message(error_msg)

        if user.verification.language == "ja":
            errors.append(
                f"システムがアイテムをインポートできませんでした。エラーコード {error_code}"
            )
        else:
            errors.append(f"System Failed to import item. Error code {error_code}")
    return None, errors
