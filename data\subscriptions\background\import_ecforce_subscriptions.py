import json
from typing import Optional
from datetime import timedelta
from pydantic import BaseModel, Field
import traceback

from data.models import (
    User, Workspace, Notification
)
from utils.logger import logger
from utils.bgjobs.hatchet_client import hatchet
from hatchet_sdk import Context
from utils.ecforce_bg_jobs.ecforce import import_ecforce_subscriptions
from utils.bgjobs.handler import set_bg_job_running, set_bg_job_completed, set_bg_job_failed
from asgiref.sync import sync_to_async
import ast
class ImportEcforceSubscriptionsPayload(BaseModel):
    user_id: str
    workspace_id: str
    channel_id: str
    history_id: str
    subscription_mapping: str
    language: str = "en"
    import_date: Optional[str] = None
    background_job_id: Optional[str] = None


@hatchet.task(name="ImportEcforceSubscriptions", input_validator=ImportEcforceSubscriptionsPayload, execution_timeout=timedelta(hours=12), schedule_timeout=timedelta(hours=1))
async def import_ecforce_subscriptions_task(input: ImportEcforceSubscriptionsPayload, context: Context):
    """
    Import subscriptions from EC-Force
    """
    logger.info(
        f"Starting EC-Force subscription import for workspace {input.workspace_id}")

    try:
        # Set background job status to running
        await sync_to_async(set_bg_job_running)(input.background_job_id or getattr(input, 'job_id', None))

        # Get the workspace and user objects
        workspace = await Workspace.objects.aget(id=input.workspace_id)
        user = await User.objects.aget(id=input.user_id)

        # Process mapping fields
        mapping_custom_fields = (
            input.subscription_mapping
            if input.subscription_mapping and input.subscription_mapping != "None"
            else {}
        )

        # Parse string mappings if needed
        if isinstance(mapping_custom_fields, str):
            mapping_custom_fields = ast.literal_eval(mapping_custom_fields)

        # Call the import function
        success = await import_ecforce_subscriptions(
            channel_id=input.channel_id,
            # Use processed field_mapping instead
            mapping_custom_fields=mapping_custom_fields,
            import_date=input.import_date,
            lang=input.language,
            history_id=input.history_id,
        )

        print(f"Import success: {success}")
        if success:
            logger.info(
                f"Successfully completed EC-Force subscription import for workspace {input.workspace_id}")

            # Set background job status to completed
            await sync_to_async(set_bg_job_completed)(input.background_job_id or getattr(input, 'job_id', None))

            # Create success notification
            if input.language == "ja":
                message = f"EC-Forceからのサブスクリプションインポートが完了しました。"
            else:
                message = f"EC-Force subscription import completed successfully."

            await Notification.objects.acreate(
                workspace=workspace,
                user=user,
                message=message,
                type="success"
            )

        else:
            logger.error(
                f"Failed to import subscriptions from EC-Force for workspace {input.workspace_id}")

            # Set background job status to failed
            await sync_to_async(set_bg_job_failed)(input.background_job_id or getattr(input, 'job_id', None))

            # Create error notification
            if input.language == "ja":
                message = f"EC-Forceからのサブスクリプションインポートに失敗しました。"
            else:
                message = f"EC-Force subscription import failed. Please check your settings and try again."

            await Notification.objects.acreate(
                workspace=workspace,
                user=user,
                message=message,
                type="error"
            )

        return {"success": success}

    except Exception as e:
        logger.error(f"Error in EC-Force subscription import task: {str(e)}")
        logger.error(f"\n{traceback.format_exc()}")

        # Set background job status to failed
        await sync_to_async(set_bg_job_failed)(input.background_job_id or getattr(input, 'job_id', None))

        try:
            workspace = await Workspace.objects.aget(id=input.workspace_id)
            user = await User.objects.aget(id=input.user_id)

            # Create error notification
            if input.language == "ja":
                message = f"EC-Forceからのサブスクリプションインポート中にエラーが発生しました: {str(e)}"
            else:
                message = f"An error occurred during EC-Force subscription import: {str(e)}"

            await Notification.objects.acreate(
                workspace=workspace,
                user=user,
                message=message,
                type="error"
            )
        except Exception as notification_error:
            logger.error(
                f"Failed to create error notification: {notification_error}")

        return {"success": False, "error": str(e)}