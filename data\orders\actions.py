import ast
import json
import traceback

from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.db.models import Q, Sum
from django.forms.models import model_to_dict
from django.http.response import HttpResponse
from django.shortcuts import redirect, render
from django.utils import timezone
from django_hosts.resolvers import reverse

from action.action import trigger_next_action
from data.constants.constant import EXCLUDE_SYNC_CHANNEL_NAME
from data.constants.properties_constant import (
    OBJECT_TYPE_TO_SLUG,
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_PURCHASE_ORDER,
    TYPE_OBJECT_WORKFLOW,
    TYPE_OBJECT_ITEM,
    TYPE_OBJECT_COMPANY,
    TYPE_OBJECT_CONTACT,
)
from data.models import (
    AssociationLabel,
    AssociationLabelObject,
    Action,
    ActionHistory,
    ActionNode,
    ActionTaskHistory,
    ActionTracker,
    Channel,
    Company,
    Contact,
    ContactsMappingFields,
    Module,
    Notification,
    OrderGroupPayment,
    PurchaseOrders,
    PurchaseOrdersItem,
    ShopTurboInventory,
    ShopTurboItemComponents,
    ShopTurboItems,
    ShopTurboItemsNameCustomField,
    ShopTurboItemsOrders,
    ShopTurboItemsValueCustomField,
    ShopTurboOrders,
    ShopTurboPurchaseItemsOrders,
    ShopTurboOrdersMappingFields,
    ShopTurboOrdersPlatforms,
    ShopTurboOrdersStatusMappingFields,
    TransferHistory,
    View,
    WorkflowActionTracker,
)
from data.shopturbo import mapping_advanced_filter_handler
from sanka.settings import PROD
from utils.actions import transfer_output_to_target_input
from utils.action_data_parsing import parse_record_ids
from utils.bgjobs.handler import add_hatchet_run_id, create_bg_job
from utils.decorator import login_or_hubspot_required
from utils.logger import logger
from utils.serializer import custom_serializer
from utils.utility import get_workspace
from data.orders.background.import_orders.models import (
    ImportOrdersShopifyPayload,
    ImportOrdersHubSpotPayload,
    ImportOrdersSquarePayload,
    ImportOrdersAmazonPayload,
    ImportOrdersEcforcePayload,
    ImportOrdersRakutenPayload,
    ImportOrdersEccubePayload,
    ImportOrdersMakeshopPayload,
    ImportOrdersYahooShoppingPayload,
    ImportOrdersBcartPayload,
    ImportOrdersWoocommercePayload,
    ImportOrdersSalesforcePayloadV2,
    ImportOrdersEbayPayload,
    ImportOrdersStripePayload,
    ImportOrdersNextEnginePayload,
)
from data.orders.background.import_orders.workflows import (
    import_shopify_orders,
    import_hubspot_orders,
    import_square_orders,
    import_amazon_orders,
    import_ecforce_orders,
    import_rakuten_orders,
    import_eccube_orders,
    import_makeshop_orders,
    import_yahoo_shopping_orders,
    import_bcart_orders,
    import_woocommerce_orders,
    import_salesforce_opportunities,
    import_ebay_orders,
    import_stripe_orders,
    import_nextengine_orders,
)


@login_or_hubspot_required
def create_stripe_order_payment_link(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    action_slug = "create-stripe-payment-link"

    if request.method == "GET":
        bulk_action = request.GET.get("bulk_action")
        is_record_action = request.GET.get("is_record_action")
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        if postfix:
            action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:  # noqa
                print("Action node does not exist")
                return HttpResponse(status=404)

        # Get active values from node input_data if they exist
        active_order_ids = []
        active_stripe_payment_integration = None
        active_stripe_link_field = None
        active_stripe_link_id_field = None
        as_quick_entry = False

        if node and node.input_data:
            if "order_ids" in node.input_data:
                active_order_ids = node.input_data["order_ids"]
            if "stripe_payment_integration" in node.input_data:
                active_stripe_payment_integration = node.input_data[
                    "stripe_payment_integration"
                ]
            if "stripe_link_field" in node.input_data:
                active_stripe_link_field = node.input_data["stripe_link_field"]
            if "stripe_link_id_field" in node.input_data:
                active_stripe_link_id_field = node.input_data["stripe_link_id_field"]
            if "as_quick_entry" in node.input_data:
                as_quick_entry = bool(node.input_data["as_quick_entry"])

        if is_record_action or bulk_action:
            active_order_ids = request.GET.get("order_ids")

            able_to_use_single_link = False
            if bulk_action:
                order_ids = ast.literal_eval(active_order_ids)
                orders = ShopTurboOrders.objects.filter(
                    id__in=order_ids, workspace=workspace
                )
                # Check if currency and company/contact object is the same
                first_order = orders.first()
                if not first_order:
                    return HttpResponse(
                        "No valid orders found for the provided IDs in this workspace.",
                        status=400,
                    )
                currency = first_order.currency
                company = first_order.company
                contact = first_order.contact
                for order in orders:
                    if (
                        order.currency != currency
                        or order.company != company
                        or order.contact != contact
                    ):
                        able_to_use_single_link = False
                        break
                    else:
                        able_to_use_single_link = True

            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_ORDER
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = module.slug
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_ORDER]

        # Get data for form dropdowns
        orders = []
        if not bulk_action and not is_record_action:
            orders = ShopTurboOrders.objects.filter(workspace=workspace)

        # Get Stripe integrations
        stripe_integrations = Channel.objects.filter(
            workspace=workspace, integration__slug="stripe", authenticated=True
        )

        context = {
            "bulk_action": bulk_action,
            "is_record_action": is_record_action,
            "action_index": action_index,
            "action_node_id": action_node_id,
            "action_slug": action_slug,
            "active_order_ids": active_order_ids,
            "active_stripe_payment_integration": active_stripe_payment_integration,
            "active_stripe_link_field": active_stripe_link_field,
            "active_stripe_link_id_field": active_stripe_link_id_field,
            "as_quick_entry": as_quick_entry,
            "able_to_use_single_link": able_to_use_single_link,
            "orders": orders,
            "stripe_integrations": stripe_integrations,
            "module": module,
            "module_object_slug": module_object_slug,
        }

        return render(
            request,
            "data/shopturbo/orders/actions/create-stripe-order-payment-link.html",
            context,
        )

    # POST
    print("Create stripe order payment link")
    print(request.POST)
    input_data = {}
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")
    history_id = request.POST.get("history_id")

    bulk_action = request.POST.get("bulk_action")
    is_record_action = request.POST.get("is_record_action")

    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)

    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)

    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index
    if postfix:
        action_slug = request.POST.get("action_slug")

    order_ids = request.POST.get("order_id" + postfix, [])

    create_single_link = False
    if bulk_action:
        order_ids = request.POST.get("order_id")
        if isinstance(order_ids, str):
            order_ids = ast.literal_eval(order_ids)
        create_single_link = request.POST.get("single_link", False)
    if isinstance(order_ids, str):
        order_ids = [order_ids]
    stripe_payment_integration = request.POST.get(
        "stripe_payment_integration" + postfix
    )
    as_quick_entry = bool(request.POST.get("as_quick_entry" + postfix))

    action_name = request.POST.get("action_name" + postfix)

    if submit_option == "save":
        node.valid_to_run = True
        input_data = {}

        if order_ids:
            input_data["order_ids"] = order_ids
        else:
            node.valid_to_run = False

        if stripe_payment_integration:
            input_data["stripe_payment_integration"] = stripe_payment_integration
        else:
            node.valid_to_run = False

        if action_name:
            input_data["action_name"] = action_name

        if as_quick_entry:
            input_data["as_quick_entry"] = as_quick_entry

        node.input_data = input_data
        node.predefined_input = input_data
        node.save()
        return HttpResponse()
    else:
        if node:
            order_ids = node.input_data.get("order_ids", [])
            stripe_payment_integration = node.input_data.get(
                "stripe_payment_integration", ""
            )
            as_quick_entry = node.input_data.get("as_quick_entry", False)
            if not order_ids:
                return HttpResponse(status=400)

        else:
            action = Action.objects.filter(slug=action_slug).first()
            history = None
            if history_id:
                try:
                    history = ActionHistory.objects.get(
                        id=history_id,
                        workspace=workspace,
                        action=action,
                    )
                except:  # noqa
                    pass
            if not history:
                history = ActionHistory.objects.create(
                    workspace=workspace,
                    action=action,
                    status="initialized",
                    created_by=request.user,
                )
            if is_record_action and order_ids:
                history.object_type = TYPE_OBJECT_ORDER
                history.object_id = order_ids[0]
                history.save()
            elif not order_ids:
                history.status = "failed"
                history.completed_at = timezone.now()
                history.save()
                return HttpResponse()

        output_data = {}

        order_group = None
        if create_single_link:
            print("Create Order Group")
            order_group = OrderGroupPayment.objects.create()
        for i, order_id in enumerate(order_ids):
            if bulk_action or is_record_action:
                task_action_history = ActionTaskHistory.objects.create(
                    workspace=history.workspace,
                    action_history=history,
                    status="running",
                    input_data={
                        "order_id": order_id,
                    },
                )

            try:
                order = ShopTurboOrders.objects.get(id=order_id, workspace=workspace)
            except ShopTurboOrders.DoesNotExist:
                print("Error creating Stripe payment link: Order not found")
                if bulk_action or is_record_action:
                    task_action_history.status = "failed"
                    task_action_history.error_message = "Failed to load order"
                    task_action_history.error_message_ja = (
                        "受注の読み込みに失敗しました"
                    )
                    task_action_history.save()
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Failed to create Stripe payment link: Failed to load order"
                        if lang == "en"
                        else "Stripe決済リンクの作成に失敗しました: 受注の読み込みに失敗しました",
                        type="error",
                    )
                continue

            try:
                if PROD:
                    if create_single_link:
                        payment_link = "https:" + reverse(
                            "generate_stripe_order_group_checkout_link",
                            kwargs={
                                "channel_id": stripe_payment_integration,
                                "order_group_id": str(order_group.id),
                            },
                        ).replace(settings.PARENT_HOST, "sanka.com")
                    else:
                        payment_link = "https:" + reverse(
                            "generate_stripe_order_checkout_link",
                            kwargs={
                                "channel_id": stripe_payment_integration,
                                "order_id": str(order.id),
                            },
                        ).replace(settings.PARENT_HOST, "sanka.com")
                else:
                    if create_single_link:
                        payment_link = "http:" + reverse(
                            "generate_stripe_order_group_checkout_link",
                            kwargs={
                                "channel_id": stripe_payment_integration,
                                "order_group_id": str(order_group.id),
                            },
                        )
                    else:
                        payment_link = "http:" + reverse(
                            "generate_stripe_order_checkout_link",
                            kwargs={
                                "channel_id": stripe_payment_integration,
                                "order_id": str(order.id),
                            },
                        )
                # Check if payment link already exists
                platform = ShopTurboOrdersPlatforms.objects.filter(
                    order=order, channel__id=stripe_payment_integration
                ).first()

                if not platform:
                    platform = ShopTurboOrdersPlatforms.objects.create(
                        order=order,
                        channel_id=stripe_payment_integration,
                        payment_link=payment_link,
                        as_quick_entry=as_quick_entry,
                    )
                else:
                    platform.payment_link = payment_link
                    platform.as_quick_entry = as_quick_entry
                    platform.save()

                if create_single_link:
                    order_group.orders.add(order)

                if bulk_action or is_record_action:
                    task_action_history.status = "success"
                    task_action_history.output_data = {
                        "order_id": str(order.order_id),
                        "stripe_payment_link": payment_link,
                    }
                    task_action_history.result_link = payment_link
                    task_action_history.completed_at = timezone.now()
                    task_action_history.save()
                else:
                    output_data[f"order_id_{i}"] = str(order.order_id)
                    output_data[f"stripe_payment_link_{i}"] = payment_link

            except Exception as e:
                print(f"Error creating Stripe payment link: {str(e)}")
                print(traceback.format_exc())
                if bulk_action or is_record_action:
                    history.completed_at = timezone.now()
                    history.status = "failed"
                    history.save()

                    task_action_history.status = "failed"
                    task_action_history.error_message = (
                        "Failed to create Stripe payment link"
                    )
                    task_action_history.error_message_ja = (
                        "Stripe決済リンクの作成に失敗しました"
                    )
                    task_action_history.completed_at = timezone.now()
                    task_action_history.save()

                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message=f"Failed to create Stripe payment link for order {order.order_id:04d}"
                        if lang == "en"
                        else f"注文 {order.order_id:04d} の Stripe 決済リンクを作成できませんでした",
                        type="error",
                    )

        if bulk_action or is_record_action:
            total_tasks_completed = (
                ActionTaskHistory.objects.filter(
                    workspace=history.workspace,
                    action_history=history,
                    status="success",
                )
                .values("id")
                .count()
            )
            if total_tasks_completed > 0:
                history.status = "success"
            else:
                history.status = "failed"
            history.completed_at = timezone.now()
            history.save()
            return HttpResponse()
        else:
            # Update action tracker
            at.status = "success"
            at.output_data = output_data
            at.completed_at = timezone.now()
            at.save()
            print(at.output_data)

            next_node = None
            if node:
                next_node = node.next_node
            if next_node:
                next_at = ActionTracker.objects.get(
                    node=next_node, workflow_action_tracker=wat
                )
                transfer_output_to_target_input(at, next_at)
                trigger_next_action(
                    current_action_tracker=at,
                    workflow_action_tracker=wat,
                    current_node=node,
                    user_id=str(request.user.id),
                    lang=lang,
                )

            # Redirect to workflow page
            module_slug = request.POST.get("module")
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
            if not module_slug:
                module = (
                    Module.objects.filter(
                        workspace=workspace,
                        object_values__contains=TYPE_OBJECT_WORKFLOW,
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                module_slug = module.slug
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
            )


@login_or_hubspot_required
def import_orders(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:  # noqa
                print("Action node does not exist")
                return HttpResponse(status=404)

        # Get active values from node input_data if they exist
        active_channel = None
        integration_slug = None

        if node and node.input_data:
            active_channel = node.input_data.get("channel", None)

        # Get all available channels including Stripe
        platforms = [
            "shopify",
            "amazon",
            "square",
            "ecforce",
            "nextengine",
            "rakuten",
            "freee",
            "ec-cube",
            "makeshop",
            "yahoo-shopping",
            "b-cart",
            "wordpress",
            "salesforce",
            "stripe",
        ]

        channels = Channel.objects.filter(
            Q(workspace=workspace, integration__slug__in=platforms)
            | Q(workspace=workspace, api_key=settings.HUBSPOT_CLIENT_ID)
        ).exclude(status="draft")

        for name in EXCLUDE_SYNC_CHANNEL_NAME:
            channels = channels.exclude(name__icontains=name)
        if active_channel:
            c = channels.filter(id=active_channel).first()
            if c:
                integration_slug = c.integration.slug

        context = {
            "action_index": action_index,
            "action_node_id": action_node_id,
            "action_slug": action_slug,
            "active_channel": active_channel,
            "integration_slug": integration_slug,
            "channels": channels,
        }

        return render(request, "data/orders/actions/import-orders-action.html", context)

    if request.method == "POST":
        print("Import orders action")
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
            )
            .order_by("order", "created_at")
            .first()
        )
        module_slug = None
        if module:
            module_slug = module.slug
            redirect_url = reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
        else:
            redirect_url = reverse("main", host="app")

        action_index = request.POST.get("action_index")
        action_node_id = request.POST.get("action_node_id")
        action_slug = request.POST.get("action_slug")
        at_id = request.POST.get("action_tracker_id")
        wat_id = request.POST.get("workflow_action_tracker_id")
        node = None

        if not (action_index, action_node_id, action_slug):
            print("Missing required parameters")
            return redirect(redirect_url)

        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
                if node.workflow:
                    workspace = node.workflow.workspace
                elif node.workflow_history:
                    workspace = node.workflow_history.workspace
            except ActionNode.DoesNotExist:
                print("ActionNode does not exist")
                return redirect(redirect_url)

        at = None
        if at_id:
            try:
                at = ActionTracker.objects.get(id=at_id)
                if at.workspace:
                    workspace = at.workspace
            except ActionTracker.DoesNotExist:
                print("ActionTracker does not exist")
                return redirect(redirect_url)
        wat = None
        if wat_id:
            try:
                wat = WorkflowActionTracker.objects.get(id=wat_id)
            except WorkflowActionTracker.DoesNotExist:
                return redirect(redirect_url)

        postfix = ""
        if action_index:
            postfix = "-" + action_index
        action_name = request.POST.get("action_name" + postfix)
        days_ago_filter = None

        try:
            active_channel_id = request.POST.get("select_integration_ids" + postfix)
            channel = None
            if active_channel_id:
                try:
                    channel = Channel.objects.get(
                        id=active_channel_id, workspace=workspace
                    )
                except Channel.DoesNotExist:
                    print("channel does not exist")
                    pass
            try:
                file_columns = request.POST.getlist("order-file-column", [])
            except:
                file_columns = request.POST.get("order-file-column", "[]")
                file_columns = ast.literal_eval(file_columns)
            try:
                sanka_properties = request.POST.getlist("order-sanka-properties", [])
            except:
                sanka_properties = request.POST.get("order-sanka-properties", "[]")
                sanka_properties = ast.literal_eval(sanka_properties)
            try:
                ignores = request.POST.getlist("order-ignore", [])
            except:
                ignores = request.POST.get("order-ignore", "[]")
                ignores = ast.literal_eval(ignores)

            try:
                file_columns_status = request.POST.getlist(
                    "order-status-file-column", []
                )
            except:
                file_columns_status = request.POST.get("order-status-file-column", "[]")
                file_columns_status = ast.literal_eval(file_columns_status)
            try:
                sanka_properties_status = request.POST.getlist(
                    "order-status-sanka-properties", []
                )
            except:
                sanka_properties_status = request.POST.get(
                    "order-status-sanka-properties", "[]"
                )
                sanka_properties_status = ast.literal_eval(sanka_properties_status)

            try:
                contact_file_columns = request.POST.getlist("file-column", [])
                contact_sanka_properties = request.POST.getlist("sanka-properties", [])
                contact_ignores = request.POST.getlist("ignore", [])

                item_file_columns = request.POST.getlist("item-file-column", [])
                item_sanka_properties = request.POST.getlist(
                    "sanka-item-properties", []
                )
                item_ignores = request.POST.getlist("item-ignore", [])
            except:
                # Fallback to get as string and parse
                contact_file_columns = ast.literal_eval(
                    request.POST.get("file-column", "[]")
                )
                contact_sanka_properties = ast.literal_eval(
                    request.POST.get("sanka-properties", "[]")
                )
                contact_ignores = ast.literal_eval(request.POST.get("ignore", "[]"))

                item_file_columns = ast.literal_eval(
                    request.POST.get("item-file-column", "[]")
                )
                item_sanka_properties = ast.literal_eval(
                    request.POST.get("sanka-item-properties", "[]")
                )
                item_ignores = ast.literal_eval(request.POST.get("item-ignore", "[]"))

            mapping_custom_fields = {}
            mapping_contact_custom_fields = {}
            mapping_item_custom_fields = {}
            mapping_status_custom_fields = {}

            order_mapping = {}
            contact_mapping = {}
            # contact_filter_mapping = {}
            item_mapping = {}
            order_mapping_status = {}
            for idx, file_column in enumerate(file_columns):
                # check ignore
                order_mapping[file_column] = sanka_properties[idx]
                if ignores[idx] == "True":
                    continue
                mapping_custom_fields[file_column] = sanka_properties[idx]

            for idx, file_column in enumerate(contact_file_columns):
                # check ignore
                contact_mapping[file_column] = contact_sanka_properties[idx]

                if contact_ignores[idx] == "True":
                    continue
                mapping_contact_custom_fields[file_column] = contact_sanka_properties[
                    idx
                ]

            for idx, file_column in enumerate(item_file_columns):
                # check ignore
                item_mapping[file_column] = item_sanka_properties[idx]
                if item_ignores[idx] == "True":
                    continue
                mapping_item_custom_fields[file_column] = item_sanka_properties[idx]

            for idx, file_column in enumerate(file_columns_status):
                order_mapping_status[file_column] = sanka_properties_status[idx]
                mapping_status_custom_fields[file_column] = sanka_properties_status[idx]

            # Stripe
            days_ago_filter = request.POST.get("days_ago" + postfix, 1)
            try:
                days_ago_filter = int(days_ago_filter)
            except (ValueError, TypeError):
                days_ago_filter = 1

        except Exception:
            traceback.print_exc()

        if "save_mapping" in request.POST:
            platform = request.POST.get("platform")
            diff_mapping_order = ["shopify"]
            if platform:
                if len(platform) > 1:
                    if platform[1] in diff_mapping_order:
                        platform = platform[1]
                    else:
                        platform = platform[0]
                else:
                    platform = platform[0]

            active_channel_id = request.POST.get("select_integration_ids" + postfix)
            channel = Channel.objects.filter(id=active_channel_id).first()

            # Old Mapping
            try:
                mapping = ShopTurboOrdersMappingFields.objects.filter(
                    workspace=workspace, platform=platform, channel__isnull=True
                ).first()
                print(f"[DEBUG] Old mapping found: {mapping}")
                if mapping:
                    mapping.channel = channel
                    mapping.save()
            except Exception as e:
                print(f"[ERROR] Failed to fetch old mapping: {e}")

            mapping, _ = ShopTurboOrdersMappingFields.objects.get_or_create(
                workspace=workspace, platform=platform, channel=channel
            )
            contact_workspace_mapping, _ = ContactsMappingFields.objects.get_or_create(
                workspace=workspace, platform=platform, channel=channel
            )
            mapping_status, _ = (
                ShopTurboOrdersStatusMappingFields.objects.get_or_create(
                    workspace=workspace, platform=platform
                )
            )

            data = {}
            for i, field in enumerate(order_mapping):
                field_data = {"value": order_mapping[field], "skip": ignores[i]}
                data[field] = field_data

            mapping.input_data = data
            mapping.save()

            contact_data = {}
            for i, field in enumerate(contact_mapping):
                field_data = {
                    "value": contact_mapping[field],
                    "skip": contact_ignores[i],
                }
                contact_data[field] = field_data
            contact_workspace_mapping.input_data = contact_data
            contact_workspace_mapping.save()

            mapping_status_data = {}
            for i, field in enumerate(order_mapping_status):
                field_data = {"value": order_mapping_status[field], "skip": False}
                mapping_status_data[field] = field_data
            mapping_status.input_data = mapping_status_data
            mapping_status.save()
        else:
            try:
                mapping_custom_fields = {}
                mapping_contact_custom_fields = {}
                mapping_status_custom_fields = {}

                for idx, file_column in enumerate(file_columns):
                    # check ignore
                    if ignores[idx] == "True":
                        continue
                    mapping_custom_fields[file_column] = sanka_properties[idx]

                for idx, file_column in enumerate(contact_file_columns):
                    # check ignore
                    contact_mapping[file_column] = contact_sanka_properties[idx]
                    if contact_ignores[idx] == "True":
                        continue
                    mapping_contact_custom_fields[file_column] = (
                        contact_sanka_properties[idx]
                    )

                for idx, file_column in enumerate(file_columns_status):
                    order_mapping_status[file_column] = sanka_properties_status[idx]
                    mapping_status_custom_fields[file_column] = sanka_properties_status[
                        idx
                    ]

            except Exception as e:
                traceback.print_exc()
                print(e)

        if request.POST.get("submit-option") == "save":
            node.valid_to_run = True
            if channel:
                if request.POST.get("set-contact-as-company"):
                    channel.ms_refresh_token = 1
                    channel.save()
                else:
                    channel.ms_refresh_token = None
                    channel.save()
            if not channel:
                node.valid_to_run = False

            hubspot_filter_type = request.POST.getlist("hubspot-filter-type", None)
            hubspot_filter_option = request.POST.getlist("hubspot-filter-option", None)
            hubspot_filter_value = request.POST.getlist("hubspot-filter-value", None)

            import_filter_type = request.POST.getlist("order-import-filter-type", None)
            import_filter_option = request.POST.getlist(
                "order-import-filter-option", None
            )
            import_filter_value = request.POST.getlist(
                "order-import-filter-value", None
            )

            hubspot_filter_option_status = request.POST.get(
                "hubspot-filter-option-status", None
            )
            hubspot_filter_value_status = request.POST.getlist(
                "hubspot-filter-value-status", None
            )
            hubspot_filter_import = {}

            if hubspot_filter_option_status and hubspot_filter_value_status:
                hubspot_filter_import["order_status"] = {
                    "key": hubspot_filter_option_status,
                    "value": hubspot_filter_value_status,
                }

            if hubspot_filter_type and hubspot_filter_option and hubspot_filter_value:
                try:
                    for idx, filter_type in enumerate(hubspot_filter_type):
                        if (
                            filter_type
                            and hubspot_filter_option[idx]
                            and hubspot_filter_value[idx]
                        ):
                            hubspot_filter_import[filter_type] = {
                                "key": hubspot_filter_option[idx],
                                "value": hubspot_filter_value[idx],
                            }
                except:
                    pass

            import_filter = {}
            if import_filter_type and import_filter_option and import_filter_value:
                try:
                    for idx, import_filter_type in enumerate(import_filter_type):
                        if (
                            import_filter_type
                            and import_filter_option[idx]
                            and import_filter_value[idx]
                        ):
                            import_filter[import_filter_type] = {
                                "key": import_filter_option[idx],
                                "value": import_filter_value[idx],
                            }
                except:
                    pass

            input_data = {
                "channel": str(channel.id) if channel else None,
                "how-to-import": request.POST.get("how-to-import", None),
                "how-to-import-hubspot": request.POST.get(
                    "how-to-import-hubspot", None
                ),
                "update-options": request.POST.get("update-options", None),
                "mapping_custom_fields": mapping_custom_fields,
                "mapping_contact_custom_fields": mapping_contact_custom_fields,
                "mapping_status_custom_fields": mapping_status_custom_fields,
                "hubspot_filter_import": hubspot_filter_import,
                "import_filter": import_filter,
                "set-contact-as-company": request.POST.get(
                    "set-contact-as-company", None
                ),
                "days_ago_filter": days_ago_filter,
                "target_property": request.POST.get("target_property" + postfix),
                "target_value": request.POST.get("target_value" + postfix),
                "only_payment_status": request.POST.get(
                    "only_payment_status" + postfix
                ),
                "action_name": action_name,
            }
            if input_data["how-to-import"] == "create":
                input_data["update-options"] = None

            if "advanced_filter" in request.POST:
                # Workflow Action Advanced Filter
                data = mapping_advanced_filter_handler(
                    request, mapping_type="save_import", input_data=input_data
                )
            node.input_data = input_data
            node.save()
        else:
            if lang == "ja":
                task_name = "受注レコードをインポート"
            else:
                task_name = "Import Orders"

            channel_id = node.input_data.get("channel", None)
            if not channel_id:
                at.status = "failed"
                at.save()
                return redirect(redirect_url)
            channel = Channel.objects.filter(id=channel_id, workspace=workspace).first()
            if not channel:
                at.status = "failed"
                at.save()
                return redirect(redirect_url)

            history_type = "import_order"
            history = TransferHistory.objects.create(
                workspace=workspace,
                user=request.user,
                status="running",
                type=history_type,
                name=task_name,
                channel=channel or None,
            )

            if lang == "ja":
                task_name = f"({channel.integration.title_ja} - {channel.name}) の受注レコードをインポート"
            else:
                task_name = (
                    f"Import ({channel.integration.title} - {channel.name}) orders"
                )
            history.name = task_name
            history.save()

            how_to_import = node.input_data.get("how-to-import", None)
            how_to_import_hubspot = node.input_data.get("how-to-import-hubspot", None)
            key_item_field = node.input_data.get("update-options", None)
            mapping_custom_fields = node.input_data.get("mapping_custom_fields", None)
            mapping_contact_custom_fields = node.input_data.get(
                "mapping_contact_custom_fields", None
            )
            mapping_status_custom_fields = node.input_data.get(
                "mapping_status_custom_fields", None
            )
            hubspot_filter_import = node.input_data.get("hubspot_filter_import", None)
            import_filter = node.input_data.get("import_filter", None)
            # contact_filter_mapping = node.input_data.get(
            #     'contact_filter_mapping', None)
            # Special platform parameters (handled by background task)
            days_ago_filter = node.input_data.get("days_ago_filter", 1)
            only_payment_status = node.input_data.get("only_payment_status")
            target_property = node.input_data.get("target_property")
            target_value = node.input_data.get("target_value")

            platform = channel.integration.slug
            if key_item_field == "":
                key_item_field = None

            history_type = "import_order"
            # Exclude the most recent running history for this channel
            four_hours_ago = timezone.now() - timezone.timedelta(hours=4)
            history_active_channel = (
                TransferHistory.objects.filter(
                    workspace=workspace,
                    type=history_type,
                    channel__isnull=False,
                    status="running",
                    created_at__gte=four_hours_ago,
                )
                .exclude(id=history.id)
                .order_by("-created_at")
            )
            channel_ids = (
                [
                    str(channel_id)
                    for channel_id in history_active_channel.values_list(
                        "channel_id", flat=True
                    )
                ]
                if history_active_channel
                else []
            )
            print("history_active_channel:", history_active_channel)
            print(f"Active channel IDs: {channel_ids}")

            if str(channel_id) not in channel_ids:
                # Note: Special platform parameters (days_ago_filter, only_payment_status, target_property, target_value)
                # are handled internally by the background task based on the platform type

                # Log import job parameters for debugging
                logger.info(
                    f"IMPORT_JOB: Starting orders import for user {request.user.email} in workspace {workspace.id}"
                )
                logger.info(
                    f"IMPORT_JOB: Channel ID: {channel.id}, Platform: {platform}"
                )

                try:
                    if platform == "shopify":
                        logger.info("Triggering Shopify orders import")
                        payload = ImportOrdersShopifyPayload(
                            user=str(request.user.id),
                            channel_id=str(channel_id),
                            mapping_contact_custom_fields=json.dumps(
                                mapping_contact_custom_fields
                            )
                            if mapping_contact_custom_fields
                            else "{}",
                            mapping_custom_fields=json.dumps(mapping_custom_fields)
                            if mapping_custom_fields
                            else "{}",
                            default_customer="",
                            key_item_field=key_item_field or "",
                            how_to_import=how_to_import or "",
                            sync_method="",
                            history_id=str(history.id),
                            lang=lang,
                        )
                        job_id = create_bg_job(
                            workspace,
                            request.user,
                            "import_orders",
                            transfer_history=history,
                            payload=payload.model_dump(mode="json"),
                        )
                        payload.background_job_id = job_id

                        ref = import_shopify_orders.run_no_wait(payload)

                    elif platform == "hubspot":
                        logger.info("Triggering HubSpot orders import")
                        payload = ImportOrdersHubSpotPayload(
                            user=str(request.user.id),
                            channel_id=str(channel_id),
                            mapping_custom_fields=json.dumps(mapping_custom_fields)
                            if mapping_custom_fields
                            else "{}",
                            mapping_status_custom_fields=json.dumps(
                                mapping_status_custom_fields
                            )
                            if mapping_status_custom_fields
                            else "{}",
                            history_id=str(history.id),
                            how_to_import=how_to_import or "",
                            how_to_import_hubspot=how_to_import_hubspot or "",
                            key_item_field=key_item_field or "",
                            as_deal=False,
                            lang=lang,
                            last_index="",
                            hubspot_filter_import=json.dumps(hubspot_filter_import)
                            if hubspot_filter_import
                            else "{}",
                        )
                        job_id = create_bg_job(
                            workspace,
                            request.user,
                            "import_orders",
                            transfer_history=history,
                            payload=payload.model_dump(mode="json"),
                        )
                        payload.background_job_id = job_id
                        ref = import_hubspot_orders.run_no_wait(payload)
                    elif platform == "square":
                        logger.info("Triggering Square orders import")
                        payload = ImportOrdersSquarePayload(
                            channel_id=str(channel_id),
                            history_id=str(history.id),
                        )
                        job_id = create_bg_job(
                            workspace,
                            request.user,
                            "import_orders",
                            transfer_history=history,
                            payload=payload.model_dump(mode="json"),
                        )
                        payload.background_job_id = job_id
                        ref = import_square_orders.run_no_wait(payload)
                    elif platform == "amazon":
                        logger.info("Triggering Amazon orders import")
                        payload = ImportOrdersAmazonPayload(
                            channel_id=str(channel.id),
                            mapping_custom_fields=json.dumps(mapping_custom_fields)
                            if mapping_custom_fields
                            else "{}",
                            default_customer="",
                            history_id=str(history.id),
                            key_item_field=key_item_field or "",
                            how_to_import=how_to_import or "",
                            import_filter=import_filter or "",
                            user=str(request.user.id),
                        )
                        job_id = create_bg_job(
                            workspace,
                            request.user,
                            "import_orders",
                            transfer_history=history,
                            payload=payload.model_dump(mode="json"),
                        )
                        payload.background_job_id = job_id
                        ref = import_amazon_orders.run_no_wait(payload)
                    elif platform == "ecforce":
                        logger.info("Triggering Ecforce orders import")
                        payload = ImportOrdersEcforcePayload(
                            channel_id=str(channel_id),
                            mapping_custom_fields=json.dumps(mapping_custom_fields)
                            if mapping_custom_fields
                            else "{}",
                            history_id=str(history.id),
                            workspace_id=str(workspace.id),
                            user_id=str(request.user.id),
                            lang=lang,
                        )
                        job_id = create_bg_job(
                            workspace,
                            request.user,
                            "import_orders",
                            transfer_history=history,
                            payload=payload.model_dump(mode="json"),
                        )
                        payload.background_job_id = job_id
                        ref = import_ecforce_orders.run_no_wait(payload)
                    elif platform == "rakuten":
                        logger.info("Triggering Rakuten orders import")
                        payload = ImportOrdersRakutenPayload(
                            channel_id=str(channel.id),
                            mapping_custom_fields=json.dumps(mapping_custom_fields)
                            if mapping_custom_fields
                            else "{}",
                            key_item_field=key_item_field or "",
                            how_to_import=how_to_import or "",
                            import_filter=import_filter or "",
                            default_customer="",
                            history_id=str(history.id),
                            user=str(request.user.id),
                        )
                        job_id = create_bg_job(
                            workspace,
                            request.user,
                            "import_orders",
                            transfer_history=history,
                            payload=payload.model_dump(mode="json"),
                        )
                        payload.background_job_id = job_id
                        ref = import_rakuten_orders.run_no_wait(payload)
                    elif platform == "ec-cube":
                        logger.info("Triggering EC-CUBE orders import")
                        payload = ImportOrdersEccubePayload(
                            channel_id=str(channel.id),
                            history_id=str(history.id),
                        )
                        job_id = create_bg_job(
                            workspace,
                            request.user,
                            "import_orders",
                            transfer_history=history,
                            payload=payload.model_dump(mode="json"),
                        )
                        payload.background_job_id = job_id
                        ref = import_eccube_orders.run_no_wait(payload)
                    elif platform == "makeshop":
                        logger.info("Triggering Makeshop orders import")
                        payload = ImportOrdersMakeshopPayload(
                            channel_id=str(channel.id),
                            mapping_custom_fields=json.dumps(mapping_custom_fields)
                            if mapping_custom_fields
                            else "{}",
                            key_item_field=key_item_field or "",
                            how_to_import=how_to_import or "",
                            import_order_date="",
                            mapping_status_custom_fields=json.dumps(
                                mapping_status_custom_fields
                            )
                            if mapping_status_custom_fields
                            else "{}",
                            default_customer="",
                            import_filter=import_filter or "",
                            history_id=str(history.id),
                        )
                        job_id = create_bg_job(
                            workspace,
                            request.user,
                            "import_orders",
                            transfer_history=history,
                            payload=payload.model_dump(mode="json"),
                        )
                        payload.background_job_id = job_id
                        ref = import_makeshop_orders.run_no_wait(payload)
                    elif platform == "yahoo-shopping":
                        logger.info("Triggering Yahoo Shopping orders import")
                        payload = ImportOrdersYahooShoppingPayload(
                            user=str(request.user.id),
                            channel_id=str(channel.id),
                            mapping_custom_fields=json.dumps(mapping_custom_fields)
                            if mapping_custom_fields
                            else "{}",
                            key_item_field=key_item_field or "",
                            how_to_import=how_to_import or "",
                            import_filter=import_filter or "",
                            history_id=str(history.id),
                        )
                        job_id = create_bg_job(
                            workspace,
                            request.user,
                            "import_orders",
                            transfer_history=history,
                            payload=payload.model_dump(mode="json"),
                        )
                        payload.background_job_id = job_id
                        ref = import_yahoo_shopping_orders.run_no_wait(payload)
                    elif platform == "b-cart":
                        logger.info("Triggering B-Cart orders import")
                        payload = ImportOrdersBcartPayload(
                            channel_id=str(channel.id),
                            mapping_custom_fields=json.dumps(mapping_custom_fields)
                            if mapping_custom_fields
                            else "{}",
                            key_item_field=key_item_field or "",
                            key_customer_field="",
                            how_to_import=how_to_import or "",
                            how_to_import_customer="",
                            import_filter=import_filter or "",
                            history_id=str(history.id),
                        )
                        job_id = create_bg_job(
                            workspace,
                            request.user,
                            "import_orders",
                            transfer_history=history,
                            payload=payload.model_dump(mode="json"),
                        )
                        payload.background_job_id = job_id
                        ref = import_bcart_orders.run_no_wait(payload)
                    elif platform == "wordpress":
                        logger.info("Triggering WooCommerce orders import")
                        payload = ImportOrdersWoocommercePayload(
                            channel_id=str(channel.id),
                            mapping_custom_fields=json.dumps(mapping_custom_fields)
                            if mapping_custom_fields
                            else "{}",
                            history_id=str(history.id),
                        )
                        job_id = create_bg_job(
                            workspace,
                            request.user,
                            "import_orders",
                            transfer_history=history,
                            payload=payload.model_dump(mode="json"),
                        )
                        payload.background_job_id = job_id
                        ref = import_woocommerce_orders.run_no_wait(payload)
                    elif platform == "salesforce":
                        logger.info("Triggering Salesforce opportunities import")
                        payload = ImportOrdersSalesforcePayloadV2(
                            background_job_id=job_id
                        )
                        job_id = create_bg_job(
                            workspace,
                            request.user,
                            "import_orders",
                            transfer_history=history,
                            payload=payload.model_dump(mode="json"),
                        )
                        ref = import_salesforce_opportunities.run_no_wait(payload)
                    elif platform == "ebay":
                        logger.info("Triggering eBay orders import")
                        payload = ImportOrdersEbayPayload(
                            channel_id=str(channel.id),
                            how_to_import=how_to_import or "",
                            history_id=str(history.id),
                        )
                        job_id = create_bg_job(
                            workspace,
                            request.user,
                            "import_orders",
                            transfer_history=history,
                            payload=payload.model_dump(mode="json"),
                        )
                        payload.background_job_id = job_id
                        ref = import_ebay_orders.run_no_wait(payload)
                    elif platform == "stripe":
                        logger.info("Triggering Stripe orders import")
                        payload = ImportOrdersStripePayload(
                            channel_id=str(channel.id),
                            days_ago_filter=days_ago_filter or "",
                            only_payment_status=only_payment_status or "",
                            target_property=target_property or "",
                            target_value=target_value or "",
                            history_id=str(history.id),
                        )
                        job_id = create_bg_job(
                            workspace,
                            request.user,
                            "import_orders",
                            transfer_history=history,
                            payload=payload.model_dump(mode="json"),
                        )
                        payload.background_job_id = job_id
                        ref = import_stripe_orders.run_no_wait(payload)
                    elif platform == "nextengine":
                        logger.info("Triggering NextEngine orders import")
                        payload = ImportOrdersNextEnginePayload(
                            channel_id=str(channel.id),
                            user_id=str(request.user.id),
                            mapping_custom_fields=json.dumps(mapping_custom_fields)
                            if mapping_custom_fields
                            else "{}",
                            history_id=str(history.id),
                        )
                        job_id = create_bg_job(
                            workspace,
                            request.user,
                            "import_orders",
                            transfer_history=history,
                            payload=payload.model_dump(mode="json"),
                        )
                        payload.background_job_id = job_id
                        ref = import_nextengine_orders.run_no_wait(payload)
                    else:
                        logger.error(f"Unsupported platform: {platform}")
                        raise ValueError(f"Unsupported platform: {platform}")

                except Exception as e:
                    logger.error(
                        f"Error in triggering import for platform {platform}: {str(e)}"
                    )
                    ref = None

                is_running = None
                if ref:
                    logger.info(
                        f"IMPORT_JOB: Background job submitted successfully for user {request.user.email}"
                    )
                    add_hatchet_run_id(job_id, ref)
                    is_running = True
                else:
                    logger.error(
                        f"IMPORT_JOB: Failed to submit background job for user {request.user.email}"
                    )
                    is_running = False

                # Handle success/failure
                if is_running:
                    # Success notification
                    if lang == "ja":
                        message = "受注レコードをインポートしております。少々お待ちください..."
                    else:
                        message = (
                            "Orders being imported. Please give it a few moments..."
                        )

                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message=message,
                        type="success",
                    )
                else:
                    # Fallback: Update transfer history to failed
                    history.status = "failed"
                    history.error_message = (
                        f"Failed to start background import for platform: {platform}"
                    )
                    history.save()

                    # Error notification
                    if lang == "ja":
                        message = "インポートの開始に失敗しました。サポートにお問い合わせください。"
                    else:
                        message = "Failed to start import. Please contact support."

                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message=message,
                        type="error",
                    )

                # Set output data for action tracker
                output_data = {
                    "background_job_triggered": is_running,
                    "platform": platform,
                }

                at.status = "success" if is_running else "failed"
                at.output_data = output_data
            else:
                # If the channel is already running, we do not start a new import
                history.status = "failed"
                history.error_message = (
                    f"Import already in progress for platform: {platform}"
                )
                history.save()

                if lang == "ja":
                    message = "インポートはすでに進行中です。"
                else:
                    message = "Import is already in progress."

                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message=message,
                    type="error",
                )
                # Set output data for action tracker
                output_data = {"background_job_triggered": False, "platform": platform}
                at.status = "failed"
                at.output_data = output_data

            # Note: Transfer history completion is now handled by the background task
            at.completed_at = timezone.now()
            at.save()

            if node.next_node:
                next_node = node.next_node
                next_at = ActionTracker.objects.get(
                    node=next_node, workflow_action_tracker=wat
                )
                transfer_output_to_target_input(at, next_at)
                trigger_next_action(
                    current_action_tracker=at,
                    workflow_action_tracker=wat,
                    current_node=node,
                    user_id=str(request.user.id),
                    lang=lang,
                )

            return redirect(redirect_url)

    return redirect(redirect_url)


@login_or_hubspot_required
def convert_order_to_po(request):
    print("In convert order to po")
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        print(request.GET)
        is_object_action = request.GET.get("bulk_action", True)
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")
        is_record_action = request.GET.get("is_record_action")

        # Getting Info if have action_index, it means from workflow
        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        if postfix:
            is_object_action = False
            order_id = request.GET.getlist("order_ids" + postfix, None)
            order_ids = order_id
        else:
            order_id = request.GET.get("order_ids", None)
            order_ids = None
            if order_id:
                if "[" in order_id and "]" in order_id:
                    order_ids = ast.literal_eval(order_id)
                    order_id = None

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:  # noqa
                print("Action node does not exist")
                return HttpResponse(status=404)

        use_cost_items = False
        use_item_components = False
        component_property = None
        use_existing_inventory = False
        buffer = None
        filter_property_ids = []
        if postfix:
            if node and node.input_data:
                if "order_ids" in node.input_data:
                    order_ids = node.input_data["order_ids"]
                    print("[DEBUG] order_ids:", order_ids)
                if "use_cost_items" in node.input_data:
                    use_cost_items = node.input_data["use_cost_items"]
                if "use_item_components" in node.input_data:
                    use_item_components = node.input_data["use_item_components"]
                if "component_property" in node.input_data:
                    component_property = node.input_data["component_property"]
                if "use_existing_inventory" in node.input_data:
                    use_existing_inventory = node.input_data["use_existing_inventory"]
                if "buffer" in node.input_data:
                    buffer = node.input_data["buffer"]
                if "filter_property_ids" in node.input_data:
                    filter_property_ids = node.input_data["filter_property_ids"]

        order = None
        orders = None
        if order_ids:
            orders = ShopTurboOrders.objects.filter(
                workspace=workspace, id__in=order_ids
            )
        elif order_id:
            order = ShopTurboOrders.objects.get(workspace=workspace, id=order_id)

        component_properties = ShopTurboItemsNameCustomField.objects.filter(
            workspace=workspace, type="components"
        )
        choice_properties = ShopTurboItemsNameCustomField.objects.filter(
            workspace=workspace, type="choice"
        )

        context = {
            "order": order,
            "orders": orders,
            "component_properties": component_properties,
            "choice_properties": choice_properties,
            "action_index": action_index,
            "action_node_id": action_node_id,
            "action_id": action_slug,
            "is_record_action": is_record_action,
            "is_object_action": is_object_action,
            "use_cost_items": use_cost_items,
            "use_item_components": use_item_components,
            "component_property": component_property,
            "use_existing_inventory": use_existing_inventory,
            "buffer": buffer,
            "filter_property_ids": filter_property_ids,
        }
        return render(request, "data/shopturbo/convert-order-to-po.html", context)

    # POST
    print("DEBUG: request.POST:", request.POST)
    input_data = {}  # Initialize input_data
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    action_slug = request.POST.get("action_slug") or request.POST.get("action_id")
    print(
        f"DEBUG: Retrieved action_slug='{action_slug}' from action_slug='{request.POST.get('action_slug')}' or action_id='{request.POST.get('action_id')}'"
    )
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")
    history_id = request.POST.get("history_id")
    is_record_action = request.POST.get("is_record_action", "")
    is_object_action = True

    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)

    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)

    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    if submit_option == "save":
        is_object_action = False
        node.valid_to_run = True
        input_data = {"is_convert_record_action": True}

        order_ids = request.POST.getlist("order_ids" + postfix, [])
        use_cost_items = request.POST.get("use_cost_items" + postfix)
        use_item_components = request.POST.get("use_item_components" + postfix)
        component_property = request.POST.get("component_property" + postfix)
        use_existing_inventory = request.POST.get("use_existing_inventory" + postfix)
        buffer = request.POST.get("buffer" + postfix)
        filter_property_ids = request.POST.getlist("filter_property_id" + postfix)
        if order_ids:
            input_data["order_ids"] = order_ids
        if use_cost_items:
            input_data["use_cost_items"] = use_cost_items
        if use_item_components:
            input_data["use_item_components"] = use_item_components
        if component_property:
            input_data["component_property"] = component_property
        if use_existing_inventory:
            input_data["use_existing_inventory"] = use_existing_inventory
        if buffer:
            input_data["buffer"] = buffer
        if filter_property_ids:
            input_data["filter_property_ids"] = filter_property_ids

        filter_property_ids = request.POST.getlist("filter_property_id")
        filter_property = {}
        for filter_property_id in filter_property_ids:
            filter_operator = request.POST.get(f"filter_operator-{filter_property_id}")
            filter_val = request.POST.get(f"filter_value-{filter_property_id}")
            if not (filter_operator and filter_val):
                continue
            filter_property[filter_property_id] = {
                "operator": filter_operator,
                "value": filter_val,
            }
        if filter_property:
            input_data["filter_property"] = filter_property

        object_source = request.POST.get("object_source" + postfix)
        object_target = request.POST.get("object_target" + postfix)
        if object_source:
            input_data["object_source"] = object_source
        if object_target:
            input_data["object_target"] = object_target
        node.input_data = input_data
        node.predefined_input = input_data
        node.save()
        return HttpResponse()

    orders = []
    order_ids = None
    use_cost_items = None
    use_item_components = None
    component_property = None
    use_existing_inventory = None
    buffer = 0
    filter_property_ids = None
    filter_property = {}
    if submit_option == "run":
        is_object_action = False
        if not (node and at and wat and node.input_data):
            print("No node, at, wat, or node.input_data")
            return HttpResponse(status=404)

        if "order_ids" in node.input_data:
            order_ids = node.input_data["order_ids"]
            orders = ShopTurboOrders.objects.filter(
                workspace=workspace, id__in=order_ids, status="active"
            )
        else:
            # From previous action data
            orders = ShopTurboOrders.objects.filter(
                workspace=workspace,
                order_id=at.input_data["order"]["order_id"],
                status="active",
            )
        if "use_cost_items" in node.input_data:
            use_cost_items = node.input_data["use_cost_items"]
        if "use_item_components" in node.input_data:
            use_item_components = node.input_data["use_item_components"]
        if "component_property" in node.input_data:
            component_property = node.input_data["component_property"]
        if "use_existing_inventory" in node.input_data:
            use_existing_inventory = node.input_data["use_existing_inventory"]
        if "buffer" in node.input_data:
            buffer = node.input_data["buffer"]
        if "filter_property_ids" in node.input_data:
            filter_property_ids = node.input_data["filter_property_ids"]
        if "filter_property" in node.input_data:
            filter_property = node.input_data["filter_property"]
    else:
        print(request.POST)
        # Use the standardized parse_record_ids utility like other actions
        order_ids = parse_record_ids(
            request.POST.get("object_ids", []), request, "order_ids"
        )
        print(f"DEBUG: Parsed order_ids: {order_ids}")

        use_cost_items = request.POST.get("use_cost_items")
        use_item_components = request.POST.get("use_item_components")
        component_property = request.POST.get("component_property")
        use_existing_inventory = request.POST.get("use_existing_inventory")
        buffer = request.POST.get("buffer")
        filter_property_ids = request.POST.getlist("filter_property_id")
        filter_property = {}
        for filter_property_id in filter_property_ids:
            filter_operator = request.POST.get(f"filter_operator-{filter_property_id}")
            filter_val = request.POST.get(f"filter_value-{filter_property_id}")
            if not (filter_operator and filter_val):
                continue
            filter_property[filter_property_id] = {
                "operator": filter_operator,
                "value": filter_val,
            }

        try:
            if order_ids:
                orders = ShopTurboOrders.objects.filter(
                    workspace=workspace, id__in=order_ids
                )
            else:
                print("No order_ids found")
                return HttpResponse(status=400)
        except:  # noqa
            print("ShopTurboOrders does not exist")
            return HttpResponse(status=404)

    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_ORDER]
    module = (
        Module.objects.filter(
            workspace=workspace, object_values__contains=TYPE_OBJECT_ORDER
        )
        .order_by("order", "created_at")
        .first()
    )
    module_slug = None
    if module:
        module_slug = module.slug

    redirect_url = redirect(reverse("main", host="app"))
    if module_slug:
        if node:
            redirect_url = redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW],
                    },
                )
                + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
            )
        elif is_record_action:
            redirect_url = redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?id={order_ids[0] if order_ids else order_id}&target={TYPE_OBJECT_ORDER}&sidedrawer=action-history"
            )
        else:
            redirect_url = redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + "?open_drawer=action_drawer_history"
            )

    if node:
        action = node.action
    else:
        action = Action.objects.filter(slug=action_slug).first()
        print(
            f"DEBUG: action_slug={action_slug}, action={action}, action.title={action.title if action else 'None'}, action.title_ja={action.title_ja if action else 'None'}"
        )
        history = None
        if history_id:
            try:
                history = ActionHistory.objects.get(
                    id=history_id,
                    workspace=workspace,
                    action=action,
                )
            except:  # noqa
                pass
        if not history:
            history = ActionHistory.objects.create(
                workspace=workspace,
                action=action,
                status="initialized",
                created_by=request.user,
            )
        if is_record_action:
            history.object_type = TYPE_OBJECT_ORDER
            if orders and len(orders) > 0:
                history.object_id = orders[0].id
            history.save()

    try:
        buffer = int(buffer) / 100
    except:  # noqa
        buffer = 0
    purchase_order_module = Module.objects.filter(
        workspace=workspace, object_values__icontains=TYPE_OBJECT_PURCHASE_ORDER
    ).first()
    purchase_order_view = View.objects.filter(
        workspace=workspace, target=TYPE_OBJECT_PURCHASE_ORDER
    ).first()
    view_q = ""
    if purchase_order_module and purchase_order_view:
        view_q = f"&view_id={purchase_order_view.id}"

    output_data = {}
    for i, order in enumerate(orders):
        items = []
        purchase_details = {}
        supplier_item_mapper = {}
        poi_manual_list = []

        # Choose the appropriate item model based on the use_cost_items toggle
        if use_cost_items:
            print(f"[PO Conversion] Using cost items from order {order.order_id}")
            items_orders_queryset = ShopTurboPurchaseItemsOrders.objects.filter(
                order=order
            ).order_by("order_view")
        else:
            print(f"[PO Conversion] Using regular items from order {order.order_id}")
            items_orders_queryset = ShopTurboItemsOrders.objects.filter(
                order=order
            ).order_by("order")

        for items_orders in items_orders_queryset:
            # Custom item by manual input
            if not items_orders.item and not filter_property:
                poi_manual = PurchaseOrdersItem.objects.create(
                    item_name=items_orders.custom_item_name,
                    amount_item=items_orders.number_item
                    + int(buffer * items_orders.number_item),
                    amount_price=items_orders.item_price_order,
                )
                poi_manual_list.append(poi_manual)
                continue

            target_items = []
            # Define target items to use as the Purchase Item (is it Shopturbo items or it's components)
            if use_item_components:
                item_component_ids = ShopTurboItemsValueCustomField.objects.filter(
                    items=items_orders.item,
                    field_name__id=component_property,
                    field_name__type="components",
                ).values_list("components__item_component")

                # Apply filter if any
                if filter_property:
                    for k in filter_property:
                        if filter_property[k]["operator"] == "is":
                            item_component_ids = (
                                ShopTurboItemsValueCustomField.objects.filter(
                                    items__id__in=item_component_ids,
                                    field_name__id=k,
                                    field_name__type="choice",
                                    value=filter_property[k]["value"],
                                ).values_list("items__id")
                            )
                        else:
                            item_component_ids = (
                                ShopTurboItemsValueCustomField.objects.filter(
                                    items__id__in=item_component_ids,
                                    field_name__id=k,
                                    field_name__type="choice",
                                )
                                .exclude(value=filter_property[k]["value"])
                                .values_list("items__id")
                            )

                item_components = ShopTurboItems.objects.filter(
                    id__in=item_component_ids
                )
                if item_components:
                    target_items = item_components
            else:
                target_items = [items_orders.item]
                # Apply filter if any
                if filter_property:
                    item_ids = []
                    for k in filter_property:
                        if filter_property[k]["operator"] == "is":
                            item_ids = ShopTurboItemsValueCustomField.objects.filter(
                                items=items_orders.item,
                                field_name__id=k,
                                field_name__type="choice",
                                value=filter_property[k]["value"],
                            ).values_list("items__id")
                        else:
                            item_ids = (
                                ShopTurboItemsValueCustomField.objects.filter(
                                    items=items_orders.item,
                                    field_name__id=k,
                                    field_name__type="choice",
                                )
                                .exclude(value=filter_property[k]["value"])
                                .values_list("items__id")
                            )
                    if not item_ids:
                        target_items = []

            if len(target_items) <= 0:
                supplier_item_mapper["no_supplier"] = []

            for item_ in target_items:
                # Get item component quantity & price (if using item component)
                demand = items_orders.number_item + int(
                    buffer * items_orders.number_item
                )
                if item_.purchase_price:
                    price = item_.purchase_price
                else:
                    price = item_.price

                if price is None:
                    price = 0

                if use_item_components:
                    try:
                        item_component_ = ShopTurboItemComponents.objects.get(
                            property__items=items_orders.item,
                            item_component=item_,
                            property__field_name__type="components",
                            property__field_name__id=component_property,
                        )
                        demand = (
                            items_orders.number_item
                            + int(buffer * items_orders.number_item)
                        ) * item_component_.quantity
                    except Exception as e:
                        print(e)
                        pass

                available_stock = 0
                if use_existing_inventory:
                    res = ShopTurboInventory.objects.filter(
                        item=item_, status="active"
                    ).aggregate(Sum("available"))
                    available_stock = (
                        res["available__sum"]
                        if res["available__sum"] is not None
                        else 0
                    )

                # Get the stock-demand gap
                demand_gap = available_stock - demand
                print("demand_gap:", demand_gap)
                if demand_gap > 0:  # skip, still have available stock
                    continue

                demand_gap = abs(demand_gap)

                item_supplier = AssociationLabelObject.objects.filter(
                    label__object_source=TYPE_OBJECT_ITEM,
                    source_object_id=item_.id,
                    target_content_type__in=[
                        ContentType.objects.get_for_model(Company),
                        ContentType.objects.get_for_model(Contact),
                    ],
                ).first()
                target_obj = item_supplier.target_object if item_supplier else None
                if item_ in items:
                    if isinstance(target_obj, Company):
                        supplier_item_mapper[str(target_obj.id) + "_company"].append(
                            item_
                        )
                    elif isinstance(target_obj, Contact):
                        supplier_item_mapper[str(target_obj.id) + "_contact"].append(
                            item_
                        )
                    else:
                        supplier_item_mapper["no_supplier"].append(item_)
                    purchase_details[item_]["quantity"] += demand_gap
                    purchase_details[item_]["total_price"] = (
                        purchase_details[item_]["quantity"]
                        * purchase_details[item_]["price"]
                    )
                else:
                    items.append(item_)
                    if isinstance(target_obj, Company):
                        if str(target_obj.id) + "_company" not in supplier_item_mapper:
                            supplier_item_mapper[str(target_obj.id) + "_company"] = [
                                item_
                            ]
                        else:
                            supplier_item_mapper[
                                str(target_obj.id) + "_company"
                            ].append(item_)
                    elif isinstance(target_obj, Contact):
                        if str(target_obj.id) + "_contact" not in supplier_item_mapper:
                            supplier_item_mapper[str(target_obj.id) + "_contact"] = [
                                item_
                            ]
                        else:
                            supplier_item_mapper[
                                str(target_obj.id) + "_contact"
                            ].append(item_)
                    else:
                        if "no_supplier" not in supplier_item_mapper:
                            supplier_item_mapper["no_supplier"] = []

                        supplier_item_mapper["no_supplier"].append(item_)
                    purchase_details[item_] = {
                        "quantity": demand_gap,
                        "price": price,
                        "total_price": demand_gap * price,
                    }

        # Fix for Issue 1: Consolidate supplier groups when using cost items with single item
        # This prevents creating multiple purchase orders when there's only one cost item
        if use_cost_items and len(items_orders_queryset) == 1 and len(items) > 0:
            print(
                f"[PO Conversion] Consolidating cost items into single PO for order {order.order_id}"
            )
            # Consolidate all items into a single purchase order regardless of supplier
            all_items = []
            for supplier_items in supplier_item_mapper.values():
                all_items.extend(supplier_items)

            if all_items:
                # Use the first item's supplier info for the consolidated PO
                first_item = all_items[0]
                if first_item.company:
                    consolidated_key = str(first_item.company.id) + "_company"
                elif first_item.contact:
                    consolidated_key = str(first_item.contact.id) + "_contact"
                else:
                    consolidated_key = "no_supplier"

                # Replace the supplier mapper with consolidated version
                supplier_item_mapper = {consolidated_key: all_items}
                print(
                    f"[PO Conversion] Consolidated {len(all_items)} items under supplier '{consolidated_key}'"
                )

        # Ensure manual items are processed by adding "no_supplier" entry if needed
        if poi_manual_list and "no_supplier" not in supplier_item_mapper:
            supplier_item_mapper["no_supplier"] = []
            print(
                f"[PO Conversion] Added 'no_supplier' entry for {len(poi_manual_list)} manual items"
            )

        po = None
        if not items and not poi_manual_list:
            if is_object_action or is_record_action:
                transfer_history = ActionTaskHistory.objects.create(
                    workspace=history.workspace,
                    action_history=history,
                    status="failed",
                    input_data=input_data,
                    error_message="Order does not have any item",
                    error_message_ja="注文に商品がありません",
                )
                history.status = "failed"
                history.save()
            elif node:
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message=f"Failed to convert order {order.order_id:04d} to Purchase Order"
                    if lang == "en"
                    else f"注文 {order.order_id:04d} の発注書への変換に失敗しました",
                    type="error",
                )
            continue

        print(items, poi_manual_list, supplier_item_mapper)
        if is_object_action or is_record_action:
            history.status = "running"
            history.save()

        for supplier_id, supplier_items in supplier_item_mapper.items():
            if not supplier_items and supplier_id != "no_supplier":
                continue

            if is_object_action or is_record_action:
                transfer_history = ActionTaskHistory.objects.create(
                    workspace=history.workspace,
                    action_history=history,
                    status="running",
                    input_data=input_data,
                )

            # Extract supplier information from items
            supplier_contact = None
            supplier_company = None

            if supplier_id != "no_supplier":
                # Find the first item with actual supplier information
                target_obj = None
                for item in supplier_items:
                    item_supplier = AssociationLabelObject.objects.filter(
                        label__object_source=TYPE_OBJECT_ITEM,
                        source_object_id=item.id,
                        target_content_type__in=[
                            ContentType.objects.get_for_model(Company),
                            ContentType.objects.get_for_model(Contact),
                        ],
                    ).first()
                    target_obj = item_supplier.target_object if item_supplier else None
                    if item and target_obj:
                        break

                if target_obj:
                    if isinstance(target_obj, Company):
                        supplier_company = target_obj
                    elif isinstance(target_obj, Contact):
                        supplier_contact = target_obj
                    print(
                        f"[PO Conversion] Found supplier for group {supplier_id}: contact={supplier_contact}, company={supplier_company}"
                    )

                    # Validate that all items in this group have consistent supplier information
                    inconsistent_items = []
                    for item in supplier_items:
                        item_supplier = AssociationLabelObject.objects.filter(
                            label__object_source=TYPE_OBJECT_ITEM,
                            source_object_id=item.id,
                            target_content_type__in=[
                                ContentType.objects.get_for_model(Company),
                                ContentType.objects.get_for_model(Contact),
                            ],
                        ).first()
                        target_obj = (
                            item_supplier.target_object if item_supplier else None
                        )
                        if item and (
                            target_obj != supplier_contact
                            and target_obj != supplier_company
                        ):
                            inconsistent_items.append(
                                f"Item {item.item_id} (supplier: {target_obj}, object: {target_obj._meta.object_name})"
                            )

                    if inconsistent_items:
                        print(
                            f"[PO Conversion] Warning: Inconsistent supplier information detected in group {supplier_id}:"
                        )
                        for inconsistent_item in inconsistent_items:
                            print(f"[PO Conversion]   - {inconsistent_item}")
                        print(
                            f"[PO Conversion] Using supplier from first item with info: contact={supplier_contact}, company={supplier_company}"
                        )
                else:
                    print(
                        f"[PO Conversion] Warning: No supplier information found in items for group {supplier_id}"
                    )
                    # Fallback: try to extract from the supplier_id if it follows the expected format
                    if "_company" in supplier_id:
                        try:
                            company_id = supplier_id.replace("_company", "")
                            supplier_company = Company.objects.get(
                                id=company_id, workspace=workspace
                            )
                            print(
                                f"[PO Conversion] Fallback: extracted company from supplier_id: {supplier_company}"
                            )
                        except Exception as e:
                            print(
                                f"[PO Conversion] Fallback failed to extract company: {e}"
                            )
                    elif "_contact" in supplier_id:
                        try:
                            contact_id = supplier_id.replace("_contact", "")
                            supplier_contact = Contact.objects.get(
                                id=contact_id, workspace=workspace
                            )
                            print(
                                f"[PO Conversion] Fallback: extracted contact from supplier_id: {supplier_contact}"
                            )
                        except Exception as e:
                            print(
                                f"[PO Conversion] Fallback failed to extract contact: {e}"
                            )
            else:
                print("[PO Conversion] Processing items without supplier")

            try:
                # Get association id from purchase order to supplier (contact/company)
                association_label = AssociationLabel.objects.filter(
                    workspace=workspace,
                    object_source=TYPE_OBJECT_PURCHASE_ORDER,
                    object_target__in=[
                        f"{TYPE_OBJECT_COMPANY},{TYPE_OBJECT_CONTACT}",
                        f"{TYPE_OBJECT_CONTACT},{TYPE_OBJECT_COMPANY}",
                    ],
                    created_by_sanka=True,
                ).first()
                po = PurchaseOrders.objects.create(
                    workspace=workspace,
                    currency=order.currency,
                    date=order.order_at.date(),
                    usage_status="active",
                    status="internal_approved",
                    contact=supplier_contact,
                    company=supplier_company,
                )

                if supplier_contact or supplier_company:
                    AssociationLabelObject.objects.create(
                        workspace=workspace,
                        label=association_label,
                        source_object=po,
                        target_object=supplier_contact
                        if supplier_contact
                        else supplier_company,
                    )
                print(
                    f"[PO Conversion] Successfully created purchase order: {po.id} {po.id_po} with supplier (contact: {supplier_contact}, company: {supplier_company})"
                )
            except Exception as e:
                print(f"[PO Conversion] Error creating purchase order: {str(e)}")
                if is_object_action or is_record_action:
                    transfer_history.status = "failed"
                    transfer_history.error_message = (
                        f"Failed to create purchase order: {str(e)}"
                    )
                    transfer_history.save()
                continue

            total_price = 0

            for item_ in supplier_items:
                PurchaseOrdersItem.objects.create(
                    purchase_order=po,
                    item=item_,
                    amount_item=purchase_details[item_]["quantity"],
                    amount_price=purchase_details[item_]["price"],
                    total_price=purchase_details[item_]["total_price"],
                )
                total_price += purchase_details[item_]["total_price"]
                print("create purchase orders item for item", {item_.item_id})

            if supplier_id == "no_supplier":
                for poi_manual in poi_manual_list:
                    poi_manual.purchase_order = po
                    poi_manual.save()
                    total_price += poi_manual.amount_price
                    print(
                        "create purchase orders item for custom item",
                        {poi_manual.item_name},
                    )

            po.total_price = total_price
            po.total_price_without_tax = total_price
            po.save()
            if is_object_action or is_record_action:
                obj_dict = model_to_dict(po)
                serialized_data = json.dumps(obj_dict, default=custom_serializer)
                transfer_history.output_data = json.loads(serialized_data)
                if purchase_order_module:
                    transfer_history.result_link = (
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": purchase_order_module.slug,
                                "object_slug": "purchase_orders",
                            },
                        )
                        + f"?id={po.id}{view_q}"
                    )
                transfer_history.completed_at = timezone.now()
                transfer_history.status = "success"
                transfer_history.save()

    if is_record_action or (is_object_action and at is None):
        total_tasks_completed = (
            ActionTaskHistory.objects.filter(
                workspace=history.workspace,
                action_history=history,
                status="success",
            )
            .values("id")
            .count()
        )
        if total_tasks_completed > 0:
            history.status = "success"
        else:
            history.status = "failed"
        history.completed_at = timezone.now()
        history.save()
    else:
        # Update action tracker
        at.status = "success"
        at.output_data = output_data
        at.completed_at = timezone.now()
        at.save()

        next_node = None
        if node:
            next_node = node.next_node
        if next_node:
            next_at = ActionTracker.objects.get(
                node=next_node, workflow_action_tracker=wat
            )
            transfer_output_to_target_input(at, next_at)
            trigger_next_action(
                current_action_tracker=at,
                workflow_action_tracker=wat,
                current_node=node,
                user_id=str(request.user.id),
                lang=lang,
            )

    return redirect_url
