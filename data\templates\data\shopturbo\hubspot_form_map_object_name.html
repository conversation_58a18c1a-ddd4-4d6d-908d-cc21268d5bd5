{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

<div class="mb-5">
    {% if edit %}
        <button
            id="back_button_form_mapping" class="mb-4 btn btn-light"
            hx-vals='{"channel_id":"{{ channel_id }}", "hub_domain": "{{hub_domain}}", "import_export_type": "{{import_export_type}}", "object_type": "{{object_type}}"}'
            {% if "Hubspot Power Inventory" in hub_domain %}
            hx-post="{% host_url 'hubspot_inventory_fields_mapping_url' host 'app' %}"
            {% elif "Hubspot Power Billing" in hub_domain %}
            hx-post="{% host_url 'hubspot_billing_fields_mapping_url' host 'app' %}"
            {% elif "Hubspot Power Subscription" in hub_domain %}
            hx-post="{% host_url 'hubspot_subscription_fields_mapping_url' host 'app' %}"
            {% endif %}
            hx-swap="innerHTML"
            hx-target="#hubspotMappingContainer"
            hx-trigger="keyup changed delay:500ms, click">
            {% if LANGUAGE_CODE == 'ja'%}
            戻る
            {% else %}
            Back
            {% endif %}
        </button>
        <script>
            document.getElementById('back_button_form_mapping').addEventListener('htmx:beforeRequest', function(event) {
                const settingsContainer = document.getElementById('hubspotMappingContainer');
                
                // Show loading spinner only in the target container for this button
                settingsContainer.innerHTML = `
                    <div class="d-flex justify-content-center">
                        <div class="loading-drawer-spinner mt-10 mb-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                `;
            });
        </script>
    {% endif %}

    <div class="d-flex justify-content-center align-items-center w-100">
        <script> 
            document.getElementById('MapObject').addEventListener('htmx:beforeRequest', function(event) {
                const settingsContainer = document.getElementById('hubspotMappingContainer');
                
                // Show loading spinner only in the target container for this button
                settingsContainer.innerHTML = `
                    <div class="d-flex justify-content-center">
                        <div class="loading-drawer-spinner mt-10 mb-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                `;
            });
        </script>
        <form id="MapObject" 
            hx-vals='{"channel_id":"{{ channel_id }}", "hubspot-access-token": "{{hubspot_access_token}}", "hub_domain": "{{hub_domain}}", "object_type": "{{object_type}}", "import_export_type": "{{import_export_type}}"}'
            hx-post="{% host_url 'save_mapped_hubspot_object_name' host 'app' %}" 
            hx-target="#hubspotMappingContainer" 
            hx-on="htmx:responseError: window.location.reload()" 
            hx-swap="innerHTML" 
            class="p-4 border rounded bg-light w-100" >
            {% csrf_token %}        
            <input type="hidden" name="hub_domain" value="{{ hub_domain }}">
            <input type="hidden" name="hub_id" value="{{ hub_id }}">

            {% if edit %}
                <input type="hidden" name="edit" value="{{ edit }}">
            {% endif %}

            <h3 class="mb-5">
                {% if LANGUAGE_CODE == 'ja'%}
                Hubspotのオブジェクトをマッピング
                {% else %}
                Map Hubspot Object
                {% endif %}
            </h3>
            
            {% if message %}
                <div id="warningMessageMapObject" style=" color: red;">{{message}}</div>
            {% endif %}
            
            <table class="table table-row-bordered table-row-dashed gy-4 align-middle">
                <thead class="{% include "data/utility/table-header.html" %}">
                    <tr>
                        <th class="">
                            {% if LANGUAGE_CODE == 'ja' %}
                            Sanka オブジェクト
                            {% else %}
                            SANKA OBJECT
                            {% endif %}
                        </th>
                        <th class="">
                            {% if LANGUAGE_CODE == 'ja' %}
                            Hubspot オブジェクト
                            {% else %}
                            HUBSPOT OBJECT NAME
                            {% endif %}
                        </th>
                    </tr>
                </thead>
                    <tbody class="fs-6 align-middle {{object_type}}-tbody" >    
                        <tr>
                            <td>
                                <input type="hidden" name="objectSankaName" value="{{object_type}}">
                                {% if object_type == 'commerce_inventory' %}
                                    {% if LANGUAGE_CODE == 'ja' %}在庫{% else %}Inventory{% endif %}
                                {% elif object_type == 'commerce_subscription' %}
                                    {% if LANGUAGE_CODE == 'ja' %}サブスクリプション{% else %}Subscription{% endif %}
                                {% elif object_type == 'estimates' %}
                                    {% if LANGUAGE_CODE == 'ja' %}見積書{% else %}Estimates{% endif %}
                                {% elif object_type == 'invoices' %}
                                    {% if LANGUAGE_CODE == 'ja' %}請求書{% else %}Invoices{% endif %}
                                {% elif object_type == 'receipts' %}
                                    {% if LANGUAGE_CODE == 'ja' %}領収書{% else %}Receipts{% endif %}
                                {% elif object_type == 'delivery_slips' %}
                                    {% if LANGUAGE_CODE == 'ja' %}納品書{% else %}Delivery Notes{% endif %}
                                {% endif %}
                            </td>
                            
                            <td class="align-middle">
                                <input type="text" class="form-control hubspot-input mb-3" id="objectHubspotName" name="objectHubspotName" placeholder='Hubspot object internal name' {% if not is_workflow %}required{% endif %}
                                {% if saved_objects.saved_object_map %} value="{{saved_objects.saved_object_map}}" {% endif %}>
                            </td>
                        </tr>
                    </tbody>
            </table>
            <button type="submit" class="btn btn-primary ">
                {% if LANGUAGE_CODE == 'ja'%}
                送信
                {% else %}
                Submit
                {% endif %}
                
            </button>
        </form>
    </div>
        <script>

            document.getElementById('objectsForm') && document.getElementById('objectsForm').addEventListener('htmx:beforeRequest', function(event) {
                const settingsContainer = document.getElementById("hubspotMappingContainer");
                
                // Show loading spinner only in the target container for this button
                settingsContainer.innerHTML = `
                    <div class="d-flex justify-content-center">
                        <div class="loading-drawer-spinner mt-10 mb-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                `;
            });
        </script>
    </div>
</div>