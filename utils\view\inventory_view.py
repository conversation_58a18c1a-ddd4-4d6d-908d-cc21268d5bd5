import ast
import re
import uuid

from django.core.paginator import <PERSON><PERSON><PERSON>
from django.db.models import OuterRef, Prefetch, Q, Subquery
from django.utils import timezone

from data.constants.constant import DEFAULT_COLUMNS_INVENTORY
from data.constants.properties_constant import TYPE_OBJECT_INVENTORY
from data.models import (
    AdvanceSearchFilter,
    Channel,
    InventoryTransaction,
    ShopTurboInventory,
    ShopTurboInventoryNameCustomField,
    ShopTurboInventoryPlatforms,
    ShopTurboInventoryValueCustomField,
    ShopTurboItems,
    ShopTurboItemsNameCustomField,
    ShopTurboItemsValueCustomField,
    ViewFilter,
)
from utils.filter import (
    apply_search_setting,
    build_view_filter,
    apply_shopturboinventory_view_filter,
)
from utils.logger import logger
from utils.utility import get_permission_filter, is_valid_uuid


def inventory_view_objects(
    workspace,
    user,
    permission,
    app_setting,
    view_filter,
    status=None,
    search_q=None,
    filter_ids=None,
    filter_object_ids=None,
    **kwargs,
):
    """
    Get ShopTurboInventory objects for the given view filter.

    This function will filter the ShopTurboInventory objects based on the
    given view filter and the user's permission. It will also apply the
    search query if one is given.

    Args:
        workspace (Workspace): The workspace of the user.
        user (User): The user object.
        permission (str): The permission of the user.
        app_setting (AppSetting): The app setting.
        view_filter (ViewFilter): The view filter object.
        status (str, optional): The status of the inventory. Defaults to None.
        search_q (str, optional): The search query. Defaults to None.
        filter_ids (list[str], optional): The list of IDs to filter. Defaults to None.
        filter_object_ids (list[str], optional): The list of object IDs to filter. Defaults to None.

    Returns:
        list[ShopTurboInventory]: The list of filtered ShopTurboInventory objects.
    """

    log_perf = kwargs.get("log_perf")
    helper = kwargs.get("inventory_view_helper")
    pagination_number = view_filter.pagination
    if not pagination_number:
        pagination_number = 25
    if "pagination_number" in kwargs:
        pagination_number = kwargs["pagination_number"]

    shopturbo_inventory_columns = DEFAULT_COLUMNS_INVENTORY[2:].copy()
    if view_filter:
        if view_filter.column:
            if isinstance(view_filter.column, str):
                shopturbo_inventory_columns = ast.literal_eval(view_filter.column)
            else:
                shopturbo_inventory_columns = view_filter.column
            if "inventory_id" in shopturbo_inventory_columns:
                shopturbo_inventory_columns.remove("inventory_id")
                view_filter.column = str(shopturbo_inventory_columns)
                view_filter.save()
            if "checkbox" in shopturbo_inventory_columns:
                shopturbo_inventory_columns.remove("checkbox")
                view_filter.column = str(shopturbo_inventory_columns)
                view_filter.save()
            for idx, column in enumerate(["checkbox", "inventory_id"]):
                shopturbo_inventory_columns.insert(idx, column)
            shopturbo_inventory_columns = [
                data.lower() for data in shopturbo_inventory_columns
            ]
        else:
            for idx, column in enumerate(["checkbox", "inventory_id"]):
                shopturbo_inventory_columns.insert(idx, column)
            shopturbo_inventory_columns = [
                data.lower() for data in shopturbo_inventory_columns
            ]
            view_filter.column = shopturbo_inventory_columns
            view_filter.save()

    filter_conditions = Q(workspace=workspace)

    filter_conditions &= get_permission_filter(permission, user)

    tmp_view_filter = ViewFilter(
        filter_value=view_filter.filter_value, view=view_filter.view
    )
    if tmp_view_filter.filter_value:
        tmp_view_filter.filter_value = {
            k: v
            for k, v in tmp_view_filter.filter_value.items()
            if v.get("key") != "excludes"
        }

    filter_conditions = apply_shopturboinventory_view_filter(
        filter_conditions, tmp_view_filter
    )

    # Handle status filter for archived items
    if status == "archived":
        filter_conditions &= Q(status="archived")
    else:
        # By default, show only active items
        filter_conditions &= Q(status="active")

    advance_search = None
    if filter_object_ids is not None:
        filter_conditions &= Q(inventory_id__in=filter_object_ids)
    elif filter_ids is not None:
        filter_conditions &= Q(id__in=filter_ids)
    else:
        advance_search = AdvanceSearchFilter.objects.filter(
            workspace=workspace, object_type=TYPE_OBJECT_INVENTORY, type="default"
        ).first()
        if not advance_search:
            advance_search = None

        if advance_search:
            if advance_search.search_settings:
                app_setting.search_setting_inventory = advance_search.search_settings
                app_setting.save()
        logger.debug(f"search settings: {app_setting.search_setting_inventory}")

    if search_q:
        # Log search query details for inventory
        log_perf(
            f"Processing inventory search query - query: {search_q}, view_id: {view_filter.view.id}, search_settings: {app_setting.search_setting_inventory}",
        )

        match_special_char = re.search(r"#(\d+)", search_q)
        if match_special_char:
            number = match_special_char.group(1)  # Get the number part
            if number:
                filter_conditions &= Q(inventory_id=number)
                logger.debug(
                    f"Applied special character search for inventory_id: {number}"
                )
        else:
            search_q = search_q.lower()
            search_filters = Q()

            # Optimize search with indexed fields first
            # Use exact match for inventory_id if it's numeric
            if search_q.isdigit():
                try:
                    inventory_id_int = int(search_q)
                    search_filters |= Q(inventory_id=inventory_id_int)
                except ValueError:
                    pass

            # Process additional search settings
            if app_setting.search_setting_inventory:
                search_fields = app_setting.search_setting_inventory.split(",")

                log_perf(
                    f"Processing inventory search fields - fields: {search_fields}, view_filter_view_id: {view_filter.id}, view_filter_type: {view_filter.view_type}",
                )

                for s_field in search_fields:
                    # Log each search field processing
                    logger.debug(
                        f"Processing inventory search field - field: {s_field}, is_uuid: {is_valid_uuid(s_field)}"
                    )

                    # Skip fields we've already optimized above
                    if s_field == "inventory_id":
                        continue

                    if s_field == "item__name":
                        search_filters |= apply_search_setting(
                            "commerce_inventory",
                            view_filter,
                            s_field,
                            search_q,
                            force=True,
                        )
                    else:
                        search_filters |= apply_search_setting(
                            "commerce_inventory", view_filter, s_field, search_q
                        )
            filter_conditions &= search_filters
            log_perf(
                f"Final inventory search filter conditions applied - query_str: {filter_conditions}",
            )

    advance_search_filter = None
    if advance_search and advance_search.is_active:
        advance_search_filter = advance_search.search_filter
        advance_search_filter_status = advance_search.search_filter_status

        # Log advance search filter details
        log_perf(
            f"Processing advance search filter for inventory - raw_filter: {advance_search_filter}, filter_status: {advance_search_filter_status}",
        )

        if advance_search_filter_status:
            advance_search_filter = {
                k: v
                for k, v in advance_search_filter.items()
                if v["value"] != "" and advance_search_filter_status.get(k) != "False"
            }
        else:
            advance_search_filter = {
                k: v for k, v in advance_search_filter.items() if v["value"] != ""
            }

        # Log filtered advance search filter
        log_perf(
            f"Filtered advance search filter for inventory - filtered_filter: {advance_search_filter}",
        )

        try:
            if advance_search_filter.get("usage_status", {}).get("value") == "all":
                del advance_search_filter["usage_status"]
        except KeyError:
            pass

        view_filter.filter_value = advance_search_filter

        # Log final view filter value
        log_perf(
            f"Set view_filter.filter_value for inventory - final_filter_value: {view_filter.filter_value}",
        )

    has_excludes_filter = False
    excludes_only_filter = None
    if not filter_ids and not filter_object_ids:
        # Log before applying view filter
        log_perf(
            f"Applying view filter for inventory - before_filter: {filter_conditions}, view_filter_id: {view_filter.id}, filter_value: {view_filter.filter_value}"
        )

        # Log workspace information for debugging workspace-specific issues
        log_perf(
            f"Inventory filtering workspace context - workspace_id: {workspace.id}, workspace_name: {workspace.name}, user_id: {user}"
        )

        # Log detailed filter_value structure if it contains excludes filters
        if view_filter.filter_value:
            for filter_key, filter_config in view_filter.filter_value.items():
                if filter_config.get("key") == "excludes":
                    has_excludes_filter = True
                    log_perf(
                        f"EXCLUDES filter detected in inventory view_filter - filter_key: {filter_key}, filter_config: {filter_config}"
                    )

        # Store the conditions before applying view filters (similar to contact search fix)
        pre_filter_conditions = filter_conditions
        log_perf(
            f"Filter conditions before build_view_filter - query_str: {filter_conditions}",
        )

        filter_conditions = build_view_filter(
            filter_conditions, view_filter, TYPE_OBJECT_INVENTORY, user=user
        )

        # Log after applying view filter
        log_perf(
            f"Applied view filter for inventory - after_filter: {filter_conditions}, view_filter_type: {view_filter.view_type}",
        )

        # If we have excludes filters and build_view_filter resulted in no results,
        # check if the excludes filter is being overridden (similar to contact search fix)
        if has_excludes_filter and filter_conditions != pre_filter_conditions:
            try:
                # Check if this is due to conflicting filters by testing excludes filter alone
                excludes_only_filter = Q(workspace=workspace, status="active")
                excludes_only_filter &= get_permission_filter(permission, user)

                # Apply only the excludes filters
                excludes_view_filter = ViewFilter(view=view_filter.view)
                excludes_view_filter.filter_value = {
                    k: v
                    for k, v in view_filter.filter_value.items()
                    if v.get("key") == "excludes"
                }

                if excludes_view_filter.filter_value:
                    excludes_only_filter = build_view_filter(
                        excludes_only_filter,
                        excludes_view_filter,
                        TYPE_OBJECT_INVENTORY,
                        user=user,
                    )
                    excludes_only_count = ShopTurboInventory.objects.filter(
                        excludes_only_filter
                    ).count()

                    log_perf(
                        f"Excludes-only filter test - count: {excludes_only_count}",
                    )

            except Exception as e:
                logger.error(f"Error during excludes filter fallback check: {e}")

    current_inventory = True
    if view_filter.value:
        if "current_inventory" in view_filter.value:
            current_inventory = bool(view_filter.value["current_inventory"])
        inventory_status_conditions = Q()
        if (
            "show_available" in view_filter.value
            and view_filter.value["show_available"]
        ):
            inventory_status_conditions |= Q(inventory_status="available")
        if (
            "show_committed" in view_filter.value
            and view_filter.value["show_committed"]
        ):
            inventory_status_conditions |= Q(inventory_status="committed")
        if (
            "show_unavailable" in view_filter.value
            and view_filter.value["show_unavailable"]
        ):
            inventory_status_conditions |= Q(inventory_status="unavailable")
        if inventory_status_conditions:
            filter_conditions &= inventory_status_conditions
    # Log final filter conditions before query execution
    log_perf(
        f"Final inventory query filter conditions - filter: {filter_conditions}, current_inventory: {current_inventory}, view_filter.value {view_filter.value}",
    )

    # # PERFORMANCE: Generate cache key for inventory query results
    # # Cache for 5 minutes to balance performance and data freshness
    # cache_key_data = {
    #     "workspace_id": str(workspace.id) if hasattr(workspace, "id") else "none",
    #     "filter_conditions": str(filter_conditions),
    #     "search_q": search_q or "none",
    #     "pagination_number": pagination_number,
    #     "current_inventory": current_inventory,
    #     "columns": str(sorted(shopturbo_inventory_columns)),
    #     "view_filter_id": str(view_filter.id) if hasattr(view_filter, "id") else "none",
    # }
    # cache_key_string = str(cache_key_data)
    # cache_key = f"inventory_query_{hashlib.md5(cache_key_string.encode()).hexdigest()}"

    # log_perf("Load cache")
    # # Try to get cached results first
    # cached_result = cache.get(cache_key)
    # if cached_result is not None:
    #     log_perf(f"Using cached inventory query results - cache_key: {cache_key}")
    #     return cached_result

    # log_perf("no cache")

    if current_inventory:
        try:
            # Base query with essential relationships only
            base_query = ShopTurboInventory.objects.select_related(
                "workspace",
                "warehouse",
                "owner",  # For owner information
                "owner__user",  # For owner user details
                "owner__user__verification",  # For profile photos
            )

            # Check if custom fields are needed for display
            # Only prefetch custom fields if they're in the displayed columns
            load_custom_fields = False
            if shopturbo_inventory_columns:
                # Check if any custom field columns are being displayed
                for column in shopturbo_inventory_columns:
                    if is_valid_uuid(str(column)):
                        # This is likely a custom field UUID
                        load_custom_fields = True
                        break

            # ALWAYS prefetch items since templates always access inventory.item.all.first
            # This prevents N+1 queries for item access in templates
            prefetch_list = [
                Prefetch(
                    "item",
                    queryset=ShopTurboItems.objects.select_related(
                        "workspace", "contact", "company"
                    ).prefetch_related(
                        "shopturbo_item_custom_field_relations__field_name"
                    )
                    if load_custom_fields
                    else ShopTurboItems.objects.select_related(
                        "workspace", "contact", "company"
                    ),
                ),
                "item_variant",
                "inventory__channel",  # For platform data
            ]

            # Only add custom field prefetch if actually needed for display
            if load_custom_fields:
                prefetch_list.append(
                    Prefetch(
                        "shopturbo_inventory_custom_field_relations",
                        queryset=ShopTurboInventoryValueCustomField.objects.select_related(
                            "field_name"
                        ),
                    )
                )
                log_perf(
                    f"Loading custom fields for inventory display - columns: {shopturbo_inventory_columns}",
                )
            else:
                log_perf(
                    "Skipping custom field prefetch for better performance - no custom columns displayed"
                )

            shopturbo_inventories = (
                base_query.prefetch_related(*prefetch_list)
                .filter(date__lte=timezone.now())
                .filter(filter_conditions)
            )

            # Log query execution without expensive count
            log_perf("Inventory query executed (current_inventory=True)")
        except Exception as e:
            logger.error(
                f"Error executing inventory query with current_inventory=True: {e}",
            )
            logger.debug(e)
            # Fallback query with same optimization logic
            fallback_prefetch_list = [
                Prefetch(
                    "item",
                    queryset=ShopTurboItems.objects.select_related(
                        "workspace", "contact", "company"
                    ),
                ),
                "item_variant",
                "inventory__channel",
            ]

            # Only add custom fields in fallback if needed
            if load_custom_fields:
                fallback_prefetch_list.append(
                    Prefetch(
                        "shopturbo_inventory_custom_field_relations",
                        queryset=ShopTurboInventoryValueCustomField.objects.select_related(
                            "field_name"
                        ),
                    )
                )

            shopturbo_inventories = (
                ShopTurboInventory.objects.select_related(
                    "workspace",
                    "warehouse",
                    "owner",
                    "owner__user",
                    "owner__user__verification",
                )
                .prefetch_related(*fallback_prefetch_list)
                .filter(date__lte=timezone.now())
            )
    else:
        # Check if custom fields are needed for non-current inventory queries too
        load_custom_fields = False
        if shopturbo_inventory_columns:
            for column in shopturbo_inventory_columns:
                if is_valid_uuid(str(column)):
                    load_custom_fields = True
                    break

        # ALWAYS prefetch items since templates always access inventory.item.all.first
        # This prevents N+1 queries for item access in templates
        else_prefetch_list = [
            Prefetch(
                "item",
                queryset=ShopTurboItems.objects.select_related(
                    "workspace", "contact", "company"
                ).prefetch_related("shopturbo_item_custom_field_relations__field_name")
                if load_custom_fields
                else ShopTurboItems.objects.select_related(
                    "workspace", "contact", "company"
                ),
            ),
            "item_variant",
            "inventory__channel",
        ]

        if load_custom_fields:
            else_prefetch_list.append(
                Prefetch(
                    "shopturbo_inventory_custom_field_relations",
                    queryset=ShopTurboInventoryValueCustomField.objects.select_related(
                        "field_name"
                    ),
                )
            )

        shopturbo_inventories = (
            ShopTurboInventory.objects.select_related(
                "workspace",
                "warehouse",
                "owner",
                "owner__user",
                "owner__user__verification",
            )
            .prefetch_related(*else_prefetch_list)
            .filter(filter_conditions)
            .annotate(
                future_amount=Subquery(
                    InventoryTransaction.objects.filter(
                        inventory_id=OuterRef("id"),
                        usage_status="active",
                    )
                    .order_by("-transaction_date", "-created_at")
                    .values("transaction_amount")[:1]
                )
            )
        )

        # Log query execution without expensive count
        log_perf("Inventory query executed (current_inventory=False)")

    if excludes_only_filter:
        shopturbo_inventories = shopturbo_inventories.filter(excludes_only_filter)
    try:
        if view_filter.sort_order_by:
            order_method = view_filter.sort_order_method
            order_by = view_filter.sort_order_by

            if order_by == "items":
                order_by = "item__item_id"

            # Handle invalid field mapping for inventory
            if order_by == "purchase_price_currency":
                # Map to the correct field for inventory model
                order_by = "currency"
                logger.warning(
                    "WARNING: Mapped invalid field 'purchase_price_currency' to 'currency' for inventory sorting"
                )
            elif order_by == "purchase_price":
                # Purchase price field doesn't exist in inventory model, fallback to default sorting
                logger.warning(
                    "WARNING: Invalid field 'purchase_price' for inventory, using default sorting"
                )
                shopturbo_inventories = shopturbo_inventories.distinct(
                    "id", "inventory_id", "date", "created_at"
                ).order_by("-inventory_id", "-date", "-created_at")
            elif order_by == "supplier":
                # Supplier field doesn't exist in inventory model, fallback to default sorting
                logger.warning(
                    "WARNING: Invalid field 'supplier' for inventory, using default sorting"
                )
                shopturbo_inventories = shopturbo_inventories.distinct(
                    "id", "inventory_id", "date", "created_at"
                ).order_by("-inventory_id", "-date", "-created_at")
            elif is_valid_uuid(order_by):
                # Check if this is an inventory custom field
                if uuid.UUID(order_by) in helper.custom_inventory_field_ids:
                    field_name = ShopTurboInventoryNameCustomField.objects.filter(
                        id=order_by, workspace=workspace
                    ).first()
                    # Optimize custom field sorting with a single subquery
                    custom_value_subquery = (
                        ShopTurboInventoryValueCustomField.objects.filter(
                            inventory=OuterRef("pk"), field_name=field_name
                        ).values(
                            "value_time"
                            if field_name.type in ["date", "date_time"]
                            else "value"
                        )[:1]
                    )

                    # Apply sorting with a single annotation and order_by, including distinct
                    shopturbo_inventories = (
                        shopturbo_inventories.annotate(
                            custom_value=Subquery(custom_value_subquery)
                        )
                        .distinct("id", "custom_value")
                        .order_by(
                            "custom_value" if order_method == "asc" else "-custom_value"
                        )
                    )
                elif uuid.UUID(order_by) in helper.custom_item_field_ids:
                    # Check if this might be an item custom field instead

                    item_field = ShopTurboItemsNameCustomField.objects.filter(
                        id=order_by, workspace=workspace
                    ).first()

                    # Create a subquery to get the item custom field value
                    # Since item is a ManyToManyField, we need to use the correct relationship
                    item_custom_value_subquery = (
                        ShopTurboItemsValueCustomField.objects.filter(
                            items__in=OuterRef("item"), field_name=item_field
                        ).values(
                            "value_time"
                            if item_field.type in ["date", "date_time"]
                            else "value"
                        )[:1]
                    )

                    # Apply sorting through the item relationship
                    shopturbo_inventories = (
                        shopturbo_inventories.annotate(
                            item_custom_value=Subquery(item_custom_value_subquery)
                        )
                        .distinct("id", "item_custom_value")
                        .order_by(
                            "item_custom_value"
                            if order_method == "asc"
                            else "-item_custom_value"
                        )
                    )
                else:
                    # Custom field not found, fall back to default sorting
                    logger.debug(
                        f"WARNING: Custom field with ID '{order_by}' not found for inventory, using default sorting"
                    )
                    shopturbo_inventories = shopturbo_inventories.distinct(
                        "id", "inventory_id", "date", "created_at"
                    ).order_by("-inventory_id", "-date", "-created_at")

            elif "-SKU" in order_by or "-Inventory ID" in order_by:
                values = "platform_sku" if "-SKU" in order_by else "platform_id"
                name = (
                    order_by.replace("-SKU", "")
                    if "-SKU" in order_by
                    else order_by.replace("-Inventory ID", "")
                )
                channel = Channel.objects.filter(
                    workspace=workspace, name=name, integration__slug="shopify"
                ).first()
                inv_platform = ShopTurboInventoryPlatforms.objects.filter(
                    inventory=OuterRef("pk"), channel=channel
                ).values(values)[:1]

                shopturbo_inventories = shopturbo_inventories.annotate(
                    custom_value=Subquery(inv_platform)
                )

                if order_method == "asc":
                    shopturbo_inventories = shopturbo_inventories.distinct(
                        "id", "custom_value"
                    ).order_by("custom_value")
                else:
                    shopturbo_inventories = shopturbo_inventories.distinct(
                        "id", "custom_value"
                    ).order_by("-custom_value")
            else:
                # Validate field exists in model before applying order_by
                try:
                    # Check if field exists by trying to get the field from the model
                    ShopTurboInventory._meta.get_field(order_by.replace("-", ""))
                    if order_method == "asc":
                        shopturbo_inventories = shopturbo_inventories.distinct(
                            "id", order_by
                        ).order_by(order_by)
                    else:
                        shopturbo_inventories = shopturbo_inventories.distinct(
                            "id", order_by
                        ).order_by("-" + order_by)
                except:
                    # Field doesn't exist, fall back to default sorting
                    logger.debug(
                        f"WARNING: Invalid sort field '{order_by}' for inventory, using default sorting"
                    )
                    shopturbo_inventories = shopturbo_inventories.distinct(
                        "id", "inventory_id", "date", "created_at"
                    ).order_by("-inventory_id", "-date", "-created_at")
        else:
            shopturbo_inventories = shopturbo_inventories.distinct(
                "id", "inventory_id", "date", "created_at"
            ).order_by("-inventory_id", "-date", "-created_at")
    except Exception as e:
        logger.error(f"===== Error at ShopTurbo Items =====: {e}")
        shopturbo_inventories = shopturbo_inventories.distinct(
            "id", "inventory_id", "date", "created_at"
        ).order_by("inventory_id", "date", "created_at")

    # PERFORMANCE: Skip expensive count operations for large datasets
    # The paginator will handle counts more efficiently when needed
    # We'll use a fast approximate count for display purposes

    # Quick check if we have a lot of data
    # has_many_records = shopturbo_inventories[:1001].count() > 1000

    # if has_many_records:
    #     # For large datasets, use approximate count
    #     total_count = "1000+"  # Display as string to indicate it's approximate
    #     log_perf("Large inventory dataset, using approximate count for performance")
    # else:
    #     # For smaller datasets, get exact count
    #     total_count = shopturbo_inventories.count()

    # Create paginator for template rendering
    log_perf(f"paginate to {pagination_number} items per page")
    paginator = Paginator(shopturbo_inventories, pagination_number)

    result = {
        "paginator": paginator,
        "object_qs": shopturbo_inventories,
        "object_count": paginator.count,
        "extra": {
            # "all_ids": shopturbo_inventories.values_list("id", flat=True),
            "filter_conditions": filter_conditions,
        },
    }

    # PERFORMANCE: Cache the query results for 5 minutes
    # This significantly improves performance for repeated identical queries
    # cache.set(cache_key, result, timeout=300)  # 5 minutes
    # log_perf(
    #     f"Cached inventory query results - cache_key: {cache_key}, total count: {paginator.count}"
    # )

    return result
