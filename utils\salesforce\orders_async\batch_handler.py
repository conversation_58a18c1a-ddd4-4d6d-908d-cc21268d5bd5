"""
Batch handler for database operations.
Handles batch upserts of Orders and related models with proper associations.
"""
import re
import asyncio
from typing import Dict, List, Any
from dataclasses import dataclass

from data.models import (
    ShopTurboOrders,
    ShopTurboOrdersPlatforms,
    ShopTurboItemsOrders,
    ShopTurboItemsPlatforms,  # Still needed for item lookups (not line items)
    ShopTurboOrdersMappingFields,
    CompanyPlatforms,
    ContactsPlatforms,
    Deals,
    DealsPlatforms,
)
from utils.error_logger.import_export_logger import ImportExportLogger
from utils.salesforce.utility import get_bulk_owner_emails_by_owner_ids
from .config import DB_BATCH_SIZE, DEFAULT_CURRENCY


@dataclass
class BatchResult:
    """Result of a batch operation."""

    total_success: int
    failed_count: int
    failed_records: List[Dict[str, Any]]


class OrdersBatchHandler:
    """
    Handles batch database operations for Orders import.
    """

    # Configuration for progress logging
    PROGRESS_LOG_INTERVAL = 25  # Log progress every 25 orders
    LINE_ITEM_LOG_THRESHOLD = 50  # Log when order has more than this many line items

    def __init__(
        self,
        workspace_id: str,
        channel_id: str,
        error_logger: ImportExportLogger,
    ):
        self.workspace_id = workspace_id
        self.channel_id = channel_id
        self.error_logger = error_logger

        # Caches for lookups
        self.company_cache: Dict[str, str] = {}
        self.contact_cache: Dict[str, str] = {}
        self.item_cache: Dict[str, str] = {}
        self.user_cache: Dict[str, str] = {}
        self.deal_cache: Dict[str, str] = {}

        # Mappings for tracking
        self.opportunity_to_order_map: Dict[str, str] = {}
        self.line_item_to_item_order_map: Dict[str, str] = {}

    async def initialize_caches(self, owner_ids: List[str] = None) -> None:
        """Initialize lookup caches from database.
        
        Args:
            owner_ids: Optional list of Salesforce owner IDs to fetch emails for
        """
        await self._load_company_cache()
        await self._load_contact_cache()
        await self._load_item_cache()
        await self._load_user_cache(owner_ids)
        await self._load_deal_cache()

        await self.error_logger.alog_info(
            "OrdersBatchHandler",
            f"Initialized caches - Companies: {len(self.company_cache)}, "
            f"Contacts: {len(self.contact_cache)}, Items: {len(self.item_cache)}, "
            f"Users: {len(self.user_cache)}",
        )

    async def _load_company_cache(self) -> None:
        """Load company cache for AccountId lookups."""
        try:
            # Get companies with Salesforce platform records
            companies = CompanyPlatforms.objects.filter(
                channel_id=self.channel_id,
            ).select_related("company")

            async for comp_platform in companies:
                if comp_platform.platform_id and comp_platform.company:
                    self.company_cache[comp_platform.platform_id] = str(
                        comp_platform.company.id
                    )
        except Exception as e:
            await self.error_logger.alog_warning(
                "OrdersBatchHandler", f"Error loading company cache: {str(e)}"
            )

    async def _load_contact_cache(self) -> None:
        """Load contact cache for ContactId lookups."""
        try:
            # Get contacts with Salesforce platform records
            contacts = ContactsPlatforms.objects.filter(
                channel_id=self.channel_id,
            ).select_related("contact")

            async for cont_platform in contacts:
                if cont_platform.platform_id and cont_platform.contact:
                    self.contact_cache[cont_platform.platform_id] = str(
                        cont_platform.contact.id
                    )
        except Exception as e:
            await self.error_logger.alog_warning(
                "OrdersBatchHandler", f"Error loading contact cache: {str(e)}"
            )

    async def _load_item_cache(self) -> None:
        """Load item cache for Product2Id lookups."""
        try:
            # Get items with Salesforce platform records
            items = ShopTurboItemsPlatforms.objects.filter(
                channel_id=self.channel_id,
            ).select_related("item")

            async for item_platform in items:
                if item_platform.platform_id and item_platform.item:
                    self.item_cache[item_platform.platform_id] = str(
                        item_platform.item.id
                    )
        except Exception as e:
            await self.error_logger.alog_warning(
                "OrdersBatchHandler", f"Error loading item cache: {str(e)}"
            )

    async def _load_user_cache(self, owner_ids: List[str] = None) -> None:
        """Load user cache for OwnerId lookups from Salesforce.
        
        Args:
            owner_ids: List of Salesforce owner IDs to fetch emails for
        """
        try:
            if not owner_ids:
                # If no owner IDs provided, skip loading
                await self.error_logger.alog_info(
                    "OrdersBatchHandler", 
                    "No owner IDs provided, skipping user cache loading"
                )
                return
            
            # Remove duplicates
            unique_owner_ids = list(set(owner_ids))
            
            await self.error_logger.alog_info(
                "OrdersBatchHandler",
                f"Fetching emails for {len(unique_owner_ids)} unique owner IDs from Salesforce"
            )
            
            # Fetch emails from Salesforce using synchronous function in thread
            owner_emails = await asyncio.to_thread(
                get_bulk_owner_emails_by_owner_ids,
                self.channel_id,
                unique_owner_ids,
                self.error_logger
            )
            
            # Map owner IDs to their emails in the cache
            # The cache maps owner_id -> email for lookups
            for owner_id, email in owner_emails.items():
                if email:
                    # Map by owner_id for lookups`
                    self.user_cache[owner_id] = email
            
            await self.error_logger.alog_info(
                "OrdersBatchHandler",
                f"Successfully loaded {len(owner_emails)} owner email mappings from Salesforce"
            )
            
        except Exception as e:
            await self.error_logger.alog_warning(
                "OrdersBatchHandler", f"Error loading user cache from Salesforce: {str(e)}"
            )

    async def _load_deal_cache(self) -> None:
        """Load deal/case cache for associations."""
        try:
            # Get deals with Salesforce platform records
            deals = DealsPlatforms.objects.filter(
                channel_id=self.channel_id,
            ).select_related("deal")

            async for deal_platform in deals:
                if deal_platform.platform_id and deal_platform.deal:
                    self.deal_cache[deal_platform.platform_id] = str(
                        deal_platform.deal.id
                    )
        except Exception as e:
            await self.error_logger.alog_warning(
                "OrdersBatchHandler", f"Error loading deal cache: {str(e)}"
            )

    async def save_orders_batch(
        self,
        orders_data: List[Dict[str, Any]],
    ) -> BatchResult:
        """
        Save a batch of orders with their line items using bulk operations.

        Args:
            orders_data: List of order data with line items

        Returns:
            BatchResult with success/failure counts
        """
        success_count = 0
        failed_count = 0
        failed_records = []
        total_in_batch = len(orders_data)

        # Log batch start
        await self.error_logger.alog_info(
            "OrdersBatchHandler",
            f"Processing batch of {total_in_batch} orders",
        )

        try:
            # Prepare collections for bulk operations
            orders_to_create = []
            orders_to_update = []
            platforms_to_create = []
            platforms_to_update = []
            line_items_to_create = []
            line_items_to_update = []
            
            # Association collections
            order_company_associations = []
            order_contact_associations = []
            order_deal_associations = []
            
            # Maps for tracking created entities
            salesforce_to_order = {}
            order_data_map = {}

            # First pass: Prepare all orders
            await self._prepare_orders_for_bulk(
                orders_data,
                orders_to_create,
                orders_to_update,
                salesforce_to_order,
                order_data_map,
            )

            # Bulk create new orders
            if orders_to_create:
                created_orders = await ShopTurboOrders.objects.abulk_create(
                    orders_to_create,
                    batch_size=DB_BATCH_SIZE,
                    ignore_conflicts=True
                )
                
                # Update mapping with created orders
                for order in created_orders:
                    if hasattr(order, '_salesforce_id'):
                        salesforce_to_order[order._salesforce_id] = order
                        self.opportunity_to_order_map[order._salesforce_id] = str(order.id)

            # Bulk update existing orders
            if orders_to_update:
                await ShopTurboOrders.objects.abulk_update(
                    orders_to_update,
                    fields=[
                        "total_price", "order_at", "status",
                        "memo", "order_type", "delivery_status",
                        "company_id", "contact_id", "platform", "updated_at",
                        "total_price_without_tax", "tax", "currency"
                    ],
                    batch_size=DB_BATCH_SIZE
                )
                
                # Update mapping for updated orders
                for order in orders_to_update:
                    if hasattr(order, '_salesforce_id'):
                        salesforce_to_order[order._salesforce_id] = order
                        self.opportunity_to_order_map[order._salesforce_id] = str(order.id)

            # Prepare platform records
            await self._prepare_platforms_for_bulk(
                salesforce_to_order,
                order_data_map,
                platforms_to_create,
                platforms_to_update,
            )

            # Bulk create/update platform records
            if platforms_to_create:
                await ShopTurboOrdersPlatforms.objects.abulk_create(
                    platforms_to_create,
                    batch_size=DB_BATCH_SIZE,
                    ignore_conflicts=True
                )

            if platforms_to_update:
                await ShopTurboOrdersPlatforms.objects.abulk_update(
                    platforms_to_update,
                    fields=["platform_order_id"],
                    batch_size=DB_BATCH_SIZE
                )

            # Process line items in bulk
            await self._prepare_line_items_for_bulk(
                salesforce_to_order,
                order_data_map,
                line_items_to_create,
                line_items_to_update,
            )

            # Bulk create/update line items
            if line_items_to_create:
                created_line_items = await ShopTurboItemsOrders.objects.abulk_create(
                    line_items_to_create,
                    batch_size=DB_BATCH_SIZE,
                    ignore_conflicts=True
                )
                
                # Track line item mappings
                for line_item in created_line_items:
                    if hasattr(line_item, '_salesforce_id'):
                        self.line_item_to_item_order_map[line_item._salesforce_id] = str(line_item.id)

            if line_items_to_update:
                await ShopTurboItemsOrders.objects.abulk_update(
                    line_items_to_update,
                    fields=[
                        "number_item", "item_price_order", "total_price",
                        "item_id", "platform_item_id", "updated_at"
                    ],
                    batch_size=DB_BATCH_SIZE
                )

            # Line item platform records not needed - Salesforce ID stored in platform_item_id

            # Prepare and process associations
            await self._prepare_associations_for_bulk(
                salesforce_to_order,
                order_data_map,
                order_company_associations,
                order_contact_associations,
                order_deal_associations,
            )

            # Bulk process associations
            await self._bulk_process_associations(
                order_company_associations,
                order_contact_associations,
                order_deal_associations,
            )

            success_count = len(salesforce_to_order)
            
            await self.error_logger.alog_info(
                "OrdersBatchHandler",
                f"Batch save completed - Success: {success_count}, Failed: {failed_count}",
            )

        except Exception as e:
            import traceback
            failed_count = total_in_batch
            import traceback
            traceback.print_exc()
            await self.error_logger.alog_error(
                "BATCH_SAVE_ERROR",
                "OrdersBatchHandler",
                f"Batch operation failed: {str(e)}\n{traceback.format_exc()}",
                e,
            )
            failed_records = orders_data

        return BatchResult(success_count, failed_count, failed_records)

    async def _upsert_order(self, order_data: Dict[str, Any]) -> ShopTurboOrders:
        """
        Create or update an order.

        Args:
            order_data: Processed order data

        Returns:
            Created or updated order instance
        """
        # Get or create order based on Salesforce ID
        salesforce_id = order_data.get("salesforce_id")

        # Check if order already exists via platform record
        existing_platform = (
            await ShopTurboOrdersPlatforms.objects.filter(
                platform_order_id=salesforce_id,
                channel_id=self.channel_id,
            )
            .select_related("order")
            .afirst()
        )

        if existing_platform and existing_platform.order:
            order = existing_platform.order
            # Update existing order
            for field, value in order_data.items():
                if field not in [
                    "salesforce_id",
                    "workspace_id",
                    "channel_id",
                    "custom_fields",
                    "owner_id",  # Excluded - handled as custom field
                ]:
                    setattr(order, field, value)
        else:
            # Create new order
            order = ShopTurboOrders(workspace_id=self.workspace_id)

            # Generate order_id to avoid sync save() method logic
            if not order_data.get("order_id"):
                # Get the last order_id asynchronously
                last_order = (
                    await ShopTurboOrders.objects.filter(
                        workspace_id=self.workspace_id,
                        order_id__isnull=False,
                    )
                    .order_by("-order_id")
                    .afirst()
                )

                if last_order and last_order.order_id:
                    order.order_id = last_order.order_id + 1
                else:
                    order.order_id = 1

            for field, value in order_data.items():
                if field not in [
                    "salesforce_id",
                    "channel_id",
                    "custom_fields",
                    "owner_id",
                ]:
                    setattr(order, field, value)

        # Set company and contact from cache
        if order_data.get("account_id"):
            company_id = self.company_cache.get(order_data["account_id"])
            if company_id:
                order.company_id = company_id

        if order_data.get("contact_id"):
            contact_id = self.contact_cache.get(order_data["contact_id"])
            if contact_id:
                order.contact_id = contact_id

        # OwnerId is handled as custom field, not mapped to order.owner_id

        try:
            await order.asave()
        except Exception as save_error:
            await self.error_logger.alog_error(
                "SAVE_ERROR",
                "OrdersBatchHandler._upsert_order",
                f"Error during order.asave(): {str(save_error)}",
                save_error,
            )
            raise

        # Track mapping
        self.opportunity_to_order_map[salesforce_id] = str(order.id)

        return order

    async def _create_order_platform(
        self,
        order: ShopTurboOrders,
        order_data: Dict[str, Any],
    ) -> None:
        """
        Create or update order platform record.

        Args:
            order: Order instance
            order_data: Order data with Salesforce ID
        """
        salesforce_id = order_data.get("salesforce_id")

        try:
            await ShopTurboOrdersPlatforms.objects.aupdate_or_create(
                order=order,
                channel_id=self.channel_id,
                defaults={
                    "platform_order_id": salesforce_id,
                    # Removed workspace_id, platform, platform_url as these fields don't exist
                },
            )
        except Exception as platform_error:
            await self.error_logger.alog_error(
                "PLATFORM_ERROR",
                "OrdersBatchHandler._create_order_platform",
                f"Error creating platform record: {str(platform_error)}",
                platform_error,
            )
            raise

    async def _process_line_items(
        self,
        order: ShopTurboOrders,
        line_items: List[Dict[str, Any]],
    ) -> None:
        """
        Process and save line items for an order.

        Args:
            order: Parent order instance
            line_items: List of line item data
        """
        for line_item_data in line_items:
            try:
                # Create or update line item
                line_item = await self._upsert_line_item(order, line_item_data)

                # Platform record not needed - Salesforce ID already stored in platform_item_id

                # Track mapping
                sf_id = line_item_data.get("salesforce_id")
                if sf_id:
                    self.line_item_to_item_order_map[sf_id] = str(line_item.id)

            except Exception as e:
                await self.error_logger.alog_error(
                    "LINE_ITEM_ERROR",
                    "OrdersBatchHandler",
                    f"Failed to process line item {line_item_data.get('salesforce_id')}: {str(e)}",
                    e,
                )

    async def _upsert_line_item(
        self,
        order: ShopTurboOrders,
        line_item_data: Dict[str, Any],
    ) -> ShopTurboItemsOrders:
        """
        Create or update a line item.

        Args:
            order: Parent order
            line_item_data: Line item data

        Returns:
            Created or updated line item instance
        """
        salesforce_id = line_item_data.get("salesforce_id")

        # Check if line item already exists by platform_item_id
        existing_line_item = await ShopTurboItemsOrders.objects.filter(
            platform_item_id=salesforce_id,
            order__workspace_id=self.workspace_id
        ).afirst()

        if existing_line_item:
            line_item = existing_line_item
            # Update existing line item
            for field, value in line_item_data.items():
                if field not in [
                    "salesforce_id",
                    "opportunity_id",
                    "workspace_id",
                    "custom_fields",
                ]:
                    setattr(line_item, field, value)
        else:
            # Create new line item
            line_item = ShopTurboItemsOrders(order=order)
            for field, value in line_item_data.items():
                if field not in [
                    "salesforce_id",
                    "opportunity_id",
                    "workspace_id",
                    "custom_fields",
                ]:
                    setattr(line_item, field, value)

        # Set item from cache if Product2Id is present
        if line_item_data.get("product_id"):
            item_id = self.item_cache.get(line_item_data["product_id"])
            if item_id:
                line_item.item_id = item_id

        # Set platform item ID
        if salesforce_id:
            line_item.platform_item_id = salesforce_id

        await line_item.asave()

        return line_item

    async def _process_order_associations(
        self,
        order: ShopTurboOrders,
        order_data: Dict[str, Any],
    ) -> None:
        """
        Process associations for an order (companies, contacts, deals).

        Args:
            order: Order instance
            order_data: Order data with association information
        """
        # Associate with companies
        if order.company_id:
            try:
                # Fetch company in thread to avoid async context issues
                company = await asyncio.to_thread(lambda: order.company)
                await asyncio.to_thread(order.companies.add, company)
            except Exception as company_error:
                await self.error_logger.alog_error(
                    "ASSOCIATION_ERROR",
                    "OrdersBatchHandler._process_order_associations",
                    f"Error adding company association: {str(company_error)}",
                    company_error,
                )
                raise

        # Associate with contacts
        if order.contact_id:
            try:
                # Fetch contact in thread to avoid async context issues
                contact = await asyncio.to_thread(lambda: order.contact)
                await asyncio.to_thread(order.contacts.add, contact)
            except Exception as contact_error:
                await self.error_logger.alog_error(
                    "ASSOCIATION_ERROR",
                    "OrdersBatchHandler._process_order_associations",
                    f"Error adding contact association: {str(contact_error)}",
                    contact_error,
                )
                raise

        # Associate with deals/cases if mapping exists
        # This would come from custom field mappings or specific logic

        # For now, we'll check if there's a deal association in custom fields
        custom_fields = order_data.get("custom_fields", {})
        deal_id = custom_fields.get("deal_id")

        if deal_id and deal_id in self.deal_cache:
            try:
                deal = await Deals.objects.aget(id=self.deal_cache[deal_id])
                await asyncio.to_thread(order.cases.add, deal)
            except Deals.DoesNotExist:
                pass

    async def fix_order_ids(self) -> None:
        """
        Fix order_id sequence for newly created orders.
        Similar to fix_company_ids in companies_async.
        """
        try:
            # Get orders without order_id
            orders_to_fix = ShopTurboOrders.objects.filter(
                workspace_id=self.workspace_id,
                order_id__isnull=True,
            ).order_by("created_at")

            # Get the last order_id
            last_order = (
                await ShopTurboOrders.objects.filter(
                    workspace_id=self.workspace_id,
                    order_id__isnull=False,
                )
                .order_by("-order_id")
                .afirst()
            )

            if last_order and last_order.order_id:
                next_id = last_order.order_id + 1
            else:
                next_id = 1

            # Update orders with sequential IDs
            async for order in orders_to_fix:
                order.order_id = next_id
                await order.asave(update_fields=["order_id"])
                next_id += 1

            await self.error_logger.alog_info(
                "OrdersBatchHandler",
                f"Fixed order_id sequence for workspace {self.workspace_id}",
            )

        except Exception as e:
            await self.error_logger.alog_warning(
                "OrdersBatchHandler", f"Error fixing order IDs: {str(e)}"
            )

    def get_opportunity_to_order_map(self) -> Dict[str, str]:
        """Get the mapping of Opportunity IDs to Order IDs."""
        return self.opportunity_to_order_map

    def get_line_item_to_item_order_map(self) -> Dict[str, str]:
        """Get the mapping of OpportunityLineItem IDs to ItemOrder IDs."""
        return self.line_item_to_item_order_map

    async def _prepare_orders_for_bulk(
        self,
        orders_data: List[Dict[str, Any]],
        orders_to_create: List[ShopTurboOrders],
        orders_to_update: List[ShopTurboOrders],
        salesforce_to_order: Dict[str, ShopTurboOrders],
        order_data_map: Dict[str, Dict[str, Any]],
    ) -> None:
        """
        Prepare orders for bulk create/update operations.
        
        Args:
            orders_data: Raw order data from processor
            orders_to_create: List to populate with new orders
            orders_to_update: List to populate with existing orders
            salesforce_to_order: Mapping of Salesforce ID to order instance
            order_data_map: Mapping of Salesforce ID to order data
        """
        # Get existing orders in bulk
        salesforce_ids = [od.get("salesforce_id") for od in orders_data if od.get("salesforce_id")]
        
        existing_platforms = {}
        if salesforce_ids:
            platforms = ShopTurboOrdersPlatforms.objects.filter(
                platform_order_id__in=salesforce_ids,
                channel_id=self.channel_id,
            ).select_related("order")
            
            async for platform in platforms:
                if platform.order:
                    existing_platforms[platform.platform_order_id] = platform.order

        # Get the last order_id for new orders
        last_order = await ShopTurboOrders.objects.filter(
            workspace_id=self.workspace_id,
            order_id__isnull=False,
        ).order_by("-order_id").afirst()
        
        next_order_id = (last_order.order_id + 1) if last_order and last_order.order_id else 1

        # Process each order
        for order_data in orders_data:
            salesforce_id = order_data.get("salesforce_id")
            order_data_map[salesforce_id] = order_data
            
            # Extract line items for later processing
            line_items = order_data.pop("line_items", [])
            order_data["_line_items"] = line_items  # Store for later use
            
            if salesforce_id in existing_platforms:
                # Update existing order
                order = existing_platforms[salesforce_id]
                
                # Update fields
                for field, value in order_data.items():
                    if field not in [
                        "salesforce_id", "workspace_id", "channel_id",
                        "custom_fields", "owner_id", "_line_items",
                        "contact_id", "account_id"
                    ]:
                        if field == "total_price":
                            if value:
                                if isinstance(value, float):
                                    value = str(value)
                                elif isinstance(value, str):
                                    value = re.sub(r'[^0-9.]', '', value)
                                value = float(value)
                            else:
                                value = 0
                        setattr(order, field, value)
                
                # Set company and contact from cache
                if order_data.get("account_id"):
                    company_id = self.company_cache.get(order_data["account_id"])
                    if company_id:
                        order.company_id = company_id
                
                if order_data.get("contact_id"):
                    contact_id = self.contact_cache.get(order_data["contact_id"])
                    if contact_id:
                        order.contact_id = contact_id
                
                # Set default currency if not provided
                if not order.currency:
                    order.currency = DEFAULT_CURRENCY
                
                # Store salesforce_id for tracking
                order._salesforce_id = salesforce_id
                orders_to_update.append(order)
                salesforce_to_order[salesforce_id] = order
                
            else:
                # Create new order
                order = ShopTurboOrders(
                    workspace_id=self.workspace_id,
                    order_id=next_order_id
                )
                next_order_id += 1
                
                # Set fields
                for field, value in order_data.items():
                    if field not in [
                        "salesforce_id", "channel_id", "custom_fields",
                        "owner_id", "_line_items",
                        "contact_id", "account_id"
                    ]:
                        if field == "total_price":
                            if value:
                                if isinstance(value, float):
                                    value = float(value)
                                elif isinstance(value, str):
                                    value = re.sub(r'[^0-9.]', '', value)
                                    value = float(value)
                                elif isinstance(value, int):
                                    value = float(value)
                                else:
                                    value = 0
                            else:
                                value = 0
                        setattr(order, field, value)
                
                # Set company and contact from cache
                if order_data.get("account_id"):
                    company_id = self.company_cache.get(order_data["account_id"])
                    if company_id:
                        order.company_id = company_id
                
                if order_data.get("contact_id"):
                    contact_id = self.contact_cache.get(order_data["contact_id"])
                    if contact_id:
                        order.contact_id = contact_id
                
                # Set default currency if not provided
                if not order.currency:
                    order.currency = DEFAULT_CURRENCY
                
                # Store salesforce_id for tracking
                order._salesforce_id = salesforce_id
                orders_to_create.append(order)
                salesforce_to_order[salesforce_id] = order

    async def _prepare_platforms_for_bulk(
        self,
        salesforce_to_order: Dict[str, ShopTurboOrders],
        order_data_map: Dict[str, Dict[str, Any]],  # Reserved for future use
        platforms_to_create: List[ShopTurboOrdersPlatforms],
        platforms_to_update: List[ShopTurboOrdersPlatforms],
    ) -> None:
        """
        Prepare platform records for bulk operations.
        """
        # Get existing platform records
        order_ids = [order.id for order in salesforce_to_order.values()]
        
        existing_platforms = {}
        if order_ids:
            platforms = ShopTurboOrdersPlatforms.objects.filter(
                order_id__in=order_ids,
                channel_id=self.channel_id,
            )
            
            async for platform in platforms:
                existing_platforms[platform.order_id] = platform

        # Create or update platform records
        for salesforce_id, order in salesforce_to_order.items():
            if order.id in existing_platforms:
                # Update existing platform
                platform = existing_platforms[order.id]
                platform.platform_order_id = salesforce_id
                platforms_to_update.append(platform)
            else:
                # Create new platform
                platform = ShopTurboOrdersPlatforms(
                    order=order,
                    channel_id=self.channel_id,
                    platform_order_id=salesforce_id,
                )
                platforms_to_create.append(platform)

    async def _prepare_line_items_for_bulk(
        self,
        salesforce_to_order: Dict[str, ShopTurboOrders],
        order_data_map: Dict[str, Dict[str, Any]],
        line_items_to_create: List[ShopTurboItemsOrders],
        line_items_to_update: List[ShopTurboItemsOrders],
    ) -> None:
        """
        Prepare line items for bulk operations.
        """
        # Collect all line item salesforce IDs
        all_line_item_sf_ids = []
        for order_data in order_data_map.values():
            for line_item in order_data.get("_line_items", []):
                sf_id = line_item.get("salesforce_id")
                if sf_id:
                    all_line_item_sf_ids.append(sf_id)

        # Get existing line items directly by platform_item_id
        existing_line_items = {}
        if all_line_item_sf_ids:
            existing_line_items_qs = ShopTurboItemsOrders.objects.filter(
                platform_item_id__in=all_line_item_sf_ids,
                order__workspace_id=self.workspace_id
            ).select_related("order")
            
            async for line_item in existing_line_items_qs:
                if line_item.platform_item_id:
                    existing_line_items[line_item.platform_item_id] = line_item

        # Process line items for each order
        for salesforce_id, order in salesforce_to_order.items():
            order_data = order_data_map.get(salesforce_id, {})
            line_items_data = order_data.get("_line_items", [])
            
            for line_item_data in line_items_data:
                line_item_sf_id = line_item_data.get("salesforce_id")
                
                if line_item_sf_id in existing_line_items:
                    # Update existing line item
                    line_item = existing_line_items[line_item_sf_id]
                    
                    for field, value in line_item_data.items():
                        if field not in [
                            "salesforce_id", "opportunity_id", "workspace_id",
                            "custom_fields"
                        ]:
                            if field == "total_price":
                                if value:
                                    if isinstance(value, float):
                                        value = float(value)
                                    elif isinstance(value, str):
                                        value = re.sub(r'[^0-9.]', '', value)
                                        value = float(value)
                                    elif isinstance(value, int):
                                        value = float(value)
                                    else:
                                        value = 0
                                else:
                                    value = 0

                            setattr(line_item, field, value)
                    
                    # Set item from cache
                    if line_item_data.get("product_id"):
                        item_id = self.item_cache.get(line_item_data["product_id"])
                        if item_id:
                            line_item.item_id = item_id
                    
                    if line_item_sf_id:
                        line_item.platform_item_id = line_item_sf_id
                    
                    line_item._salesforce_id = line_item_sf_id
                    line_items_to_update.append(line_item)
                    
                else:
                    # Create new line item
                    line_item = ShopTurboItemsOrders(order=order)
                    
                    for field, value in line_item_data.items():
                        if field not in [
                            "salesforce_id", "opportunity_id", "workspace_id",
                            "custom_fields"
                        ]:
                            if field == "total_price":
                                if value:
                                    try:
                                        value = re.sub(r'[^0-9.]', '', value)
                                        value = float(value)
                                    except:
                                        value = float(value)
                                else:
                                    value = 0
                            setattr(line_item, field, value)
                    
                    # Set item from cache
                    if line_item_data.get("product_id"):
                        item_id = self.item_cache.get(line_item_data["product_id"])
                        if item_id:
                            line_item.item_id = item_id
                    
                    if line_item_sf_id:
                        line_item.platform_item_id = line_item_sf_id
                    
                    line_item._salesforce_id = line_item_sf_id
                    line_items_to_create.append(line_item)
                    
                    # Platform record not needed - Salesforce ID already stored in platform_item_id

    async def _prepare_associations_for_bulk(
        self,
        salesforce_to_order: Dict[str, ShopTurboOrders],
        order_data_map: Dict[str, Dict[str, Any]],
        order_company_associations: List[tuple],
        order_contact_associations: List[tuple],
        order_deal_associations: List[tuple],
    ) -> None:
        """
        Prepare associations for bulk operations.
        """
        for salesforce_id, order in salesforce_to_order.items():
            order_data = order_data_map.get(salesforce_id, {})
            
            # Company associations
            if order.company_id:
                order_company_associations.append((order.id, order.company_id))
            
            # Contact associations
            if order.contact_id:
                order_contact_associations.append((order.id, order.contact_id))
            
            # Deal associations from custom fields
            custom_fields = order_data.get("custom_fields", {})
            deal_id = custom_fields.get("deal_id")
            
            if deal_id and deal_id in self.deal_cache:
                order_deal_associations.append((order.id, self.deal_cache[deal_id]))

    async def _bulk_process_associations(
        self,
        order_company_associations: List[tuple],
        order_contact_associations: List[tuple],
        order_deal_associations: List[tuple],
    ) -> None:
        """
        Process associations using bulk operations.
        """
        # Process company associations
        if order_company_associations:
            # Get the through model for the ManyToMany relationship
            CompanyThrough = ShopTurboOrders.companies.through
            company_throughs = [
                CompanyThrough(shopturboorders_id=order_id, company_id=company_id)
                for order_id, company_id in order_company_associations
            ]
            await CompanyThrough.objects.abulk_create(
                company_throughs,
                batch_size=DB_BATCH_SIZE,
                ignore_conflicts=True
            )
        
        # Process contact associations
        if order_contact_associations:
            # Get the through model for the ManyToMany relationship
            ContactThrough = ShopTurboOrders.contacts.through
            contact_throughs = [
                ContactThrough(shopturboorders_id=order_id, contact_id=contact_id)
                for order_id, contact_id in order_contact_associations
            ]
            await ContactThrough.objects.abulk_create(
                contact_throughs,
                batch_size=DB_BATCH_SIZE,
                ignore_conflicts=True
            )
        
        # Process deal/case associations
        if order_deal_associations:
            # Get the through model for the ManyToMany relationship
            CaseThrough = ShopTurboOrders.cases.through
            case_throughs = [
                CaseThrough(shopturboorders_id=order_id, deals_id=deal_id)
                for order_id, deal_id in order_deal_associations
            ]
            await CaseThrough.objects.abulk_create(
                case_throughs,
                batch_size=DB_BATCH_SIZE,
                ignore_conflicts=True
            )

    async def process_field_mappings(
        self,
        field_mapping: Dict[str, Dict],
    ) -> None:
        """
        Save field mapping configuration to database.

        Args:
            field_mapping: Field mapping configuration
        """
        try:
            # Get the channel to access the integration slug
            from data.models import Channel

            channel = await Channel.objects.select_related("integration").aget(
                id=self.channel_id
            )

            # Store the entire field mapping as JSON in input_data field
            # await ShopTurboOrdersMappingFields.objects.aupdate_or_create(
            #     workspace_id=self.workspace_id,
            #     platform=channel.integration.slug
            #     if channel.integration
            #     else "salesforce",
            #     channel_id=self.channel_id,
            #     defaults={
            #         "input_data": field_mapping,
            #     },
            # )

            await self.error_logger.alog_info(
                "OrdersBatchHandler",
                f"Saved field mapping configuration with {len(field_mapping)} fields",
            )
        except Exception as e:
            await self.error_logger.alog_warning(
                "OrdersBatchHandler",
                f"Error saving field mappings: {str(e)}",
            )
