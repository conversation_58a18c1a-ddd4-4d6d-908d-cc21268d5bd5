{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}


<div class="{% include "data/utility/manage-sync.html" %}">
    <div class="card-header" id="kt_help_header">
        <h5 class="{% include "data/utility/card-header.html" %}">
            {% if LANGUAGE_CODE == 'ja'%}
            機能設定 - {{app.title_ja}}
            {% else %}
            Feature Settings - {{app.title}}
            {% endif %}
        </h5>
        <div class="card-toolbar">
            <button type="button" class="btn btn-sm btn-icon explore-btn-dismiss me-n5" data-kt-drawer-dismiss="true">
                <span class="svg-icon svg-icon-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="black"></rect>
                        <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="black"></rect>
                    </svg>
                </span>
            </button>
        </div>
    </div>

    <div class="card-body">
        <div id="change-setting-bill-section" class="mb-10">
            {% if 'payables' in setting_type %}
            <form method="POST" action="{% host_url 'expense_settings' host 'app' %}" class="mb-15" id="expense_setting" enctype="multipart/form-data">
                <div class="justify-content-between d-flex">
                    <div class="d-flex d-flex align-items-end">
                        <div class="me-2">
                            <a href="{% host_url 'workspace_setting' host 'app' %}?setting_type=payables" {% if setting_type == "payables" or setting_type == "payables-bills" %} class="fs-2 fw-bolder text-dark" {%else%} class="fs-2 fw-bold text-gray-500" {%endif%}>
                                {% if LANGUAGE_CODE == 'ja'%}
                                支払請求
                                {% else %}
                                Bills
                                {% endif %}
                            </a>
                        </div>  
                        <div class="me-2">
                            <a href="{% host_url 'workspace_setting' host 'app' %}?setting_type=payables-expense" {% if setting_type == "payables-expense" %} class="fs-2 fw-bolder text-dark" {%else%} class="fs-2 fw-bold text-gray-500" {%endif%}>
                                {% if LANGUAGE_CODE == 'ja'%}
                                経費
                                {% else %}
                                Expense
                                {% endif %}
                            </a>
                        </div>

                    </div>
                </div>
                {% csrf_token %}
                <script>
                    $('.select2-this').select2();
                    checkbox = document.getElementById('base_currency')
                    checkbox.addEventListener('change', function() {
                        if (this.checked) {
                            document.querySelector('.currency_section').classList.remove('d-none')
                        } 
                        else {
                            
                            document.querySelector('.currency_section').classList.add('d-none')
                        }
                    })

                    var category_expense = document.querySelector("#category_expense");
                    var category = new Tagify(category_expense, {
                        whitelist: [
                        ],
                        dropdown: {
                            maxItems: 20,           // <- mixumum allowed rendered suggestions
                            classname: "tagify__inline__suggestions", // <- custom classname for this dropdown, so it could be targeted
                            enabled: 0,             // <- show suggestions on focus
                            closeOnSelect: true    // <- do not hide the suggestions dropdown once an item has been selected
                        },
                        originalInputValueFormat: valuesArr => valuesArr.map(item => item.value).join(',')
                        });
                </script>
                <input type="hidden" class="" name="setting_type" value="{{setting_type}}"/>
                
                <div class="mt-5 pb-5">
                    <div class="py-5 {% if setting_type == "payables-expense"%} d-none {% endif %}">
                        <h1 class="">
                            {% if LANGUAGE_CODE == 'ja'%}
                            支払請求設定
                            {% else %}
                            Bills Settings
                            {% endif %}
                        </h1>

                        {% if LANGUAGE_CODE == 'ja'%}
                            {% include 'data/commerce/commerce-custom-property-form.html' with NameCustomField=BillNameCustomField setting_type='Bills' app_title="支払請求" %}
                        {% else %}
                            {% include 'data/commerce/commerce-custom-property-form.html' with NameCustomField=BillNameCustomField setting_type='Bills' app_title="Bills" %}
                        {% endif %}
                    </div>

                    <div class="py-5 {% if setting_type == "payables-bills" or setting_type == "payables" %} d-none {% endif %}">
                        <h1 class="mb-10">
                            {% if LANGUAGE_CODE == 'ja'%}
                                経費設定
                            {% else %}
                                Expense Settings
                            {% endif %}
                        </h1>
                        <div class="input-group">
                            <div id="base_currency_section" class="form-check form-switch form-check-custom form-check-solid col-3">
                                <input class="form-check-input" 
                
                                id="base_currency"
                                name="base_currency"
                                type="checkbox"
                                />
                                <label class="form-check-label cursor-pointer ms-1" for="base_currency">
                                    {% if LANGUAGE_CODE == 'ja' %}
                                    ベース通貨
                                    {% else %}
                                    Base Currency
                                    {% endif %}
                                </label>
                
                            </div>
                            <div class="col-3 currency_section align-items-center">
                                <label class="{% include 'data/utility/form-label.html' %}">
                                    <span>
                                        {% if LANGUAGE_CODE == 'ja'%}
                                            ベース通貨
                                        {% else %}
                                            Base Currency
                                        {% endif %}
                                    </span>
                                </label>
                                
                                <div class="mb-5">
                                    <select currency="{{expense_currency}}" class="form-select h-40px select2-this" name="currency" data-placeholder="{{app_setting.expense_currency}}">
                                        {% include 'data/partials/money-currency.html' with obj=app_setting.expense_currency %}
                                    </select>  
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="mt-5">
                    <div id="submit-custom-admin-button">
                        <button type="submit" name="app_settings" class="btn btn-dark">
                            {% if LANGUAGE_CODE == 'ja'%}
                            更新
                            {% else %}
                            Update
                            {% endif %}
                        </button>
                    </div>
                </div>
            </form>
            {% endif %}

            {% if 'purchase' in setting_type %}
                    <form method="POST" action="{% host_url 'procurement_settings' host 'app' %}" class="mb-15" id="expense_setting" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                    <div class="justify-content-between d-flex">
                        <div class="d-flex d-flex align-items-end">
                            <div class="me-2">
                                <a href="{% host_url 'workspace_setting' host 'app' %}?setting_type=purchase" {% if setting_type == "purchase" or setting_type == "purchase-orders" %} class="fs-2 fw-bolder text-dark" {%else%} class="fs-2 fw-bold text-gray-500" {%endif%}>
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        発注
                                        {% else %}
                                        Purchase Orders
                                        {% endif %}
                                </a>
                            </div>
                        </div>
                    </div>
                        <input type="hidden" class="" name="setting_type" value="{{setting_type}}"/>
                        
                        {% if setting_type == "purchase" or setting_type == "purchase-orders" %}
                            <div class="mt-5 pb-5">
                                <h1 class="my-5">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    発注設定
                                    {% else %}
                                    Puchase Orders Settings
                                    {% endif %}
                                </h1>
                            </div>

                            <div class="mb-10" 
                                hx-get="{% host_url 'properties' host 'app' %}"
                                hx-vals='{"page_group_type": "purchaseorder"}'
                                hx-trigger="load"
                                hx-target="this"
                                hx-swap="innerHTML"
                                >
                            </div>
                            

                            <div id="submit-custom-admin-button" class="mt-5">
                                <button type="submit" name="app_settings" value="purchase_order" class="btn btn-dark">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    更新
                                    {% else %}
                                    Update
                                    {% endif %}
                                </button>
                            </div>
                        {% endif %}
                    </form>
            {% endif %}
        </div>
    </div>
</div>
