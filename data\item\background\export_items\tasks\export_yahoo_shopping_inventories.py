from datetime import timedelta
from hatchet_sdk import Context
from utils.logger import logger
from data.models import TransferHistory
from utils.yahoo import push_yahoo_shopping_inventories

from ..models import ExportInventoryYahooShoppingPayload, ExportItemsResultPayload
from ..workflows import export_yahoo_shopping_inventories


@export_yahoo_shopping_inventories.task(name="ExportYahooShoppingInventoriesTask", execution_timeout=timedelta(hours=5), schedule_timeout=timedelta(hours=1))
def export_yahoo_shopping_inventories_task(input: ExportInventoryYahooShoppingPayload, ctx: Context) -> dict:
    """
    Child task for exporting inventories to Yahoo Shopping
    """
    logger.info("Run Yahoo Shopping inventories export child task")
    logger.info(f"Input: {input}")
    
    history_id = input.history_id
    task = None
    
    try:
        task = TransferHistory.objects.get(id=history_id)
        task.keep_alive = True
        task.save()
    except TransferHistory.DoesNotExist:
        logger.warning("TransferHistory does not exist")
        return ExportItemsResultPayload(
            success=False,
            platform="yahoo-shopping",
            error="TransferHistory does not exist"
        ).model_dump(mode='json')
    
    try:
        # Call the Yahoo Shopping export utility
        push_yahoo_shopping_inventories(
            id=input.channel_id,
            inventories=input.inventories,
        )
        
        logger.info("Successfully exported inventories to Yahoo Shopping")
        task.status = 'completed'
        task.progress = 100
        task.save()
        return ExportItemsResultPayload(
            success=True,
            platform="yahoo-shopping",
            message="Successfully exported inventories to Yahoo Shopping",
            exported_count=len(input.inventories)
        ).model_dump(mode='json')
        
    except Exception as e:
        logger.error(f"Error exporting inventories to Yahoo Shopping: {str(e)}")
        if task:
            task.status = 'failed'
            task.save()
        return ExportItemsResultPayload(
            success=False,
            platform="yahoo-shopping",
            error=str(e)
        ).model_dump(mode='json')