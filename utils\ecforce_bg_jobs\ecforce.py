import json
import time
import traceback
from datetime import datetime, timedelta
import pytz

import aiohttp
from data.constants.integration_constant import ECFORCE_SUBSCRIPTION_PROPERTY_MAPPING, ECFORCE_PROPERTY_MAPPING, ECFORCE_CUSTOMER_PROPERTY_MAPPING
from data.models import *
from utils.contact import display_contact_name
from utils.hubspot import push_hubspot_items, push_hubspot_orders
from utils.meter import *
from utils.logger import logger
from utils.error_logger.import_export_logger import ImportExportLogger as EcforceErrorLogger
from asgiref.sync import sync_to_async
from sanka.settings import LOCAL

MAX_PER_PAGE = 100  # Maximum number of orders to fetch per page in ECFORCE API
REQUEST_TIME_DELAY_S = 10
MAX_RETRIES = 10
# if LOCAL:
#     DEBUG_MODE = True
# else:
#     DEBUG_MODE = False
DEBUG_MODE = False

async def get_authentication_token(url, email, password):
    source_url = f'{url}/api/v2/admins/sign_in'
    data = {
        "admin[email]": email,
        "admin[password]": password
    }
    async with aiohttp.ClientSession() as session:
        async with session.post(source_url, data=data) as response:
            content = await response.text()
            parsed_content = json.loads(content)
            return parsed_content['authentication_token']


async def get_ecforce_items(url, authentication_token):
    source_url = f'{url}/api/v2/admin/products?page=1&per=100'
    data = []
    header = {
        'Authorization': f'Token token="{authentication_token}"'
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.get(source_url, headers=header) as response:
            content = await response.text()
            parsed_result = json.loads(content)
            data = data + parsed_result['data']
            meta = parsed_result['meta']
            links = parsed_result['links']

        no_page = int(meta['total_pages'])
        total = int(meta['total_count'])
        per = MAX_PER_PAGE
        i = 2

        while i <= no_page:
            tmp = f'{url}/api/v2/admin/products?page={i}&per={per}'
            async with session.get(tmp, headers=header) as page_response:
                page_content = await page_response.text()
                payload = json.loads(page_content)
                data = data + payload['data']
                i += 1
    return data


async def get_ecforce_orders(url, authentication_token, import_order_date=None):
    try:
        per = MAX_PER_PAGE  # MAX ORDER TO FETCH PER PAGE

        now = datetime.utcnow().date()
        if import_order_date:
            try:
                start_date = datetime.strptime(import_order_date, '%Y-%m-%d')
            except:
                start_date = now - timedelta(days=720)
        else:
            start_date = now - timedelta(days=720)

        start_date = start_date.isoformat()

        source_url = f'{url}/api/v2/admin/orders?page=1&per={per}&q[created_at_gt]={start_date}'

        header = {
            'Authorization': f'Token token="{authentication_token}"'
        }

        logger.info('source_url: '+source_url)
        logger.info('headers: '+str(header))
        logger.info('Sending Request Now')

        async with aiohttp.ClientSession() as session:
            async with session.get(source_url, headers=header) as response:
                content = await response.text()
                logger.info('Finish getting result')
                parsed_result = json.loads(content)
                meta = parsed_result['meta']

                total_orders = int(meta['total_count'])
                total_pages = int(meta['total_pages'])

                return total_orders, total_pages, start_date

    except Exception as e:
        logger.error(f"Error in get_ecforce_orders: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        # Return empty lists to prevent unpacking errors
        return 0,0, None


async def get_ecforce_orders_buffer_page(url, authentication_token, page=1, start_date=None):
    try:
        per = MAX_PER_PAGE  # MAX ORDER TO FETCH PER PAGE
        header = {
            'Authorization': f'Token token="{authentication_token}"'
        }
        data = []
        included = []

        logger.info( f'Fetching page: {page}')
        source_url = f'{url}/api/v2/admin/orders?page={page}&per={per}&q[created_at_gt]={start_date}&include=customer,order_items,order_items.variant,customer.billing_address,customer.shipping_addresses'
        logger.info( f'source_url: {source_url}')
        
        async with aiohttp.ClientSession() as session:
            async with session.get(source_url, headers=header) as page_result:
                if page_result.status == 200:
                    content = await page_result.text()
                    payload = json.loads(content)
                    data = data + payload['data']
                    included = included + payload['included']
                else:
                    content = await page_result.text()
                    logger.info(f'Failed to fetch page {content} - Got Request Limit')
                    data = content

                logger.info(f'Finish getting page {page} - {page_result.status}')

        customers = [i for i in included if i['type'] == 'customer']
        order_items = [i for i in included if i['type'] == 'order_item']
        variants = [i for i in included if i['type'] == 'variant']
        addresses = [i for i in included if i['type'] == 'address']

        return data, customers, order_items, variants, addresses

    except Exception as e:
        logger.error(f"Error in get_ecforce_orders_buffer_page: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        # Return empty lists to prevent unpacking errors
        return [], [], [], [], []


async def get_ecforce_customer(customer_id=None, url=None, token=None, import_date=None,page=None):
    try:
        per = MAX_PER_PAGE
        header = {
            'Authorization': f'Token token="{token}"'
        }
        now = datetime.utcnow().date()
        if import_date:
            try:
                import_date = datetime.strptime(import_date, '%Y-%m-%d')
            except:
                import_date = now - timedelta(days=720)
        else:
            import_date = now - timedelta(days=720)
            
        async with aiohttp.ClientSession() as session:
            if not customer_id: # Getting Pages and Customer Total
                source_url = f'{url}/api/v2/admin/customers/?page=1&per=1&q[created_at_gt]={import_date}'
                async with session.get(source_url, headers=header) as response:
                    if response.status == 200:
                        content = await response.text()
                        response_data = json.loads(content)
                        meta_data = response_data.get('meta', [])
                        total_customers = meta_data.get('total_count', 0)
                        total_pages = meta_data.get('total_pages', 0)
                        return total_customers, total_pages
                    else:
                        logger.error(f"Failed to fetch customer {customer_id}: {response.status}")
                    return response.status
            elif customer_id == "get_customers": # Getting Customers
                source_url = f'{url}/api/v2/admin/customers/?page={page}&per={per}&q[created_at_gt]={import_date}&include=billing_address,notes'
                async with session.get(source_url, headers=header) as response:
                    if response.status == 200:
                        content = await response.text()
                        response_data = json.loads(content)
                        customers = response_data.get('data', [])
                        included = response_data.get('included', [])
                        return customers, included
                    else:
                        logger.error(f"Failed to fetch customer {customer_id}: {response.status}")
                        content = await response.text()
                    return content, response.status
            else:
                source_url = f'{url}/api/v2/admin/customers/{customer_id}.json?include=billing_address,notes'
                async with session.get(source_url, headers=header) as response:
                    if response.status == 200:
                        content = await response.text()
                        response_data = json.loads(content)
                        customer_data = response_data.get('data', {})
                        customer_data = response_data.get('attributes', {})
                        included_data = response_data.get('included', [])
                        for data in included_data:
                            if data.get('type') == 'address':
                                customer_data.update(data.get('attributes', {}))
                                return customer_data
                        return {}
                    else:
                        logger.error(f"Failed to fetch customer {customer_id}: {response.status}")
                        return response.status
    except Exception as e:
        logger.error(f"Error fetching customer {customer_id}: {str(e)} - {traceback.print_exc()}")
        return {}


async def get_ecforce_order_by_id(order_id, url, authentication_token):
    try:
        header = {
            'Authorization': f'Token token="{authentication_token}"'
        }

        source_url = f'{url}/api/v2/admin/orders/{order_id}.json'
        async with aiohttp.ClientSession() as session:
            async with session.get(source_url, headers=header) as response:
                if response.status == 200:
                    content = await response.text()
                    response_data = json.loads(content)
                    return response_data.get('data', {}).get('attributes', {})  
                else:
                    logger.error(f"Failed to fetch order {order_id}: {response.status}")
                    return {}
    except Exception as e:
        logger.error(f"Error fetching order {order_id}: {str(e)}")
        return {}


# Streaming Generators for EC-Force Data

async def ecforce_orders_stream(url, token, start_date=None):
    """
    Async generator that streams EC-Force orders page by page
    """
    page = 1
    request_time_delay = REQUEST_TIME_DELAY_S
    max_retries = MAX_RETRIES
    
    while True:
        logger.info(f'Streaming orders page: {page}')
        
        # Retry logic for rate limiting
        for attempt in range(max_retries):
            orders, customers, order_items, variants, addresses = await get_ecforce_orders_buffer_page(
                url, token, page, start_date)
            
            if not orders or type(orders) != list:
                if attempt < max_retries - 1:
                    logger.info(f"Rate limited on page {page}. Retrying in {request_time_delay} seconds")
                    time.sleep(request_time_delay)
                    continue
                else:
                    logger.info(f"Max retries reached for page {page}")
                    return  # End stream
            else:
                break
        
        if not orders:
            break  # No more data
            
        # Yield each order with its related data
        for order in orders:
            yield {
                'order': order,
                'customers': customers,
                'order_items': order_items,
                'variants': variants,
                'addresses': addresses
            }
        
        page += 1


async def ecforce_customers_stream(url, token, import_date=None):
    """
    Async generator that streams EC-Force customers page by page
    """
    page = 1
    request_time_delay = REQUEST_TIME_DELAY_S
    max_retries = MAX_RETRIES
    
    while True:
        logger.info(f'Streaming customers page: {page}')
        
        # Retry logic for rate limiting
        for attempt in range(max_retries):
            customers, included = await get_ecforce_customer(
                customer_id="get_customers", url=url, token=token, page=page, import_date=import_date)
            
            if not customers or type(customers) != list:
                if attempt < max_retries - 1:
                    logger.info(f"Rate limited on page {page}. Retrying in {request_time_delay} seconds")
                    time.sleep(request_time_delay)
                    continue
                else:
                    logger.info(f"Max retries reached for page {page}")
                    return  # End stream
            else:
                break
        
        if not customers:
            break  # No more data
            
        # Yield each customer with its related data
        for customer in customers:
            yield {
                'customer': customer,
                'included': included
            }
        
        page += 1


async def ecforce_subscriptions_stream(url, token, start_date=None):
    """
    Async generator that streams EC-Force subscriptions page by page
    """
    page = 1
    request_time_delay = REQUEST_TIME_DELAY_S
    max_retries = MAX_RETRIES
    
    while True:
        logger.info(f'Streaming subscriptions page: {page}')
        
        # Retry logic for rate limiting
        for attempt in range(max_retries):
            subscriptions, customers, sub_order_items = await get_ecforce_subscriptions_buffer_page(
                url, token, page, start_date)
            
            if not subscriptions or type(subscriptions) != list:
                if attempt < max_retries - 1:
                    logger.info(f"Rate limited on page {page}. Retrying in {request_time_delay} seconds")
                    time.sleep(request_time_delay)
                    continue
                else:
                    logger.info(f"Max retries reached for page {page}")
                    return  # End stream
            else:
                break
        
        if not subscriptions:
            break  # No more data
            
        # Yield each subscription with its related data
        for subscription in subscriptions:
            yield {
                'subscription': subscription,
                'customers': customers,
                'sub_order_items': sub_order_items
            }
        
        page += 1


async def pull_ecforce_items(channel_id, hubspot_channel_id=None):
    channel = await Channel.objects.select_related('integration', 'workspace').aget(id=channel_id)
    url = channel.app_id
    token = channel.access_token
    platform = channel.integration.slug
    workspace = channel.workspace

    # HubSpot
    sku_map_items = {}

    logger.info("====== pull_ecforce_items")
    # TODO: Update logic later
    items = await get_ecforce_items(url, token)
    for item in items:
        try:
            payload = item['attributes']
            item_id = payload['id']
            item_name = payload['name']
            item_description = payload['description']
            item_status = payload['state']
            if item_status == 'inactive':
                item_status = 'draft'
            else:
                item_status = 'active'
            item_price = payload['master_list_price']
            currency = 'JPY'

            item_platform, created = await ShopTurboItemsPlatforms.objects.aget_or_create(
                channel=channel,
                platform_id=item_id,
                platform_type='default'
            )
            
            # Refetch with select_related to avoid sync queries
            item_platform = await ShopTurboItemsPlatforms.objects.select_related('item').aget(id=item_platform.id)

            if item_platform.item:
                shopturbo_item = item_platform.item
            else:
                shopturbo_item, _ = await ShopTurboItems.objects.aget_or_create(
                    workspace=workspace,
                    product_id=item_id,
                    platform=platform,
                )
                item_platform.item = shopturbo_item
                await item_platform.asave()

            price, _ = await ShopTurboItemsPrice.objects.aget_or_create(
                item=shopturbo_item,
                price=float(item_price),
                currency=currency
            )

            check_default_exists = await ShopTurboItemsPrice.objects.filter(item=shopturbo_item,default=True).aexists()
            if not check_default_exists:
                price.default = True
            shopturbo_item.price = float(item_price)
            if currency:
                shopturbo_item.currency = currency
            if item_name:
                shopturbo_item.name = item_name
                price.name = item_name
            if item_description:
                shopturbo_item.description = item_description
            if item_status:
                shopturbo_item.status = item_status

            await price.asave()
            await shopturbo_item.asave()

            sku_map_items[f'{item_id}'] = {
                'platform': channel.integration.title_ja if channel.integration.title_ja else channel.integration.title,
                'name': shopturbo_item.name,
                'price': shopturbo_item.price,
            }
        except:
            continue

    # HubSpot
    if hubspot_channel_id:
        skus = sku_map_items.keys()
        if len(skus) == 0:
            return True

        channel = await Channel.objects.select_related('integration', 'workspace').aget(id=hubspot_channel_id)
        logger.info(f'>>>>>>>>>>>>>>>> Start PUSH items to HubSpot channel {channel.name}')
        push_hubspot_items(str(channel.id), sku_map_items)
        logger.info(f'<<<<<<<<<<<<<<<< Finish PUSH items to HubSpot channel {channel.name}')

    return True


async def push_ecforce_items(items, channel_id):
    channel = await Channel.objects.select_related('integration', 'workspace').aget(id=channel_id)
    url = channel.app_id
    token = channel.access_token
    platform = channel.integration.slug
    workspace = channel.workspace
    logger.info("====== push_ecforce_items")
    # TODO: Update logic later

    return True


async def import_ecforce_customers(channel_id, mapping_custom_fields=None, hubspot_channel_id=None, import_date=None,lang='ja',history_id=None):
    channel = await Channel.objects.select_related('integration', 'workspace').aget(id=channel_id)
    url = channel.app_id
    token = channel.access_token
    platform = channel.integration.slug
    workspace = channel.workspace
    error_logger = EcforceErrorLogger(lang=lang)
    with error_logger.function_context("import_ecforce_customers"):
        try:
            error_logger.log_info("import_ecforce_customers", f"Starting import for platform: {platform}")
            error_logger.log_info(
                "import_ecforce_customers", "Starting Ecforce Customer import"
            )

            try:
                # TODO: Update logic later
                total_customers, total_pages = await get_ecforce_customer(
                    url=url, token=token, import_date=import_date)

                # Testing variable
                if DEBUG_MODE:
                    total_customers = 100
                    total_pages = 1
            except Exception as e:
                error_logger.log_api_error(
                    "import_ecforce_customers",
                    f"Failed to connect to Ecforce: {str(e)}",
                    e,
                )
                raise
            
            try:
                if history_id:
                    task = await TransferHistory.objects.filter(
                        workspace=workspace, id=history_id
                    ).afirst()
                else:
                    task = await TransferHistory.objects.filter(
                        workspace=workspace, type="import_order", channel=channel
                    ).order_by("-created_at").afirst()

                error_logger.log_info(
                    "import_ecforce_customers",
                    f"TransferHistory task initialized: {task.id}",
                )
            except Exception as e:
                error_logger.log_database_error(
                    "import_ecforce_customers",
                    f"Failed to update TransferHistory: {str(e)}",
                    e,
                )
                raise
            
            if task:
                task.total_number = total_customers
                await task.asave()
                
            # Time delay in seconds for rate limiting
            request_time_delay = REQUEST_TIME_DELAY_S
            
            # Maximum number of retries for rate limiting
            max_retries = MAX_RETRIES # Maximum number of retries for rate limiting
            for page in range(1, total_pages + 1):
                error_logger.log_info("import_ecforce_customers", f'Processing page {page} of {total_pages}')
                valid_to_import = True
                for attempt in range(max_retries):
                    try:
                        customers, included = await get_ecforce_customer(
                            customer_id="get_customers", url=url, token=token, page=page, import_date=import_date)
                        if DEBUG_MODE:
                            customers = [customers[0]]
                    except Exception as e:
                        error_logger.log_import_error("import_ecforce_customers",
                                                      f"Error fetching customers for page {page}", e)
                    if not customers or type(customers) != list:
                        message = customers
                        error_logger.log_import_error("import_ecforce_customers", f"Page {page} - Got limit request with message = {message}")
                        if attempt < max_retries - 1:
                            # Exponential backoff with jitter
                            error_logger.log_info(
                                "import_ecforce_customers", f"Rate limited at requesting page {page}. Retrying in {request_time_delay} seconds (attempt {attempt + 1}/{max_retries})")
                            time.sleep(request_time_delay)
                            continue
                        # If at max try, continue to next page
                        error_logger.log_info(
                            "import_ecforce_customers", f"Page {page} - Max retries reached for rate limiting")
                        valid_to_import = False
                    else:
                        # If order listed , start importing those orders
                        break
                    
                if not valid_to_import:
                    continue
                
                error_logger.log_info(
                    "import_ecforce_customers", f"Found {len(customers)} customers to import")
                
                for customer in customers:
                    # Checking task status while pulling order 
                    error_logger.log_info("import_ecforce_customers", f'Checking History status {task.id}')
                    if history_id:
                        task = await TransferHistory.objects.filter(
                            workspace=workspace, id=history_id
                        ).afirst()
                        if task:
                            if task.status == 'canceled':
                                break
                    try:
                        try:
                            error_logger.log_info("import_ecforce_customers", f'Processing customer: {customer.get("id")}')
                            customer_data = customer
                            customer_relationship = customer.get(
                                'relationships', {})
                            customer = customer.get('attributes', {})
                            customer_include = customer_relationship.get(
                                'billing_address', {}).get('data', {}).get('id', {})
                            for include in included:
                                if include.get('type') == "address" and include.get('id') == customer_include:
                                    customer.update(include.get("attributes",{}))
                        except Exception as e:
                            error_logger.log_import_error("import_ecforce_customers",
                                                        f"Error processing customer", e)
                            continue
                        if customer.get('id'):
                            error_logger.log_info(
                                "import_ecforce_customers", f'Processing customer: {customer.get("id")}')
                            contact_platform, created = await ContactsPlatforms.objects.aget_or_create(
                                channel=channel,
                                platform_id=customer.get('id')
                            )
                            
                            # Refetch with select_related to avoid sync queries
                            contact_platform = await ContactsPlatforms.objects.select_related('contact').aget(id=contact_platform.id)
                            
                            if contact_platform.contact:
                                contact = contact_platform.contact
                            else:
                                contact, _ = await Contact.objects.aget_or_create(
                                    workspace=workspace,
                                    email=customer.get('email', ''),
                                    name=customer.get('name01',''),
                                    phone_number=customer.get('full_tel',''),
                                    last_name=customer.get('name02','')
                                )
                                
                            contact.email = customer.get('email', '')
                            contact.name = customer.get('name01','')
                            contact.phone_number = customer.get('full_tel','')
                            contact.last_name = customer.get('name02','')
                            await contact.asave()
                            
                            contact_platform.contact = contact
                            await contact_platform.asave()

                        if mapping_custom_fields:
                            # [MAPPING] Customer Mapping Stage
                            for key, sanka_field in mapping_custom_fields.items():
                                try:
                                    customer_attributes = customer
                                    # Simplified if|else code base
                                    value = None
                                    sanka_source_field = sanka_field.split('|')[
                                        0]
                                    key_source = key.split('|')[0]
                                    # Start Mapping
                                    if key_source in ECFORCE_CUSTOMER_PROPERTY_MAPPING:
                                        source, ecforce_field = ECFORCE_CUSTOMER_PROPERTY_MAPPING[key_source]
                                        value = customer_attributes.get(
                                            ecforce_field, None)
                                        custom_field = await ContactsNameCustomField.objects.filter(
                                            workspace=workspace, name=sanka_source_field
                                        ).afirst()
                                        if custom_field:
                                            custom_value, _ = await ContactsValueCustomField.objects.aget_or_create(
                                                field_name=custom_field, contact=contact
                                            )
                                            # Truncate value to 500 characters to prevent database error
                                            custom_value.value = str(
                                                value)[:500] if value else None
                                            await custom_value.asave()
                                except:
                                    value = None
                                    traceback.print_exc()

                        task.success_number += 1
                        task.progress = 100 * \
                            (task.success_number) / task.total_number
                        await task.asave()
                    except Exception as e:
                        error_logger.log_import_error("import_ecforce_customers",
                                                      f"Error processing customer", e)
                        error_logger.log_info(
                            "import_ecforce_customers", traceback.format_exc())
                        task.failed_number += 1
                        await task.asave()
                        continue
        except Exception as e:
            error_logger.log_api_error(
                "import_ecforce_customers", f"Import failed: {str(e)}", e
            )
            logger.error(f"Error importing Ecforce Customers: {e}")
            traceback.print_exc()
            if "task" in locals():
                task.status = "failed"
                await task.asave()
            return False
        finally:
            # Save error logs to TransferHistory if any errors occurred
            if error_logger.has_errors() and "task" in locals():
                try:
                    await error_logger.asave_to_transfer_history(
                        task,
                        f"orders_import_errors_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    )
                    error_logger.log_info(
                        "import_ecforce_customers",
                        f"Saved {error_logger.get_error_count()} errors to CSV file",
                    )
                except Exception as e:
                    logger.error(f"Failed to save error CSV: {str(e)}")



async def import_ecforce_orders(channel_id, mapping_custom_fields=None, hubspot_channel_id=None, import_order_date=None,lang='ja',history_id=None):
    channel = await Channel.objects.select_related('integration', 'workspace').aget(id=channel_id)
    url = channel.app_id
    token = channel.access_token
    platform = channel.integration.slug
    workspace = channel.workspace
    error_logger = EcforceErrorLogger(lang=lang)
    with error_logger.function_context("import_ecforce_orders"):
        try:
            # HubSpot
            order_id_map_orders = {}

            error_logger.log_info("import_ecforce_orders", f"Starting import for platform: {platform}")
            error_logger.log_info(
                "import_ecforce_orders", "Starting Ecforce Order import"
            )

            try:
                # TODO: Update logic later
                total_orders, total_pages, start_date = await get_ecforce_orders(
                    url, token, import_order_date)
                
                # Testing variable
                if DEBUG_MODE:
                    total_orders = 100
                    total_pages = 1
            except Exception as e:
                error_logger.log_api_error(
                    "import_ecforce_orders",
                    f"Failed to connect to Ecforce: {str(e)}",
                    e,
                )
                raise
            
            try:
                if history_id:
                    task = await (TransferHistory.objects.filter(
                        workspace=workspace, id=history_id)
                    .afirst())
                else:
                    task = await (TransferHistory.objects.filter(
                        workspace=workspace, type="import_order", channel=channel)
                    .order_by("-created_at")
                    .afirst())

                error_logger.log_info(
                    "import_ecforce_orders",
                    f"TransferHistory task initialized: {task.id}",
                )
            except Exception as e:
                error_logger.log_database_error(
                    "import_ecforce_orders",
                    f"Failed to update TransferHistory: {str(e)}",
                    e,
                )
                raise
            
            if task:
                task.total_number = total_orders
                await task.asave()
                
            # Time delay in seconds for rate limiting
            request_time_delay = REQUEST_TIME_DELAY_S
            
            # Maximum number of retries for rate limiting
            max_retries = MAX_RETRIES # Maximum number of retries for rate limiting
            for page in range(1, total_pages + 1):
                error_logger.log_info("import_ecforce_orders", f'Processing page {page} of {total_pages}')
                valid_to_import = True
                for attempt in range(max_retries):
                    logger.info(f"About to call get_ecforce_orders_buffer_page with url={url}, token={token[:10]}..., page={page}, start_date={start_date}")
                    result = await get_ecforce_orders_buffer_page(url, token, page, start_date)
                    logger.info(f"get_ecforce_orders_buffer_page returned: {type(result)}")
                    orders, customers, order_items, variants, addresses = result
                    
                    if DEBUG_MODE:
                        orders = [orders[0]]
                        
                    if not orders or type(orders) != list:
                        message = orders
                        error_logger.log_import_error("import_ecforce_orders", f"Page {page} - Got limit request with message = {message}")
                        if attempt < max_retries - 1:
                            # Exponential backoff with jitter
                            error_logger.log_info("import_ecforce_orders", f"Rate limited at requesting page {page}. Retrying in {request_time_delay} seconds (attempt {attempt + 1}/{max_retries})")
                            time.sleep(request_time_delay)
                            continue
                        # If at max try, continue to next page
                        error_logger.log_info("import_ecforce_orders", f"Page {page} - Max retries reached for rate limiting")
                        valid_to_import = False
                    else:
                        # If order listed , start importing those orders
                        break
                    
                if not valid_to_import:
                    continue
                
                error_logger.log_info("import_ecforce_orders", f"Found {len(orders)} orders to import")
                for order in orders:
                    # Checking task status while pulling order 
                    if "task" in locals() and task:
                        task = await TransferHistory.objects.filter(
                            workspace=workspace, id=task.id
                        ).afirst()
                        if task.status == 'canceled':
                            break
                    try:
                        payload = order['attributes']
                        order_id = payload['id']
                        order_display_name = payload['number']
                        currency = 'JPY'
                        item_price = payload['subtotal']
                        total_price = payload['total']
                        tax = payload['tax']
                        platform_order_id = order_id

                        error_logger.log_info("import_ecforce_orders", f'Processing order: {order_id}')
                        order_platform, created = await ShopTurboOrdersPlatforms.objects.aget_or_create(
                            channel=channel,
                            platform_order_id=platform_order_id
                        )
                        
                        # Refetch with select_related to avoid sync queries
                        order_platform = await ShopTurboOrdersPlatforms.objects.select_related('order').aget(id=order_platform.id)

                        if order_platform.order:
                            shopturbo_order = order_platform.order
                        else:
                            if not await sync_to_async(has_quota)(workspace, ORDER_USAGE_CATEGORY):
                                users = await sync_to_async(list)(workspace.user.all())
                                for user in users:
                                    if user.verification.language == 'ja':
                                        await Notification.objects.acreate(
                                            workspace=workspace, user=user, message='制限を超えたため、受注レコードを作成できませんでした。サブスクリプション プランをアップグレードして割り当てを増やすか、既存のレコードをアーカイブしてスペースを解放してください。', type="error")
                                    else:
                                        await Notification.objects.acreate(
                                            workspace=workspace, user=user, message='Order could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some records to free up space.', type="error")
                                return False

                            timezone = pytz.timezone('UTC')
                            dt_naive = datetime.strptime(
                                payload['created_at'], '%Y/%m/%d %H:%M:%S')
                            dt_aware = timezone.localize(dt_naive)
                            shopturbo_order = await ShopTurboOrders.objects.acreate(
                                workspace=workspace,
                                currency=currency,
                                platform=platform,
                                status='active',
                                order_type='item_order',
                                order_at=dt_aware
                            )

                        order_platform.order = shopturbo_order
                        order_platform.order_display_name = order_display_name
                        # order_platform.order_at = payload['created_at']
                        await order_platform.asave()
                        if item_price:
                            shopturbo_order.item_price_order = float(item_price)

                        if total_price:
                            shopturbo_order.total_price = float(total_price)
                            shopturbo_order.total_price_without_tax = float(
                                total_price)

                        if tax and total_price and float(total_price) > 0:
                            shopturbo_order.tax = round(
                                100 * float(tax) / float(total_price), 2)
                            shopturbo_order.total_price_without_tax = float(
                                total_price - tax)

                        if currency:
                            shopturbo_order.currency = currency

                        await shopturbo_order.asave()
                        await shopturbo_order.shopturboitemsorders_set.all().adelete()
                        item_relationships = order['relationships']['order_items']['data']

                        if item_relationships and len(item_relationships) > 0:
                            for relationship in item_relationships:
                                order_item_id = relationship['id']
                                products = [
                                    item for item in order_items if order_item_id == item['id']]
                                if len(products) > 0:
                                    error_logger.log_info("import_ecforce_orders", f'Found products: {products[0]["id"]}')
                                    variant_id = products[0]['attributes']['variant_id']
                                    variant_ = False
                                    if variants:
                                        product_variants = [
                                            item for item in variants if variant_id == item['attributes']['id']]
                                        variant_ = True
                                    else:
                                        product_variants = [
                                            item for item in products]
                                    if len(product_variants) > 0:
                                        product = product_variants[0]
                                        error_logger.log_info("import_ecforce_orders", 
                                            f'Processing product variant: {product.get("id")}')
                                        if variant_:
                                            item_id = product['attributes']['product_id']
                                            item_name = product['attributes']['name']
                                            item_price = product['attributes']['sales_price']
                                        else:
                                            item_id = product['attributes']['id']
                                            item_name = product['attributes']['variant_id']
                                            item_price = product['attributes']['price']

                                        number_of_item = products[0]['attributes']['quantity']

                                        item_platform, created = await ShopTurboItemsPlatforms.objects.aget_or_create(
                                            channel=channel,
                                            platform_id=item_id,
                                            platform_type='default'
                                        )
                                        
                                        # Refetch with select_related to avoid sync queries
                                        item_platform = await ShopTurboItemsPlatforms.objects.select_related('item').aget(id=item_platform.id)
                                        try:
                                            sku = product['attributes']['sku']
                                            if sku:
                                                item_id = sku
                                                item_platform.platform_id = sku
                                                await item_platform.asave()
                                        except:
                                            pass
                                        shopturbo_item_order, created = await ShopTurboItemsOrders.objects.aget_or_create(
                                            platform_item_id=item_id,
                                            order=shopturbo_order,
                                            order_platform=order_platform
                                        )
                                        
                                        # Refetch with select_related to avoid sync queries
                                        shopturbo_item_order = await ShopTurboItemsOrders.objects.select_related('item').aget(id=shopturbo_item_order.id)
                                        if item_platform.item:
                                            shopturbo_item = item_platform.item
                                        else:
                                            if shopturbo_item_order.item:
                                                shopturbo_item = shopturbo_item_order.item
                                            else:
                                                shopturbo_item = await ShopTurboItems.objects.acreate(
                                                    workspace=workspace,
                                                    platform=platform,)
                                        item_platform.item = shopturbo_item
                                        await item_platform.asave()

                                        if number_of_item:
                                            shopturbo_item_order.number_item = float(
                                                number_of_item)
                                        if item_price:
                                            shopturbo_item_order.item_price_order = float(
                                                item_price)
                                        if currency:
                                            shopturbo_item_order.currency = currency
                                        await shopturbo_item_order.asave()

                                        if item_name:
                                            shopturbo_item.name = item_name
                                        if item_price:
                                            shopturbo_item.price = float(
                                                item_price)
                                        if currency:
                                            shopturbo_item.currency = currency

                                        shopturbo_item.status = 'active'
                                        await shopturbo_item.asave()

                                        price, _ = await ShopTurboItemsPrice.objects.aget_or_create(
                                            item=shopturbo_item,
                                            price=float(item_price),
                                            currency=currency
                                        )
                                        check_default = [item async for item in ShopTurboItemsPrice.objects.filter(
                                            item=shopturbo_item,
                                            default=True
                                        )]
                                        if len(check_default) == 0:
                                            price.default = True
                                        price.name = item_name
                                        await price.asave()

                                        shopturbo_item_order.item = shopturbo_item
                                        shopturbo_item_order.item_price = price
                                        await shopturbo_item_order.asave()
                                        products_attributes = products[0].get('attributes',{})
                                        if product: # Combine information from sku to product attribute
                                            products_attributes.update(product)
                                        if mapping_custom_fields:
                                            # [MAPPING] Item Mapping Stage
                                            for key, sanka_field in mapping_custom_fields.items():
                                                try:
                                                    # Simplify if|else code base
                                                    value = None
                                                    sanka_source_field = sanka_field.split('|')[0]
                                                    key_source = key.split('|')[0]
                                                    # Mapping
                                                    if key_source in ECFORCE_PROPERTY_MAPPING:
                                                        value = products_attributes.get(
                                                            ECFORCE_PROPERTY_MAPPING[key_source], None)
                                                        if '|item' in key:  # Item Custom Field
                                                            custom_field = await ShopTurboItemsNameCustomField.objects.filter(
                                                                workspace=workspace, name=sanka_source_field).afirst()
                                                            if custom_field:
                                                                custom_value, _ = await ShopTurboItemsValueCustomField.objects.aget_or_create(
                                                                    field_name=custom_field, items=shopturbo_item)
                                                                # Truncate value to 500 characters to prevent database error
                                                                custom_value.value = str(
                                                                    value)[:500] if value else None
                                                                await custom_value.asave()
                                                except Exception as e:
                                                    logger.info(
                                                        f"Error processing custom field '{sanka_field}': {e}")

                            shopturbo_order.number_item = len(order_items)
                            await shopturbo_order.asave()

                        order_customer = order['relationships']['customer']['data']
                        address_payload = None
                        if order_customer:
                            customer_payloads = [
                                customer for customer in customers if customer['id'] == order_customer['id']]
                            if not customer_payloads:
                                logger.warning(
                                    f"Customer with ID {order_customer['id']} not found in customers data")
                                continue
                            customer_payload = customer_payloads[0]
                            error_logger.log_info("import_ecforce_orders", f'Processing customer: {customer_payload["id"]}')

                            contact_platform, created = await ContactsPlatforms.objects.aget_or_create(
                                channel=channel,
                                platform_id=customer_payload['id']
                            )
                            
                            # Refetch with select_related to avoid sync queries
                            contact_platform = await ContactsPlatforms.objects.select_related('contact').aget(id=contact_platform.id)
                            
                            if contact_platform.contact:
                                contact = contact_platform.contact

                            # Use endpoint to Getting Customer attribute
                            customer_attr = await get_ecforce_customer(
                                customer_id=order_customer['id'], url=url, token=token)
                            if customer_payload:
                                customer_attr.update(
                                    customer_payload.get('attributes', {}))
                            if not contact_platform.contact:
                                contact, _ = await Contact.objects.aget_or_create(
                                    workspace=workspace,
                                    email=customer_attr.get('email', ''),
                                    name=customer_attr.get('name01',''),
                                    phone_number=customer_attr.get('full_tel',''),
                                    last_name=customer_attr.get('name02','')
                                )
                                contact_platform.contact = contact
                                await contact_platform.asave()
                            else:
                                contact.email = customer_attr.get('email', '')
                                contact.name = customer_attr.get('name01','')
                                contact.phone_number = customer_attr.get('full_tel','')
                                contact.last_name = customer_attr.get('name02','')
                                await contact.asave()

                            address_relationship = customer_payload['relationships']['billing_address']['data']
                            customer_addresses = [
                                address for address in addresses if address['id'] == address_relationship['id']]
                            shipping_address_relationships = customer_payload[
                                'relationships']['shipping_addresses']['data']
                            shipping_addresses = []
                            for shipping_address_relationship in shipping_address_relationships:
                                for address in addresses:
                                    if address['id'] == shipping_address_relationship['id']:
                                        shipping_addresses.append(address)
                            shipping_address_payload = shipping_addresses[0] if len(
                                shipping_addresses) > 0 else None
                            error_logger.log_info("import_ecforce_orders", 
                                f'Processing shipping address: {shipping_address_payload}')

                            if len(customer_addresses) > 0:
                                address_payload = customer_addresses[0]
                                error_logger.log_info("import_ecforce_orders", 
                                    f'Processing customer address: {address_payload}')
                                contact.name = address_payload['attributes']['name01']
                                contact.last_name = address_payload['attributes']['name02']
                                phone_number = address_payload['attributes']['tel01']
                                if len(phone_number) > 0:
                                    if phone_number[0] == '0':
                                        tmp = list(phone_number)
                                        tmp[0] = '+81-'
                                        phone_number = ''.join(tmp)
                                    else:
                                        phone_number = '+81-' + phone_number
                                contact.phone_number = phone_number

                            contact.status = 'active'
                            await contact.asave()
                            
                            shopturbo_order.contact = contact
                            await shopturbo_order.asave()

                        if mapping_custom_fields:
                            # [MAPPING] Order and customer Mapping Stage
                            for key, sanka_field in mapping_custom_fields.items():
                                try:
                                    order_attributes = order['attributes'] if order else {
                                    }
                                    customer_attributes = customer_payload['attributes'] if customer_payload and "attributes" in customer_payload else {
                                    }
                                    address_attributes = address_payload['attributes'] if address_payload else {
                                    }
                                    shipping_address_attributes = shipping_address_payload['attributes'] if shipping_address_payload else {
                                    }

                                    source_map = {
                                        'order_attributes': order_attributes,
                                        'customer_attributes': customer_attributes,
                                        'address_attributes': address_attributes,
                                        'shipping_address_attributes': shipping_address_attributes,
                                    }

                                    # Simplified if|else code base
                                    value = None
                                    sanka_source_field = sanka_field.split('|')[0]
                                    key_source = key.split('|')[0]
                                    # Start Mapping
                                    if key_source in ECFORCE_PROPERTY_MAPPING:
                                        source, ecforce_field = ECFORCE_PROPERTY_MAPPING[key_source]
                                        value = source_map.get(source, {}).get(ecforce_field, None)
                                        if '|contact' in key: # Contact custom Field
                                            custom_field = await ContactsNameCustomField.objects.filter(
                                                workspace=workspace, name=sanka_source_field).afirst()
                                            if custom_field:
                                                custom_value, _ = await ContactsValueCustomField.objects.aget_or_create(
                                                    field_name=custom_field, contact=contact)
                                                # Truncate value to 500 characters to prevent database error
                                                custom_value.value = str(
                                                    value)[:500] if value else None
                                                await custom_value.asave()
                                        else: # Order custom field
                                            custom_field = await ShopTurboOrdersNameCustomField.objects.filter(
                                                workspace=workspace, name=sanka_source_field).afirst()
                                            if custom_field:
                                                custom_value, _ = await ShopTurboOrdersValueCustomField.objects.aget_or_create(
                                                    field_name=custom_field, orders=shopturbo_order)
                                                # Truncate value to 500 characters to prevent database error
                                                custom_value.value = str(
                                                    value)[:500] if value else None
                                                await custom_value.asave()
                                except:
                                    value = None
                                    traceback.print_exc()


                        # HubSpot
                        if hubspot_channel_id:
                            _items = []
                            _amount = 0
                            async for item_order in shopturbo_order.shopturboitemsorders_set.all():
                                _item_platform = await item_order.item.item.filter(
                                    channel=channel, platform_type='default').afirst()
                                if _item_platform and _item_platform.platform_id:
                                    _items.append({
                                        'name': item_order.item.name,
                                        'description': item_order.item.description,
                                        'hs_sku': _item_platform.platform_id,
                                        'price': item_order.item.price,
                                        'quantity': item_order.number_item,
                                        # 'currency': None                            # TODO: update later
                                    })
                                    _amount += item_order.number_item * item_order.item.price

                            _contact = None
                            if shopturbo_order.contact:
                                _contact = {
                                    "sanka_id": f'{shopturbo_order.contact.contact_id}',
                                    "name": display_contact_name(shopturbo_order.contact),
                                    "email": shopturbo_order.contact.email if shopturbo_order.contact.email else '',
                                    "phone": shopturbo_order.contact.phone_number if shopturbo_order.contact.phone_number else '',
                                    "address": shopturbo_order.contact.location if shopturbo_order.contact.location else ''
                                }

                            order_id_map_orders[f'{shopturbo_order.order_id}'] = {
                                'order_at': shopturbo_order.order_at,
                                'platform_order_id': platform_order_id,
                                'platform': channel.integration.title_ja if channel.integration.title_ja else channel.integration.title,
                                'amount': _amount,
                                'items': _items,
                                'company': _contact
                            }
                        task.success_number += 1
                        task.progress = 100 * \
                            (task.success_number) / task.total_number
                        await task.asave()
                    except Exception as e:
                        error_logger.log_import_error("import_ecforce_orders",
                            f"Error processing order",e)
                        error_logger.log_info("import_ecforce_orders", traceback.format_exc())
                        task.failed_number += 1
                        await task.asave()
                        continue

            # HubSpot
            if hubspot_channel_id:
                order_ids = order_id_map_orders.keys()
                if len(order_ids) == 0:
                    return True

                channel = await Channel.objects.select_related('integration', 'workspace').aget(id=hubspot_channel_id)
                error_logger.log_info("import_ecforce_orders", 
                    f'>>>>>>>>>>>>>>>> Start PUSH orders to HubSpot channel {channel.name}')
                push_hubspot_orders(str(channel.id), order_id_map_orders)
                error_logger.log_info("import_ecforce_orders", 
                    f'<<<<<<<<<<<<<<<< Finish PUSH orders to HubSpot channel {channel.name}')

            return True
        except Exception as e:
            error_logger.log_api_error(
                "import_ecforce_orders", f"Import failed: {str(e)}", e
            )
            logger.error(f"Error importing Ecforce Orders: {e}")
            traceback.print_exc()
            if "task" in locals():
                task.status = "failed"
                await task.asave()
            return False
        finally:
            # Save error logs to TransferHistory if any errors occurred
            if error_logger.has_errors() and "task" in locals():
                try:
                    await error_logger.asave_to_transfer_history(
                        task,
                        f"orders_import_errors_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    )
                    error_logger.log_info(
                        "import_ecforce_orders",
                        f"Saved {error_logger.get_error_count()} errors to CSV file",
                    )
                except Exception as e:
                    logger.error(f"Failed to save error CSV: {str(e)}")



async def push_ecforce_orders(user, order_ids, channel_id):
    channel = await Channel.objects.select_related('integration', 'workspace').aget(id=channel_id)
    api_key = channel.api_key
    url = channel.account_id
    platform = channel.integration.slug
    workspace = channel.workspace
    logger.info("====== push_ecforce_orders")
    orders = [order async for order in ShopTurboOrders.objects.filter(id__in=order_ids)]

    # TODO: Update logic later

    return True


# Streaming Import Functions with Full Async ORM

async def import_ecforce_orders_streaming(channel_id, mapping_custom_fields=None, hubspot_channel_id=None, import_order_date=None, lang='ja', history_id=None):
    """
    Stream-based EC-Force orders import with real-time progress and full async ORM
    """
    channel = await Channel.objects.select_related('integration', 'workspace').aget(id=channel_id)
    url = channel.app_id
    token = channel.access_token
    platform = channel.integration.slug
    workspace = channel.workspace
    error_logger = EcforceErrorLogger(lang=lang)
    
    with error_logger.function_context("import_ecforce_orders_streaming"):
        try:
            # HubSpot tracking
            order_id_map_orders = {}
            
            error_logger.log_info("import_ecforce_orders_streaming", f"Starting streaming import for platform: {platform}")
            
            # Get total count for progress tracking
            try:
                total_orders, total_pages, start_date = await get_ecforce_orders(url, token, import_order_date)
                if DEBUG_MODE:
                    total_orders = 100
            except Exception as e:
                error_logger.log_api_error("import_ecforce_orders_streaming", f"Failed to connect to Ecforce: {str(e)}", e)
                raise
            
            # Initialize task tracking
            try:
                if history_id:
                    task = await TransferHistory.objects.filter(workspace=workspace, id=history_id).afirst()
                else:
                    task = await TransferHistory.objects.filter(
                        workspace=workspace, type="import_order", channel=channel
                    ).order_by("-created_at").afirst()
                
                if task:
                    task.total_number = total_orders
                    await task.asave()
                    
            except Exception as e:
                error_logger.log_database_error("import_ecforce_orders_streaming", f"Failed to update TransferHistory: {str(e)}", e)
                raise
            
            processed_count = 0
            
            # Stream orders and process them one by one
            async for order_data in ecforce_orders_stream(url, token, start_date):
                # Check for cancellation
                if task:
                    task = await TransferHistory.objects.filter(workspace=workspace, id=task.id).afirst()
                    if task.status == 'canceled':
                        break
                
                try:
                    # Extract data from stream
                    order = order_data['order']
                    customers = order_data['customers']
                    order_items = order_data['order_items']
                    variants = order_data['variants']
                    addresses = order_data['addresses']
                    
                    # Process single order
                    result = await process_single_order_async(
                        order, customers, order_items, variants, addresses,
                        channel, workspace, platform, mapping_custom_fields, 
                        hubspot_channel_id, order_id_map_orders, error_logger
                    )
                    
                    processed_count += 1
                    
                    # Update progress in real-time
                    if task:
                        if result:
                            task.success_number += 1
                        else:
                            task.failed_number += 1
                        
                        task.progress = 100 * processed_count / total_orders
                        await task.asave()
                    
                    # Yield progress update (for real-time UI updates)
                    yield {
                        "type": "progress",
                        "processed": processed_count,
                        "total": total_orders,
                        "order_id": order['attributes']['id'],
                        "status": "success" if result else "error"
                    }
                    
                    if DEBUG_MODE and processed_count >= 5:
                        break
                        
                except Exception as e:
                    error_logger.log_import_error("import_ecforce_orders_streaming", f"Error processing order", e)
                    processed_count += 1
                    
                    if task:
                        task.failed_number += 1
                        task.progress = 100 * processed_count / total_orders
                        await task.asave()
                    
                    yield {
                        "type": "error",
                        "processed": processed_count,
                        "total": total_orders,
                        "error": str(e)
                    }
                    continue
            
            # Final HubSpot push
            if hubspot_channel_id and order_id_map_orders:
                hubspot_channel = await Channel.objects.select_related('integration', 'workspace').aget(id=hubspot_channel_id)
                push_hubspot_orders(str(hubspot_channel.id), order_id_map_orders)
            
            yield {
                "type": "completed",
                "processed": processed_count,
                "total": total_orders,
                "success": True
            }
            
        except Exception as e:
            error_logger.log_api_error("import_ecforce_orders_streaming", f"Import failed: {str(e)}", e)
            if task:
                task.status = "failed"
                await task.asave()
            
            yield {
                "type": "error",
                "error": str(e),
                "success": False
            }


async def process_single_order_async(order, customers, order_items, variants, addresses, 
                                   channel, workspace, platform, mapping_custom_fields, 
                                   hubspot_channel_id, order_id_map_orders, error_logger):
    """
    Process a single order with full async ORM (extracted from the main loop for streaming)
    """
    url = channel.app_id
    token = channel.access_token
    try:
        payload = order['attributes']
        order_id = payload['id']
        order_display_name = payload['number']
        currency = 'JPY'
        item_price = payload['subtotal']
        total_price = payload['total']
        tax = payload['tax']
        platform_order_id = order_id

        error_logger.log_info("process_single_order_async", f'Processing order: {order_id}')

        # Create/get order platform
        order_platform, created = await ShopTurboOrdersPlatforms.objects.aget_or_create(
            channel=channel, platform_order_id=platform_order_id
        )
        
        # Refetch with select_related to avoid sync queries
        order_platform = await ShopTurboOrdersPlatforms.objects.select_related('order').aget(id=order_platform.id)

        if order_platform.order:
            shopturbo_order = order_platform.order
        else:
            # Check quota
            if not await sync_to_async(has_quota)(workspace, ORDER_USAGE_CATEGORY):
                users = await sync_to_async(list)(workspace.user.all())
                for user in users:
                    message = '制限を超えたため、受注レコードを作成できませんでした。' if user.verification.language == 'ja' else 'Order could not be created due to exceeding the limit.'
                    await Notification.objects.acreate(
                        workspace=workspace, user=user, message=message, type="error"
                    )
                return False

            # Create new order
            timezone = pytz.timezone('UTC')
            dt_naive = datetime.strptime(payload['created_at'], '%Y/%m/%d %H:%M:%S')
            dt_aware = timezone.localize(dt_naive)
            
            shopturbo_order = await ShopTurboOrders.objects.acreate(
                workspace=workspace, currency=currency, platform=platform,
                status='active', order_type='item_order', order_at=dt_aware
            )

        # Update order details
        order_platform.order = shopturbo_order
        order_platform.order_display_name = order_display_name
        await order_platform.asave()

        # Set prices
        if item_price:
            shopturbo_order.item_price_order = float(item_price)
        if total_price:
            shopturbo_order.total_price = float(total_price)
            shopturbo_order.total_price_without_tax = float(total_price)
        if tax and total_price and float(total_price) > 0:
            shopturbo_order.tax = round(100 * float(tax) / float(total_price), 2)
            shopturbo_order.total_price_without_tax = float(total_price - tax)
        if currency:
            shopturbo_order.currency = currency

        await shopturbo_order.asave()
        
        # Process order items
        await shopturbo_order.shopturboitemsorders_set.all().adelete()
        
        item_relationships = order['relationships']['order_items']['data']

        if item_relationships and len(item_relationships) > 0:
            for relationship in item_relationships:
                order_item_id = relationship['id']
                products = [item for item in order_items if order_item_id == item['id']]
                
                if len(products) > 0:
                    error_logger.log_info("process_single_order_async", f'Found products: {products[0]["id"]}')
                    variant_id = products[0]['attributes']['variant_id']
                    variant_ = False
                    
                    if variants:
                        product_variants = [item for item in variants if variant_id == item['attributes']['id']]
                        variant_ = True
                    else:
                        product_variants = [item for item in products]
                    
                    if len(product_variants) > 0:
                        product = product_variants[0]
                        error_logger.log_info("process_single_order_async", f'Processing product variant: {product.get("id")}')
                        
                        if variant_:
                            item_id = product['attributes']['product_id']
                            item_name = product['attributes']['name']
                            item_price = product['attributes']['sales_price']
                        else:
                            item_id = product['attributes']['id']
                            item_name = product['attributes']['variant_id']
                            item_price = product['attributes']['price']

                        number_of_item = products[0]['attributes']['quantity']

                        item_platform, created = await ShopTurboItemsPlatforms.objects.aget_or_create(
                            channel=channel,
                            platform_id=item_id,
                            platform_type='default'
                        )
                        
                        # Refetch with select_related to avoid sync queries
                        item_platform = await ShopTurboItemsPlatforms.objects.select_related('item').aget(id=item_platform.id)
                        
                        try:
                            sku = product['attributes']['sku']
                            if sku:
                                item_id = sku
                                item_platform.platform_id = sku
                                await item_platform.asave()
                        except:
                            pass
                            
                        shopturbo_item_order, created = await ShopTurboItemsOrders.objects.aget_or_create(
                            platform_item_id=item_id,
                            order=shopturbo_order,
                            order_platform=order_platform
                        )
                        
                        # Refetch with select_related to avoid sync queries
                        shopturbo_item_order = await ShopTurboItemsOrders.objects.select_related('item').aget(id=shopturbo_item_order.id)
                        
                        if item_platform.item:
                            shopturbo_item = item_platform.item
                        else:
                            if shopturbo_item_order.item:
                                shopturbo_item = shopturbo_item_order.item
                            else:
                                shopturbo_item = await ShopTurboItems.objects.acreate(
                                    workspace=workspace,
                                    platform=platform,
                                )
                        
                        item_platform.item = shopturbo_item
                        await item_platform.asave()

                        if number_of_item:
                            shopturbo_item_order.number_item = float(number_of_item)
                        if item_price:
                            shopturbo_item_order.item_price_order = float(item_price)
                        if currency:
                            shopturbo_item_order.currency = currency
                        await shopturbo_item_order.asave()

                        if item_name:
                            shopturbo_item.name = item_name
                        if item_price:
                            shopturbo_item.price = float(item_price)
                        if currency:
                            shopturbo_item.currency = currency

                        shopturbo_item.status = 'active'
                        await shopturbo_item.asave()

                        price, _ = await ShopTurboItemsPrice.objects.aget_or_create(
                            item=shopturbo_item,
                            price=float(item_price),
                            currency=currency
                        )
                        check_default = [item async for item in ShopTurboItemsPrice.objects.filter(
                            item=shopturbo_item,
                            default=True
                        )]
                        if len(check_default) == 0:
                            price.default = True
                        price.name = item_name
                        await price.asave()

                        shopturbo_item_order.item = shopturbo_item
                        shopturbo_item_order.item_price = price
                        await shopturbo_item_order.asave()
                        
                        # Handle custom field mapping for items
                        products_attributes = products[0].get('attributes',{})
                        if product:
                            products_attributes.update(product)
                        if mapping_custom_fields:
                            for key, sanka_field in mapping_custom_fields.items():
                                try:
                                    value = None
                                    sanka_source_field = sanka_field.split('|')[0]
                                    key_source = key.split('|')[0]
                                    
                                    if key_source in ECFORCE_PROPERTY_MAPPING:
                                        value = products_attributes.get(ECFORCE_PROPERTY_MAPPING[key_source], None)
                                        if '|item' in key:
                                            if value:
                                                custom_field = await ShopTurboItemsNameCustomField.objects.filter(
                                                    workspace=workspace, name=sanka_source_field
                                                ).afirst()
                                                if custom_field:
                                                    custom_value, _ = await ShopTurboItemsValueCustomField.objects.aget_or_create(
                                                        field_name=custom_field, item=shopturbo_item
                                                    )
                                                    custom_value.value = str(value)[:500] if value else None
                                                    await custom_value.asave()
                                except:
                                    traceback.print_exc()

        # Process customer information
        if order['relationships'].get('customer'):
            order_customer = order['relationships']['customer']['data']
            customer_payloads = [customer for customer in customers if customer['id'] == order_customer['id']]
            
            if len(customer_payloads) > 0:
                customer_payload = customer_payloads[0]
                error_logger.log_info("process_single_order_async", f'Processing customer: {customer_payload["id"]}')

                contact_platform, created = await ContactsPlatforms.objects.aget_or_create(
                    channel=channel,
                    platform_id=customer_payload['id']
                )
                
                # Refetch with select_related to avoid sync queries
                contact_platform = await ContactsPlatforms.objects.select_related('contact').aget(id=contact_platform.id)
                
                if contact_platform.contact:
                    contact = contact_platform.contact

                # Use endpoint to Getting Customer attribute
                customer_attr = await get_ecforce_customer(
                    customer_id=order_customer['id'], url=url, token=token)
                if customer_payload:
                    customer_attr.update(customer_payload.get('attributes', {}))
                    
                if not contact_platform.contact:
                    contact, _ = await Contact.objects.aget_or_create(
                        workspace=workspace,
                        email=customer_attr.get('email', ''),
                        name=customer_attr.get('name01',''),
                        phone_number=customer_attr.get('full_tel',''),
                        last_name=customer_attr.get('name02','')
                    )
                    contact_platform.contact = contact
                    await contact_platform.asave()
                else:
                    contact.email = customer_attr.get('email', '')
                    contact.name = customer_attr.get('name01','')
                    contact.phone_number = customer_attr.get('full_tel','')
                    contact.last_name = customer_attr.get('name02','')
                    await contact.asave()

                # Process address information
                address_relationship = customer_payload['relationships']['billing_address']['data']
                customer_addresses = [address for address in addresses if address['id'] == address_relationship['id']]
                
                if len(customer_addresses) > 0:
                    address_payload = customer_addresses[0]
                    error_logger.log_info("process_single_order_async", f'Processing customer address: {address_payload}')
                    contact.name = address_payload['attributes']['name01']
                    contact.last_name = address_payload['attributes']['name02']
                    phone_number = address_payload['attributes']['tel01']
                    if len(phone_number) > 0:
                        if phone_number[0] == '0':
                            tmp = list(phone_number)
                            tmp[0] = '+81-'
                            phone_number = ''.join(tmp)
                        else:
                            phone_number = '+81-' + phone_number
                    contact.phone_number = phone_number

                contact.status = 'active'
                await contact.asave()
                
                shopturbo_order.contact = contact
                await shopturbo_order.asave()

        # Handle HubSpot integration
        if hubspot_channel_id:
            _items = []
            _amount = 0
            async for item_order in shopturbo_order.shopturboitemsorders_set.all():
                _item_platform = await item_order.item.item.filter(
                    channel=channel, platform_type='default').afirst()
                if _item_platform and _item_platform.platform_id:
                    _items.append({
                        'name': item_order.item.name,
                        'description': item_order.item.description,
                        'hs_sku': _item_platform.platform_id,
                        'price': item_order.item.price,
                        'quantity': item_order.number_item,
                    })
                    _amount += item_order.number_item * item_order.item.price

            _contact = None
            if shopturbo_order.contact:
                _contact = {
                    "sanka_id": f'{shopturbo_order.contact.contact_id}',
                    "name": display_contact_name(shopturbo_order.contact),
                    "email": shopturbo_order.contact.email if shopturbo_order.contact.email else '',
                    "phone": shopturbo_order.contact.phone_number if shopturbo_order.contact.phone_number else '',
                    "address": shopturbo_order.contact.location if shopturbo_order.contact.location else ''
                }

            order_id_map_orders[f'{shopturbo_order.order_id}'] = {
                'order_at': shopturbo_order.order_at,
                'platform_order_id': platform_order_id,
                'platform': channel.integration.title_ja if channel.integration.title_ja else channel.integration.title,
                'amount': _amount,
                'items': _items,
                'company': _contact
            }
        
        return True
        
    except Exception as e:
        error_logger.log_import_error("process_single_order_async", f"Error processing order {order_id}: {str(e)}", e)
        return False


async def import_ecforce_customers_streaming(channel_id, mapping_custom_fields=None, hubspot_channel_id=None, import_date=None, lang='ja', history_id=None):
    """
    Stream-based EC-Force customers import with full async ORM
    """
    channel = await Channel.objects.select_related('integration', 'workspace').aget(id=channel_id)
    url = channel.app_id
    token = channel.access_token
    platform = channel.integration.slug
    workspace = channel.workspace
    error_logger = EcforceErrorLogger(lang=lang)
    
    with error_logger.function_context("import_ecforce_customers_streaming"):
        try:
            # Get total count
            total_customers, total_pages = await get_ecforce_customer(url=url, token=token, import_date=import_date)
            if DEBUG_MODE:
                total_customers = 10
            
            # Initialize task
            if history_id:
                task = await TransferHistory.objects.filter(workspace=workspace, id=history_id).afirst()
            else:
                task = await TransferHistory.objects.filter(
                    workspace=workspace, type="import_customer", channel=channel
                ).order_by("-created_at").afirst()
            
            if task:
                task.total_number = total_customers
                await task.asave()
            
            processed_count = 0
            
            # Stream customers
            async for customer_data in ecforce_customers_stream(url, token, import_date):
                # Check cancellation
                if task:
                    task = await TransferHistory.objects.filter(workspace=workspace, id=task.id).afirst()
                    if task.status == 'canceled':
                        break
                
                try:
                    customer = customer_data['customer']
                    included = customer_data['included']
                    
                    # Process single customer
                    result = await process_single_customer_async(
                        customer, included, channel, workspace, mapping_custom_fields, error_logger
                    )
                    
                    processed_count += 1
                    
                    # Update progress
                    if task:
                        if result:
                            task.success_number += 1
                        else:
                            task.failed_number += 1
                        
                        task.progress = 100 * processed_count / total_customers
                        await task.asave()
                    
                    # Yield progress
                    yield {
                        "type": "progress",
                        "processed": processed_count,
                        "total": total_customers,
                        "customer_id": customer['attributes']['id'],
                        "status": "success" if result else "error"
                    }
                    
                    if DEBUG_MODE and processed_count >= 5:
                        break
                        
                except Exception as e:
                    error_logger.log_import_error("import_ecforce_customers_streaming", f"Error processing customer", e)
                    processed_count += 1
                    
                    if task:
                        task.failed_number += 1
                        task.progress = 100 * processed_count / total_customers
                        await task.asave()
                    
                    yield {
                        "type": "error",
                        "processed": processed_count,
                        "total": total_customers,
                        "error": str(e)
                    }
                    continue
            
            yield {
                "type": "completed",
                "processed": processed_count,
                "total": total_customers,
                "success": True
            }
            
        except Exception as e:
            error_logger.log_api_error("import_ecforce_customers_streaming", f"Import failed: {str(e)}", e)
            if task:
                task.status = "failed"
                await task.asave()
            
            yield {
                "type": "error",
                "error": str(e),
                "success": False
            }


async def process_single_customer_async(customer, included, channel, workspace, mapping_custom_fields, error_logger):
    """
    Process a single customer with full async ORM
    """
    try:
        customer_data = customer
        customer_relationship = customer.get('relationships', {})
        customer = customer.get('attributes', {})
        customer_include = customer_relationship.get('billing_address', {}).get('data', {}).get('id', {})
        
        for include in included:
            if include.get('type') == "address" and include.get('id') == customer_include:
                customer.update(include.get("attributes",{}))
        
        if customer.get('id'):
            error_logger.log_info("process_single_customer_async", f'Processing customer: {customer.get("id")}')
            
            contact_platform, _ = await ContactsPlatforms.objects.aget_or_create(
                channel=channel,
                platform_id=customer.get('id')
            )
            
            if contact_platform.contact:
                contact = contact_platform.contact
            else:
                contact, _ = await Contact.objects.aget_or_create(
                    workspace=workspace,
                    email=customer.get('email', ''),
                    name=customer.get('name01',''),
                    phone_number=customer.get('full_tel',''),
                    last_name=customer.get('name02','')
                )
                
            contact.email = customer.get('email', '')
            contact.name = customer.get('name01','')
            contact.phone_number = customer.get('full_tel','')
            contact.last_name = customer.get('name02','')
            await contact.asave()
            
            contact_platform.contact = contact
            await contact_platform.asave()

        # Handle custom field mapping
        if mapping_custom_fields:
            for key, sanka_field in mapping_custom_fields.items():
                try:
                    customer_attributes = customer
                    value = None
                    sanka_source_field = sanka_field.split('|')[0]
                    key_source = key.split('|')[0]
                    
                    if key_source in ECFORCE_CUSTOMER_PROPERTY_MAPPING:
                        ecforce_field = ECFORCE_CUSTOMER_PROPERTY_MAPPING[key_source]
                        value = customer_attributes.get(ecforce_field, None)
                        
                        custom_field = await ContactsNameCustomField.objects.filter(
                            workspace=workspace, name=sanka_source_field
                        ).afirst()
                        
                        if custom_field:
                            custom_value, _ = await ContactsValueCustomField.objects.aget_or_create(
                                field_name=custom_field, contact=contact
                            )
                            custom_value.value = str(value)[:500] if value else None
                            await custom_value.asave()
                except:
                    traceback.print_exc()
        
        return True
        
    except Exception as e:
        error_logger.log_import_error("process_single_customer_async", f"Error processing customer: {str(e)}", e)
        return False


async def import_ecforce_subscriptions_streaming(channel_id, mapping_custom_fields=None, import_date=None, lang='ja', history_id=None):
    """
    Stream-based EC-Force subscriptions import with full async ORM
    """
    channel = await Channel.objects.select_related('integration', 'workspace').aget(id=channel_id)
    url = channel.app_id
    token = channel.access_token
    platform = channel.integration.slug
    workspace = channel.workspace
    error_logger = EcforceErrorLogger(lang=lang)
    
    with error_logger.function_context("import_ecforce_subscriptions_streaming"):
        try:
            # Get total count
            total_subscriptions, total_pages, start_date = await get_ecforce_subscriptions(url, token, import_date)
            if DEBUG_MODE:
                total_subscriptions = 5
            
            # Initialize task
            if history_id:
                task = await TransferHistory.objects.filter(workspace=workspace, id=history_id).afirst()
            else:
                task = await TransferHistory.objects.filter(
                    workspace=workspace, type="import_subscription", channel=channel
                ).order_by("-created_at").afirst()
            
            if task:
                task.total_number = total_subscriptions
                await task.asave()
            
            processed_count = 0
            
            # Stream subscriptions
            async for subscription_data_item in ecforce_subscriptions_stream(url, token, start_date):
                # Check cancellation
                if task:
                    task = await TransferHistory.objects.filter(workspace=workspace, id=task.id).afirst()
                    if task.status == 'canceled':
                        break
                
                try:
                    subscription_data = subscription_data_item['subscription']
                    customers = subscription_data_item['customers']
                    sub_order_items = subscription_data_item['sub_order_items']
                    
                    # Process single subscription
                    subscription = await import_ecforce_subscription(subscription_data, channel, workspace)
                    
                    processed_count += 1
                    
                    # Update progress
                    if task:
                        if subscription:
                            task.success_number += 1
                        else:
                            task.failed_number += 1
                        
                        task.progress = 100 * processed_count / total_subscriptions
                        await task.asave()
                    
                    # Yield progress
                    yield {
                        "type": "progress",
                        "processed": processed_count,
                        "total": total_subscriptions,
                        "subscription_id": subscription_data['attributes']['id'],
                        "status": "success" if subscription else "error"
                    }
                    
                    if DEBUG_MODE and processed_count >= 3:
                        break
                        
                except Exception as e:
                    error_logger.log_import_error("import_ecforce_subscriptions_streaming", f"Error processing subscription", e)
                    processed_count += 1
                    
                    if task:
                        task.failed_number += 1
                        task.progress = 100 * processed_count / total_subscriptions
                        await task.asave()
                    
                    yield {
                        "type": "error",
                        "processed": processed_count,
                        "total": total_subscriptions,
                        "error": str(e)
                    }
                    continue
            
            yield {
                "type": "completed",
                "processed": processed_count,
                "total": total_subscriptions,
                "success": True
            }
            
        except Exception as e:
            error_logger.log_api_error("import_ecforce_subscriptions_streaming", f"Import failed: {str(e)}", e)
            if task:
                task.status = "failed"
                await task.asave()
            
            yield {
                "type": "error",
                "error": str(e),
                "success": False
            }


# Usage example for consuming the streams:
async def consume_ecforce_orders_stream_example(channel_id):
    """
    Example of how to consume the streaming import
    """
    async for progress_update in import_ecforce_orders_streaming(channel_id):
        if progress_update["type"] == "progress":
            print(f"Progress: {progress_update['processed']}/{progress_update['total']} orders processed")
        elif progress_update["type"] == "error":
            print(f"Error: {progress_update['error']}")
        elif progress_update["type"] == "completed":
            print(f"Completed! Total processed: {progress_update['processed']}")


### Subscription

async def get_ecforce_subscriptions(url, authentication_token, import_date=None):
    """
    Get EC-Force subscriptions with pagination support
    """
    try:
        per = MAX_PER_PAGE
        now = datetime.utcnow().date()
        
        if import_date:
            try:
                start_date = datetime.strptime(import_date, '%Y-%m-%d')
            except:
                start_date = now - timedelta(days=720)
        else:
            start_date = now - timedelta(days=720)

        start_date = start_date.isoformat()

        source_url = f'{url}/api/v2/admin/subs_orders?page=1&per={per}&q[created_at_gt]={start_date}'

        header = {
            'Authorization': f'Token token="{authentication_token}"'
        }

        logger.info('source_url: ' + source_url)
        logger.info('headers: ' + str(header))
        logger.info('Sending Request Now')

        async with aiohttp.ClientSession() as session:
            async with session.get(source_url, headers=header) as response:
                content = await response.text()
                logger.info('Finish getting result')
                parsed_result = json.loads(content)
                meta = parsed_result['meta']

                total_subscriptions = int(meta['total_count'])
                total_pages = int(meta['total_pages'])

                return total_subscriptions, total_pages, start_date

    except Exception as e:
        logger.error(f"Error in get_ecforce_subscriptions: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return 0, 0, None


async def get_ecforce_subscriptions_buffer_page(url, authentication_token, page=1, start_date=None):
    """
    Get EC-Force subscriptions for a specific page
    """
    try:
        per = MAX_PER_PAGE
        header = {
            'Authorization': f'Token token="{authentication_token}"'
        }
        data = []
        included = []

        logger.info(f'Fetching subscriptions page: {page}')
        source_url = f'{url}/api/v2/admin/subs_orders?page={page}&per={per}&q[created_at_gt]={start_date}&include=customer,sub_order_items,orders'
        logger.info(f'source_url: {source_url}')
        
        async with aiohttp.ClientSession() as session:
            async with session.get(source_url, headers=header) as page_result:
                if page_result.status == 200:
                    content = await page_result.text()
                    payload = json.loads(content)
                    data = data + payload['data']
                    included = included + payload['included']
                else:
                    content = await page_result.text()
                    logger.info(f'Failed to fetch page {content} - Got Request Limit')
                    data = content

                logger.info(f'Finish getting subscriptions page {page} - {page_result.status}')

        customers = [i for i in included if i['type'] == 'customer']
        sub_order_items = [i for i in included if i['type'] == 'sub_order_item']

        return data, customers, sub_order_items

    except Exception as e:
        logger.error(f"Error in get_ecforce_subscriptions_buffer_page: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return [], [], []


def map_ecforce_subscription_data(subscription_data, workspace, channel):
    """
    Map EC-Force subscription data to ShopTurboSubscription model fields
    """
    attributes = subscription_data.get('attributes', {})
    
    # Basic mapping
    subscription_mapping = {
        'workspace': workspace,
        'channel': channel,
        'status': map_subscription_status(attributes.get('state')),
        'currency': 'JPY',  # Default for EC-Force
        'billing_timing': map_billing_cycle(attributes),
        'total_price': float(attributes.get('subtotal', 0)),
        'created_at': parse_ecforce_datetime(attributes.get('created_at')),
        'updated_at': parse_ecforce_datetime(attributes.get('updated_at')),
    }
    
    return subscription_mapping


def map_subscription_status(ecforce_status):
    """
    Map EC-Force subscription status to your system's status
    """
    status_mapping = {
        'active': 'active',
        'suspended': 'paused',
        'canceled': 'canceled',
        'expired': 'expired',
        'draft': 'draft',
    }
    return status_mapping.get(ecforce_status, 'draft')


def map_billing_cycle(attributes):
    """
    Determine billing cycle from EC-Force scheduling data
    """
    if attributes.get('scheduled_to_be_delivered_every_x_day'):
        days = attributes.get('scheduled_to_be_delivered_every_x_day')
        if days == 7:
            return 'weekly'
        elif days == 14:
            return 'biweekly'
        elif days == 30:
            return 'monthly'
        else:
            return 'custom'
    elif attributes.get('scheduled_to_be_delivered_every_x_month'):
        months = attributes.get('scheduled_to_be_delivered_every_x_month')
        if months == 1:
            return 'monthly'
        elif months == 3:
            return 'quarterly'
        elif months == 12:
            return 'yearly'
        else:
            return 'custom'
    else:
        return 'monthly'  # default


def parse_ecforce_datetime(date_string):
    """
    Parse EC-Force datetime format: "2025/08/22 12:25:59"
    """
    if not date_string:
        return None
    try:
        timezone = pytz.timezone('UTC')
        dt_naive = datetime.strptime(date_string, '%Y/%m/%d %H:%M:%S')
        return timezone.localize(dt_naive)
    except:
        return None


def parse_ecforce_date(date_string):
    """
    Parse EC-Force date format: "2025/09/01"
    """
    if not date_string:
        return None
    try:
        return datetime.strptime(date_string, '%Y/%m/%d').date()
    except:
        return None


async def import_ecforce_subscription(subscription_data, channel, workspace, mapping_custom_fields):
    """
    Import a single EC-Force subscription
    """
    try:
        attributes = subscription_data.get('attributes', {})
        platform_subscription_id = str(attributes.get('id'))
        
        # Get or create subscription platform record
        subscription_platform, _ = await ShopTurboSubscriptionPlatforms.objects.aget_or_create(
                channel=channel,
                platform_id=platform_subscription_id
            )
        
        # Map the data
        subscription_mapping = map_ecforce_subscription_data(
            subscription_data, workspace, channel
        )
        
        # Use async query to check if subscription exists
        # Check if subscription_platform has a source_subscription_id without triggering DB access
        source_subscription_id = getattr(subscription_platform, 'source_subscription_id', None)
        existing_subscription = await ShopTurboSubscriptions.objects.filter(
            id=source_subscription_id
        ).afirst() if source_subscription_id else None
        
        if existing_subscription:
            subscription = existing_subscription
            # Update existing subscription
            for field, value in subscription_mapping.items():
                # Use try/except instead of hasattr to avoid triggering descriptors
                try:
                    setattr(subscription, field, value)
                except AttributeError:
                    continue
        else:
            # Create new subscription
            subscription = await ShopTurboSubscriptions.objects.acreate(**subscription_mapping)
            subscription_platform.source_subscription = subscription
        
        await subscription.asave()
        await subscription_platform.asave()
        
        if mapping_custom_fields:
            logger.info(f"Mapping: {mapping_custom_fields}")
            logger.info(f"attributes: {attributes}")
            for key, sanka_field in mapping_custom_fields.items():
                try:
                    value = None
                    sanka_source_field = sanka_field.split('|')[0]
                    key_source = key.split('|')[0]
                    
                    if key_source in ECFORCE_SUBSCRIPTION_PROPERTY_MAPPING:
                        _,key = ECFORCE_SUBSCRIPTION_PROPERTY_MAPPING[key_source]
                        value = attributes.get(key, None)
                        if value:
                            custom_field = await ShopTurboSubscriptionsNameCustomField.objects.filter(
                                workspace=workspace, name=sanka_source_field
                            ).afirst()
                            if custom_field:
                                custom_value, _ = await ShopTurboSubscriptionsValueCustomField.objects.aget_or_create(
                                    field_name=custom_field, subscriptions=subscription
                                )
                                custom_value.value = str(value)[:500] if value else None
                                await custom_value.asave()
                except:
                    traceback.print_exc()
        
        return subscription
        
    except Exception as e:
        logger.error(f"Error importing subscription {platform_subscription_id}: {str(e)} - {traceback.print_exc()}")
        return None


async def import_ecforce_subscriptions(channel_id, mapping_custom_fields=None, import_date=None, lang='ja', history_id=None):
    """
    Import subscriptions from EC-Force
    """
    channel = await Channel.objects.select_related('integration', 'workspace').aget(id=channel_id)
    url = channel.app_id
    token = channel.access_token
    platform = channel.integration.slug
    workspace = channel.workspace
    error_logger = EcforceErrorLogger(lang=lang)
    
    with error_logger.function_context("import_ecforce_subscriptions"):
        try:
            error_logger.log_info("import_ecforce_subscriptions", f"Starting import for platform: {platform}")
            error_logger.log_info("import_ecforce_subscriptions", "Starting Ecforce Subscription import")

            try:
                total_subscriptions, total_pages, start_date = await get_ecforce_subscriptions(
                    url, token, import_date)
            except Exception as e:
                error_logger.log_api_error(
                    "import_ecforce_subscriptions",
                    f"Failed to connect to Ecforce: {str(e)}",
                    e,
                )
                raise
            if DEBUG_MODE:
                total_pages = 1
                total_subscriptions = 1
            try:
                if history_id:
                    task = await TransferHistory.objects.filter(
                            workspace=workspace, id=history_id
                        ).afirst()
                else:
                    task = await TransferHistory.objects.filter(
                            workspace=workspace, type="import_subscription", channel=channel
                        ).order_by("-created_at").afirst()

                error_logger.log_info(
                    "import_ecforce_subscriptions",
                    f"TransferHistory task initialized: {task.id}",
                )
            except Exception as e:
                error_logger.log_database_error(
                    "import_ecforce_subscriptions",
                    f"Failed to update TransferHistory: {str(e)}",
                    e,
                )
                raise
            
            if task:
                task.total_number = total_subscriptions
                await task.asave()
                
            # Time delay in seconds for rate limiting
            request_time_delay = REQUEST_TIME_DELAY_S
            max_retries = MAX_RETRIES
            
            for page in range(1, total_pages + 1):
                error_logger.log_info("import_ecforce_subscriptions", f'Processing page {page} of {total_pages}')
                valid_to_import = True
                
                for attempt in range(max_retries):
                    subscriptions, customers, sub_order_items = await get_ecforce_subscriptions_buffer_page(
                        url, token, page, start_date)
                    if DEBUG_MODE:
                        subscriptions = [subscriptions[0]]
                    if not subscriptions or type(subscriptions) != list:
                        message = subscriptions
                        error_logger.log_import_error("import_ecforce_subscriptions", 
                                                    f"Page {page} - Got limit request with message = {message}")
                        if attempt < max_retries - 1:
                            error_logger.log_info("import_ecforce_subscriptions", 
                                                f"Rate limited at requesting page {page}. Retrying in {request_time_delay} seconds (attempt {attempt + 1}/{max_retries})")
                            time.sleep(request_time_delay)
                            continue
                        error_logger.log_info("import_ecforce_subscriptions", 
                                            f"Page {page} - Max retries reached for rate limiting")
                        valid_to_import = False
                    else:
                        break
                    
                if not valid_to_import:
                    continue
                
                error_logger.log_info("import_ecforce_subscriptions", f"Found {len(subscriptions)} subscriptions to import")
                
                for subscription_data in subscriptions:
                    # Check task status while processing
                    if "task" in locals() and task:
                        task = await TransferHistory.objects.filter(
                                workspace=workspace, id=task.id
                            ).afirst()
                        if task.status == 'canceled':
                            break
                    
                    try:
                        attributes = subscription_data.get('attributes', {})
                        subscription_id = attributes.get('id')
                        
                        error_logger.log_info("import_ecforce_subscriptions", f'Processing subscription: {subscription_id}')
                        
                        # Import the subscription
                        subscription = await import_ecforce_subscription(subscription_data, channel, workspace, mapping_custom_fields)

                        if subscription:
                            task.success_number += 1
                        else:
                            task.failed_number += 1
                            
                        task.progress = 100 * (task.success_number) / task.total_number
                        await task.asave()
                        
                    except Exception as e:
                        error_logger.log_import_error("import_ecforce_subscriptions",
                                                    f"Error processing subscription", e)
                        error_logger.log_info("import_ecforce_subscriptions", traceback.format_exc())
                        task.failed_number += 1
                        await task.asave()
                        continue

            return True
            
        except Exception as e:
            error_logger.log_api_error(
                "import_ecforce_subscriptions", f"Import failed: {str(e)}", e
            )
            logger.error(f"Error importing Ecforce Subscriptions: {e}")
            traceback.print_exc()
            if "task" in locals():
                task.status = "failed"
                await task.asave()
            return False
        finally:
            # Save error logs to TransferHistory if any errors occurred
            if error_logger.has_errors() and "task" in locals():
                try:
                    await error_logger.asave_to_transfer_history(
                        task,
                        f"subscriptions_import_errors_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    )
                    error_logger.log_info(
                        "import_ecforce_subscriptions",
                        f"Saved {error_logger.get_error_count()} errors to CSV file",
                    )
                except Exception as e:
                    logger.error(f"Failed to save error CSV: {str(e)}")
