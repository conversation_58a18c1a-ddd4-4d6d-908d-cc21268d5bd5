from datetime import timedelta
import traceback

from asgiref.sync import sync_to_async
from hatchet_sdk import Context

from data.models import TransferHistory
from utils.bgjobs.handler import (
    aio_set_bg_job_completed,
    aio_set_bg_job_failed,
    aio_set_bg_job_running,
)
from utils.eccube import import_eccube_orders as eccube_import_util
from utils.logger import logger

from ..models import ImportOrdersEccubePayload
from ..workflows import import_eccube_orders


@import_eccube_orders.task(
    name="ImportEccubeOrdersTask",
    execution_timeout=timedelta(hours=2),
    schedule_timeout=timedelta(hours=1),
)
async def import_eccube_orders_task(
    input: ImportOrdersEccubePayload, ctx: Context
) -> dict:
    """
    Child task for importing EC-CUBE orders
    """
    logger.info("Run EC-CUBE orders import child task")
    logger.info(f"Input: {input}")

    history_id = input.history_id
    task = None

    try:
        task = await TransferHistory.objects.select_related("workspace", "user").aget(
            id=history_id
        )
        task.keep_alive = True
        await task.asave()
    except TransferHistory.DoesNotExist:
        logger.warning("TransferHistory does not exist")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])

    if input.background_job_id:
        await aio_set_bg_job_running(input.background_job_id)

    try:
        # Call the EC-CUBE import utility
        await sync_to_async(eccube_import_util)(input.channel_id)

        logger.info("Successfully imported EC-CUBE orders")

        if task:
            await task.arefresh_from_db()
            task.status = "completed"
            await task.asave(update_fields=["status"])

        if input.background_job_id:
            await aio_set_bg_job_completed(input.background_job_id)

    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error importing ec-cube orders: {str(e)}")
        if input.background_job_id:
            await aio_set_bg_job_failed(input.background_job_id)
        if task:
            task.status = "failed"
            await task.asave(update_fields=["status"])
