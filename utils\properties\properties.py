import ast
import json
import traceback

from django.apps import apps
from django.core.cache import cache
from django.db.models import Q, Sum
import pytz

from data.constants.constant import (
    BILL_OBJECTS,
    DEFAULT_COLUMNS_INVENTORY,
    DEFAULT_COLUMNS_ITEM,
    DEFAULT_COLUMNS_ORDER,
    DEFAULT_COLUMNS_WORKFLOW,
    DEFAULT_FORM_FIELD_COMMERCE,
    DEFAULT_FORM_FIELD_WORKFLOW,
    FORMULA_RELATED_COLUMNS_DISPLAY,
    INVENTORY_TRANSACTION_COLUMNS_DISPLAY,
    INVOICE_COLUMNS_DISPLAY,
    OBJECT_GROUP_TYPE,
    OBJECT_GROUP_TYPE_SINGULAR,
    ORDERS_COLUMNS_DISPLAY,
    SEARCHABLE_PROPERTY_TYPES,
    SEARCH_COLUMNS_DISPLAY,
    SUBSCRIPTIONS_COLUMNS_DISPLAY,
)
from data.constants.properties_constant import (
    DEFAULT_OBJECT_DISPLAY,
    OBJECT_TYPE_TO_SLUG,
    OBJECT_TYPE_TO_URL_NAME,
    OBJ_CUSTOM_FIELD_MAP_PAGE_GROUP_TYPE,
    SIMPLE_TYPE_MAPPING,
    TYPE_OBJECTS,
    TYPE_OBJECT_BILL,
    TYPE_OBJECT_CASE,
    TYPE_OBJECT_CASE_LINE_ITEM,
    TYPE_OBJECT_COMMERCE_METER,
    TYPE_OBJECT_COMPANY,
    TYPE_OBJECT_CONTACT,
    TYPE_OBJECT_CONVERSATION,
    TYPE_OBJECT_CUSTOM_OBJECT,
    TYPE_OBJECT_DELIVERY_NOTE,
    TYPE_OBJECT_ESTIMATE,
    TYPE_OBJECT_EXPENSE,
    TYPE_OBJECT_INVENTORY,
    TYPE_OBJECT_INVENTORY_TRANSACTION,
    TYPE_OBJECT_INVENTORY_WAREHOUSE,
    TYPE_OBJECT_INVOICE,
    TYPE_OBJECT_ITEM,
    TYPE_OBJECT_JOBS,
    TYPE_OBJECT_JOURNAL,
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_ORDER_LINE_ITEM,
    TYPE_OBJECT_PURCHASE_ORDER,
    TYPE_OBJECT_RECEIPT,
    TYPE_OBJECT_SESSION_EVENT,
    TYPE_OBJECT_SLIP,
    TYPE_OBJECT_SUBSCRIPTION,
    TYPE_OBJECT_TASK,
    TYPE_OBJECT_WORKFLOW,
)
from data.models import (
    Association,
    AssociationLabel,
    Channel,
    Company,
    CompanyNameCustomField,
    Contact,
    ContactsNameCustomField,
    CustomProperty,
    Deals,
    DealsNameCustomField,
    InventoryTransaction,
    InventoryTransactionNameCustomField,
    InventoryWarehouse,
    Invoice,
    ObjectManager,
    PropertySet,
    PurchaseItems,
    ShopTurboInventoryNameCustomField,
    ShopTurboItems,
    ShopTurboItemsNameCustomField,
    ShopTurboItemsOrdersNameCustomField,
    ShopTurboItemsPrice,
    ShopTurboOrders,
    ShopTurboOrdersNameCustomField,
    ShopTurboSubscriptions,
    ShopTurboSubscriptionsNameCustomField,
    Task,
    TaskCustomFieldName,
    User,
    WorkerNameCustomField,
)
from utils.contact import get_order_customer_custom_property
from utils.currency_symbols.currency_symbols import CurrencySymbols
from utils.discord import DiscordNotification
from utils.inventory import get_and_update_inventory_amount
from utils.logger import logger
from utils.properties.model_properties import get_model_columns
from utils.properties.non_default_properties import (
    get_custom_properties,
    get_custom_properties_ids,
    get_integration_properties,
)
from utils.properties.page_object import get_page_object
from utils.utility import get_attr, is_valid_uuid, reorder_columns, translate_language
from utils.association_label_utils import get_association_label_display_name


def safe_format_id(id_value, padding=4):
    """
    Safely format an ID value with zero-padding, handling both integers and strings.

    Args:
        id_value: The ID value to format (can be int, str, or None)
        padding: Number of digits to pad to (default: 4)

    Returns:
        Formatted string with zero-padding, or empty string if None
    """
    if id_value is None:
        return ""

    try:
        # If it's already a string that looks like a formatted ID, return as-is
        if isinstance(id_value, str):
            # Check if it's already properly formatted (all digits)
            if id_value.isdigit():
                # Re-format to ensure consistent padding
                return f"{int(id_value):0{padding}d}"
            else:
                # Return as-is if it's not a pure number string
                return str(id_value)

        # If it's a number, format it
        return f"{int(id_value):0{padding}d}"
    except (ValueError, TypeError):
        # Fallback: return string representation
        return str(id_value)


def get_report_metrics(workspace):
    props = {}

    for data_source in [
        "orders",
        "subscriptions",
        "contacts",
        "companies",
        "events",
        "deals",
        "inventory",
    ]:
        base_props = []
        custom_props = []

        if data_source == "orders":
            base_props = get_base_props(ShopTurboOrders, data_source, ["kanban_order"])
            for _prop in base_props:
                if _prop["name"] == "status":
                    _prop["values"] = ["active", "archived"]
                    break
            custom_props = get_custom_props(
                ShopTurboOrdersNameCustomField, data_source, workspace
            )

        elif data_source == "subscriptions":
            base_props = get_base_props(ShopTurboSubscriptions, data_source)
            for _prop in base_props:
                if _prop["name"] == "status":
                    _prop["values"] = [
                        "draft",
                        "active",
                        "paused",
                        "canceled",
                        "archived",
                    ]
                    break
            custom_props = get_custom_props(
                ShopTurboSubscriptionsNameCustomField, data_source, workspace
            )

        elif data_source == "contacts":
            base_props = get_base_props(Contact, data_source)
            custom_props = get_custom_props(
                ContactsNameCustomField, data_source, workspace
            )

        elif data_source == "companies":
            base_props = get_base_props(Company, data_source)
            custom_props = get_custom_props(
                CompanyNameCustomField, data_source, workspace
            )

        elif data_source == "deals":
            base_props = get_base_props(Deals, data_source)
            custom_props = get_custom_props(
                DealsNameCustomField, data_source, workspace
            )

        elif data_source == "inventory":
            base_props = get_base_props(InventoryTransaction, data_source)
            custom_props = get_custom_props(
                InventoryTransactionNameCustomField, data_source, workspace
            )

        props[data_source] = {"base": base_props, "custom": custom_props}

    return props


def get_base_props(model, data_source, excludes=[]):
    properties = []
    for field in model._meta.get_fields():
        field_type = field.get_internal_type()
        if (
            field_type
            in [
                "ForeignKey",
                "UUIDField",
                "JSONField",
                "URLField",
                "FileField",
                "ManyToManyField",
            ]
            or "url" in field.name
            or field.name in excludes
        ):
            continue

        properties.append(
            get_prop_from_field(model, field.name, field_type, data_source)
        )

    for field in model._meta.many_to_many:
        value = field.name
        properties.append(get_prop_from_field(model, value, "object", data_source))

    return properties


def get_custom_props(model, data_source, workspace):
    properties = []
    custom_fields = model.objects.filter(workspace=workspace)
    for ct_field in custom_fields:
        try:
            type_mapped = SIMPLE_TYPE_MAPPING.get(
                ct_field.type, "string"
            )  # Set default to string
            properties.append(
                {
                    "name": f"{ct_field.name}",
                    "type": type_mapped,
                    "source": data_source,
                    "values": [],
                }
            )
        except Exception as e:
            logger.error(f"[ERROR] === properties.py - 36: {e}")

    return properties


def get_prop_from_field(model, field_name, field_type, data_source):
    property = {
        "name": field_name,
        "type": "string",
        "values": [],
        "source": data_source,
    }

    if field_type in ["CharField", "TextField"]:
        property["type"] = "string"
        choices = model._meta.get_field(field_name).choices
        if choices:
            property["values"] = choices
    elif field_type in ["IntegerField", "FloatField"]:
        property["type"] = "numeric"
    elif field_type in ["BooleanField"]:
        property["type"] = "boolean"
        property["values"] = ["true", "false"]
    elif field_type in ["DateTimeField"]:
        property["type"] = "datetime"
    elif field_type in ["object"]:
        property["type"] = "object"

    return property


def base_model_to_object_type(base_model_ref):
    """Builds the map from base_model class to object_type string."""
    for obj_type in OBJECT_TYPE_TO_SLUG:
        try:
            page_config = get_page_object(obj_type)
            base_model = page_config.get("base_model")
            if base_model == base_model_ref:
                return obj_type
        except Exception as e:
            # Handle cases where get_page_object might fail for a type or type is invalid
            logger.warning(f"[WARN] Could not map object_type '{obj_type}': {e}")
    return None


def get_property_type(property, default_properties_dict, model):
    if property in default_properties_dict.keys():
        property_type = default_properties_dict[property].lower()

        if any(item in property_type for item in ["integer", "float"]):
            return "number"
        elif any(item in property_type for item in ["date"]):
            return "date_time"
        elif model._meta.get_field(property).choices:
            return "choice"
        elif "image_" in property:
            return "profile_pic"
        # Association
        elif any(item in property_type for item in ["foreignkey", "onetoonefield"]):
            if property == "user":
                return "user"
            return "association"
    elif property == "owner":
        return "user"
    elif property == "customer":
        return "association"

    # === Purchase Order
    elif property == "supplier":
        return "association"
    elif property == "item_price":
        return "item_price"
    elif property == "item_price_without_tax":
        return "item_price_without_tax"
    # =====

    elif property in ["items", "item", "source_item"]:
        return "items"
    elif property == "durations":
        return "date_time"
    elif property in [
        "item-price",
        "line_item_price",
        "line_item_price_without_tax",
        "line_item_quantity",
    ]:
        return "number"
    elif property == "line_item":
        return "line_item"

    # Accounting
    elif property == "counter_category":
        return "choice"
    elif property == "tax_rate":
        return "choice"

    elif property == "sub_tasks":
        return "task_object"

    # Override related Inventory
    elif property in [
        "inventory__total_inventory",
        "inventory__available_amount",
        "inventory__unavailable_amount",
        "inventory__committed_amount",
    ]:
        return "number"

    # Override Association
    elif property in [
        "journal_entry",
        "journalentry",
        "customers",
        "inventory_transactions",
        "associate#purchase_orders",
        "partner",
        "invoice",
        "estimate",
        "orders",
        "item_name",
        TYPE_OBJECT_INVOICE,
        TYPE_OBJECT_ESTIMATE,
        "tasks",
        "assignee",
        TYPE_OBJECT_SUBSCRIPTION,
        TYPE_OBJECT_PURCHASE_ORDER,
        TYPE_OBJECT_CASE,
        TYPE_OBJECT_DELIVERY_NOTE,
        TYPE_OBJECT_ORDER,
        TYPE_OBJECT_INVENTORY,
        TYPE_OBJECT_INVENTORY_TRANSACTION,
        TYPE_OBJECT_TASK,
        TYPE_OBJECT_RECEIPT,
        TYPE_OBJECT_EXPENSE,
        TYPE_OBJECT_COMPANY,
        TYPE_OBJECT_CONTACT,
        TYPE_OBJECT_BILL,
        TYPE_OBJECT_ITEM,
        TYPE_OBJECT_INVENTORY_WAREHOUSE,
    ]:
        return "association"

    return "string"


def get_object_display_based_columns(page_group_type, object, columns, timezone, lang):
    """
    Get display string for an object based on its columns.

    Args:
        page_group_type (str): Type of page group
        object (Model or dict): Django model instance or dictionary representation
        columns (list): List of column names to include in display
        timezone (str): Timezone string
        lang (str): Language code

    Returns:
        str: Formatted display string containing values from specified columns
    """
    res = ""
    if page_group_type == TYPE_OBJECT_ITEM:
        if not object:  # Add this check
            return res  # Return early if object is None

        # Check if object is a dictionary (from model_to_dict) instead of a model instance
        is_dict = isinstance(object, dict)

        if not is_dict:
            # Handle ShopTurboItemsOrders objects that might be passed with TYPE_OBJECT_ITEM
            if object.__class__.__name__ == "ShopTurboItemsOrders":
                # For ShopTurboItemsOrders, we should display the item information if available
                if object.item:
                    # Recursively call with the actual item
                    return get_object_display_based_columns(
                        page_group_type, object.item, columns, timezone, lang
                    )
                else:
                    # If no item, use custom_item_name
                    return object.custom_item_name or str(object.id)

        # Get object ID for debugging - handle both dict and model instance
        object_id = object.get("id") if is_dict else getattr(object, "id", "unknown")
        logger.debug(
            f"[DEBUG] get_object_display_based_columns for item {object_id} with columns: {columns}"
        )
        logger.debug(
            f"[DEBUG] Object type: {'dict' if is_dict else type(object).__name__}"
        )

        for col in columns:
            logger.debug(f"[DEBUG] Processing column: '{col}'")
            try:
                if col == "item_id":
                    if object:
                        # Handle both dict and model instance
                        item_id = (
                            object.get("item_id")
                            if is_dict
                            else getattr(object, "item_id", None)
                        )
                        if item_id is not None:
                            res += f"#{safe_format_id(item_id)} "
                elif col == "name":
                    try:
                        if not is_dict:
                            instance = type(object)
                            # Handle objects that don't have workspace attribute directly
                            workspace = None
                            if hasattr(object, "workspace"):
                                workspace = object.workspace
                            elif hasattr(object, "order") and hasattr(
                                object.order, "workspace"
                            ):
                                workspace = object.order.workspace

                            if workspace:
                                cp = CustomProperty.objects.get(
                                    workspace=workspace,
                                    model=instance._meta.db_table,
                                    name=col,
                                )
                                val = ast.literal_eval(cp.value)
                                if hasattr(object, "name") and object.name:
                                    res += object.name[: val["max_text_length"]] + " "
                        else:
                            # Handle dictionary case - just get the name value
                            name = object.get("name")
                            if name:
                                res += str(name) + " "
                    except:
                        # Fallback for both dict and model instance
                        name = (
                            object.get("name")
                            if is_dict
                            else getattr(object, "name", None)
                        )
                        if name:
                            res += str(name) + " "
                elif col == "platform":
                    if object:
                        platform = (
                            object.get("platform")
                            if is_dict
                            else getattr(object, "platform", None)
                        )
                        if platform:
                            res += f"{platform} "
                elif col == "status":
                    if is_dict:
                        status = object.get("status")
                        if status:
                            res += f"{status} "
                    else:
                        res += f"{object.get_status_display()} "
                elif col == "description":
                    description = (
                        object.get("description")
                        if is_dict
                        else getattr(object, "description", None)
                    )
                    if description:
                        res += f"{str(description)[:25]} "
                elif col == "price":
                    if is_dict:
                        # For dictionaries, we can't easily get related price data
                        # Just skip or use a default value
                        currency = object.get("currency", "")
                        res += f"{currency} 0 "
                    else:
                        currency = object.currency
                        try:
                            symbol = CurrencySymbols.get_symbol(object.currency)
                            if symbol is None:
                                currency = symbol
                        except:
                            pass
                        price = 0
                        item_price = ShopTurboItemsPrice.objects.filter(
                            item=object, default=True
                        ).first()
                        if item_price:
                            price = getattr(item_price, "price", 0)
                        res += f"{currency} {price} "
                elif col == "purchase_price":
                    purchase_price = (
                        object.get("purchase_price")
                        if is_dict
                        else getattr(object, "purchase_price", None)
                    )
                    if purchase_price:
                        currency = (
                            object.get("currency")
                            if is_dict
                            else getattr(object, "currency", "")
                        )
                        if not is_dict:
                            try:
                                symbol = CurrencySymbols.get_symbol(object.currency)
                                if symbol is None:
                                    currency = symbol
                            except:
                                pass
                        res += f"{currency} {purchase_price} "
                elif col == "tax":
                    if is_dict:
                        # For dictionaries, we can't easily get related price data
                        res += "0 % "
                    else:
                        tax = 0
                        item_price = ShopTurboItemsPrice.objects.filter(
                            item=object, default=True
                        ).first()
                        if item_price:
                            tax = getattr(item_price, "tax", 0)
                        res += f"{tax} % "
                elif "-item id" in col.lower() and col.startswith("item_platform|"):
                    if not is_dict:
                        platform_name, _ = (
                            col.lower().replace("item_platform|", "").split("-item id")
                        )
                        try:
                            res += f"{object.get_display_name()[platform_name]} "
                        except Exception:
                            pass
                elif "-sku" in col.lower() and col.startswith("item_platform|"):
                    if not is_dict:
                        platform_name, _ = (
                            col.lower().replace("item_platform|", "").split("-sku")
                        )
                        try:
                            res += f"{object.get_platform_sku()[platform_name]} "
                        except Exception:
                            pass
                elif "inventory__" in col.lower():
                    if not is_dict:
                        # Map inventory column names to inventory types
                        inventory_col_mapping = {
                            "inventory__total_inventory": "total_inventory",
                            "inventory__available_amount": "available_inventory_amount",
                            "inventory__unavailable_amount": "unavailable_inventory_amount",
                            "inventory__committed_amount": "committed_inventory_amount",
                        }

                        inventory_col = inventory_col_mapping.get(col.lower())
                        inventory_amount = getattr(object, inventory_col, None)

                        # Add inventory amount if it's not None
                        if inventory_amount is not None:
                            res += f"({int(inventory_amount)}) "
                elif "platform_ids" in col.lower():
                    if not is_dict:
                        platforms_ids = []
                        platforms = object.item.filter(platform_type="default")
                        for platform in platforms:
                            platforms_ids.append(platform.platform_id)
                        res += " - ".join(platforms_ids) + " "
                elif "| child" in col.lower():
                    if not is_dict:
                        obj_list = []
                        property_values = object.property_child_item.all().order_by(
                            "-created_at"
                        )
                        for property_value in property_values:
                            if property_value.items not in obj_list:
                                obj_list.append(property_value.items)
                        res += " ".join(obj_list) + " "
                elif col == "updated_at":
                    updated_at = (
                        object.get("updated_at")
                        if is_dict
                        else getattr(object, "updated_at", None)
                    )
                    if updated_at:
                        if is_dict and isinstance(updated_at, str):
                            # If it's a string from dict, just display it
                            res += f"{updated_at} "
                        elif hasattr(updated_at, "astimezone"):
                            res += f"{updated_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "created_at":
                    created_at = (
                        object.get("created_at")
                        if is_dict
                        else getattr(object, "created_at", None)
                    )
                    if created_at:
                        if is_dict and isinstance(created_at, str):
                            # If it's a string from dict, just display it
                            res += f"{created_at} "
                        elif hasattr(created_at, "astimezone"):
                            res += f"{created_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "warehouse_inventory_amount":
                    warehouses = InventoryWarehouse.objects.filter(
                        usage_status="active",
                        workspace=workspace,
                        shopturboinventory__item=object,
                        shopturboinventory__status="active",
                        shopturboinventory__item__status="active",
                    )
                    total_warehouse_inventory = warehouses.values("warehouse").annotate(
                        total_inventory=Sum("shopturboinventory__total_inventory")
                    )
                    for warehouse in total_warehouse_inventory:
                        res += f"{warehouse['warehouse']}: {warehouse['total_inventory']} | "
                else:
                    # Custom Property - only works with model instances, not dictionaries
                    if not is_dict:
                        custom_prop_val = (
                            object.shopturbo_item_custom_field_relations.filter(
                                field_name__id=str(col)
                            ).first()
                        )
                        if not custom_prop_val:
                            continue
                        res += custom_prop_display_for_object(
                            custom_prop_val, timezone, lang
                        )

            except Exception as e:
                logger.debug(f"[DEBUG] Error processing column '{col}': {e}")
                traceback.print_exc()
                # Don't send Discord notification for dict vs object issues - they're expected now
                if not is_dict:
                    DiscordNotification().send_message(
                        f"Error in get_object_display_based_columns: {traceback.format_exc()}"
                    )
                continue  # Continue processing other columns instead of returning early
    elif page_group_type == TYPE_OBJECT_INVENTORY:
        res = ""
        for col in columns:
            if col == "inventory_id":
                res += f"#{safe_format_id(object.inventory_id)} "
            elif col == "items":
                items = []
                for val in object.item.all():
                    items.append(f"#{safe_format_id(val.item_id)} {val.name}")
                items_str = ",".join(items)
                res += f"{items_str} "
            elif col == "warehouse":
                if object.warehouse and object.warehouse.location:
                    res += f"{object.warehouse.location} "
            elif col == "status":
                res += f"{object.get_status_display()} "
            elif col in ["available", "committed", "unavailable", "total_inventory"]:
                res += f"{get_and_update_inventory_amount(object)} "
            elif col == "inventory_value":
                try:
                    res += f"{int(object.inventory_value)} "
                except:
                    continue
            elif col == "updated_at":
                res += f"{object.updated_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
            elif col == "created_at":
                res += f"{object.created_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
            elif col == "date":
                if object.date:
                    res += f"{object.date.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y')} "
            elif "|" in col:
                parent_object, col_ = col.split("|")
                if parent_object != TYPE_OBJECT_ITEM:
                    continue
                object_ = object.item.first()
                res += f"{get_object_display_based_columns(parent_object, object_, [col_], timezone, lang)} "
            else:
                # Custom Property
                try:
                    custom_prop_val = (
                        object.shopturbo_inventory_custom_field_relations.filter(
                            field_name__id=str(col)
                        ).first()
                    )
                except:
                    continue
                if not custom_prop_val:
                    continue
                res += custom_prop_display_for_object(custom_prop_val, timezone, lang)
    elif page_group_type == TYPE_OBJECT_INVENTORY_TRANSACTION:
        res = ""
        for col in columns:
            if col == "transaction_id":
                res += f"#{safe_format_id(object.transaction_id)} "
            elif col == "inventory":
                try:
                    # Handle objects that don't have workspace attribute directly
                    workspace = None
                    if hasattr(object, "workspace"):
                        workspace = object.workspace
                    elif hasattr(object, "order") and hasattr(
                        object.order, "workspace"
                    ):
                        workspace = object.order.workspace
                    elif hasattr(object, "inventory") and hasattr(
                        object.inventory, "workspace"
                    ):
                        workspace = object.inventory.workspace

                    if workspace:
                        om, _ = ObjectManager.objects.get_or_create(
                            workspace=workspace, page_group_type=TYPE_OBJECT_INVENTORY
                        )
                        columns_ = om.column_display.split(",")
                        inventory_str = get_object_display_based_columns(
                            TYPE_OBJECT_INVENTORY,
                            object.inventory,
                            columns_,
                            timezone,
                            lang,
                        )
                    else:
                        inventory_str = ""
                except:
                    inventory_str = ""
                res += f"{inventory_str} "
            elif col == "inventory_type":
                try:
                    res += f"{INVENTORY_TRANSACTION_COLUMNS_DISPLAY[object.inventory.inventory_status]} "
                except:
                    res += f"{object.inventory.inventory_status} "
            elif col == "amount":
                res += f"{object.amount} "
            elif col == "transaction_amount":
                res += f"{object.transaction_amount} "
            elif col == "user":
                res += f"{object.user.first_name} "
            elif col == "transaction_type":
                res += f"{object.get_transaction_type_display()} "
            elif col == "updated_at":
                res += f"{object.updated_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
            elif col == "created_at":
                res += f"{object.created_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
            elif col == "transaction_date":
                res += f"{object.transaction_date.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
            elif "|" in col:
                parent_object, col_ = col.split("|")
                if parent_object == TYPE_OBJECT_ITEM:
                    object_ = object.inventory.item.first()
                    res += f"{get_object_display_based_columns(parent_object, object_, [col_], timezone, lang)} "
                elif parent_object == TYPE_OBJECT_INVENTORY:
                    object_ = object.inventory
                    res += f"{get_object_display_based_columns(parent_object, object_, [col_], timezone, lang)} "
            else:
                # Custom Property
                custom_prop_val = (
                    object.inventory_transaction_custom_field_relations.filter(
                        field_name__id=str(col)
                    ).first()
                )
                if not custom_prop_val:
                    continue
                res += custom_prop_display_for_object(custom_prop_val, timezone, lang)
    elif page_group_type == TYPE_OBJECT_EXPENSE:
        res = ""
        for col in columns:
            if col == "id_pm":
                res += f"#{safe_format_id(object.id_pm)} "
            elif col == "partner":
                if object.contact:
                    res += f"#{safe_format_id(object.contact.contact_id)} - {object.contact.name} "
                elif object.company:
                    res += f"#{safe_format_id(object.company.company_id)} - {object.company.name} "
            elif col == "submitter__first_name":
                if object.submitter:
                    res += f"{object.submitter.first_name} "
            elif col == "status":
                res += f"{object.status} "
            elif col == "amount":
                res += f"{object.amount} "
            elif col == "due_date":
                res += f"{object.due_date.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
            elif col == "description":
                res += f"{object.description} "
            else:
                # Custom Property
                custom_prop_val = object.expense_custom_field_relations.filter(
                    field_name__id=str(col)
                ).first()
                if not custom_prop_val:
                    continue
                res += custom_prop_display_for_object(custom_prop_val, timezone, lang)
    elif page_group_type == TYPE_OBJECT_ORDER:
        res = ""
        for col in columns:
            if col == "order_id":
                res += f"#{safe_format_id(object.order_id)} "
            else:
                # Custom Property
                custom_prop_val = object.shopturbo_custom_field_relations.filter(
                    field_name__id=str(col)
                ).first()
                if not custom_prop_val:
                    continue
                res += custom_prop_display_for_object(custom_prop_val, timezone, lang)
    elif page_group_type == TYPE_OBJECT_SUBSCRIPTION:
        res = ""
        for col in columns:
            if col == "subscriptions_id":
                res += f"#{safe_format_id(object.subscriptions_id)} "
            elif col == "item_name":
                items = []
                for val in object.shopturboitemssubscriptions_set.all():
                    if val.item:
                        items.append(
                            f"#{safe_format_id(val.item.item_id)} {val.item.name}"
                        )
                    else:
                        items.append(val.custom_item_name)
                items_str = ",".join(items)
                res += f"{items_str} "
            elif col == "customer":
                if object.contact:
                    res += f"#{safe_format_id(object.contact.contact_id)} - {object.contact.name} "
                elif object.company:
                    res += f"#{safe_format_id(object.company.company_id)} - {object.company.name} "
            elif col == "durations":
                if object.start_date and object.end_date:
                    res += f"{object.start_date.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y')} - {object.end_date.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y')}"
                else:
                    until = "Indefinitely"
                    if lang == "ja":
                        until = "無期限"
                    res += f"{object.start_date.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y')} - {until}"
            elif col == "total_price":
                currency = object.currency
                try:
                    symbol = CurrencySymbols.get_symbol(object.currency)
                    if symbol is None:
                        currency = symbol
                except:
                    pass
                res += f"{currency} {object.total_price} "
                res += f"{object}"
            elif col == "created_at":
                res += f"{object.created_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
            elif col == "subscription_status":
                if object.subscription_status:
                    subscription_status = translate_language(
                        object.subscription_status, lang
                    )
                    res += f"{subscription_status}"
            elif col == "tax":
                res += f"{object.tax_rate} %"
            else:
                # Custom Property
                custom_prop_val = (
                    object.shopturbo_subscriptions_custom_field_relations.filter(
                        field_name__id=str(col)
                    ).first()
                )
                if not custom_prop_val:
                    continue
                res += custom_prop_display_for_object(custom_prop_val, timezone, lang)
    elif page_group_type == TYPE_OBJECT_INVOICE:
        res = ""
        for col in columns:
            try:
                if res != "":
                    res += "- "

                if col == "id_inv":
                    res += f"#{safe_format_id(object.id_inv)} "
                elif col == "customers":
                    if object.contact:
                        res += f"#{safe_format_id(object.contact.contact_id)} - {object.contact.name} "
                    elif object.company:
                        res += f"#{safe_format_id(object.company.company_id)} - {object.company.name} "
                elif col == "start_date":
                    if lang == "ja":
                        # locale.setlocale(locale.LC_ALL, 'ja_JP.UTF-8')
                        res += f"{object.start_date.strftime('%Y年%m月%d日')} "
                    else:
                        # locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
                        res += f"{object.start_date.strftime('%B %d, %Y')} "
                elif col == "due_date":
                    if lang == "ja":
                        # locale.setlocale(locale.LC_ALL, 'ja_JP.UTF-8')
                        res += f"{object.due_date.strftime('%Y年%m月%d日')} "
                    else:
                        # locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
                        res += f"{object.due_date.strftime('%B %d, %Y')} "
                elif col == "status":
                    res += f"{object.get_status_display()} "
                elif col == "total_price":
                    currency = object.currency
                    try:
                        symbol = CurrencySymbols.get_symbol(object.currency)
                        if symbol is None:
                            currency = symbol
                    except:
                        pass
                    if object.total_price:
                        res += f"{currency} {object.total_price:,.2f} "
                    else:
                        res += f"{currency} {object.total_price} "
                elif col == "total_price_without_tax":
                    currency = object.currency
                    try:
                        symbol = CurrencySymbols.get_symbol(object.currency)
                        if symbol is None:
                            currency = symbol
                    except:
                        pass
                    if object.total_price_without_tax:
                        res += f"{currency} {object.total_price_without_tax:,.2f} "
                    else:
                        res += f"{currency} {object.total_price_without_tax} "
                elif col == "created_at":
                    res += f"{object.created_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "updated_at":
                    res += f"{object.updated_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "currency":
                    res += f"{object.currency} "
                elif col == "notes":
                    res += f"{object.notes} "
                elif col == "send_from":
                    res += f"{object.send_from} "
                elif col == "tax_rate":
                    res += f"{object.tax_rate} % "
                elif col == "discount":
                    res += f"{object.discount} % "
                elif col == "discount_option":
                    res += f"{object.discount_option} "
                elif col in [
                    "customers__company__name",
                    "customers__contact__name",
                    "customers__contact__first_name",
                    "customers__contact__last_name",
                ]:
                    if object.contact:
                        res += f"{object.contact.name} "
                    elif object.company:
                        res += f"{object.company.name} "
                else:
                    # Custom Property
                    custom_prop_val = object.invoice_custom_field_relations.filter(
                        field_name__id=str(col)
                    ).first()
                    if not custom_prop_val:
                        continue
                    res += custom_prop_display_for_object(
                        custom_prop_val, timezone, lang
                    )
            except:
                pass
    elif page_group_type == TYPE_OBJECT_ESTIMATE:
        res = ""
        for col in columns:
            try:
                if res != "":
                    res += "- "

                if col == "id_est":
                    res += f"#{safe_format_id(object.id_est)} "
                elif col == "customers":
                    if object.contact:
                        res += f"#{safe_format_id(object.contact.contact_id)} - {object.contact.name} "
                    elif object.company:
                        res += f"#{safe_format_id(object.company.company_id)} - {object.company.name} "
                elif col == "start_date":
                    if lang == "ja":
                        # locale.setlocale(locale.LC_ALL, 'ja_JP.UTF-8')
                        res += f"{object.start_date.strftime('%Y年%m月%d日')} "
                    else:
                        # locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
                        res += f"{object.start_date.strftime('%B %d, %Y')} "
                elif col == "due_date":
                    if lang == "ja":
                        # locale.setlocale(locale.LC_ALL, 'ja_JP.UTF-8')
                        res += f"{object.due_date.strftime('%Y年%m月%d日')} "
                    else:
                        # locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
                        res += f"{object.due_date.strftime('%B %d, %Y')} "
                elif col == "status":
                    res += f"{object.get_status_display()} "
                elif col == "total_price":
                    currency = object.currency
                    try:
                        symbol = CurrencySymbols.get_symbol(object.currency)
                        if symbol is None:
                            currency = symbol
                    except:
                        pass
                    if object.total_price:
                        res += f"{currency} {object.total_price:,.2f} "
                    else:
                        res += f"{currency} {object.total_price} "
                elif col == "total_price_without_tax":
                    currency = object.currency
                    try:
                        symbol = CurrencySymbols.get_symbol(object.currency)
                        if symbol is None:
                            currency = symbol
                    except:
                        pass
                    if object.total_price_without_tax:
                        res += f"{currency} {object.total_price_without_tax:,.2f} "
                    else:
                        res += f"{currency} {object.total_price_without_tax} "
                elif col == "usage_status":
                    res += f"{object.get_usage_status_display()} "
                elif col == "invoice":
                    pass
                elif col == "created_at":
                    res += f"{object.created_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "updated_at":
                    res += f"{object.updated_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "currency":
                    res += f"{object.currency} "
                elif col == "notes":
                    res += f"{object.notes} "
                elif col == "send_from":
                    res += f"{object.send_from} "
                elif col == "tax_rate":
                    res += f"{object.tax_rate} % "
                elif col == "discount":
                    res += f"{object.discount} % "
                elif col == "discount_option":
                    res += f"{object.discount_option} "
                elif col in [
                    "customers__company__name",
                    "customers__contact__name",
                    "customers__contact__first_name",
                    "customers__contact__last_name",
                ]:
                    if object.contact:
                        res += f"{object.contact.name} "
                    elif object.company:
                        res += f"{object.company.name} "
                else:
                    # Custom Property
                    custom_prop_val = object.estimate_custom_field_relations.filter(
                        field_name__id=str(col)
                    ).first()
                    if not custom_prop_val:
                        continue
                    res += custom_prop_display_for_object(
                        custom_prop_val, timezone, lang
                    )
            except:
                pass
    elif page_group_type == TYPE_OBJECT_RECEIPT:
        res = ""
        for col in columns:
            try:
                if res != "":
                    res += "- "

                if col == "id_rcp":
                    res += f"#{safe_format_id(object.id_rcp)} "
                elif col == "customers":
                    if object.contact:
                        res += f"#{safe_format_id(object.contact.contact_id)} - {object.contact.name} "
                    elif object.company:
                        res += f"#{safe_format_id(object.company.company_id)} - {object.company.name} "
                elif col == "start_date":
                    if lang == "ja":
                        # locale.setlocale(locale.LC_ALL, 'ja_JP.UTF-8')
                        res += f"{object.start_date.strftime('%Y年%m月%d日')} "
                    else:
                        # locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
                        res += f"{object.start_date.strftime('%B %d, %Y')} "
                elif col == "status":
                    res += f"{object.get_status_display()} "
                elif col == "total_price":
                    currency = object.currency
                    try:
                        symbol = CurrencySymbols.get_symbol(object.currency)
                        if symbol is None:
                            currency = symbol
                    except:
                        pass
                    if object.total_price:
                        res += f"{currency} {object.total_price:,.2f} "
                    else:
                        res += f"{currency} {object.total_price} "
                elif col == "total_price_without_tax":
                    currency = object.currency
                    try:
                        symbol = CurrencySymbols.get_symbol(object.currency)
                        if symbol is None:
                            currency = symbol
                    except:
                        pass
                    if object.total_price_without_tax:
                        res += f"{currency} {object.total_price_without_tax:,.2f} "
                    else:
                        res += f"{currency} {object.total_price_without_tax} "
                elif col == "created_at":
                    res += f"{object.created_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "updated_at":
                    res += f"{object.updated_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "currency":
                    res += f"{object.currency} "
                elif col == "notes":
                    res += f"{object.notes} "
                elif col == "send_from":
                    res += f"{object.send_from} "
                elif col == "tax_rate":
                    res += f"{object.tax_rate} % "
                elif col in [
                    "customers__company__name",
                    "customers__contact__name",
                    "customers__contact__first_name",
                    "customers__contact__last_name",
                ]:
                    if object.contact:
                        res += f"{object.contact.name} "
                    elif object.company:
                        res += f"{object.company.name} "
                else:
                    # Custom Property
                    custom_prop_val = object.receipt_custom_field_relations.filter(
                        field_name__id=str(col)
                    ).first()
                    if not custom_prop_val:
                        continue
                    res += custom_prop_display_for_object(
                        custom_prop_val, timezone, lang
                    )
            except:
                pass
    elif page_group_type == TYPE_OBJECT_DELIVERY_NOTE:
        res = ""
        for col in columns:
            try:
                if res != "":
                    res += "- "

                if col == "id_ds":
                    res += f"#{safe_format_id(object.id_ds)} "
                elif col == "customers":
                    if object.contact:
                        res += f"#{safe_format_id(object.contact.contact_id)} - {object.contact.name} "
                    elif object.company:
                        res += f"#{safe_format_id(object.company.company_id)} - {object.company.name} "
                elif col == "start_date":
                    if lang == "ja":
                        # locale.setlocale(locale.LC_ALL, 'ja_JP.UTF-8')
                        res += f"{object.start_date.strftime('%Y年%m月%d日')} "
                    else:
                        # locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
                        res += f"{object.start_date.strftime('%B %d, %Y')} "
                elif col == "status":
                    res += f"{object.get_status_display()} "
                elif col == "total_price":
                    currency = object.currency
                    try:
                        symbol = CurrencySymbols.get_symbol(object.currency)
                        if symbol is None:
                            currency = symbol
                    except:
                        pass
                    if object.total_price:
                        res += f"{currency} {object.total_price:,.2f} "
                    else:
                        res += f"{currency} {object.total_price} "
                elif col == "total_price_without_tax":
                    currency = object.currency
                    try:
                        symbol = CurrencySymbols.get_symbol(object.currency)
                        if symbol is None:
                            currency = symbol
                    except:
                        pass
                    if object.total_price_without_tax:
                        res += f"{currency} {object.total_price_without_tax:,.2f} "
                    else:
                        res += f"{currency} {object.total_price_without_tax} "
                elif col == "created_at":
                    res += f"{object.created_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "updated_at":
                    res += f"{object.updated_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "currency":
                    res += f"{object.currency} "
                elif col == "notes":
                    res += f"{object.notes} "
                elif col == "send_from":
                    res += f"{object.send_from} "
                elif col == "tax_rate":
                    res += f"{object.tax_rate} % "
                elif col in [
                    "customers__company__name",
                    "customers__contact__name",
                    "customers__contact__first_name",
                    "customers__contact__last_name",
                ]:
                    if object.contact:
                        res += f"{object.contact.name} "
                    elif object.company:
                        res += f"{object.company.name} "
                else:
                    # Custom Property
                    custom_prop_val = (
                        object.delivery_slip_custom_field_relations.filter(
                            field_name__id=str(col)
                        ).first()
                    )
                    if not custom_prop_val:
                        continue
                    res += custom_prop_display_for_object(
                        custom_prop_val, timezone, lang
                    )
            except:
                pass
    elif page_group_type == TYPE_OBJECT_SLIP:
        res = ""
        for col in columns:
            try:
                if res != "":
                    res += "- "

                if col == "id_slip":
                    res += f"#{safe_format_id(object.id_slip)} "
                elif col == "customers":
                    if object.contact:
                        res += f"#{safe_format_id(object.contact.contact_id)} - {object.contact.name} "
                    elif object.company:
                        res += f"#{safe_format_id(object.company.company_id)} - {object.company.name} "
                elif col == "slip_type":
                    res += f"{object.get_slip_type_display()} "
                elif col == "start_date":
                    if lang == "ja":
                        # locale.setlocale(locale.LC_ALL, 'ja_JP.UTF-8')
                        res += f"{object.start_date.strftime('%Y年%m月%d日')} "
                    else:
                        # locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
                        res += f"{object.start_date.strftime('%B %d, %Y')} "
                elif col == "notes":
                    res += f"{object.notes} "
                elif col == "send_from":
                    res += f"{object.send_from} "
                elif col == "created_at":
                    res += f"{object.created_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "journal_entry":
                    res += f"{object.journal_entry} "
                elif col == "updated_at":
                    res += f"{object.updated_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "currency":
                    res += f"{object.currency} "
                elif col == "notes":
                    res += f"{object.notes} "
                elif col == "tax_rate":
                    res += f"{object.tax_rate} % "
                elif col in [
                    "customers__company__name",
                    "customers__contact__name",
                    "customers__contact__first_name",
                    "customers__contact__last_name",
                ]:
                    if object.contact:
                        res += f"{object.contact.name} "
                    elif object.company:
                        res += f"{object.company.name} "
                else:
                    # Custom Property
                    custom_prop_val = object.slip_custom_field_relations.filter(
                        field_name__id=str(col)
                    ).first()
                    if not custom_prop_val:
                        continue
                    res += custom_prop_display_for_object(
                        custom_prop_val, timezone, lang
                    )
            except:
                pass
    elif page_group_type == TYPE_OBJECT_INVENTORY_WAREHOUSE:
        res = ""
        for col in columns:
            if col == "id_iw":
                res += f"#{safe_format_id(object.id_iw)} "
            else:
                if is_valid_uuid(str(col)):
                    # Custom Property
                    custom_prop_val = (
                        object.inventory_warehouse_custom_field_relations.filter(
                            field_name__id=str(col)
                        ).first()
                    )
                    if not custom_prop_val:
                        continue
                    res += custom_prop_display_for_object(
                        custom_prop_val, timezone, lang
                    )
                else:
                    value = getattr(object, str(col), None)
                    if value:
                        res += f"{value} "
    elif page_group_type == TYPE_OBJECT_PURCHASE_ORDER:
        res = ""
        for col in columns:
            if col == "id_po":
                res += f"#{safe_format_id(object.id_po)} "
            else:
                # Custom Property
                custom_prop_val = object.purchase_order_field_relations.filter(
                    field_name__id=str(col)
                ).first()
                if not custom_prop_val:
                    continue
                res += custom_prop_display_for_object(custom_prop_val, timezone, lang)
    elif page_group_type == TYPE_OBJECT_BILL:
        res = ""
        for col in columns:
            if col == "id_bill":
                res += f"#{safe_format_id(object.id_bill)} "
            else:
                if is_valid_uuid(str(col)):
                    # Custom Property
                    custom_prop_val = object.bill_custom_field_relations.filter(
                        field_name__id=str(col)
                    ).first()
                    if not custom_prop_val:
                        continue
                    res += custom_prop_display_for_object(
                        custom_prop_val, timezone, lang
                    )
                else:
                    res += getattr(object, str(col))

    elif page_group_type == TYPE_OBJECT_CASE:
        res = ""
        for col in columns:
            if col == "deal_id":
                res += f"#{safe_format_id(object.deal_id)} "
            elif col == "name":
                res += f"{object.name} "
            elif col == "customer":
                if object.contact:
                    if object.contact.first():
                        res += object.contact.first().name
                elif object.company:
                    if object.company.first():
                        res += object.company.first().name
            elif col == "updated_at":
                res += f"{object.updated_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
            elif col == "created_at":
                res += f"{object.created_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
            else:
                if is_valid_uuid(str(col)):
                    # Custom Property
                    custom_prop_val = object.deals_custom_field_relations.filter(
                        field_name__id=str(col)
                    ).first()
                    if not custom_prop_val:
                        continue
                    res += custom_prop_display_for_object(
                        custom_prop_val, timezone, lang
                    )
                else:
                    res += getattr(object, str(col))

    elif page_group_type == TYPE_OBJECT_JOURNAL:
        res = ""
        for col in columns:
            if col == "id_journal":
                res += f"#{safe_format_id(object.id_journal)} "
            else:
                # Custom Property
                custom_prop_val = object.journal_custom_field_relations.filter(
                    field_name__id=str(col)
                ).first()
                if not custom_prop_val:
                    continue
                res += custom_prop_display_for_object(custom_prop_val, timezone, lang)

    elif page_group_type == TYPE_OBJECT_CUSTOM_OBJECT:
        res = ""
        for col in columns:
            if col == "row_id":
                res += f"#{safe_format_id(object.row_id)} "
            else:
                # Custom Property
                custom_prop_val = object.custom_object_property_row_relations.filter(
                    field_name__id=str(col)
                ).first()
                if not custom_prop_val:
                    continue
                res += custom_prop_display_for_object(custom_prop_val, timezone, lang)
    return res


def custom_prop_display_for_object(custom_prop_val, timezone, lang):
    """
    Formats the display value for a custom property based on its type and format settings.

    Args:
        custom_prop_val: The custom property value object containing field name, value and metadata
        timezone: The timezone to use for formatting dates/times
        lang: The language code (e.g. 'en', 'ja') for localization

    Returns:
        str: The formatted display string for the custom property value
    """
    res = ""
    if custom_prop_val.field_name.type == "number":
        if custom_prop_val.field_name.number_format == "%":
            res = f"{custom_prop_val.value} {custom_prop_val.field_name.number_format.upper()} "
        elif custom_prop_val.field_name.number_format == "number":
            res = f"{custom_prop_val.value}"
        else:
            res = f"{custom_prop_val.field_name.number_format.upper()} {custom_prop_val.value} "
    elif custom_prop_val.field_name.type == "date":
        res = f"{custom_prop_val.value_time.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y')} "
    elif custom_prop_val.field_name.type == "date_time":
        res = f"{custom_prop_val.value_time.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
    elif custom_prop_val.field_name.type == "choice":
        try:
            for choice in ast.literal_eval(custom_prop_val.field_name.choice_value):
                if (
                    "value" in choice
                    and "label" in choice
                    and choice["value"] == custom_prop_val.value
                ):
                    res = f"{choice['label']} "
        except Exception as e:
            logger.error(e)
    elif custom_prop_val.field_name.type == "components":
        for component in custom_prop_val.components.all():
            res = f"{safe_format_id(component.item_component.item_id)}-{component.item_component.name}"
    elif custom_prop_val.field_name.type == "tag":
        try:
            for tag in json.loads(custom_prop_val.value):
                res = f"{tag['value']} "
        except:
            pass
    elif custom_prop_val.field_name.type == "user":
        user = User.objects.filter(id=custom_prop_val.value).first()
        if user:
            res = f"{user.first_name} "
    elif custom_prop_val.field_name.type == "contact":
        contact = Contact.objects.filter(id=custom_prop_val.value).first()
        if contact:
            try:
                full_name = contact.name
                if contact.last_name:
                    if lang == "ja":
                        full_name = contact.last_name + " " + full_name
                    else:
                        full_name = full_name + " " + contact.last_name
            except:
                full_name = ""
            res = f"#{safe_format_id(contact.contact_id)} - {full_name} "
    elif custom_prop_val.field_name.type == "company":
        company = Company.objects.filter(id=custom_prop_val.value).first()
        if company:
            res = f"#{safe_format_id(company.company_id)}-{company.name} "
    elif custom_prop_val.field_name.type == "invoice_objects":
        invoice = Invoice.objects.filter(id=custom_prop_val.value).first()
        if invoice:
            res = f"#{safe_format_id(invoice.id_inv)}"
    elif custom_prop_val.field_name.type in ["image", "svg"]:
        pass
    else:
        res = f"{custom_prop_val.value} "
    return res


def property_display(arg, lang, workspace):
    try:
        # print(f"DEBUG PROPERTY_DISPLAY: Input arg: {arg}")
        args = arg.split("|")
        # print(f"DEBUG PROPERTY_DISPLAY: Split args: {args}")
        row_type = args[0].lower()
        page = args[1]
        item_id = None
        if len(args) > 2:
            item_id = args[2]
        # print(
        #     f"DEBUG PROPERTY_DISPLAY: row_type={row_type}, page={page}, item_id={item_id}")
        if row_type == "shipping_info":
            row_type = args[1]
            page = args[-1]
        if row_type == "line_item":
            row_type = args[0]
            page = args[-1]
        if page == "message_thread":
            page = TYPE_OBJECT_CONVERSATION
        
        # Helper function to map page names to TYPE_OBJECT constants for association labels
        def get_association_page_mapping(page_name):
            page_mapping = {
                "orders": TYPE_OBJECT_ORDER,
                "items": TYPE_OBJECT_ITEM,
                "commerce_items": TYPE_OBJECT_ITEM,
                "inventory": TYPE_OBJECT_INVENTORY,
                "contacts": TYPE_OBJECT_CONTACT,
                "company": TYPE_OBJECT_COMPANY,
                "estimates": TYPE_OBJECT_ESTIMATE,
                "invoices": TYPE_OBJECT_INVOICE,
                "cases": TYPE_OBJECT_CASE,
                "tasks": TYPE_OBJECT_TASK,
                "subscriptions": TYPE_OBJECT_SUBSCRIPTION,
                "receipts": TYPE_OBJECT_RECEIPT,
                "bills": TYPE_OBJECT_BILL,
                "expenses": TYPE_OBJECT_EXPENSE,
                "delivery_notes": TYPE_OBJECT_DELIVERY_NOTE,
                "purchase_orders": TYPE_OBJECT_PURCHASE_ORDER,
                "inventory_transactions": TYPE_OBJECT_INVENTORY_TRANSACTION,
                "inventory_warehouses": TYPE_OBJECT_INVENTORY_WAREHOUSE,
            }
            return page_mapping.get(page_name, page_name)
        
        
        logger.debug(f"[DEBUG] property_display processing arg: {arg}")
        if "_platform|" in arg:
            logger.debug(f"[DEBUG] Found platform in arg: {arg}")
            if is_valid_uuid(arg.split("|")[1][:36]):
                page = args[2]
                row_type = (args[0] + "|" + args[1]).lower()
                logger.debug(f"[DEBUG] Platform row_type: {row_type}, page: {page}")

        # association Label
        if is_valid_uuid(row_type):
            association_label = AssociationLabel.objects.filter(
                id=row_type, workspace=workspace
            ).first()
            if association_label:
                # Default to generic label - use our function for user-created labels
                display_label = get_association_label_display_name(association_label, lang)
                
                # Apply page mapping for association labels only
                mapped_page = get_association_page_mapping(page)
           
                # If label_pair exists, try to find a specific label for the current context
                if association_label.label_pair:
                    # The page parameter represents the current object context
                    # Split object_target to get list of target objects
                    object_targets = [target.strip() for target in association_label.object_target.split(',') if target.strip()]
                    
                    # If current page is in the target objects list, use the specific label from label_pair
                    if mapped_page in object_targets and mapped_page in association_label.label_pair:
                        display_label = association_label.label_pair[mapped_page]
                    # If current page is the source object, use the default label
                    elif mapped_page == association_label.object_source:
                        display_label = association_label.label
                    # For other contexts, try to find the most appropriate label
                    elif mapped_page in association_label.label_pair:
                        display_label = association_label.label_pair[mapped_page]

                return {
                    "name": display_label,
                    "id": str(association_label.id),
                    "value": "",
                    "type": "association",
                }
        else:
            # Apply page mapping for association label lookup
            mapped_page_for_lookup = get_association_page_mapping(page)
            association_labels = AssociationLabel.objects.filter(
                workspace=workspace, object_source=mapped_page_for_lookup
            )
            if association_labels.filter(label__iexact=row_type).exists():
                association_label = association_labels.filter(
                    label__iexact=row_type
                ).first()  # Fix return more than 1

                # Default to generic label - use our function for user-created labels
                display_label = get_association_label_display_name(association_label, lang)
            
                # Apply page mapping for association labels only
                mapped_page = get_association_page_mapping(page)
             
             
                # If label_pair exists, try to find a specific label for the current context
                if association_label.label_pair:
                    # The page parameter represents the current object context
                    # Split object_target to get list of target objects
                    object_targets = [target.strip() for target in association_label.object_target.split(',') if target.strip()]
                    
                    # If current page is in the target objects list, use the specific label from label_pair
                    if mapped_page in object_targets and mapped_page in association_label.label_pair:
                        display_label = association_label.label_pair[mapped_page]
                    # If current page is the source object, use the default label
                    elif mapped_page == association_label.object_source:
                        display_label = association_label.label
                    # For other contexts, try to find the most appropriate label
                    elif mapped_page in association_label.label_pair:
                        display_label = association_label.label_pair[mapped_page]

                row_type = display_label

        page_obj = get_page_object(page, lang)
        if page_obj is None:
            # Return default result if page object is not found
            return {"name": row_type, "id": row_type, "value": "", "type": ""}
        
        custom_model = page_obj["custom_model"]
        value_model = page_obj["custom_value_model"]
        columns = page_obj["columns_display"]
        base_model = page_obj["base_model"]
        customfield_name = None
        associate = None
        result = {"name": row_type, "id": row_type, "value": "", "type": ""}

        if row_type:
            if is_valid_uuid(row_type):
                customfield_name = custom_model.objects.filter(
                    id=row_type, workspace=workspace
                ).first()

        if customfield_name or associate:
            if associate:
                if associate.case_associate:
                    attr = getattr(associate, "case_associate", None)
                    if attr:
                        if lang == "ja":
                            row_name = f"{attr.name} (アソシエーション)"
                        else:
                            row_name = f"{attr.name} (Association Object)"
                        result["name"] = row_name
                        result["id"] = associate.id

            if customfield_name:
                result["name"] = customfield_name.name
                result["id"] = str(customfield_name.id)
                result["type"] = customfield_name.type

                if customfield_name.type == "components":
                    if item_id == "amount":
                        if lang == "ja":
                            result["name"] += " | 構成数量"
                        else:
                            result["name"] += " | Component Quantity"
                elif customfield_name.type == "shipping_info":
                    try:
                        sub_property = args[3]
                        if sub_property:
                            result["name"] = (
                                customfield_name.name + " - " + sub_property
                            )
                    except:
                        pass
                elif item_id:
                    try:
                        if page == TYPE_OBJECT_BILL:
                            filter_condition = Q(bill__id=item_id)
                        else:
                            filter_condition = Q(
                                **{page_obj["custom_value_relation"] + "__id": item_id}
                            )
                    except:  # Error happening for other app if just need value of custom field , I add this to handle that Cc: Khan
                        obj = base_model.objects.filter(id=item_id).last()
                        str_base_model = str(obj).lower().split(" ")[0]
                        filter_condition = Q(**{str_base_model: obj})

                    customfield_value = value_model.objects.filter(
                        Q(field_name=customfield_name) & filter_condition
                    ).last()

                    if customfield_value:
                        if customfield_name.type == "image":
                            if customfield_value.file:
                                result["value"] = customfield_value.file.url
                        elif customfield_name.type == "contact":
                            if is_valid_uuid(customfield_value.value):
                                try:
                                    contact = Contact.objects.filter(
                                        id=customfield_value.value
                                    ).first()
                                    if contact:
                                        if lang == "ja":
                                            result["value"] = (
                                                f"#{'%04d' % contact.contact_id} "
                                            )
                                            if contact.last_name:
                                                result["value"] += (
                                                    f"{contact.last_name} "
                                                )
                                            if contact.name:
                                                result["value"] += f"{contact.name}"
                                        else:
                                            result["value"] = (
                                                f"#{'%04d' % contact.contact_id} {contact.name}"
                                            )
                                            if contact.last_name:
                                                result["value"] += (
                                                    f" {contact.last_name}"
                                                )
                                        result["contact_id"] += f" {contact.id}"
                                except:
                                    pass
                        elif customfield_name.type == "item":
                            if is_valid_uuid(customfield_value.value):
                                try:
                                    item = ShopTurboItems.objects.filter(
                                        id=customfield_value.value
                                    ).first()
                                    if item:
                                        if item.price:
                                            if item.currency == "¥":
                                                price = f"{item.currency} {'%0g' % item.price}"
                                            else:
                                                price = f"{item.currency} {'%2g' % item.price}"
                                            result["value"] = (
                                                f"#{'%04d' % item.item_id} {item.name} - {price}"
                                            )
                                        else:
                                            itemsprice = (
                                                ShopTurboItemsPrice.objects.filter(
                                                    item=item, default=True
                                                ).first()
                                            )
                                            if itemsprice.price:
                                                if itemsprice.currency == "¥":
                                                    price = f"{itemsprice.currency} {'%0g' % itemsprice.price}"
                                                else:
                                                    price = f"{itemsprice.currency} {'%2g' % itemsprice.price}"
                                                result["value"] = (
                                                    f"#{'%04d' % item.item_id} {item.name} - {price}"
                                                )
                                            else:
                                                result["value"] = (
                                                    f"#{'%04d' % item.item_id} {item.name}"
                                                )
                                except:
                                    pass
                        elif customfield_name.type == "purchase_item":
                            if is_valid_uuid(customfield_value.value):
                                purchase_item = PurchaseItems.objects.filter(
                                    id=customfield_value.value
                                ).first()
                                if purchase_item:
                                    if purchase_item.amount:
                                        if purchase_item.currency == "¥":
                                            price = f"{purchase_item.currency} {'%0g' % float(purchase_item.amount)}"
                                        else:
                                            price = f"{purchase_item.currency} {'%2g' % float(purchase_item.amount)}"
                                        logger.debug(purchase_item.id_item)
                                        result["value"] = (
                                            f"#{'%04d' % int(purchase_item.id_item)} {purchase_item.name} - {price}"
                                        )
                                    else:
                                        result["value"] = (
                                            f"#{'%04d' % int(purchase_item.id_item)} {purchase_item.name}"
                                        )
                        elif customfield_name.type == "file":
                            try:
                                result["value"] = customfield_value.file.url
                            except:
                                pass
                        elif customfield_name.type in ["date", "date_time"]:
                            try:
                                result["value"] = customfield_value.value_time
                            except:
                                pass
                        elif customfield_name.type == "choice":
                            try:
                                choices = ast.literal_eval(
                                    customfield_name.choice_value
                                )
                                for choice in choices:
                                    if choice["value"] == customfield_value.value:
                                        result["value"] = choice["value"]
                                        if "color" in choice:
                                            result["color"] = (
                                                choice["color"]
                                                if "color" in choice
                                                else "#000000"
                                            )

                                if page in [
                                    TYPE_OBJECT_ORDER,
                                    TYPE_OBJECT_CONTACT,
                                    TYPE_OBJECT_INVENTORY,
                                    TYPE_OBJECT_CASE,
                                ]:
                                    result["value"] = customfield_value.value
                                    result["customfield_name"] = customfield_name
                                    result["customfield_value"] = customfield_value
                            except:
                                pass
                        else:
                            result["value"] = customfield_value.value

                elif row_type in ["customer", "contact", "company"]:
                    customer_result = get_order_customer_custom_property(
                        args[:3], workspace, lang
                    )
                    if customer_result:
                        result["id"] = str(customer_result["id"])
                        result["name"] = customer_result["name"]

        elif row_type == "line_item":
            custom_line_item_model = page_obj["custom_line_item_model"]
            line_item_id = args[1]
            if is_valid_uuid(line_item_id):
                custom_line_item_model = custom_line_item_model.objects.filter(
                    id=line_item_id, workspace=workspace
                ).first()

                result["name"] = custom_line_item_model.name + " (Line Item)"
                result["value"] = "line_item|" + str(custom_line_item_model.id)
                result["id"] = str(custom_line_item_model.id)
                result["type"] = "text"
            else:
                if row_type in columns:
                    result["name"] = columns[row_type][lang]
        elif "invoice_platform|" in row_type and is_valid_uuid(
            row_type.split("|")[1][:36]
        ):
            channel_id = row_type.split("|")[1][:36]
            logger.debug(channel_id)
            channel = Channel.objects.filter(
                workspace=workspace, id=channel_id, integration__slug="stripe"
            ).first()
            if channel:
                field = row_type.split("|")[1][37:]
                result["name"] = (
                    channel.name + " - " + INVOICE_COLUMNS_DISPLAY[field][lang]
                )
        elif "subscription_platform|" in row_type and is_valid_uuid(
            row_type.split("|")[1][:36]
        ):
            channel_id = row_type.split("|")[1][:36]
            channel = Channel.objects.filter(
                workspace=workspace, id=channel_id, integration__slug="stripe"
            ).first()
            if channel:
                field = row_type.split("|")[1][37:]
                result["name"] = (
                    channel.name + "-" + SUBSCRIPTIONS_COLUMNS_DISPLAY[field][lang]
                )
        elif "order_platform|" in row_type and is_valid_uuid(
            row_type.split("|")[1][:36]
        ):
            channel_id = row_type.split("|")[1][:36]
            channel = Channel.objects.filter(workspace=workspace, id=channel_id).first()
            if channel:
                field = row_type.split("|")[1][37:]
                result["name"] = (
                    channel.name
                    + " - "
                    + ORDERS_COLUMNS_DISPLAY.get(field, {}).get(lang, field)
                )
        elif "case_platform|" in row_type and is_valid_uuid(
            row_type.split("|")[1][:36]
        ):
            logger.debug(f"[DEBUG] Processing case_platform row_type: {row_type}")
            channel_id = row_type.split("|")[1][:36]
            channel = Channel.objects.filter(workspace=workspace, id=channel_id).first()
            if channel:
                field = row_type.split("|")[1][37:]
                # Map field names to display names (handle both uppercase and lowercase)
                field_display_map = {
                    "Ticket ID": "Ticket ID",
                    "Deal ID": "Deal ID",
                    "Status": "Status",
                    "Display Name": "Display Name",
                    "Case ID": "Case ID",
                    "ticket id": "Ticket ID",
                    "deal id": "Deal ID",
                    "status": "Status",
                    "display name": "Display Name",
                    "case id": "Case ID",
                }
                display_field = field_display_map.get(field, field.title())
                result["name"] = f"{channel.name} - {display_field}"
                logger.debug(f"[DEBUG] Case platform result: {result}")
        elif (
            row_type == "commerce_inventory"
            and len(args) > 1
            and is_valid_uuid(args[1])
        ):
            # Handle commerce_inventory|{custom_field_uuid}|{page_group_type} format (3 parts)
            # or commerce_inventory|{custom_field_uuid} format (2 parts)
            custom_field_id = args[1]
            inventory_custom_field = ShopTurboInventoryNameCustomField.objects.filter(
                id=custom_field_id, workspace=workspace
            ).first()
            if inventory_custom_field:
                parent_obj_display = OBJECT_GROUP_TYPE_SINGULAR["commerce_inventory"][
                    lang
                ]
                result["name"] = f"{parent_obj_display} - {inventory_custom_field.name}"
                # Use 2-part format for ID
                result["id"] = f"commerce_inventory|{custom_field_id}"
                result["type"] = inventory_custom_field.type
                # print(
                #     f"DEBUG PROPERTY_DISPLAY: Processed inventory custom field: {result['name']}")
                # print(f"DEBUG PROPERTY_DISPLAY: Returning result: {result}")
                return result  # Make sure we return the result
            else:
                pass  # Inventory custom field not found
        else:
            row_name = row_type
            if columns:
                if row_type in columns:
                    row_name = columns[row_type][lang]
                elif page == "timegenie" and row_type == "time_card":
                    row_name = "Time Card"
            result["name"] = row_name

        return result

    except Exception as e:
        logger.error("Error as: %s", e)

        traceback.print_exc()

        return {"name": arg.split("|")[0], "id": arg.split("|")[0], "value": ""}


def get_list_properties(
    page_group_type,
    workspace,
    lang,
    project_target=None,
    searchable_only=False,
    include_integration_properties=True,
    excludes=[],
    includes=[],
    shopify_sync_only=False,
    auto_association_only=False,
):
    """WILL BE DEPRECATED"""
    return get_properties_with_details(
        page_group_type,
        workspace,
        lang,
        project_target,
        searchable_only,
        include_integration_properties,
        excludes,
        includes,
        shopify_sync_only,
        auto_association_only,
    )


def get_properties_with_details(
    page_group_type,
    workspace,
    lang,
    project_target=None,
    searchable_only=False,
    include_integration_properties=True,
    excludes=[],
    includes=[],
    shopify_sync_only=False,
    auto_association_only=False,
    custom_object=None,
    page=None,
    per_page=None,
):
    """
    Returns a list of properties along with their details.

    Returns:
        list: A list of dictionaries, where each dictionary contains the details
        of a property (e.g., name, type, value, etc.).
    """
    # Create cache key for non-paginated, non-filtered requests
    cache_key = None
    if (
        page is None
        and per_page is None
        and not searchable_only
        and not shopify_sync_only
        and not auto_association_only
        and not excludes
        and not includes
        and custom_object is None
    ):
        cache_key = f"properties_details_{page_group_type}_{workspace.id}_{lang}"

        # Try to get from cache first
        cached_result = cache.get(cache_key)
        if cached_result is not None:
            logger.debug(
                f"DEBUG PROPERTIES CACHE: Retrieved {len(cached_result)} properties from cache"
            )
            return cached_result

    page_obj = get_page_object(page_group_type, lang)
    if page_obj is None:
        return None
    base_model = page_obj["base_model"]
    custom_model = page_obj["custom_model"]
    editable_columns = page_obj["editable_columns"]
    exclude_custom_types = page_obj["exclude_custom_types"]

    # Handle case where base_model is None (object type not fully implemented)
    if base_model is None:
        return []

    properties = []
    if not shopify_sync_only and not auto_association_only:
        default_properties_dict = {
            field.name: field.get_internal_type() for field in base_model._meta.fields
        }
        default_columns = get_list_view_columns(
            page_group_type,
            workspace,
            include_integration_properties,
            include_cf=False,
            include_default_association=False,
            for_form=True if page_group_type == TYPE_OBJECT_WORKFLOW else False,
        )

        if not default_columns:
            default_columns = page_obj["default_columns"]
            if page_obj["additional_column_fields"]:
                default_columns = default_columns + page_obj["additional_column_fields"]

        if not default_columns:
            default_columns = []

        logger.debug(f"[DEBUG] Processing {len(default_columns)} default columns for page_group_type={page_group_type}")
        for _column in default_columns:
            if "platform" in str(_column):
                logger.debug(f"[DEBUG] Found platform column: {_column}")
            if _column in excludes:
                continue

            if "custom_price" == _column and page_group_type == TYPE_OBJECT_ESTIMATE:
                continue
            if (
                any(item in str(_column) for item in ["checkbox", "id", "usage_status"])
                and "platform" not in str(_column)
            ) or (
                _column == "status" and "usage_status" not in default_properties_dict
            ) or (
                _column == "usage_status"  # Explicitly exclude usage_status
            ):
                if "platform" in str(_column):
                    logger.debug(f"[DEBUG] Platform column {_column} was excluded by filter")
                continue
            if _column == "category" and page_group_type == TYPE_OBJECT_JOURNAL:
                _type = "hierarchy_choice"
            elif (
                (_column == "tax_rate" and page_group_type == TYPE_OBJECT_JOURNAL)
                or (_column == "tax_rate" and page_group_type == TYPE_OBJECT_EXPENSE)
                or (
                    _column == "inventory_status"
                    and page_group_type == TYPE_OBJECT_INVENTORY
                )
            ):
                _type = "choice"
            elif (
                (_column == "applications" and page_group_type == TYPE_OBJECT_JOBS)
                or (_column == "interview" and page_group_type == TYPE_OBJECT_JOBS)
                or (_column == "scorecard" and page_group_type == TYPE_OBJECT_JOBS)
            ):
                _type = "object"
            elif _column == "owner":
                _type = "user"
            elif _column in [TYPE_OBJECT_COMPANY,TYPE_OBJECT_JOURNAL]:
                _type = "association"
            elif "platform" in str(_column):
                logger.debug(f"[DEBUG] Processing platform column {_column}, setting type to 'string'")
                _type = "string"
            else:
                _type = get_property_type(_column, default_properties_dict, base_model)

            if searchable_only:
                if _type not in SEARCHABLE_PROPERTY_TYPES:
                    continue

            custom_property = CustomProperty.objects.filter(
                workspace=workspace, model=base_model._meta.db_table, name=_column
            ).first()

            _updated_at = custom_property.updated_at if custom_property else None
            _edit_by = custom_property.edit_by if custom_property else None
            _immutable = False if _column in editable_columns else True

            properties.append(
                {
                    "id": _column,
                    "type": _type,
                    "created_at": None,
                    "updated_at": _updated_at,
                    "edit_by": _edit_by,
                    "immutable": _immutable,
                }
            )

            if "platform" in str(_column):
                logger.debug(f"[DEBUG] Added platform property: {_column} with type {_type}")
            
            logger.debug(
                {
                    "id": _column,
                    "type": _type,
                    "created_at": None,
                    "updated_at": _updated_at,
                    "edit_by": _edit_by,
                    "immutable": _immutable,
                }
            )

        if default_columns:
            properties.extend(includes)

    # NOTE: Modularized this filter if we need more filter in the future
    custom_fields_filter = Q()
    if project_target:
        custom_fields_filter = Q(project_target=project_target)

    if not custom_model:
        return properties

    if custom_object:
        custom_fields = custom_model.objects.filter(
            custom_fields_filter,
            workspace=workspace,
            custom_object=custom_object,
            name__isnull=False,
        ).exclude(type__in=exclude_custom_types)
    else:
        # Add for avoid not settable name of property ##Faris
        custom_fields = custom_model.objects.filter(
            custom_fields_filter, workspace=workspace, name__isnull=False
        ).exclude(type__in=exclude_custom_types)

        # Optimize query by prefetching edit_by user if the field exists
        if hasattr(custom_model, "edit_by"):
            custom_fields = custom_fields.select_related("edit_by")

    # Exclude Shopify Sync
    if not shopify_sync_only and not auto_association_only:
        if page_group_type in [TYPE_OBJECT_ITEM, TYPE_OBJECT_INVENTORY]:
            custom_fields = custom_fields.exclude(is_shopify_sync=True)
        if page_group_type in [TYPE_OBJECT_ORDER]:
            custom_fields = custom_fields.exclude(is_association=True)
    else:
        if shopify_sync_only:
            custom_fields = custom_fields.filter(is_shopify_sync=True)
        if auto_association_only:
            custom_fields = custom_fields.filter(is_association=True)

    if "order" in [field.name for field in custom_model._meta.fields]:
        custom_fields = custom_fields.order_by("order")
    else:
        custom_fields = custom_fields.order_by("-created_at")

    # Exclude production line for task object
    # if page_group_type == TYPE_OBJECT_TASK:
    #     custom_fields = custom_fields.exclude(type="production_line")

    # Apply pagination if specified
    total_count = None
    if page is not None and per_page is not None:
        total_count = custom_fields.count()
        start = (page - 1) * per_page
        end = start + per_page
        custom_fields = custom_fields[start:end]

    # Prefetch all users that might be needed to avoid N+1 queries
    edit_by_ids = []
    custom_fields_list = list(custom_fields)
    for ctf in custom_fields_list:
        if hasattr(ctf, "edit_by_id") and ctf.edit_by_id:
            edit_by_ids.append(ctf.edit_by_id)

    # Fetch all users in one query
    users_dict = {}
    if edit_by_ids:
        users = User.objects.filter(id__in=edit_by_ids)
        users_dict = {user.id: user for user in users}

    print("========= custom_fields_list: ", custom_fields_list)
    for ctf in custom_fields_list:
        if ctf.id in excludes:
            continue

        if searchable_only:
            if ctf.type not in SEARCHABLE_PROPERTY_TYPES:
                continue

        # Use prefetched user data instead of individual queries
        if (
            hasattr(ctf, "edit_by_id")
            and ctf.edit_by_id
            and ctf.edit_by_id in users_dict
        ):
            ctf.__dict__["edit_by"] = users_dict[ctf.edit_by_id]
        elif hasattr(ctf, "edit_by") and ctf.edit_by:
            # If select_related was used, the edit_by is already loaded
            ctf.__dict__["edit_by"] = ctf.edit_by

        properties.append(ctf.__dict__)

    associates = None

    if associates and not searchable_only:
        for associate in associates:
            properties.append(associate.__dict__)

    # Return pagination metadata if pagination was requested
    if page is not None and per_page is not None and total_count is not None:
        return {
            "properties": properties,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total_count": total_count,
                "total_pages": (total_count + per_page - 1) // per_page,
                "has_next": page * per_page < total_count,
                "has_prev": page > 1,
            },
        }

    # Cache the result if we have a cache key (non-paginated, non-filtered requests)
    if cache_key is not None:
        # Cache for 15 minutes (properties don't change very frequently)
        cache.set(cache_key, properties, 60 * 15)
        logger.debug(
            f"DEBUG PROPERTIES CACHE: Cached {len(properties)} properties with key {cache_key}"
        )

    return properties


def get_context_formula_properties(context, page_group_type, workspace, lang):
    # Custom Column type
    page_obj = get_page_object(page_group_type)
    if page_obj is None:
        return None
    custom_model = page_obj["custom_model"]
    base_model = page_obj["base_model"]
    base_columns = page_obj["base_columns"]
    multiple_custom_types = page_obj["multiple_custom_types"]
    related_data = page_obj["related_data"]
    custom_value_related_model = page_obj["custom_value_related_model"]

    # Handle case where base_model is None (object type not fully implemented)
    if base_model is None:
        return context

    relation_properties = custom_model.objects.filter(
        workspace=workspace, name__isnull=False
    )
    context["relation_properties"] = relation_properties

    # Default Column type
    default_properties_dict = {
        field.name: field.get_internal_type()
        for field in base_model._meta.fields
        if field.name in base_columns
    }
    default_number_properties = list(
        {
            k: v
            for k, v in default_properties_dict.items()
            if v in ["IntegerField", "FloatField"]
        }.keys()
    )
    if default_number_properties:
        # pop ID
        default_number_properties.pop(0)
        if "item_price_order" in default_number_properties:
            default_number_properties.remove("item_price_order")

    context["default_property_column_display"] = {
        **page_obj["columns_display"],
        **SEARCH_COLUMNS_DISPLAY,
    }
    context["default_number_properties"] = default_number_properties

    default_text_properties = [
        k
        for k, v in default_properties_dict.items()
        if v in ["CharField", "TextField"] or k.endswith("_id") or k.startswith("id_")
    ]

    if page_group_type in [TYPE_OBJECT_ESTIMATE, TYPE_OBJECT_ORDER]:
        for cf in SEARCH_COLUMNS_DISPLAY:
            default_text_properties.append(cf)

    context["default_text_properties"] = default_text_properties
    custom_text_properties = list(
        {"id": val.id, "name": val.name}
        for val in relation_properties.filter(type__in=["text", "text-area", "choice"])
    )
    context["custom_text_properties"] = custom_text_properties

    # Custom-object Column type
    context["object_properties_number"] = []
    obj_properties = relation_properties.filter(
        type__in=OBJ_CUSTOM_FIELD_MAP_PAGE_GROUP_TYPE.keys()
    )
    for prop in obj_properties:
        page_obj_ = get_page_object(
            OBJ_CUSTOM_FIELD_MAP_PAGE_GROUP_TYPE[prop.type], lang
        )
        base_model = page_obj_["base_model"]
        columns_display = page_obj_["columns_display"]
        id_field = page_obj_["id_field"]

        # Skip if base_model is None (object type not fully implemented)
        if base_model is None:
            continue

        default_properties_dict = {
            field.name: field.get_internal_type() for field in base_model._meta.fields
        }
        for prop_name, prop_type in default_properties_dict.items():
            if prop_type not in ["IntegerField", "FloatField"] or prop_name == id_field:
                continue

            if prop.type in multiple_custom_types:
                try:
                    context["object_properties_number"].append(
                        {
                            "id": f"{prop.id}|{prop_name}|sum",
                            "value": f"{prop.name} - SUM({columns_display[prop_name][lang]})",
                        }
                    )
                except Exception as e:
                    logger.warning(e)
                    continue
            else:
                try:
                    context["object_properties_number"].append(
                        {
                            "id": f"{prop.id}|{prop_name}",
                            "value": f"{prop.name} - {columns_display[prop_name][lang]}",
                        }
                    )
                except Exception as e:
                    logger.warning(e)
                    continue

    context["functions_properties"] = []
    context["functions_properties"].extend(
        [
            {"id": "count", "value": "Count"},
            {"id": "round", "value": "round"},
            {"id": "round_up", "value": "round_up"},
            {"id": "round_down", "value": "round_down"},
            {"id": "#if", "value": "#if"},
            {"id": "#elif", "value": "#elif"},
            {"id": "#else", "value": "#else"},
            {"id": "#endif", "value": "#endif"},
            {"id": "break;", "value": "break;"},
        ]
    )

    context["related_data"] = []
    for related_obj in related_data:
        if related_obj in TYPE_OBJECTS:
            page_obj_ = get_page_object(related_obj, lang)
            if page_obj_ is None:
                continue
            base_model = page_obj_["base_model"]
            columns_display = page_obj_["columns_display"]
            id_field = page_obj_["id_field"]
            custom_model = page_obj_["custom_model"]

            # Skip if base_model is None (object type not fully implemented)
            if base_model is None:
                continue

            default_properties_dict = {
                field.name: field.get_internal_type()
                for field in base_model._meta.fields
            }
            for prop_name, prop_type in default_properties_dict.items():
                if (
                    prop_type not in ["IntegerField", "FloatField"]
                    or prop_name == id_field
                ):
                    continue

                try:
                    context["related_data"].append(
                        {
                            "object_name": FORMULA_RELATED_COLUMNS_DISPLAY[related_obj][
                                lang
                            ]
                            if related_obj in ["order", "journal"]
                            else OBJECT_GROUP_TYPE[related_obj][lang],
                            "property_name": columns_display[prop_name][lang],
                            # expected format: object_type|field|function
                            "value": f"{related_obj}|{prop_name}|sum",
                        }
                    )
                except Exception as e:
                    logger.warning(e)
                    continue

            custom_fields = custom_model.objects.filter(
                workspace=workspace, type__in=["components", "number"]
            )
            for custom_field in custom_fields:
                if custom_field.type == "number":
                    context["related_data"].append(
                        {
                            "object_name": FORMULA_RELATED_COLUMNS_DISPLAY[related_obj][
                                lang
                            ]
                            if related_obj in ["order", "journal"]
                            else OBJECT_GROUP_TYPE[related_obj][lang],
                            "property_name": custom_field.name,
                            "value": f"{related_obj}|{custom_field.id}|sum",
                        }
                    )
                else:
                    custom_object_prop = get_page_object(related_obj, lang)
                    if custom_object_prop is None:
                        continue
                    base_model = custom_object_prop["base_model"]
                    columns_display = custom_object_prop["columns_display"]
                    id_field = page_obj_["id_field"]

                    default_properties_dict = {
                        field.name: field.get_internal_type()
                        for field in base_model._meta.fields
                    }
                    for prop_name, prop_type in default_properties_dict.items():
                        if (
                            prop_type not in ["IntegerField", "FloatField"]
                            or prop_name == id_field
                        ):
                            continue

                        context["related_data"].append(
                            {
                                "object_name": FORMULA_RELATED_COLUMNS_DISPLAY[
                                    related_obj
                                ][lang]
                                if related_obj in ["order", "journal"]
                                else OBJECT_GROUP_TYPE[related_obj][lang],
                                "property_name": custom_field.name,
                                "sub_property_name": columns_display[prop_name][lang],
                                "value": f"{related_obj}|{custom_field.id}|{prop_name}|sum",
                            }
                        )

        else:
            context["related_data"].append(
                {"object_type": related_obj, "value": related_obj}
            )

    for custom_value_related_model_ in custom_value_related_model:
        custom_objs = custom_model.objects.filter(
            workspace=workspace, type=custom_value_related_model_["type"]
        ).values("id", "name")
        related_model = custom_value_related_model_["model"]
        related_model_columns_display = custom_value_related_model_["columns_display"]
        default_properties_dict = {
            field.name: field.get_internal_type()
            for field in related_model._meta.fields
        }
        for custom_obj in custom_objs:
            for prop_name, prop_type in default_properties_dict.items():
                if prop_type not in ["IntegerField", "FloatField"]:
                    continue

                # Single property sum
                context["object_properties_number"].append(
                    {
                        "id": f"{custom_obj['id']}|{custom_value_related_model_['relation']}|{prop_name}|sum",
                        "value": f"{custom_obj['name']} - SUM({related_model_columns_display[prop_name][lang]})",
                    }
                )

                page_obj_ = get_page_object(
                    OBJ_CUSTOM_FIELD_MAP_PAGE_GROUP_TYPE[
                        custom_value_related_model_["type"]
                    ],
                    lang,
                )
                base_model_ = page_obj_["base_model"]
                columns_display_ = page_obj_["columns_display"]
                id_field_ = page_obj_["id_field"]
                default_properties_dict_ = {
                    field.name: field.get_internal_type()
                    for field in base_model_._meta.fields
                    if columns_display_
                    and field.name in columns_display_
                    and field.name != id_field_
                }
                for prop_name_, prop_type_ in default_properties_dict_.items():
                    if prop_type_ not in ["IntegerField", "FloatField"]:
                        continue

                    # Sumproduct interaction between two properties
                    context["object_properties_number"].append(
                        {
                            "id": f"{custom_obj['id']}|{custom_value_related_model_['relation']}|{prop_name}|sumproduct|{prop_name_}",
                            "value": f"{custom_obj['name']} - SUM({related_model_columns_display[prop_name][lang]}*{columns_display_[prop_name_][lang]})",
                        }
                    )

    context["related_property_column_display"] = FORMULA_RELATED_COLUMNS_DISPLAY

    # This is custom written properties for each page group type
    if page_group_type == "production":
        context["related_item_properties"] = (
            ShopTurboItemsNameCustomField.objects.filter(
                workspace=workspace, name__isnull=False, type="number"
            )
        )
    if page_group_type in ["contacts", "company"]:
        context["related_item_properties"] = (
            ShopTurboItemsNameCustomField.objects.filter(
                workspace=workspace, name__isnull=False, type="number"
            )
        )
    elif page_group_type == TYPE_OBJECT_CASE:
        # Attendance <> Employee Related Columns (Number)
        related_attendance = DealsNameCustomField.objects.filter(
            workspace=workspace, name__isnull=False, type="attendance"
        )
        if related_attendance:
            context["related_attendance_columns"] = ["hours", "minutes", "seconds"]
            # Get Attendance <> Employee Related Columns (Number)
            context["related_attendance_employee_properties"] = (
                WorkerNameCustomField.objects.filter(
                    workspace=workspace, name__isnull=False, type="number"
                )
            )
        attendance_associates = Association.objects.filter(
            workspace=workspace,
            associate_type=TYPE_OBJECT_CASE,
            attendance_associate__isnull=False,
        )
        if attendance_associates:
            context["attendance_associates"] = ["hours", "minutes", "seconds"]
            # Association Part
            # Get Attendance <> Employee Related Columns(Number)
            context["attendance_employee_associates_properties"] = (
                WorkerNameCustomField.objects.filter(
                    workspace=workspace, name__isnull=False, type="number"
                )
            )

        context["functions_properties"] = ["total_wage_cost"]

        # Get Price Information Custom Fields for Case
        price_information_cf = DealsNameCustomField.objects.filter(
            workspace=workspace, name__isnull=False, type="price-information"
        )
        if price_information_cf:
            for cf in price_information_cf:
                val = (
                    f"商品項目 - {cf.name}"
                    if lang == "ja"
                    else f"Line Items - {cf.name}"
                )
                context["object_properties_number"].append(
                    {"id": f"{str(cf.id)}", "value": val}
                )

        context["related_task_properties"] = TaskCustomFieldName.objects.filter(
            workspace=workspace, name__isnull=False, type="number"
        )

    elif page_group_type == TYPE_OBJECT_INVENTORY:
        # association label
        # self associtiopn
        related_association_labels = AssociationLabel.objects.filter(
            created_by_sanka=False,
            workspace=workspace,
            object_source__icontains=TYPE_OBJECT_INVENTORY,
            object_target__icontains=TYPE_OBJECT_INVENTORY,
        ).order_by("created_at")
        context["related_association_labels"] = related_association_labels

    return context


def get_list_view_columns(
    page_group_type,
    workspace,
    include_integration_properties=True,
    include_cf=True,
    include_default_association=True,
    for_form=False,
):
    """
    Returns a list of column identifiers for the list view of a specified object type.

    The columns are dynamically determined based on the object type, workspace configuration, and optional inclusion of integration properties, custom fields, and default associations. Supports a wide range of object types including items, orders, subscriptions, invoices, tasks, contacts, companies, inventory, inventory transactions, cases, purchase orders, bills, journals, expenses, and workflows. The returned columns may include base fields, integration-specific fields, custom fields, and related object properties as appropriate for each type.

    Args:
        page_group_type: The object type or page group for which to retrieve list view columns.
        include_integration_properties: If True, includes columns related to external integrations.
        include_cf: If True, includes custom field columns defined for the workspace.
        include_default_association: If True, includes columns for default associations such as related contacts or companies.

    Returns:
        A list of column names or identifiers to be displayed in the list view for the specified object type.
    """

    column_values = []
    line_item_columns = [
        "line_item",
        "line_item_name",
        "line_item_quantity",
        "line_item_name_quantity",
        "line_item_status",
        "line_item_price",
        "line_item_tax",
    ]
    if page_group_type == TYPE_OBJECT_ITEM:
        column_values = DEFAULT_COLUMNS_ITEM[1:].copy() + [
            "tax",
            # Below are the inventory amount columns
            "supplier__contact__name",
            "supplier__company__name",
            "supplier__contact__first_name",
            "supplier__contact__last_name",
            "inventory__total_inventory",
            "inventory__available_amount",
            "inventory__unavailable_amount",
            "inventory__committed_amount",
            "warehouse_inventory_amount",
        ]
        if include_integration_properties:
            column_values += get_integration_properties(page_group_type, workspace)
        if include_cf:
            column_values += get_custom_properties_ids(page_group_type, workspace)

        column_values.append(TYPE_OBJECT_ORDER)
        column_values.append(TYPE_OBJECT_INVENTORY)

        column_values.append(TYPE_OBJECT_SUBSCRIPTION)
        column_values.append(TYPE_OBJECT_PURCHASE_ORDER)
        column_values.append(TYPE_OBJECT_CASE)
        column_values.append(TYPE_OBJECT_ESTIMATE)
        column_values.append(TYPE_OBJECT_INVOICE)
        column_values.append(TYPE_OBJECT_RECEIPT)

        return column_values

    elif page_group_type == TYPE_OBJECT_ORDER:
        page_obj = get_page_object(TYPE_OBJECT_ORDER)
        if page_obj is None:
            return []
        exclude_custom_types = page_obj["exclude_custom_types"]
        column_values = DEFAULT_COLUMNS_ORDER.copy()
        column_values.remove("checkbox")
        column_values.remove("order_id")

        # Subscriptipons
        column_values.insert(len(column_values), TYPE_OBJECT_SUBSCRIPTION)

        if include_integration_properties:
            column_values += get_integration_properties(page_group_type, workspace)
        # Invoice
        column_values.append(TYPE_OBJECT_INVOICE)
        # Estimate
        column_values.append(TYPE_OBJECT_ESTIMATE)
        # Inventory Transactions
        column_values.append(TYPE_OBJECT_INVENTORY_TRANSACTION)

        # New Assoc
        column_values.append(TYPE_OBJECT_CASE)
        column_values.append(TYPE_OBJECT_DELIVERY_NOTE)
        column_values.append(TYPE_OBJECT_PURCHASE_ORDER)
        column_values.append(TYPE_OBJECT_TASK)
        column_values.append(TYPE_OBJECT_BILL)
        column_values.append(TYPE_OBJECT_ITEM)

        # owner
        column_values.append("owner")

        # Line Items inventory amount in location
        column_values.append("line_item_inventory_location")

        # Line items tax
        column_values.append("line_item_tax")

        # Line Items
        line_item_cf = ShopTurboItemsOrdersNameCustomField.objects.filter(
            workspace=workspace
        ).order_by("type")
        for c in line_item_cf:
            column_values.append("line_item|" + str(c.id))
        # print("++++ column_values: ", column_values)

        # Warehouse inventory amount
        column_values.append("warehouse_inventory_amount")
        print("include_default_association: ", include_default_association)
        if include_default_association:
            # Customer Columns
            _contact_properties = get_list_view_columns("contacts", workspace)
            if "name" not in _contact_properties:
                _contact_properties.append("name")
            for p in _contact_properties:
                column_values.append(f"customer|contact|{p}")

            _company_properties = get_list_view_columns("company", workspace)
            for p in _company_properties:
                column_values.append(f"customer|company|{p}")

            # Item Columns
            _item_columns = get_list_view_columns("commerce_items", workspace)
            column_values.append("source_item")
            for p in _item_columns:
                column_values.append(f"source_item__{p}")

            # Contact/Company Object
            order_namecustomfields = ShopTurboOrdersNameCustomField.objects.filter(
                workspace=workspace
            ).exclude(type__in=exclude_custom_types)
            for customfield in order_namecustomfields:
                if customfield.type == "contact":
                    for p in _contact_properties:
                        column_values.append(f"contact|{customfield.id}|{p}")

                elif customfield.type == "company":
                    for p in _company_properties:
                        column_values.append(f"company|{customfield.id}|{p}")

                if customfield.type == "shipping_info":
                    sub_property = ast.literal_eval(customfield.sub_property)
                    column_values.append(
                        f"shipping_info|{customfield.id}|{customfield.name}|ID"
                    )
                    for p in sub_property:
                        column_values.append(
                            f"shipping_info|{customfield.id}|{customfield.name}|{p}"
                        )

                else:
                    column_values.append(str(customfield.id))
        #association label
        association_labels = AssociationLabel.objects.filter(workspace=workspace, object_source=page_group_type, created_by_sanka=False).order_by("created_at")
        for association_label in association_labels:
            column_values.append(str(association_label.id))
        #association label related
        related_association_labels = AssociationLabel.objects.filter(created_by_sanka=False, workspace=workspace, object_target__icontains=page_group_type).order_by("created_at")
        for related_association_label in related_association_labels:
            column_values.append(str(related_association_label.id))

        print("========== column_values: ", column_values)
        return column_values

    elif page_group_type == TYPE_OBJECT_SUBSCRIPTION:
        page_object = get_page_object(TYPE_OBJECT_SUBSCRIPTION)
        if page_object is None:
            return []
        column_values = page_object["base_columns"]
        exclude_custom_types = page_object["exclude_custom_types"]
        if include_integration_properties:
            column_values += get_integration_properties(page_group_type, workspace)

        if include_cf:
            custom_properties = get_custom_properties(page_group_type, workspace)
            custom_properties = custom_properties.exclude(type__in=exclude_custom_types)
            _contact_properties = get_list_view_columns("contacts", workspace)
            _company_properties = get_list_view_columns("company", workspace)
            for custom_property in custom_properties:
                if custom_property.type == "contact":
                    for p in _contact_properties:
                        column_values.append(f"contact|{custom_property.id}|{p}")

                elif custom_property.type == "company":
                    for p in _company_properties:
                        column_values.append(f"company|{custom_property.id}|{p}")
                else:
                    column_values.append(str(custom_property.id))

        if "item_name" in column_values:
            column_values.remove("item_name")
        if "orders" in column_values:
            column_values.remove("orders")

        column_values.append(TYPE_OBJECT_ITEM)
        column_values.append(TYPE_OBJECT_INVOICE)
        column_values.append(TYPE_OBJECT_ORDER)
        
        #check duplicate
        column_values = list(set(column_values))

        return column_values

    elif page_group_type == TYPE_OBJECT_INVOICE:
        page_object = get_page_object(TYPE_OBJECT_INVOICE)
        if page_object is None:
            return []
        column_values = page_object["base_columns"] + ["line_item"]
        exclude_custom_types = page_object["exclude_custom_types"]

        if include_integration_properties:
            column_values += get_integration_properties(page_group_type, workspace)

        if include_default_association:
            # Customer Columns
            _contact_properties = get_list_view_columns("contacts", workspace)
            if "name" not in _contact_properties:
                _contact_properties.append("name")
            for p in _contact_properties:
                column_values.append(f"customer|contact|{p}")

            _company_properties = get_list_view_columns("company", workspace)
            for p in _company_properties:
                column_values.append(f"customer|company|{p}")

        # Add custom properties for invoices
        custom_model = page_object["custom_model"]
        if custom_model:
            for field in custom_model.objects.filter(workspace=workspace).exclude(
                type__in=exclude_custom_types
            ):
                column_values.append(str(field.id))

        if "journalentry" in column_values:
            column_values.remove("journalentry")

        # delete customers
        if "customers__contact__name" in column_values:
            column_values.remove("customers__contact__name")
        if "customers__contact__first_name" in column_values:
            column_values.remove("customers__contact__first_name")
        if "customers__contact__last_name" in column_values:
            column_values.remove("customers__contact__last_name")
        if "customers__company__name" in column_values:
            column_values.remove("customers__company__name")
        if "order_association" in column_values:
            column_values.remove("order_association")
        if "associate#receipts" in column_values:
            column_values.remove("associate#receipts")
        if "subscription" in column_values:
            column_values.remove("subscription")

        # association label
        column_values.append("customer")
        column_values.append(TYPE_OBJECT_SUBSCRIPTION)
        column_values.append(TYPE_OBJECT_ORDER)
        column_values.append(TYPE_OBJECT_CASE)
        column_values.append(TYPE_OBJECT_RECEIPT)
        column_values.append(TYPE_OBJECT_ESTIMATE)
        column_values.append(TYPE_OBJECT_ITEM)

        return column_values

    elif page_group_type == TYPE_OBJECT_TASK:
        page_obj = get_page_object(TYPE_OBJECT_TASK)
        if page_obj is None:
            return []
        exclude_custom_types = page_obj["exclude_custom_types"]
        column_values = get_model_columns(
            Task,
            includes=["assignee", "sub_tasks", "owner"],
            excludes=["line_item", "order"],
        ) + ["line_item"]
        if include_cf:
            tasknamecf = TaskCustomFieldName.objects.filter(
                workspace=workspace
            ).exclude(type__in=exclude_custom_types).values_list("id", flat=True)
            column_values.extend([str(item) for item in tasknamecf])

        # Update order of 'assignee' column to be after 'title' column
        column_values.remove("assignee")
        column_values.insert(2, "assignee")

        return column_values

    elif page_group_type == TYPE_OBJECT_CONTACT:
        column_values = get_model_columns(
            Contact,
            excludes=["created_at"],
            includes=[
                "owner",
                TYPE_OBJECT_COMPANY,
                TYPE_OBJECT_CASE,
                TYPE_OBJECT_ORDER,
                TYPE_OBJECT_ESTIMATE,
                TYPE_OBJECT_DELIVERY_NOTE,
                TYPE_OBJECT_INVOICE,
                TYPE_OBJECT_RECEIPT,
                TYPE_OBJECT_SUBSCRIPTION,
                TYPE_OBJECT_EXPENSE,
                TYPE_OBJECT_BILL,
                TYPE_OBJECT_PURCHASE_ORDER,
                TYPE_OBJECT_ITEM,
            ],
        )

        column_values = [
            "first_name" if col == "name" else col for col in column_values
        ]
        channels = Channel.objects.filter(
            workspace=workspace,
            integration__slug__in=[
                "line",
                "shopify",
                "hubspot",
                "rakuten",
                "salesforce",
                "b-cart",
            ],
        )  # Add Others, Might Be add others

        for channel in channels:
            if channel.integration.slug == "line":
                column_values.insert(
                    len(column_values),
                    f"{str(channel.id)} - {str(channel.name)} - Line User ID",
                )
            else:
                column_values.insert(
                    len(column_values), f"{str(channel.name)} - customer ID"
                )

        if include_cf:
            column_values.extend(get_custom_properties_ids(page_group_type, workspace))

        return column_values

    elif page_group_type == TYPE_OBJECT_COMPANY:
        column_values = get_model_columns(
            Company,
            excludes=["created_at"],
            includes=[
                "owner",
                TYPE_OBJECT_CONTACT,
                TYPE_OBJECT_CASE,
                TYPE_OBJECT_ORDER,
                TYPE_OBJECT_ESTIMATE,
                TYPE_OBJECT_DELIVERY_NOTE,
                TYPE_OBJECT_INVOICE,
                TYPE_OBJECT_RECEIPT,
                TYPE_OBJECT_SUBSCRIPTION,
                TYPE_OBJECT_EXPENSE,
                TYPE_OBJECT_BILL,
                TYPE_OBJECT_PURCHASE_ORDER,
                TYPE_OBJECT_ITEM,
            ],
        )
        column_values.append("image_file")
        # column_values = ['first_name' if col == 'name' else col for col in column_values]
        channels = Channel.objects.filter(
            workspace=workspace,
            integration__slug__in=["line", "hubspot", "salesforce", "b-cart"],
        )  # Add Others, Might Be add others

        for channel in channels:
            if channel.integration.slug == "line":
                column_values.insert(
                    len(column_values),
                    f"{str(channel.id)} - {str(channel.name)} - Line User ID",
                )
            else:
                column_values.insert(
                    len(column_values), f"{str(channel.name)} - company id"
                )

        if include_cf:
            column_values.extend(get_custom_properties_ids(page_group_type, workspace))

        return column_values

    elif page_group_type == TYPE_OBJECT_INVENTORY:
        column_values = DEFAULT_COLUMNS_INVENTORY[1:].copy() + [
            "inventory_value",
            "initial_value",  # Add initial_value to selectable columns
        ]

        # Add association properties
        column_values.append(TYPE_OBJECT_ITEM)
        column_values.append(TYPE_OBJECT_INVENTORY_TRANSACTION)
        column_values.append(TYPE_OBJECT_INVENTORY_WAREHOUSE)

        if include_cf:
            column_values.extend(get_custom_properties_ids(page_group_type, workspace))

        if include_integration_properties:
            column_values += get_integration_properties(page_group_type, workspace)
        return column_values

    elif page_group_type == TYPE_OBJECT_INVENTORY_TRANSACTION:
        page_obj = get_page_object(TYPE_OBJECT_INVENTORY_TRANSACTION)
        if page_obj is None:
            return []
        custom_model = page_obj["custom_model"]
        base_columns = page_obj["base_columns"]
        default_columns = page_obj["default_columns"]
        exclude_custom_types = page_obj["exclude_custom_types"]
        pre_default_column = default_columns
        pre_default_column.remove("checkbox")
        pre_default_column.remove(page_obj["id_field"])
        column_values = base_columns
        custom_columns = []

        # Add inventory custom properties using pipe syntax
        if include_cf:
            inventory_custom_fields = get_custom_properties_ids(
                page_group_type, workspace
            )
            for cf in inventory_custom_fields:
                column_option = f"commerce_inventory|{cf}"
                column_values.append(column_option)

        column_values = base_columns.copy()
        # NOTE: Add Elements of custom columns
        if custom_model and include_cf:
            custom_columns = custom_model.objects.filter(
                name__isnull=False, workspace=workspace
            ).exclude(type__in=exclude_custom_types).values_list("id", flat=True)
            column_values.extend([str(item) for item in custom_columns])
        # NOTE: Add Elements of default columns
        column_values.extend(
            [str(item) for item in default_columns if item not in column_values]
        )

        # already replace by TYPE_OBJECT_INVENTORY
        if "inventory" in column_values:
            column_values.remove("inventory")

        # association label
        column_values.append(TYPE_OBJECT_INVENTORY)
        column_values.append(TYPE_OBJECT_ORDER)
        column_values.append(TYPE_OBJECT_PURCHASE_ORDER)

        return column_values

    elif page_group_type == TYPE_OBJECT_CASE:
        page_obj = get_page_object(page_group_type)
        if page_obj is None:
            return []
        column_values = page_obj["base_columns"].copy()
        exclude_custom_types = page_obj["exclude_custom_types"]
        column_values.extend(
            [
                str(item)
                for item in page_obj["default_columns"].copy()
                + [
                    TYPE_OBJECT_ORDER,
                    TYPE_OBJECT_INVOICE,
                    TYPE_OBJECT_ESTIMATE,
                    TYPE_OBJECT_TASK,
                    TYPE_OBJECT_ITEM,
                ]
                if item not in column_values
            ]
        )

        if "owner" in column_values:
            column_values.remove("owner")

        if include_cf:
            custom_columns = []
            custom_model = page_obj["custom_model"]
            custom_columns = custom_model.objects.filter(
                name__isnull=False, workspace=workspace
            ).exclude(type__in=exclude_custom_types).values_list("id", flat=True)
            column_values.extend([str(item) for item in custom_columns])
        column_values.remove("currency")
        
        if include_integration_properties:
            integration_props = get_integration_properties(page_group_type, workspace)
            column_values += integration_props

        if include_default_association:
            # Customer Columns
            _contact_properties = get_list_view_columns("contacts", workspace)
            if "name" not in _contact_properties:
                _contact_properties.append("name")
            for p in _contact_properties:
                column_values.append(f"customer|contact|{p}")

            _company_properties = get_list_view_columns("company", workspace)
            for p in _company_properties:
                column_values.append(f"customer|company|{p}")

        if "kanban_order" in column_values:
            column_values.remove("kanban_order")

        return column_values

    elif page_group_type == TYPE_OBJECT_SESSION_EVENT:
        page_object = get_page_object(TYPE_OBJECT_SESSION_EVENT)
        if page_object is None:
            return []
        return page_object["default_columns"]

    elif page_group_type == TYPE_OBJECT_PURCHASE_ORDER:
        page_obj = get_page_object(page_group_type)
        if page_obj is None:
            return []
        column_values = page_obj["base_columns"].copy()
        exclude_custom_types = page_obj["exclude_custom_types"]
        # Line Items
        for c in line_item_columns:
            if c not in column_values:
                column_values.append(c)
        column_values.append("line_item_price_without_tax")
        # Default columns
        column_values.extend(
            [
                str(item)
                for item in page_obj["default_columns"].copy()
                if item not in column_values
            ]
        )
        # Custom Fields
        if include_cf:
            custom_columns = []
            custom_model = page_obj["custom_model"]
            custom_columns = custom_model.objects.filter(
                name__isnull=False, workspace=workspace
            ).values_list("id", flat=True)
            column_values.extend([str(item) for item in custom_columns])

        if include_default_association:
            column_values.append("source_item")

        if "inventory_transactions" in column_values:
            column_values.remove("inventory_transactions")

        column_values.append(TYPE_OBJECT_INVENTORY_TRANSACTION)
        column_values.append(TYPE_OBJECT_ORDER)
        column_values.append(TYPE_OBJECT_ITEM)

        return column_values

    elif page_group_type in BILL_OBJECTS:
        page_obj = get_page_object(page_group_type)
        if page_obj is None:
            return []
        exclude_custom_types = page_obj["exclude_custom_types"]
        column_values = DEFAULT_FORM_FIELD_COMMERCE.copy()
        for col_val in column_values:
            if (
                col_val not in page_obj["base_columns"]
                and col_val not in page_obj["default_columns"]
            ):
                column_values.remove(col_val)
        column_values.extend(
            [
                str(item)
                for item in page_obj["base_columns"].copy()
                if item not in column_values
            ]
        )
        column_values.extend(
            [
                str(item)
                for item in page_obj["default_columns"].copy()
                if item not in column_values
            ]
        )

        if include_cf:
            custom_columns = []
            custom_model = page_obj["custom_model"]
            custom_columns = custom_model.objects.filter(
                name__isnull=False, workspace=workspace
            ).exclude(type__in=exclude_custom_types).values_list("id", flat=True)
            column_values.extend([str(item) for item in custom_columns])

        if (
            page_group_type == TYPE_OBJECT_INVOICE
            or page_group_type == TYPE_OBJECT_DELIVERY_NOTE
        ):  # This follow what "order" did
            if include_default_association:
                # Remove Contact__ or Company__ -> doesnt suppoort Custom Property
                column_values = [
                    str(item) for item in column_values if "customers__" not in item
                ]
                if "journalentry" in column_values:
                    column_values.remove("journalentry")

                _contact_properties = get_list_view_columns("contacts", workspace)
                if "name" not in _contact_properties:
                    _contact_properties.append("name")
                for p in _contact_properties:
                    column_values.append(f"customer|contact|{p}")

                _company_properties = get_list_view_columns("company", workspace)
                for p in _company_properties:
                    column_values.append(f"customer|company|{p}")

                contact_custom_columns = ContactsNameCustomField.objects.filter(
                    workspace=workspace
                ).order_by("created_at")
                column_values.extend(
                    [
                        "customer|contact|" + str(item.id)
                        for item in contact_custom_columns
                    ]
                )
                contact_custom_columns = CompanyNameCustomField.objects.filter(
                    workspace=workspace
                ).order_by("created_at")
                column_values.extend(
                    [
                        "customer|company|" + str(item.id)
                        for item in contact_custom_columns
                    ]
                )
            
            if page_group_type == TYPE_OBJECT_DELIVERY_NOTE:
                column_values.append('customer')
                column_values.append(TYPE_OBJECT_ORDER)
                column_values.append(TYPE_OBJECT_INVOICE)
                
            column_values.append(TYPE_OBJECT_ITEM)
            
            if 'customers' in column_values:column_values.remove('customers')
            
            
        elif page_group_type == TYPE_OBJECT_RECEIPT:
            if "customers__contact__name" in column_values:
                column_values.remove("customers__contact__name")
            if "customers__contact__first_name" in column_values:
                column_values.remove("customers__contact__first_name")
            if "customers__contact__last_name" in column_values:
                column_values.remove("customers__contact__last_name")
            if "customers__company__name" in column_values:
                column_values.remove("customers__company__name")
            if "order_association" in column_values:
                column_values.remove("order_association")
            if "invoice" in column_values:
                column_values.remove("invoice")
            if "customers" in column_values:
                column_values.remove("customers")

            column_values.append("customer")
            column_values.append(TYPE_OBJECT_INVOICE)
            column_values.append(TYPE_OBJECT_ITEM)

        elif page_group_type == TYPE_OBJECT_ESTIMATE:
            if "customers__contact__name" in column_values:
                column_values.remove("customers__contact__name")
            if "customers__contact__first_name" in column_values:
                column_values.remove("customers__contact__first_name")
            if "customers__contact__last_name" in column_values:
                column_values.remove("customers__contact__last_name")
            if "customers__company__name" in column_values:
                column_values.remove("customers__company__name")
            if "order_association" in column_values:
                column_values.remove("order_association")
            if "invoice" in column_values:
                column_values.remove("invoice")
            if "customers" in column_values:
                column_values.remove("customers")

            column_values.append("customer")
            column_values.append(TYPE_OBJECT_ORDER)
            column_values.append(TYPE_OBJECT_CASE)
            column_values.append(TYPE_OBJECT_INVOICE)
            column_values.append(TYPE_OBJECT_ITEM)

        else:
            if include_default_association:
                contact_custom_columns = ContactsNameCustomField.objects.filter(
                    workspace=workspace
                ).values_list("name", flat=True)
                column_values.extend(
                    ["Customer-" + str(item) for item in contact_custom_columns]
                )

        return column_values

    elif page_group_type == TYPE_OBJECT_BILL:
        page_obj = get_page_object(page_group_type)
        if page_obj is None:
            return []
        column_values = page_obj["base_columns"].copy()
        exclude_custom_types = page_obj["exclude_custom_types"]
        
        default_form_fields = page_obj["default_form_fields"].copy()
        column_values = reorder_columns(default_form_fields, column_values)
        # Custom Fields
        if include_cf:
            custom_columns = []
            custom_model = page_obj["custom_model"]
            custom_columns = custom_model.objects.filter(
                name__isnull=False, workspace=workspace
            ).exclude(type__in=exclude_custom_types).values_list("id", flat=True)
            column_values.extend([str(item) for item in custom_columns])
        
        if 'journal_entry' in column_values:
            column_values.remove("journal_entry")
        
        column_values.append(TYPE_OBJECT_JOURNAL)
        

    elif page_group_type in TYPE_OBJECT_WORKFLOW:
        column_values = DEFAULT_COLUMNS_WORKFLOW.copy()
        if for_form:
            column_values = DEFAULT_FORM_FIELD_WORKFLOW.copy()

        return column_values

    elif page_group_type in TYPE_OBJECT_JOURNAL:
        page_object = get_page_object(TYPE_OBJECT_JOURNAL)
        if page_object is None:
            return []
        column_values = page_object["base_columns"]
        exclude_custom_types = page_object["exclude_custom_types"]
        # Default columns
        column_values.extend(
            [
                str(item)
                for item in page_object["default_columns"].copy()
                if item not in column_values
            ]
        )
        # Line transaction in Journal Entry
        column_values.append("line_transaction")

        # Custom Fields
        if include_cf:
            custom_columns = []
            custom_model = page_object["custom_model"]
            custom_columns = custom_model.objects.filter(
                name__isnull=False, workspace=workspace
            ).exclude(type__in=exclude_custom_types).values_list("id", flat=True)
            column_values.extend([str(item) for item in custom_columns])

    elif page_group_type in TYPE_OBJECT_EXPENSE:
        page_object = get_page_object(TYPE_OBJECT_EXPENSE)
        if page_object is None:
            return []
        column_values = page_object["base_columns"].copy()
        exclude_custom_types = page_object["exclude_custom_types"]
        # Default columns
        column_values.extend(
            [
                str(item)
                for item in page_object["default_columns"].copy()
                if item not in column_values
            ]
        )
        # Custom Fields
        if include_cf:
            custom_columns = []
            custom_model = page_object["custom_model"]
            custom_columns = custom_model.objects.filter(
                name__isnull=False, workspace=workspace
            ).exclude(type__in=exclude_custom_types).values_list("id", flat=True)
            column_values.extend([str(item) for item in custom_columns])

    elif page_group_type in TYPE_OBJECT_COMMERCE_METER:
        page_object = get_page_object(TYPE_OBJECT_COMMERCE_METER)
        if page_object is None:
            return []
        column_values = page_object["base_columns"].copy()
        exclude_custom_types = page_object["exclude_custom_types"]
        # Default columns
        column_values.extend(
            [
                str(item)
                for item in page_object["default_columns"].copy()
                if item not in column_values
            ]
        )
        # Custom Fields
        if include_cf:
            custom_columns = []
            custom_model = page_object["custom_model"]
            custom_columns = custom_model.objects.filter(
                name__isnull=False, workspace=workspace
            ).exclude(type__in=exclude_custom_types).values_list("id", flat=True)
            column_values.extend([str(item) for item in custom_columns])
    return column_values


def get_default_property_set(page_group_type, workspace, lang, name=None):
    excludes = []
    if page_group_type == TYPE_OBJECT_CASE:
        excludes = ["estimates", "invoices", "tasks", "line_item", "currency"]
    if page_group_type == TYPE_OBJECT_TASK:
        excludes = ["line_item"]

    payload = {"workspace": workspace, "name": name, "target": page_group_type}
    try:
        default_set, status = PropertySet.objects.get_or_create(**payload)
    except PropertySet.MultipleObjectsReturned:
        status = None
        default_set = PropertySet.objects.filter(**payload).first()
        PropertySet.objects.filter(**payload).exclude(id=default_set.id).delete()

    if status:
        default_set.as_default = True

    page_obj = get_page_object(page_group_type)
    if page_obj is None:
        return []  # or raise an appropriate error
    exclude_custom_types = page_obj["exclude_custom_types"]
    default_properties = page_obj["default_form_fields"].copy()
    if default_properties:
        if not default_set.children:
            default_set.children = default_properties

            # Add inventory custom properties for inventory transactions
            if page_group_type == TYPE_OBJECT_INVENTORY_TRANSACTION:
                logger.debug(
                    f"DEBUG GET_DEFAULT_PROPERTY_SET: Adding inventory custom properties for {page_group_type}"
                )
                inventory_custom_fields = (
                    ShopTurboInventoryNameCustomField.objects.filter(
                        workspace=workspace
                    ).exclude(type__in=exclude_custom_types)
                )
                for cf in inventory_custom_fields:
                    inventory_custom_property = f"commerce_inventory|{cf.id}"
                    if inventory_custom_property not in default_set.children:
                        default_set.children.append(inventory_custom_property)
                        logger.debug(
                            f"DEBUG GET_DEFAULT_PROPERTY_SET: Added inventory custom property: {inventory_custom_property} (name: {cf.name})"
                        )
                logger.debug(
                    f"DEBUG GET_DEFAULT_PROPERTY_SET: Final children: {default_set.children}"
                )
        else:
            # PropertySet already has children, but we need to add inventory custom properties if missing
            if page_group_type == TYPE_OBJECT_INVENTORY_TRANSACTION:
                logger.debug(
                    f"DEBUG GET_DEFAULT_PROPERTY_SET: Updating existing PropertySet children for {page_group_type}"
                )
                inventory_custom_fields = (
                    ShopTurboInventoryNameCustomField.objects.filter(
                        workspace=workspace
                    ).exclude(type__in=exclude_custom_types)
                )
                for cf in inventory_custom_fields:
                    inventory_custom_property = f"commerce_inventory|{cf.id}"
                    if inventory_custom_property not in default_set.children:
                        default_set.children.append(inventory_custom_property)
                        logger.debug(
                            f"DEBUG GET_DEFAULT_PROPERTY_SET: Added missing inventory custom property: {inventory_custom_property} (name: {cf.name})"
                        )
                logger.debug(
                    f"DEBUG GET_DEFAULT_PROPERTY_SET: Updated children: {default_set.children}"
                )
    else:
        properties = get_properties_with_details(
            page_group_type, workspace, lang, excludes=excludes
        )
        default_set.children = [str(get_attr(p, "id")) for p in properties]

    default_set.save()

    return default_set


def filter_form_children_and_add_associations(form, object_type, workspace, lang):
    """
    Filter form.children to remove custom fields with excluded types and add association labels
    """
    try:
        from data.constants.properties_constant import (
            TYPE_OBJECT_BILL,
            TYPE_OBJECT_COMPANY,
            TYPE_OBJECT_CONTACT,
            TYPE_OBJECT_INVENTORY_WAREHOUSE,
            TYPE_OBJECT_INVOICE,
            TYPE_OBJECT_ORDER,
            TYPE_OBJECT_PURCHASE_ORDER,
            TYPE_OBJECT_SUBSCRIPTION,
            TYPE_OBJECT_TASK,
            TYPE_OBJECT_USER_MANAGEMENT,
        )

        mapping_cf_type_to_association_label = {
            "contact": TYPE_OBJECT_CONTACT,
            "company": TYPE_OBJECT_COMPANY,
            "customer": TYPE_OBJECT_COMPANY,
            "purchase_order": TYPE_OBJECT_PURCHASE_ORDER,
            "subscription": TYPE_OBJECT_SUBSCRIPTION,
            "user_management": TYPE_OBJECT_USER_MANAGEMENT,
            "bill_objects": TYPE_OBJECT_BILL,
            "invoice_objects": TYPE_OBJECT_INVOICE,
            "order_objects": TYPE_OBJECT_ORDER,
            "task": TYPE_OBJECT_TASK,
            "warehouse_objects": TYPE_OBJECT_INVENTORY_WAREHOUSE,
        }

        if not form.children:
            return

        page_obj = get_page_object(object_type, lang)
        if page_obj is None:
            return []  # or raise an appropriate error
        exclude_custom_types = page_obj["exclude_custom_types"]
        custom_model = page_obj["custom_model"]

        # Track removed types for association label replacement
        removed_custom_field_types = set()
        filtered_children = []

        for child_id in form.children:
            should_include = True

            # Check if this is a custom field UUID that should be excluded
            if is_valid_uuid(child_id) and custom_model:
                try:
                    custom_field = custom_model.objects.filter(
                        id=child_id, workspace=workspace
                    ).first()
                    if custom_field and custom_field.type in exclude_custom_types:
                        should_include = False
                        removed_custom_field_types.add(custom_field.type)
                except Exception:
                    pass  # If query fails, include the field

            if should_include:
                filtered_children.append(child_id)

        # Add association labels for removed custom field types
        for removed_type in removed_custom_field_types:
            # Get target object type from mapping
            target_object_type = mapping_cf_type_to_association_label.get(removed_type)
            if target_object_type:
                # Find association labels that target this object type
                association_labels = AssociationLabel.objects.filter(
                    workspace=workspace,
                    object_source=object_type,
                    object_target=target_object_type,
                )

                for assoc_label in association_labels:
                    assoc_id = str(assoc_label.id)
                    if assoc_id not in filtered_children:
                        filtered_children.append(assoc_id)

        # Update form children with filtered results
        form.children = filtered_children
        form.save()
    except Exception as e:
        logger.error(f"Error filtering form children: {e}")


def get_properties_from_set(set_id, page_group_type, workspace, as_default=None):
    property_set = None
    properties = {
        "list_all": [],  # Need to get all List (faris)
        "default": [],
        "custom": [],
    }

    try:
        if set_id and is_valid_uuid(set_id):
            property_set = PropertySet.objects.filter(
                id=set_id, name__isnull=False
            ).first()
            if not property_set:
                property_set = PropertySet.objects.filter(
                    workspace=workspace, target=page_group_type, as_default=True
                ).first()
            if not property_set:
                property_set = PropertySet.objects.filter(id=set_id).first()

        if not property_set:
            property_set = PropertySet.objects.filter(
                workspace=workspace, target=page_group_type, as_default=True
            ).first()
        if not property_set:
            property_set = PropertySet.objects.filter(
                workspace=workspace, target=page_group_type, name__isnull=True
            ).first()
        if not property_set:
            get_default_property_set(page_group_type, workspace, "en")
            property_set = PropertySet.objects.filter(
                workspace=workspace, target=page_group_type, name__isnull=True
            ).first()

        if property_set:
            set_id = str(property_set.id)
            if property_set.children:
                for p in property_set.children:
                    properties["list_all"].append(p)
                    if is_valid_uuid(p):
                        properties["custom"].append(p)
                    else:
                        properties["default"].append(p)
    except Exception as e:
        logger.error(f"... ERROR === property.py -- 490: {e}")

    set_id = property_set.id if property_set else None
    return set_id, properties


def object_display(page_group_type, workspace, include_custom_properties=True):
    """
    Get all properties of an object type, including custom properties and nested item properties to be used in object display options.

    :param page_group_type: The type of object (e.g. TYPE_OBJECT_CONTACT)
    :param workspace: The workspace
    :param include_custom_properties: True to include custom properties, False to exclude
    :return: A list of property names
    """

    props = get_properties_with_details(
        page_group_type, workspace, "en", include_integration_properties=False
    )
    builtin_properties = []
    custom_properties = []
    default_properties = DEFAULT_OBJECT_DISPLAY[page_group_type]
    for prop in props:
        # Custom property
        if is_valid_uuid(prop["id"]):
            if not include_custom_properties:
                continue
            if str(prop["id"]) in custom_properties:
                continue
            custom_properties.append(prop["id"])
        # Built-in Property
        else:
            # Hop to sub object property
            if prop["type"] == "items":
                for val in object_display(
                    TYPE_OBJECT_ITEM, workspace, include_custom_properties=True
                ):
                    builtin_properties.append(f"{TYPE_OBJECT_ITEM}|{val}")
            else:
                builtin_properties.append(prop["id"])

    for prop_ in reversed(default_properties):
        if prop_ in builtin_properties:
            continue
        builtin_properties = [prop_] + builtin_properties

    return builtin_properties + custom_properties


def get_default_prompt_set(workspace, target, CustomFieldName=None):
    get_default_property_set(target, workspace, lang="en")

    condition_filter = Q(workspace=workspace, target=target)
    condition_filter &= Q(as_default=True) | Q(name__isnull=True)
    property_set = PropertySet.objects.filter(condition_filter).first()
    properties = {"list_all": []}

    if property_set:
        if property_set.children:
            for p in property_set.children:
                properties["list_all"].append(p)

    if not properties["list_all"]:
        _, properties = get_properties_from_set(None, target, workspace)

    CustomFieldMap = {}
    if CustomFieldName:
        explorecustomfield = CustomFieldName.objects.filter(
            workspace=workspace
        ).order_by("order")
        for ctf in explorecustomfield:
            CustomFieldMap[str(ctf.id)] = ctf

    return properties, CustomFieldMap


def url_name_by_object_type(object_type):
    try:
        # First try the direct lookup
        return OBJECT_TYPE_TO_URL_NAME[object_type]
    except:
        # If not found, try to map through OBJ_CUSTOM_FIELD_MAP_PAGE_GROUP_TYPE
        try:
            mapped_type = OBJ_CUSTOM_FIELD_MAP_PAGE_GROUP_TYPE.get(object_type)
            if mapped_type:
                return OBJECT_TYPE_TO_URL_NAME[mapped_type]
        except:
            pass
        # Return None for completely invalid types
        return None


def get_field(base_model, base_columns, field_types: list):
    """
    Get the field names of the base model that match the specified field types.
    """
    properties_dict = {
        field.name: field.get_internal_type()
        for field in base_model._meta.fields
        if field.name in base_columns
    }
    res_properties = list(
        {k: v for k, v in properties_dict.items() if v in field_types}.keys()
    )
    return res_properties


def get_field_data_type(model_name, field):
    """
    Determine the data type of a field in a given model.

    Parameters:
    - model_name (str): The name of the model to check.
    - field (str): The name of the field to determine the data type for.

    Returns:
    - str or None: The data type of the field if it exists, otherwise None.
    """
    model_class = apps.get_model("data", model_name)
    if field not in [f.name for f in model_class._meta.fields]:
        return None
    return model_class._meta.get_field(field).get_internal_type()


def populate_object_display_context(request, workspace, context):
    """
    Populate the object display context with necessary information for rendering the object display page.

    This function prepares the context dictionary with the following information:
    - Workspace: The current workspace of the user.
    - Language: The language code of the current user's session.
    - Page Group Type: The type of page group being displayed (e.g., components, bill_objects, etc.).
    - Columns: A list of columns to be displayed for the page group type.
    - Object Manager: The ObjectManager instance associated with the workspace and page group type.
    - Object Display Options: A list of display options for the object, including custom properties.
    - Field ID: A dictionary containing the ID and name of the field to be used for identification.

    :param request: The current request object.
    :param context: The context dictionary to be populated.
    """
    lang = request.LANGUAGE_CODE
    columns = []
    object_manager = None
    page_group_type = request.GET.get("setting_type")
    custom_object = context["custom_object"]
    if custom_object:
        page_group_type = TYPE_OBJECT_CUSTOM_OBJECT
        object_manager = ObjectManager.objects.filter(
            workspace=workspace,
            custom_object=custom_object,
            page_group_type=page_group_type,
        ).first()
        columns = object_display(page_group_type, workspace)
    else:
        columns = object_display(page_group_type, workspace)
        object_manager = ObjectManager.objects.filter(
            workspace=workspace, page_group_type=page_group_type
        ).first()

    object_display_options = []
    page_obj = get_page_object(page_group_type, lang)

    # Handle case where page_obj is None
    if page_obj is None:
        logger.error(
            f"No page object configuration found for page_group_type: {page_group_type}"
        )
        context.update(
            {
                "workspace": workspace,
                "lang": lang,
                "page_group_type": page_group_type,
                "columns": columns,
                "object_manager": object_manager,
                "object_display_options": [],
                "field_id": {},
            }
        )
        return context

    custom_prop_model = page_obj["custom_model"]
    field_id = {}
    for col in columns:
        if is_valid_uuid(col):
            if custom_object:
                custom_prop = custom_prop_model.objects.filter(
                    id=col, workspace=workspace, custom_object=custom_object
                ).first()
            else:
                custom_prop = custom_prop_model.objects.filter(
                    id=col, workspace=workspace
                ).first()
            if custom_prop:
                object_display_options.append({"id": col, "name": custom_prop.name})
        elif "line_item" in col:
            pass
        elif "|" in col:
            col_split = col.split("|")

            if len(col_split) > 2:
                continue
            else:
                parent_obj = col_split[0]
                col_ = col_split[1]

            logger.debug(f"DEBUG POPULATE_CONTEXT: Processing pipe column: {col_split}")
            logger.debug(
                f"DEBUG POPULATE_CONTEXT: parent_obj={parent_obj}, col_={col_}"
            )

            # Skip if parent_obj is not in OBJECT_GROUP_TYPE_SINGULAR
            if parent_obj not in OBJECT_GROUP_TYPE_SINGULAR:
                logger.warning(f"Unknown parent object type: {parent_obj}")
                continue

            parent_obj_display = OBJECT_GROUP_TYPE_SINGULAR[parent_obj][lang]
            page_obj_ = get_page_object(parent_obj, lang)

            # Skip if get_page_object returns None
            if page_obj_ is None:
                logger.warning(f"No page object configuration found for: {parent_obj}")
                continue

            col_display = col_
            if (
                page_obj_.get("columns_display")
                and col_ in page_obj_["columns_display"]
            ):
                col_display = page_obj_["columns_display"][col_][lang]
            elif is_valid_uuid(col_):
                custom_model = page_obj_.get("custom_model")
                if custom_model:
                    custom_prop = custom_model.objects.filter(
                        id=col_, workspace=workspace
                    ).first()
                    if custom_prop:
                        col_display = custom_prop.name
            object_display_options.append(
                {"id": col, "name": parent_obj_display + " - " + col_display}
            )
        else:
            col_display = col
            if page_obj.get("columns_display") and col in page_obj["columns_display"]:
                col_display = page_obj["columns_display"][col][lang]
            object_display_options.append({"id": col, "name": col_display})

            if page_obj.get("id_field") and col == page_obj["id_field"]:
                field_id["id"] = col
                field_id["name"] = col_display

    # default display settings
    exclude_fields = []
    if page_group_type == TYPE_OBJECT_CASE:
        default_display_setting = [page_obj["id_field"], "name"]
        exclude_fields = ["estimate", "invoice", "tasks"]
    else:
        default_display_setting = [page_obj["id_field"]]

    object_display_options = [
        field for field in object_display_options if field["id"] not in exclude_fields
    ]
    if object_manager:
        if not object_manager.column_display:
            object_manager.column_display = ",".join(default_display_setting)
            object_manager.save()
    else:
        if custom_object:
            object_manager, _ = ObjectManager.objects.get_or_create(
                workspace=workspace,
                custom_object=custom_object,
                page_group_type=TYPE_OBJECT_CUSTOM_OBJECT,
            )
        else:
            object_manager, _ = ObjectManager.objects.get_or_create(
                workspace=workspace, page_group_type=page_group_type
            )
        object_manager.column_display = ",".join(default_display_setting)
        object_manager.save()

    context["object_manager"] = object_manager
    context["object_display_options"] = object_display_options
    context["field_id"] = field_id  # not being used
    return context


def get_prefix_rel(source, dest):
    prefix = ""

    ### ORDER OBJECT RELATION ============================================================
    if source == TYPE_OBJECT_ORDER:
        if dest == TYPE_OBJECT_INVOICE:
            prefix = "invoice__"
        if dest == TYPE_OBJECT_COMPANY:
            prefix = "company__"
        if dest == TYPE_OBJECT_CONTACT:
            prefix = "contact__"

    ### INVOICE OBJECT RELATION ==========================================================
    if source == TYPE_OBJECT_INVOICE:
        if dest == TYPE_OBJECT_ORDER:
            prefix = "shopturboorders__"
        if dest == TYPE_OBJECT_SUBSCRIPTION:
            prefix = "subscriptions__"
        if dest == TYPE_OBJECT_COMPANY:
            prefix = "company__"
        if dest == TYPE_OBJECT_CONTACT:
            prefix = "contact__"

    ### CASES LINE ITEM OBJECT RELATION ==================================================
    if source == TYPE_OBJECT_CASE_LINE_ITEM:
        if dest == TYPE_OBJECT_CASE:
            prefix = "deal__"
        if dest == TYPE_OBJECT_ITEM:
            prefix = "item__"

    ### CASES OBJECT RELATION =============================================================
    if source == TYPE_OBJECT_CASE:
        if dest == TYPE_OBJECT_CASE_LINE_ITEM:
            prefix = "dealsitems__"

    if source == TYPE_OBJECT_ITEM and dest == TYPE_OBJECT_ORDER:
        prefix = "shopturbo_item_orders__"
    if source == TYPE_OBJECT_ORDER and dest == TYPE_OBJECT_ITEM:
        prefix = "shopturboitemsorders__item__"
    if source == TYPE_OBJECT_ORDER and dest == TYPE_OBJECT_INVENTORY_TRANSACTION:
        prefix = "inventory_transactions__"
    if (
        source == TYPE_OBJECT_COMPANY or source == TYPE_OBJECT_CONTACT
    ) and dest == TYPE_OBJECT_INVOICE:
        prefix = "invoice__"
    if (
        source == TYPE_OBJECT_COMPANY or source == TYPE_OBJECT_CONTACT
    ) and dest == TYPE_OBJECT_ORDER:
        prefix = "shopturboorders__"
    if (
        source == TYPE_OBJECT_COMPANY or source == TYPE_OBJECT_CONTACT
    ) and dest == TYPE_OBJECT_ESTIMATE:
        prefix = "estimate__"
    if source == TYPE_OBJECT_ITEM and dest == TYPE_OBJECT_ORDER_LINE_ITEM:
        prefix = "shopturbo_item_orders__"
    if source == TYPE_OBJECT_ITEM and dest == TYPE_OBJECT_ORDER:
        prefix = "shopturbo_item_orders__order__"

    return prefix
