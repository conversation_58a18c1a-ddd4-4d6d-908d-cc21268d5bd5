import base64
import uuid
from io import BytesIO

import pyotp
import qrcode
from django.conf import settings
from django.contrib import messages
from django.contrib.auth import authenticate
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.core.mail import send_mail
from django.core.validators import validate_email
from django.core.exceptions import ValidationError
from django.utils.encoding import force_bytes, force_str
from django.http import HttpResponse
from django.shortcuts import redirect, render
from django_hosts.resolvers import reverse
from django.utils import translation
from django.template.loader import render_to_string
from PIL import Image

from data.constants.properties_constant import *
from data.home import redirect_language_post
from data.models import *
from sanka.settings import (AWS_LOCATION, AWS_STORAGE_BUCKET_NAME, BASE_DIR,
                            S3_CLIENT)
from utils.decorator import login_or_hubspot_required
from utils.logger import logger
from utils.utility import (get_workspace)


@login_or_hubspot_required
def account(request):
    lang = request.LANGUAGE_CODE
    if lang == 'ja':
        page_title = 'アカウント設定'
    else:
        page_title = 'Account Settings'
    user = request.user
    page_group_type = 'workspace_account'
    workspace = get_workspace(request.user)
    if workspace:
        if workspace.subscription_status == 'preactive':
            page_cat = 'no_sidebar'
        else:
            page_cat = None
    else:
        page_cat = None

    verification = Verification.objects.get(user=request.user)
    available_timezone = dict(Workspace.timezone.field.choices)

    email_changed = False
    param = ""

    if request.method == 'POST':

        type = request.POST.get('type')

        if type == 'password':

            current_password = request.POST.get('current_password')

            user = authenticate(email=request.user.email,
                                password=current_password)

            if user is not None:
                logger.info('authenticated')
                confirm_password = request.POST.get('confirm_password')
                new_password = request.POST.get('new_password')

                if confirm_password != new_password:
                    if lang == 'ja':
                        Notification.objects.create(workspace=get_workspace(
                            request.user), user=request.user, message='確認パスコードが一致しません', type='error')
                    else:
                        Notification.objects.create(workspace=get_workspace(
                            request.user), user=request.user, message='Your confirmation passcode does not match', type='error')
                    return redirect('account')

                if current_password == new_password:
                    if lang == 'ja':
                        Notification.objects.create(workspace=get_workspace(
                            request.user), user=request.user, message='現在のパスコードを変更するには、別の新しいパスコードを入力してください', type='error')
                    else:
                        Notification.objects.create(workspace=get_workspace(
                            request.user), user=request.user, message='Please enter different new passcode to change your current passcode', type='error')
                    return redirect('account')

                user.set_password(new_password)
                user.save()
                
                if user.usermanagement_set.first().type == UserManagement.RoleType.VIEW_ONLY:
                    user_management = user.usermanagement_set.first()
                    user_management.set_view_only_password(raw_password=new_password)
                    user_management.save()
                    
                # Send Email
                # ======= EMAIL Notifications changing password =============#
                if lang == 'ja':
                    mail_subject = 'Sanka パスワード変更通知'
                    message = render_to_string(
                        'data/email/pass-changed-ja.html', {'fullname': request.user.first_name, })
                else:
                    mail_subject = 'Sanka Password Changed'
                    message = render_to_string(
                        'data/email/pass-changed.html', {'fullname': request.user.first_name, })

                from_email = 'Sanka <<EMAIL>>'
                to_email = [request.user.email]
                send_mail(mail_subject, message, from_email,
                          to_email, fail_silently=False)

                if lang == 'ja':
                    Notification.objects.create(workspace=get_workspace(
                        request.user), user=request.user, message='パスワードが変更されました。', type='success')
                else:
                    Notification.objects.create(workspace=get_workspace(
                        request.user), user=request.user, message='Account saved successfully.', type='success')
                return redirect('account')

            else:
                logger.info('not authenticated')
                if lang == 'ja':
                    Notification.objects.create(workspace=get_workspace(
                        request.user), user=request.user, message='パスワードの認証ができませんでした。再度入力してみてください。', type='success')
                else:
                    messages.warning(
                        request, "Password wasn't matched. Please try again.")
                return redirect('account')

        elif type == 'edit_account':

            selected_email = request.POST.get('input_email')
            if selected_email:
                selected_email = selected_email.strip()
            
            if not selected_email:
                if lang == 'ja':
                    Notification.objects.create(workspace=get_workspace(
                        request.user), user=request.user, message='メールアドレスを入力してください。', type='error')
                else:
                    Notification.objects.create(workspace=get_workspace(
                        request.user), user=request.user, message='Please enter an email address.', type='error')
                return redirect('account')
            
            # Validate email format
            try:
                validate_email(selected_email)
            except ValidationError:
                if lang == 'ja':
                    Notification.objects.create(workspace=get_workspace(
                        request.user), user=request.user, message='有効なメールアドレスを入力してください。', type='error')
                else:
                    Notification.objects.create(workspace=get_workspace(
                        request.user), user=request.user, message='Please enter a valid email address.', type='error')
                return redirect('account')

            if request.user.email != selected_email:
                email_changed = True

                email_exist = User.objects.filter(
                    email=selected_email).exists()
                if email_exist:
                    if lang == 'ja':
                        Notification.objects.create(workspace=get_workspace(
                            request.user), user=request.user, message='メールアドレスは既に使用されています。', type='error')
                    else:
                        Notification.objects.create(workspace=get_workspace(
                            request.user), user=request.user, message='Email already in use.', type='error')
                    param = "?email_exist="+str(email_exist)
                    return redirect(reverse('account', host='app')+param)
                else:
                    param = "?email_changed="+str(email_changed)

                    linkparam = f"user_id={request.user.id}#email_old={request.user.email}#email={selected_email}"
                    link = request.build_absolute_uri().replace(param, "")+"emailcallback/?emailparam=" + \
                        urlsafe_base64_encode(force_bytes(linkparam))

                    # Send Email
                    # ======= EMAIL Notifications changing password =============#
                    if lang == 'ja':
                        mail_subject = 'Sanka - メールアドレス変更確認'
                        message = render_to_string(
                            'data/email/email-changed-ja.html', {'fullname': request.user.first_name, 'link': link})
                    else:
                        mail_subject = 'Sanka - Email change confirmation'
                        message = render_to_string(
                            'data/email/email-changed.html', {'fullname': request.user.first_name, 'link': link})

                    from_email = 'Sanka <<EMAIL>>'
                    to_email = [selected_email]
                    send_mail(mail_subject, message, from_email,
                              to_email, fail_silently=False)

            # Full Name
            user.first_name = request.POST.get('fullname')
            user.save()

            # language
            selected_language_key = request.POST.get('language')
            verification.language = selected_language_key
            verification.save()

            translation.activate(selected_language_key)
            response = HttpResponse(...)
            response.set_cookie(
                settings.LANGUAGE_COOKIE_NAME, selected_language_key)
            Notification.objects.create(workspace=get_workspace(
                request.user), user=request.user, message='Account saved successfully.', type='success')

            if lang != selected_language_key:
                logger.info('success')
                logger.info(selected_language_key)

                return redirect_language_post(request)
            else:
                return redirect(reverse('account', host='app')+param)
        elif "delete-profile-pic" in request.POST:
            if "nyc3.digitaloceanspaces.com" in verification.profile_photo.url:
                file_name = verification.profile_photo.url.split("/")[-1]
                folder = "profile-photos"
                file_name = f"{folder}/{file_name}"
                sc_file = verification.profile_photo
                S3_CLIENT.delete_object(
                    Bucket=AWS_STORAGE_BUCKET_NAME,  Key=f"{AWS_LOCATION}/{sc_file.file}")

            verification.profile_photo = None
            verification.save()

            if lang == 'ja':
                Notification.objects.create(workspace=get_workspace(
                    request.user), user=request.user, message="プロフィール写真が正常に削除されました", type="success")
            else:
                Notification.objects.create(workspace=get_workspace(
                    request.user), user=request.user, message='Profile Picture Deleted successfully.', type="success")

                # return redirect(reverse('load_object_page', host = 'app', kwargs = { 'module_slug': module_slug, 'object_slug': module_object_slug }))
            return redirect('account')

        elif "change-profile-image" in request.POST:
            # Delete OLD
            verification.profile_photo = None

            # Change New
            # File [Image]
            files = request.FILES.getlist('file-change-profile-pic')
            for file in files:
                file.name = str(uuid.uuid4()) + '.' + file.name.split('.')[-1]
                verification.profile_photo = file
                verification.save()
                break

            if lang == 'ja':
                Notification.objects.create(workspace=get_workspace(
                    request.user), user=request.user, message="プロフィール写真は正常に更新されました。", type="success")
            else:
                Notification.objects.create(workspace=get_workspace(
                    request.user), user=request.user, message='Profile Photo Updated successfully.', type="success")

            return redirect('account')

    menu_key = 'account'
    page_group_type = 'account'

    context = {
        'page_title': page_title,
        'user': user,
        'email_changed': email_changed,
        'verification': verification,
        'available_timezone': available_timezone,
        'page_group_type': page_group_type,
        'workspace': workspace,
        'page_cat': page_cat,
        'menu_key': menu_key,

    }
    return render(request, 'data/account/account_template.html', context)


@login_or_hubspot_required
def security(request):
    page_title = 'Workspace Security'
    workspace = get_workspace(request.user)
    page_group_type = 'workspace_security'
    lang = request.LANGUAGE_CODE

    menu_key = 'account'
    page_group_type = 'security'
    messages = []

    if request.method == 'POST':
        if request.POST.get('type') == 'disable':
            two_factor_authenticator = TwoFactorAuthenticator.objects.filter(
                user=request.user).first()
            two_factor_authenticator.delete()
            return redirect('security')
        else:
            twoFA = True
            key = request.POST.get('key', '').replace(' ', '')
            code = request.POST.get('code', '')
            qr_image = request.POST.get('qr_image', '')

            status = pyotp.TOTP(key).verify(code)

            if status:
                two_factor_authenticator = TwoFactorAuthenticator.objects.get_or_create(
                    user=request.user, secret=key)
                # messages= [{'message': 'Your two factor authenticator has been enabled successfully.' if lang == 'en' else '2FAが正常に有効になりました。', 'tags': 'success'}]
                if lang == 'en':
                    Notification.objects.create(workspace=get_workspace(
                        request.user), user=request.user, message='Your two factor authenticator has been enabled successfully.', type='success')
                else:
                    Notification.objects.create(workspace=get_workspace(
                        request.user), user=request.user, message='2FAが正常に有効になりました。', type='success')
                return redirect('security')
            else:
                context = {
                    'page_title': page_title,
                    'workspace': workspace,
                    'page_group_type': page_group_type,
                    'menu_key': menu_key,
                    'twoFactorAuth': twoFA,
                    'qr_image': qr_image,
                    'key': ' '.join([key[i:i+4] for i in range(0, len(key), 4)]),
                    'account': 'Sanka:'+request.user.email,
                    'messages': [{'message': 'Your two factor authenticator code is not wrong. Please try again.' if lang == 'en' else '2FAコードが間違っています。再度入力してみてください。', 'tags': 'error'}]
                }

                return render(request, 'data/account/security.html', context)

    two_factor_authenticator = TwoFactorAuthenticator.objects.filter(
        user=request.user)
    if two_factor_authenticator:
        twoFA = False
        qr_image = None
        separated_key = None
        generated_key = None
        account = None
    else:
        twoFA = True
        generated_key = pyotp.random_base32()

        # Create QRCode 2fa using totp
        totp = pyotp.TOTP(generated_key).provisioning_uri(
            name=request.user.email, issuer_name='Sanka')
        qr = qrcode.make(totp)
        if not isinstance(qr, Image.Image):  # Ensure it's a PIL Image
            qr = qr.convert("RGB")

        canvas = Image.new('RGB', qr.size, 'white')

        # Use a proper 4-item box if needed, e.g., (x, y, x+width, y+height)
        canvas.paste(qr, (0, 0))

        # Save the image to a buffer
        buffer = BytesIO()
        canvas.save(buffer, 'PNG')

        # Encode the image to base64
        qr_image = base64.b64encode(buffer.getvalue()).decode(
            'utf-8').replace('\n', '')

        buffer.close()

        # Loop generated key and separate the code with space every 4 characters
        separated_key = ' '.join([generated_key[i:i+4]
                                 for i in range(0, len(generated_key), 4)])
        account = 'Sanka:'+request.user.email

    context = {
        'page_title': page_title,
        'workspace': workspace,
        'page_group_type': page_group_type,
        'menu_key': menu_key,
        'twoFactorAuth': twoFA,
        'qr_image': qr_image,
        'key': separated_key,
        'account': account,
        'messages': messages
    }

    return render(request, 'data/account/security.html', context)


@login_or_hubspot_required
def invitation(request):
    page_title = 'Workspace Invitations'
    workspace = get_workspace(request.user)
    page_group_type = 'workspace_invitation'
    lang = request.LANGUAGE_CODE

    invited_workspaces = Invitation.objects.filter(
        email=request.user.email, status='invited')

    # create workspace
    if request.method == 'POST':

        if 'accept_invite' in request.POST:
            logger.info('accepting')

            code = request.POST.get('code')
            invitation = Invitation.objects.filter(
                code=code, email=request.user.email).order_by('-date_sent').first()

            if invitation:
                invitation.status = 'accepted'
                invitation.user = request.user
                invitation.save()

                invitation_workspace = invitation.workspace
                invitation_workspace.user.add(request.user)
                invitation_workspace.save()

                role = UserManagement.RoleType.STAFF
                if invitation.role:
                    role = invitation.role

                UserManagement.objects.create(
                    workspace=invitation_workspace, type=role, user=request.user, permission=invitation.permission)

                Notification.objects.create(
                    workspace=invitation_workspace, user=request.user, message='Invitation accepted.', type='success')

                verification = Verification.objects.get(user=request.user)
                verification.save()

                invitation_workspace.user.add(request.user)

                if lang == 'ja':
                    messages.success(
                        request, 'ワークスペースがアカウントに正常に追加されました。 ワークスペースのリストを確認してください')
                else:
                    messages.success(
                        request, 'Workspace is successfully added to your account. Please, check on your workspace List')

                return redirect('invitation')

            else:
                messages.warning(request, 'Invitation code not found.')

        elif 'decline_invite' in request.POST:
            logger.info('declining')
            workspace_id = request.POST.get('workspace_id')
            try:
                workspace = Workspace.objects.get(id=workspace_id)
                invitation = Invitation.objects.get(
                    workspace=workspace, email=request.user.email)

                logger.info(f'Declining invitation for email {request.user.email} in workspace {workspace.id}')
                invitation.delete()
                Notification.objects.create(workspace=get_workspace(
                    request.user), user=request.user, message='Invitation declined.', type='success')
                return redirect('invitation')

            except (Workspace.DoesNotExist, Invitation.DoesNotExist) as e:
                logger.warning(f'Failed to decline invitation: {str(e)} - workspace_id: {workspace_id}, user: {request.user.email}')
                messages.warning(request, 'Invitation not found.')

    menu_key = 'account'
    page_group_type = 'invitation'

    context = {
        'page_title': page_title,
        'invited_workspaces': invited_workspaces,
        'workspace': workspace,
        'page_group_type': page_group_type,
        'menu_key': menu_key,
    }

    return render(request, 'data/account/invitations.html', context)


@login_or_hubspot_required
def delete_invitation(request, id):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)

    try:
        invitation = Invitation.objects.get(id=id)
        logger.info(f'Deleting invitation {id} for email {invitation.email} by user {request.user.email} in workspace {workspace.id}')
        invitation.delete()

        if lang == 'ja':
            Notification.objects.create(workspace=workspace,
                user=request.user, message='招待は削除されました。', type='success')
        else:
            Notification.objects.create(workspace=workspace,
                user=request.user, message='Invitation was deleted.', type='success')
    except Invitation.DoesNotExist:
        logger.warning(f'Attempted to delete non-existent invitation {id} by user {request.user.email} in workspace {workspace.id}')
        if lang == 'ja':
            Notification.objects.create(workspace=workspace,
                user=request.user, message='招待が見つかりませんでした。', type='error')
        else:
            Notification.objects.create(workspace=workspace,
                user=request.user, message='Invitation not found.', type='error')

    return redirect('workspace')


@login_or_hubspot_required
def delete_account(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    if workspace:
        workspace.user.remove(request.user)
        workspace.save()

    user = request.user
    user.delete()

    if lang == 'ja':
        messages.success(request, 'アカウントが正常に削除されました。')
    else:
        messages.success(request, 'Your account was deleted successfully.')

    return redirect('login')


@login_or_hubspot_required
def download_app(request):
    platform = request.GET.get('platform', False)
    download_url = {
        'mac': str(BASE_DIR) + '/sanka-desktop/build/Sanka_mac_arm64_0.1.0.dmg',
        'mac-intel': str(BASE_DIR) + '/sanka-desktop/build/Sanka_mac_x64_0.1.0.dmg',
        'windows': str(BASE_DIR) + '/sanka-desktop/build/Sanka_win_x64_0.1.0.exe',
    }

    if not platform or platform not in download_url:
        context = {
            'page_group_type': 'download',
            'menu_key': 'workspace',
        }
        return render(request, 'data/account/workspace/workspace_template.html', context)

    try:
        with open(download_url[platform], 'rb') as f:
            file_data = f.read()
    except IOError:
        # handle file not exist case here
        redirect(reverse('download_app', host='app'))

    # sending response
    response = HttpResponse(file_data, content_type='application/octet-stream')
    filename = download_url[platform].split('/')[-1]
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    return response


def emailcallback(request):
    """Handle email change confirmation callback"""
    lang = request.LANGUAGE_CODE
    emailparam = request.GET.get('emailparam')
    
    if not emailparam:
        messages.error(request, 'Invalid email confirmation link.' if lang == 'en' else '無効なメール確認リンクです。')
        return redirect('account')
    
    try:
        # Decode the parameter
        decoded_param = force_str(urlsafe_base64_decode(emailparam))
        params = {}
        for param in decoded_param.split('#'):
            if '=' in param:
                key, value = param.split('=', 1)
                params[key] = value
        
        user_id = params.get('user_id')
        email_old = params.get('email_old')
        email_new = params.get('email')
        
        if not all([user_id, email_old, email_new]):
            raise ValueError("Missing required parameters")
        
        # Verify the user
        user = User.objects.get(id=user_id, email=email_old)
        
        # Check if the new email is still available
        if User.objects.filter(email=email_new).exclude(id=user_id).exists():
            if lang == 'ja':
                Notification.objects.create(
                    workspace=get_workspace(user), 
                    user=user, 
                    message='メールアドレスは既に使用されています。', 
                    type='error'
                )
            else:
                Notification.objects.create(
                    workspace=get_workspace(user), 
                    user=user, 
                    message='Email already in use.', 
                    type='error'
                )
            return redirect('account')
        
        # Update the email
        user.email = email_new
        user.save()
        
        # Send confirmation email to the old email address
        if lang == 'ja':
            mail_subject = 'Sanka - メールアドレスが変更されました'
            message = f'''
こんにちは {user.first_name}様、

あなたのSankaアカウントのメールアドレスが正常に変更されました。
新しいメールアドレス: {email_new}

この変更に心当たりがない場合は、すぐにサポートまでご連絡ください。

Sankaチーム
'''
            notification_msg = 'メールアドレスが正常に変更されました。'
        else:
            mail_subject = 'Sanka - Email address changed'
            message = f'''
Dear {user.first_name},

Your Sanka account email address has been successfully changed.
New email address: {email_new}

If you did not make this change, please contact support immediately.

Best regards,
Sanka Team
'''
            notification_msg = 'Email address changed successfully.'
        
        from_email = 'Sanka <<EMAIL>>'
        to_email = [email_old]  # Send to old email for security
        send_mail(mail_subject, message, from_email, to_email, fail_silently=False)
        
        Notification.objects.create(
            workspace=get_workspace(user), 
            user=user, 
            message=notification_msg, 
            type='success'
        )
        
        # If the current user is the one who changed email, log them out for security
        if request.user.id == int(user_id):
            messages.success(
                request, 
                'Email changed successfully. Please login with your new email.' if lang == 'en' else 'メールアドレスが変更されました。新しいメールアドレスでログインしてください。'
            )
            from django.contrib.auth import logout
            logout(request)
            return redirect('login')
        
        return redirect('account')
        
    except (User.DoesNotExist, ValueError, Exception) as e:
        logger.error(f"Email callback error: {str(e)}")
        messages.error(
            request, 
            'Invalid or expired email confirmation link.' if lang == 'en' else '無効または期限切れのメール確認リンクです。'
        )
        return redirect('account' if request.user.is_authenticated else 'login')
