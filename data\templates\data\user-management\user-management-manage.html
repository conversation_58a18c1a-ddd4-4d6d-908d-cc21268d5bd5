{% load tz %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% get_current_language as LANGUAGE_CODE %}

<style>
    /* Tooltip container */
    .hover-tooltip {
        position: relative;
        display: inline-block;
    }

    /* Tooltip text */
    .hover-tooltip .hover-tooltip-text,
    .hover-tooltip .hover-tooltip-text-no-access {
        visibility: hidden;
        width: 100px;
        background-color: #555;
        color: #fff;
        text-align: center;
        padding: 3px 0;
        border-radius: 8px;

        /* Position the hover-tooltip text */
        position: absolute;
        z-index: 1001;
        top: 50%;
        transform: translateY(-50%);

        /* Fade in hover-tooltip */
        opacity: 0;
        transition: opacity 0.5s;
    }

    .hover-tooltip .hover-tooltip-text {
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .hover-tooltip .hover-tooltip-text-no-access {
        right: 100%; /* position right outside the container */
        margin-left: 8px; /* small gap */
        transform: translate(0, -50%);
    }
    .hover-tooltip:hover .hover-tooltip-text,
    .hover-tooltip:hover .hover-tooltip-text-no-access {
        visibility: visible;
        opacity: 0.9;
    }
</style>

<div class="{% include "data/utility/drawer-header.html" %}">
    <div class="card-header" id="kt_help_header">
        <h5 class="{% include "data/utility/card-header.html" %}">
            {% if LANGUAGE_CODE == 'ja'%}
            ユーザーを管理
            {% else %}
            Manage
            {% endif %}
        </h5>
        <div class="card-toolbar">
            <div class="d-flex align-items-center">
                
                <!-- Close Button (X) -->
                <button class="btn btn-icon btn-sm ms-2" data-kt-drawer-dismiss="true" type="button">
                    <span class="svg-icon svg-icon-2x">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="currentColor"></rect>
                            <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="currentColor"></rect>
                        </svg>
                    </span>
                </button>
                
                <script>
                    KTMenu.createInstances()
                </script>
                
            </div>
        </div>
    </div>

    {% if permission == 'hide' %}
        {% include 'data/static/no-access-information.html' %}
    {% else %}

    {% if user_management %}
        <div class="{% include "data/utility/drawer-height.html" %}">
            <div class="mb-10 px-10 p-5 pb-20 col-md-8">
                <div class="pb-0">
                    <div class="fv-rowd-flex flex-column">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            ID
                        </label>
                        <div class="mb-2">
                            <span class="fw-bolder" >{% if user_management.id_user %}{{user_management.id_user|stringformat:"04d"}}{% endif %}</span>        
                        </div>
                    </div>
                </div>
        
                <form id='user-management-manage-form' method="POST" action="{% host_url 'user_management_manage' host 'app' %}" onsubmit="return validateForm(event)" enctype="multipart/form-data">
                    {% csrf_token %}
                    <div class="card-body px-0">
                        {% if view_id %}
                            <input type="hidden" name="view_id" value="{{view_id}}" />
                        {% endif %}

                        <input hidden name="update_user_management" value="">
                        <input hidden name="user_management_id" value="{{user_management.id}}">
                        <input hidden name="page" value="{{page}}">
                        {% if from_page_object == constant.TYPE_OBJECT_WORKER %}
                        <input hidden name="source_url" class="source-url-input" value=""> 
                        {% endif %}

                        {% for column in default_columns %}
                            {% if column == 'name' %}
                                <div class="mb-5">
                                    <label class="{% include 'data/utility/form-label.html' %}">
                                        <span>
                                            {% if LANGUAGE_CODE == 'ja' %}
                                            名前
                                            {% else %}
                                            Name
                                            {% endif %}
                                        </span>
                                    </label>
                                    {% if user_management.user %}
                                        {% if user_management.user.verification.profile_photo %}
                                            <img class="w-20px rounded-circle me-2" src="{{user_management.user.verification.profile_photo.url}}" style="object-fit: cover !important;aspect-ratio: 16 / 16;" />
                                        {% else %}
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M6.28548 15.0861C7.34369 13.1814 9.35142 12 11.5304 12H12.4696C14.6486 12 16.6563 13.1814 17.7145 15.0861L19.3493 18.0287C20.0899 19.3618 19.1259 21 17.601 21H6.39903C4.87406 21 3.91012 19.3618 4.65071 18.0287L6.28548 15.0861Z" fill="currentColor"/>
                                                <rect opacity="0.3" x="8" y="3" width="8" height="8" rx="4" fill="currentColor"/>
                                            </svg>    
                                        {% endif %}
                                        <span >
                                            {{user_management.user.first_name}}
                                        </span> 
                                    {% else %}
                                        -
                                    {% endif %}
                                </div>
                            {% elif user_management.type == 'view_only' %}
                                {% if role_myself.user == user_management.user and role_myself.type == 'view_only' or role_myself.type == 'admin' %}
                                    {% if column == 'view_only_password' %}
                                        <div class="mb-5">
                                            <label class="{% include 'data/utility/form-label.html' %}">
                                                <span class="required">
                                                    {% if LANGUAGE_CODE == 'ja' %}
                                                    閲覧専用ユーザーパスワード
                                                    {% else %}
                                                    View Only User Password
                                                    {% endif %}
                                                </span>
                                            </label>
                                            <div class="d-flex">
                                                {% if role_myself.type == 'admin' %}
                                                <input required autocomplete="off" class="form-control" name="view_only_password"
                                                value="{{user_management.get_view_only_password}}"
                                                {% if LANGUAGE_CODE == 'ja' %}
                                                placeholder="閲覧専用ユーザーパスワード"
                                                {% else %}
                                                placeholder="View-Only User Password"
                                                {% endif %}/>
                                                {% elif role_myself.type == 'view_only' %}
                                                <span>{{user_management.get_view_only_password}}</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% elif column == 'email' %}
                                        <div class="mb-5">
                                            <label class="{% include 'data/utility/form-label.html' %}">
                                                <span>
                                                    {% if LANGUAGE_CODE == 'ja' %}
                                                    メールアドレス
                                                    {% else %}
                                                    Email Address
                                                    {% endif %}
                                                </span>
                                            </label>
                                            <span>
                                                {{user_management.user.email}}
                                            </span>
                                        </div>
                                    {% endif %}
                                {% endif %}
                            {% endif %}
                        {% endfor %}
                        {% if from_page_object == constant.TYPE_OBJECT_WORKER %}
                        {% for CustomFieldName in user_management_properties %}    
                            {% if CustomFieldName %}
                                <div class="fv-rowd-flex flex-column mb-8">
                                    <div class="mb-4">
                                        <span class="{% include 'data/utility/form-label.html' %}">
                                            {{CustomFieldName.name}}
                                        </span>
                                        {% include 'data/common/custom_field/custom-property.html' with CustomFieldName=CustomFieldName obj=user_management object_type=object_type %}
                                    </div>
                                </div>
                            {% endif %}
                        {% endfor %}
                        {% endif %}
    
                        <!-- User permission settings -->
                        <div class="mb-5">
                            <div class="separator separator-dashed mb-5"></div>
                            
                            <h5 class="{% include 'data/utility/form-label.html' %}">
                            {% if LANGUAGE_CODE == 'ja'%}
                            権限を変更
                            {% else %}
                            Change Permissions
                            {% endif %}
                            </h5>
                            
                            <!-- Add permissions form content here -->
                            <div id="permission-settings">
                                <!-- Role change buttons for admins and support users -->
                                {% if role_myself.type == 'admin' or role_myself.type == 'support' or role_myself.type == 'partner' %}
                                    {% if user_management.type == 'admin' %}
                                        {% if user_management.user != role_myself.user or self_convert_staff %}
                                            <div class="mb-5">
                                                <button name="change_role" value="convert_to_staff" type="submit" class="btn btn-white rounded border border-1">
                                                    {% if LANGUAGE_CODE == 'ja'%}
                                                    スタッフに変更
                                                    {% else %}
                                                    Convert to Staff
                                                    {% endif %}
                                                </button>
                                            </div>
                                        {% else%}
                                            <div class="mb-5">
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                他のアドミンユーザーがいないため、スタッフに変更できません
                                                {% else %}
                                                Cannot convert to staff because there is no another admin
                                                {% endif %}
                                            </div>
                                        {% endif %}
                                    {% elif user_management.type == 'staff' %}
                                        <div class="mb-5">
                                            <button name="change_role" value="convert_to_admin" type="submit" class="btn btn-white rounded border border-1">
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                アドミンに変更
                                                {% else %}
                                                Convert to Admin
                                                {% endif %}
                                            </button>
                                        </div>
                                    {% elif user_management.type == 'support' %}
                                        {% if role_myself.type == 'support' %}
                                            <div class="mb-5">
                                                <button name="change_role" value="convert_to_admin" type="submit" class="btn btn-white rounded border border-1">
                                                    {% if LANGUAGE_CODE == 'ja'%}
                                                    アドミンに変更
                                                    {% else %}
                                                    Convert to Admin
                                                    {% endif %}
                                                </button>
                                            </div>
                                        {% else %}
                                            <div class="mb-5">
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                サポートユーザーの権限変更は、サポートユーザーのみが行えます
                                                {% else %}
                                                Only support users can change support user permissions
                                                {% endif %}
                                            </div>
                                        {% endif %}
                                    {% elif user_management.type == 'partner' %}
                                            <div class="mb-5">
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                パートナーユーザーの権限変更はSankaサポートまでお問い合わせください。
                                                {% else %}
                                                Please contact Sanka Support for managing partner permission.
                                                {% endif %}
                                            </div>
                                    {% endif %}
                                {% else %}
                                <div class="mb-5">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    アドミンまたはサポートではないため、権限の変更や更新はできません
                                    {% else %}
                                    You are not admin or support, you don't have permission to change role or update permission
                                    {% endif %}
                                </div>
                                {% endif %}

                                <!-- Import the permission tables from teammate.html -->
                                {% if role_myself.type == 'admin' and not admin_myself or role_myself.type == 'support' and not admin_myself or role_myself.type == 'partner' and not admin_myself %}
                                    {% if user_management.type == 'staff' or user_management.type == 'view_only' %}
                                        <input type="hidden" name="update_permissions" value="true">
                                        <label class="{% include 'data/utility/form-label.html' %} mt-5">
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            アクセスコントロール
                                            {% else %}
                                            Access Control
                                            {% endif %}
                                        </label>
                                        
                                        <table class="{% include "data/utility/table.html" %} px-5">
                                            <tbody>
                                                {% for group_type in group_types  %}
                                                    {% if not group_type|in_list:"timegenie,attendance,inventory,session_event,calendar,contact_lists,user_management,spendpocket,bill,user,worker_review,absence,jobs,jobs_applicant,conversation,campaigns,worker" %}
                                                    <tr>
                                                        <td >
                                                            <div id="{{group_type}}-permission-info-container" class="d-none"></div>

                                                            <div class="d-flex align-items-center">

                                                                <div class="accordion-header d-flex collapsed cursor-pointer w-100" data-bs-toggle="collapse" data-bs-target="#related_{{group_type}}_1" >
                                                                    <span class="accordion-icon">
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-chevron-down accordion-icon-off" viewBox="0 0 16 16">
                                                                            <path fill-rule="evenodd" d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708"/>
                                                                        </svg>
                                                                    </span>
                                                                    <div class="d-flex flex-column">
                                                                        <div id="name-group-type-{{group_type}}" class="ms-4 mb-2">
                                                                            {{group_type|object_group_type:request}}
                                                                        </div>
                                                                        <div id="permission-description-{{group_type}}" class="d-flex w-80 flex-wrap gap-1 ms-4">
                                                                            {% permission_description permission_dict|get_attr:group_type group_type request as desc %}
                                                                            {{ desc|safe }}                                                                        
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <div class="form-check form-switch form-check-custom form-check-solid hover-tooltip">
                                                                    <input id="{{group_type}}-allow-access-checker"  name="allow-access-checker" class="form-check-input" type="checkbox" {% if permission_dict|get_attr:group_type == 'hide' %} checked {% endif %} onclick="toggleHideObject(this, '{{group_type}}')">
                                                                    <div class="tw-flex mx-3 tw-w-[24px] tw-fill-[#B5B0A1] tw-rounded-full tw-justify-center tw-items-center">
                                                                        <svg class="tw-mx-auto" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512">
                                                                            <path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2S-1.2 34.7 9.2 42.9l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L525.6 386.7c39.6-40.6 66.4-86.1 79.9-118.4c3.3-7.9 3.3-16.7 0-24.6c-14.9-35.7-46.2-87.7-93-131.1C465.5 68.8 400.8 32 320 32c-68.2 0-125 26.3-169.3 60.8L38.8 5.1zM223.1 149.5C248.6 126.2 282.7 112 320 112c79.5 0 144 64.5 144 144c0 24.9-6.3 48.3-17.4 68.7L408 294.5c8.4-19.3 10.6-41.4 4.8-63.3c-11.1-41.5-47.8-69.4-88.6-71.1c-5.8-.2-9.2 6.1-7.4 11.7c2.1 6.4 3.3 13.2 3.3 20.3c0 10.2-2.4 19.8-6.6 28.3l-90.3-70.8zM373 389.9c-16.4 6.5-34.3 10.1-53 10.1c-79.5 0-144-64.5-144-144c0-6.9 .5-13.6 1.4-20.2L83.1 161.5C60.3 191.2 44 220.8 34.5 243.7c-3.3 7.9-3.3 16.7 0 24.6c14.9 35.7 46.2 87.7 93 131.1C174.5 443.2 239.2 480 320 480c47.8 0 89.9-12.9 126.2-32.5L373 389.9z"/>
                                                                        </svg>
                                                                    </div>
                                                                    <span class="hover-tooltip-text-no-access">{% if LANGUAGE_CODE == 'ja' %}アクセスなし{% else %}No Access{% endif %}
                                                                    </span>
                                                                </div>
                                                            </div>

                                                            <div id="related_{{group_type}}_1" class="collapse fs-6 mx-5" data-bs-parent="#related_{{group_type}}">
                                                                <div class="separator separator-dashed mt-3"></div>
                                                                <table class="{% include "data/utility/table.html" %} px-10">
                                                                    <thead class="{% include "data/utility/table-header.html" %}">
                                                                        <tr class="align-middle">
                                                                            <th class="tw-w-2/6"></th>
                                                                            <th class="tw-w-1/6" >
                                                                                <div class="tw-flex">
                                                                                    <div class="tw-flex tw-mx-auto tw-w-[24px] tw-fill-[#B5B0A1] tw-rounded-full tw-justify-center tw-items-center">
                                                                                        <svg class="tw-mx-auto" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512">
                                                                                            <path d="M288 32c-80.8 0-145.5 36.8-192.6 80.6C48.6 156 17.3 208 2.5 243.7c-3.3 7.9-3.3 16.7 0 24.6C17.3 304 48.6 356 95.4 399.4C142.5 443.2 207.2 480 288 480s145.5-36.8 192.6-80.6c46.8-43.5 78.1-95.4 93-131.1c3.3-7.9 3.3-16.7 0-24.6c-14.9-35.7-46.2-87.7-93-131.1C433.5 68.8 368.8 32 288 32zM144 256a144 144 0 1 1 288 0 144 144 0 1 1 -288 0zm144-64c0 35.3-28.7 64-64 64c-7.1 0-13.9-1.2-20.3-3.3c-5.5-1.8-11.9 1.6-11.7 7.4c.3 6.9 1.3 13.8 3.2 20.7c13.7 51.2 66.4 81.6 117.6 67.9s81.6-66.4 67.9-117.6c-11.1-41.5-47.8-69.4-88.6-71.1c-5.8-.2-9.2 6.1-7.4 11.7c2.1 6.4 3.3 13.2 3.3 20.3z"/>
                                                                                        </svg>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="d-flex tw-justify-center">{% if LANGUAGE_CODE == 'ja'%}アクセス{% else %}Access{% endif %}</div>
                                                                            </th>
                                                                            {% if user_management.user.usermanagement_set.first.type != 'view_only' %}
                                                                                <th class="tw-w-1/6">
                                                                                    <div class="tw-flex">
                                                                                        <div class="tw-flex tw-mx-auto tw-w-[20px] tw-fill-[#B5B0A1] tw-rounded-full tw-justify-center tw-items-center">
                                                                                            <svg class="tw-mx-auto" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                                                                                <path d="M471.6 21.7c-21.9-21.9-57.3-21.9-79.2 0L362.3 51.7l97.9 97.9 30.1-30.1c21.9-21.9 21.9-57.3 0-79.2L471.6 21.7zm-299.2 220c-6.1 6.1-10.8 13.6-13.5 21.9l-29.6 88.8c-2.9 8.6-.6 18.1 5.8 24.6s15.9 8.7 24.6 5.8l88.8-29.6c8.2-2.7 15.7-7.4 21.9-13.5L437.7 172.3 339.7 74.3 172.4 241.7zM96 64C43 64 0 107 0 160L0 416c0 53 43 96 96 96l256 0c53 0 96-43 96-96l0-96c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 96c0 17.7-14.3 32-32 32L96 448c-17.7 0-32-14.3-32-32l0-256c0-17.7 14.3-32 32-32l96 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L96 64z"/>
                                                                                            </svg>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="d-flex tw-justify-center">{% if LANGUAGE_CODE == 'ja'%}編集{% else %}Edit{% endif %}</div>
                                                                                </th>
                                                                                <th class="tw-w-1/6">
                                                                                    <div class="tw-flex">
                                                                                        <div class="tw-flex tw-mx-auto tw-w-[20px] tw-fill-[#B5B0A1] tw-rounded-full tw-justify-center tw-items-center">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                                                                                <path d="M32 32l448 0c17.7 0 32 14.3 32 32l0 32c0 17.7-14.3 32-32 32L32 128C14.3 128 0 113.7 0 96L0 64C0 46.3 14.3 32 32 32zm0 128l448 0 0 256c0 35.3-28.7 64-64 64L96 480c-35.3 0-64-28.7-64-64l0-256zm128 80c0 8.8 7.2 16 16 16l160 0c8.8 0 16-7.2 16-16s-7.2-16-16-16l-160 0c-8.8 0-16 7.2-16 16z"/>
                                                                                            </svg>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="d-flex tw-justify-center">{% if LANGUAGE_CODE == 'ja'%}アーカイブ{% else %}Archive{% endif %}</div>
                                                                                </th>
                                                                            {% endif %}
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody class="fs-6">
                                                                        <tr class='row-1'>
                                                                            <td style="width: 30%;">
                                                                            <div id="title-all-{{group_type}}" class="mb-1">
                                                                                {% if LANGUAGE_CODE == 'ja' %}
                                                                                    すべての{{group_type|object_group_type:request}}
                                                                                {% else %}
                                                                                    All {{group_type|object_group_type:request}}
                                                                                {% endif %}
                                                                            </div>
                                                                            </td>
                                                                            <td>
                                                                                <div class="tw-flex tw-justify-center tw-items-center">
                                                                                    <input id="group_type_{{group_type}}" class="tw-mx-auto form-check-input type_read" onchange="onChangeTypePermission('all', 'read','{{group_type}}')" type="radio" name="{{group_type}}_access" value="read" for="group_type_{{group_type}}" {% if permission_dict|get_attr:group_type|check_access:'read' %} checked {% endif %}>
                                                                                </div>
                                                                            </td>
                                                                            {% if user_management.user.usermanagement_set.first.type != 'view_only' %}
                                                                            <td>
                                                                                <div class="tw-flex tw-justify-center tw-items-center">
                                                                                    <input id="group_type_{{group_type}}" class="tw-mx-auto form-check-input type_edit" onchange="onChangeTypePermission('all', 'edit','{{group_type}}')" type="radio" name="{{group_type}}_edit" value="edit" for="group_type_{{group_type}}" {% if permission_dict|get_attr:group_type|check_access:'edit' %} checked {% endif %}>
                                                                                </div>
                                                                            </td>
                                                                            <td>
                                                                                <div class="tw-flex tw-justify-center tw-items-center">
                                                                                    <input id="group_type_{{group_type}}" class="tw-mx-auto form-check-input type_archive" onchange="onChangeTypePermission('all', 'archive','{{group_type}}')" type="radio" name="{{group_type}}_archive" value="archive" for="group_type_{{group_type}}" {% if permission_dict|get_attr:group_type|check_access:'archive' %} checked {% endif %}>
                                                                                </div>
                                                                            </td>
                                                                            {% endif %}
                                                                        </tr>
                                                                        <tr class="row-2">
                                                                            <td>
                                                                            <div id="title-team-{{group_type}}" class="mb-1">
                                                                                {% if LANGUAGE_CODE == 'ja' %}
                                                                                    {{group_type|object_group_type:request}} チームが所有
                                                                                {% else %}
                                                                                    {{group_type|object_group_type:request}} team owns
                                                                                {% endif %}
                                                                            </div>
                                                                            </td>
                                                                            <td>
                                                                                <div class="tw-flex tw-justify-center tw-items-center">
                                                                                    <input id="group_type_{{group_type}}" class="tw-mx-auto form-check-input type_read" onchange="onChangeTypePermission('team', 'read','{{group_type}}')" type="radio" name="{{group_type}}_access" value="read" for="group_type_{{group_type}}" {% if permission_dict|get_attr:group_type|check_access:'team_read' %} checked {% endif %}>
                                                                                </div>
                                                                            </td>
                                                                            {% if user_management.user.usermanagement_set.first.type != 'view_only' %}
                                                                            <td>
                                                                                <div class="tw-flex tw-justify-center tw-items-center">
                                                                                    <input id="group_type_{{group_type}}" class="tw-mx-auto form-check-input type_edit" onchange="onChangeTypePermission('team', 'edit','{{group_type}}')" type="radio" name="{{group_type}}_edit" value="edit" for="group_type_{{group_type}}" {% if permission_dict|get_attr:group_type|check_access:'team_edit' %} checked {% endif %}>
                                                                                </div>
                                                                            </td>
                                                                            <td>
                                                                                <div class="tw-flex tw-justify-center tw-items-center">
                                                                                    <input id="group_type_{{group_type}}" class="tw-mx-auto form-check-input type_archive" onchange="onChangeTypePermission('team', 'archive','{{group_type}}')" type="radio" name="{{group_type}}_archive" value="archive" for="group_type_{{group_type}}" {% if permission_dict|get_attr:group_type|check_access:'team_archive' %} checked {% endif %}>
                                                                                </div>
                                                                            </td>
                                                                            {% endif %}
                                                                        </tr>
                                                                        <tr class="row-3">
                                                                            <td>
                                                                            <div id="title-user-{{group_type}}" class="mb-1">
                                                                                {% if LANGUAGE_CODE == 'ja' %}
                                                                                    {{group_type|object_group_type:request}} ユーザー所有
                                                                                {% else %}
                                                                                    {{group_type|object_group_type:request}} user owns
                                                                                {% endif %}
                                                                            </div>
                                                                            </td>
                                                                            
                                                                            <td>
                                                                                <div class="tw-flex tw-justify-center tw-items-center">
                                                                                    <input id="group_type_{{group_type}}" class="tw-mx-auto form-check-input type_read" onchange="onChangeTypePermission('user', 'read','{{group_type}}')" type="radio" name="{{group_type}}_access" value="read" for="group_type_{{group_type}}" {% if permission_dict|get_attr:group_type|check_access:'user_read' %} checked {% endif %}>
                                                                                </div>
                                                                            </td>
                                                                            {% if user_management.user.usermanagement_set.first.type != 'view_only' %}
                                                                            <td>
                                                                                <div class="tw-flex tw-justify-center tw-items-center">
                                                                                    <input id="group_type_{{group_type}}" class="tw-mx-auto form-check-input type_edit" onchange="onChangeTypePermission('user', 'edit','{{group_type}}')" type="radio" name="{{group_type}}_edit" value="edit" for="group_type_{{group_type}}" {% if permission_dict|get_attr:group_type|check_access:'user_edit' %} checked {% endif %}>
                                                                                </div>
                                                                            </td>
                                                                            <td>
                                                                                <div class="tw-flex tw-justify-center tw-items-center">
                                                                                    <input id="group_type_{{group_type}}" class="tw-mx-auto form-check-input type_archive" onchange="onChangeTypePermission('user', 'archive','{{group_type}}')"  type="radio" name="{{group_type}}_archive" value="archive" for="group_type_{{group_type}}" {% if permission_dict|get_attr:group_type|check_access:'user_archive' %} checked {% endif %}>
                                                                                </div>
                                                                            </td>
                                                                            {% endif %}
                                                                        </tr>
                                                                        <tr class="row-4">
                                                                            <td>
                                                                            <div class="mb-1">
                                                                                {% if LANGUAGE_CODE == 'ja' %}
                                                                                    なし
                                                                                {% else %}
                                                                                    None
                                                                                {% endif %}
                                                                            </div>
                                                                            </td>
                                                                            
                                                                            <!-- Access doesn't have "None" radio button -->
                                                                            <td>
                                                                                <div class="tw-flex tw-justify-center tw-items-center">
                                                                                    <input id="group_type_{{group_type}}" class="tw-mx-auto form-check-input type_read d-none" onchange="onChangeTypePermission('none', 'read','{{group_type}}')" type="radio" name="{{group_type}}_access" value="none" for="group_type_{{group_type}}">
                                                                                </div>
                                                                            </td>
                                                                            {% if user_management.user.usermanagement_set.first.type != 'view_only' %}
                                                                            <td>
                                                                                <div class="tw-flex tw-justify-center tw-items-center">
                                                                                    <input id="group_type_{{group_type}}" class="tw-mx-auto form-check-input type_edit" onchange="onChangeTypePermission('none', 'edit','{{group_type}}')" type="radio" name="{{group_type}}_edit" value="none" for="group_type_{{group_type}}" {% if permission_dict|get_attr:group_type|check_access:'none_edit' %} checked {% endif %}>
                                                                                </div>
                                                                            </td>
                                                                            <td>
                                                                                <div class="tw-flex tw-justify-center tw-items-center">
                                                                                    <input id="group_type_{{group_type}}" class="tw-mx-auto form-check-input type_archive" onchange="onChangeTypePermission('none', 'archive','{{group_type}}')"  type="radio" name="{{group_type}}_archive" value="none" for="group_type_{{group_type}}" {% if permission_dict|get_attr:group_type|check_access:'none_archive' %} checked {% endif %}>
                                                                                </div>
                                                                            </td>
                                                                            {% endif %}
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    {% endif %}
                                                {% endfor %}
                                            </tbody>
                                        </table>

                                        <div class="separator separator-dashed"></div>
                                        
                                        <table class="{% include "data/utility/table.html" %} px-5">
                                            <tbody>
                                                {% for setting_type in system_setting_type  %}
                                                    <tr>
                                                        <td >
                                                            <div id="{{setting_type}}-permission-info-container" class="d-none"></div>

                                                            <div class="d-flex align-items-center">

                                                                <div class="w-100" >
                                                                    <div class="d-flex flex-column">
                                                                        <div id="name-group-type-{{setting_type}}" class="ms-4 mb-2">
                                                                            {{setting_type|system_setting_type:request}}
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <div class="form-check form-switch form-check-custom form-check-solid hover-tooltip">
                                                                    <input id="{{setting_type}}-allow-access-checker"  name="allow-access-checker" class="form-check-input" type="checkbox" {% if permission_dict|get_attr:setting_type == 'hide' %} checked {% endif %} onclick="toggleHideObject(this, '{{setting_type}}')">
                                                                    <div class="tw-flex mx-3 tw-w-[24px] tw-fill-[#B5B0A1] tw-rounded-full tw-justify-center tw-items-center">
                                                                        <svg class="tw-mx-auto" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512">
                                                                            <path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2S-1.2 34.7 9.2 42.9l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L525.6 386.7c39.6-40.6 66.4-86.1 79.9-118.4c3.3-7.9 3.3-16.7 0-24.6c-14.9-35.7-46.2-87.7-93-131.1C465.5 68.8 400.8 32 320 32c-68.2 0-125 26.3-169.3 60.8L38.8 5.1zM223.1 149.5C248.6 126.2 282.7 112 320 112c79.5 0 144 64.5 144 144c0 24.9-6.3 48.3-17.4 68.7L408 294.5c8.4-19.3 10.6-41.4 4.8-63.3c-11.1-41.5-47.8-69.4-88.6-71.1c-5.8-.2-9.2 6.1-7.4 11.7c2.1 6.4 3.3 13.2 3.3 20.3c0 10.2-2.4 19.8-6.6 28.3l-90.3-70.8zM373 389.9c-16.4 6.5-34.3 10.1-53 10.1c-79.5 0-144-64.5-144-144c0-6.9 .5-13.6 1.4-20.2L83.1 161.5C60.3 191.2 44 220.8 34.5 243.7c-3.3 7.9-3.3 16.7 0 24.6c14.9 35.7 46.2 87.7 93 131.1C174.5 443.2 239.2 480 320 480c47.8 0 89.9-12.9 126.2-32.5L373 389.9z"/>
                                                                        </svg>
                                                                    </div>
                                                                    <span class="hover-tooltip-text-no-access">{% if LANGUAGE_CODE == 'ja' %}アクセスなし{% else %}No Access{% endif %}
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>

                                        <div class="separator separator-dashed"></div>

                                        <!-- Additional permission tables would go here -->
                                    {% endif %}
                                {% endif %}
                            </div>
                        </div>
    
                        <div class="d-flex submit-button-container">
                            <button name='update-items' type="submit" class="btn btn-dark">
                                {% if LANGUAGE_CODE == 'ja'%}
                                更新
                                {% else %}
                                Update
                                {% endif %}
                            </button>
                            {% if role_myself.type == 'admin' or role_myself.type == 'support' %}
                                {% if user_management.type == 'admin' %}
                                {% else %}
                                    {% if user_management.user != role_myself.user %}
                                        <button name='remove_user_from_workspace' type="submit" class="btn btn-danger ms-5">
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            ワークスペースから削除
                                            {% else %}
                                            Remove from Workspace
                                            {% endif %}
                                        </button>
                                    {% endif %}
                                {% endif %}
                            {% endif %}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    {% else %}
        <div class="card-body mt-0 mb-4">
            <div class="fv-rowd-flex flex-column">
                <h3>
                    {% if LANGUAGE_CODE == 'ja'%}
                    該当するユーザーがありません。もう一度お試しください。
                    {% else %}
                    The user wasn't found. Please try again.
                    {% endif %}
                </h3>
            </div>
        </div>
    {% endif %}

    {% endif %}
</div>
{{ permission_dict|json_script:"permissionDictData" }}

{% block js %}
    {% include 'data/property/property-validate-form-script.html' %}
    {% include 'data/user-management/user-management-javascript.html' %}
    <script>
        if (typeof $ !== 'undefined') {
            $(document).ready(function() {
                $('.emojize-this').emojioneArea();
                $('.select2-this').select2();
                $('.source-url-input').val(window.location.href)
            });
        }
        function handleCreateBtnClick() {
            const button = document.getElementById('create-obj-btn');
            button.disabled = true;
            document.getElementById('create_object_form').submit()
        }
        
        function select_all_type(type){
            if (type == 'read'){
                document.querySelectorAll('.type_read').forEach(function(element) {
                    element.checked = true;
                });
            }
            else if (type == 'edit'){
                document.querySelectorAll('.type_edit').forEach(function(element) {
                    element.checked = true;
                });
            }
            else if (type == 'archive'){
                document.querySelectorAll('.type_archive').forEach(function(element) {
                    element.checked = true;
                });
            }
            else if (type == 'hide'){
                document.querySelectorAll('.type_hide').forEach(function(element) {
                    element.checked = true;
                });
            }
        }

    </script>
{% endblock %}


<style>
    .submit-button-container {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        background-color: white;
        padding: 15px;
        border-top: 1px solid #eee;
        z-index: 100;
        margin-left: 0;
        margin-right: 0;
        margin-bottom: 0;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
    }
    
    /* Add padding to the form to prevent the submit button from overlapping content */
    #user_management_form {
        padding-bottom: 20px;
        position: relative;
    }
</style>