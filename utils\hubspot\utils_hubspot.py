from uuid import uuid4
from hubspot import HubSpot
from hubspot.crm.objects import ApiException, SimplePublicObjectInputForCreate
from hubspot.crm import products, deals as hs_deals, line_items, associations, companies
from hubspot.crm.deals import ApiException as DealsApiException
from hubspot.crm.line_items import ApiException as LineItemsApiException
from hubspot.crm.products import ApiException as ProductsApiException
from hubspot.crm.associations import ApiException as AssociationsApiException
from hubspot.crm.associations import BatchInputPublicObjectId
from hubspot.crm.properties import PropertyCreate, PropertyUpdate, ApiException

from hubspot.crm.deals.models import (
    SimplePublicObjectInputForCreate as SimplePublicObjectInputForCreateDeals,
)
from hubspot.crm.contacts import ApiException as ContactsApiException
from hubspot.crm.contacts.models import (
    SimplePublicObjectInput as ContactInput,
    PublicObjectSearchRequest,
    Filter,
    FilterGroup,
)
from hubspot.crm.companies import ApiException as CompaniesApiException
from hubspot.crm.companies.models import SimplePublicObjectInput as CompanyInput

# Import necessary models for V4 batch associations
from hubspot.crm.associations.v4.models import (
    BatchInputPublicAssociationMultiPost,
    PublicDefaultAssociation,
)

from data.models import *
from utils.logger import logger
from utils.meter import *
from utils.utility import chunks_dict, chunks_list, get_workspace
from collections import defaultdict

import requests
from sanka.settings import HUBSPOT_INTERNAL_ACCESS_TOKEN

import hmac
import hashlib
import base64
import time
from contextlib import nullcontext


def decode_uri(uri):
    """
    Decode the specific URL-encoded characters in the request URI.
    """
    encoded_values = {
        "%3A": ":",
        "%2F": "/",
        "%3F": "?",
        "%40": "@",
        "%21": "!",
        "%24": "$",
        "%27": "'",
        "%28": "(",
        "%29": ")",
        "%2A": "*",
        "%2C": ",",
        "%3B": ";",
    }

    for encoded, decoded in encoded_values.items():
        uri = uri.replace(encoded, decoded)
    return uri


def validate_hubspot_request(request, app_secret):
    """
    Validates the HubSpot v3 signature for a Django request.

    Parameters:
        - request: The Django request object
        - app_secret: Your app's client secret for generating the HMAC
    """
    # Extract relevant headers
    hubspot_signature = request.headers.get("X-HubSpot-Signature-v3")
    timestamp = request.headers.get("X-HubSpot-Request-Timestamp")

    if not hubspot_signature or not timestamp:
        return False  # Missing required headers

    # Check if the request is older than 5 minutes
    current_time = int(time.time()) * 1000
    if abs(current_time - int(timestamp)) > 300000:
        return False  # Request is too old

    # Decode the request URI
    decoded_uri = decode_uri(
        request.build_absolute_uri().replace("http://", "https://")
    )
    # Get the request body as a string
    request_body = request.body.decode("utf-8")

    # Create the concatenated string
    string_to_hash = f"{request.method}{decoded_uri}{request_body}{timestamp}".encode(
        "utf-8"
    )

    # Create the HMAC SHA-256 hash
    hmac_hash = hmac.new(
        app_secret.encode("utf-8"), string_to_hash, hashlib.sha256
    ).digest()

    # Base64 encode the result
    calculated_signature = base64.b64encode(hmac_hash).decode("utf-8")

    # Use constant-time comparison to prevent timing attacks
    return hmac.compare_digest(calculated_signature, hubspot_signature)


def get_custom_object(access_token, object_name):
    response = requests.get(
        f"https://api.hubapi.com/crm/v3/schemas/{object_name}",
        headers={
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        },
    )

    if response.ok:
        response_data = response.json()
        return True, response_data
    else:
        return (
            False,
            f"Failed get the data. Error {response.status_code},{response.text}",
        )


def update_batch_records(
    access_token, object_name, inputs, is_update=False, error_logger=None
):
    with (
        error_logger.function_context("update_batch_records")
        if error_logger
        else nullcontext()
    ):
        if error_logger:
            error_logger.log_info(
                "update_batch_records",
                f"Updating {len(inputs)} records for object {object_name}",
            )

        url = f"https://api.hubapi.com/crm/v3/objects/{object_name}/batch/upsert"
        if is_update:
            url = f"https://api.hubapi.com/crm/v3/objects/{object_name}/batch/update"

        try:
            response = requests.post(
                url,
                headers={
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json",
                },
                json={"inputs": inputs},
            )
        except Exception as e:
            error_msg = f"Network error during batch update: {str(e)}"
            if error_logger:
                error_logger.log_network_error("update_batch_records", error_msg, e)
            return False, error_msg

        if response.ok:
            try:
                response_data = response.json()
            except Exception as e:
                error_msg = f"Failed to parse response JSON: {str(e)}"
                if error_logger:
                    error_logger.log_api_error("update_batch_records", error_msg, e)
                return False, error_msg

            successful_records = []
            failed_records = []

            if response_data.get("results", None):
                for result in response_data.get("results"):
                    if "id" in result:
                        successful_records.append(result)
                    else:
                        failed_records.append(result)
                # Log success and failure counts
                print(f"Successfully update {len(successful_records)} records.")
                print(f"Failed to update {len(failed_records)} records.")

                if error_logger:
                    error_logger.log_info(
                        "update_batch_records",
                        f"Successfully updated {len(successful_records)} records, failed {len(failed_records)} records",
                    )
                    if failed_records:
                        error_logger.log_warning(
                            "update_batch_records",
                            f"Failed to update {len(failed_records)} records",
                        )

            return True, successful_records
        else:
            error_msg = f"Failed update data to hubspot. Error {response.status_code},{response.text}"
            if error_logger:
                error_logger.log_api_error("update_batch_records", error_msg)
            return False, error_msg


def create_batch_records(
    access_token, object_type, inputs, workspace=None, user=None, error_logger=None
):
    with (
        error_logger.function_context("create_batch_records")
        if error_logger
        else nullcontext()
    ):
        if error_logger:
            error_logger.log_info(
                "create_batch_records",
                f"Creating {len(inputs)} records for object {object_type}",
            )

        successful_records = []
        failed_records = []

        for chunk_index, chunk in enumerate(chunks_list(inputs, 100)):
            try:
                if error_logger:
                    error_logger.log_info(
                        "create_batch_records",
                        f"Processing chunk {chunk_index + 1} with {len(chunk)} records",
                    )

                try:
                    response = requests.post(
                        f"https://api.hubapi.com/crm/v3/objects/{object_type}/batch/create",
                        headers={
                            "Authorization": f"Bearer {access_token}",
                            "Content-Type": "application/json",
                        },
                        json={"inputs": chunk},
                    )
                except Exception as e:
                    error_msg = f"Network error during batch create for chunk {chunk_index + 1}: {str(e)}"
                    if error_logger:
                        error_logger.log_network_error(
                            "create_batch_records", error_msg, e
                        )
                    failed_records.extend(chunk)
                    continue

                try:
                    response_data = response.json()
                except Exception as e:
                    error_msg = f"Failed to parse response JSON for chunk {chunk_index + 1}: {str(e)}"
                    if error_logger:
                        error_logger.log_api_error("create_batch_records", error_msg, e)
                    failed_records.extend(chunk)
                    continue

                if response.status_code == 201:
                    results = response_data.get("results", [])
                    if results:
                        successful_records.extend(results)
                        if error_logger:
                            error_logger.log_info(
                                "create_batch_records",
                                f"Successfully created {len(results)} records in chunk {chunk_index + 1}",
                            )
                else:
                    error_msg = f"Error {response.status_code}: {response.text}"
                    if error_logger:
                        error_logger.log_api_error(
                            "create_batch_records",
                            f"Failed to create records in chunk {chunk_index + 1}: {error_msg}",
                        )
                    logger.error(error_msg)
                    failed_records.extend(chunk)
                    if workspace and user:
                        Notification.objects.create(
                            workspace=workspace,
                            user=user,
                            type="error",
                            message=f"Failed to create records in HubSpot: {response.status_code} - {response.text}",
                        )

            except Exception as e:
                error_msg = f"Unexpected error in chunk {chunk_index + 1}: {str(e)}"
                if error_logger:
                    error_logger.log_api_error("create_batch_records", error_msg, e)
                logger.error(
                    f"... ERROR === hubspot.py create_batch_records function: {e}"
                )
                failed_records.extend(chunk)
                if workspace and user:
                    Notification.objects.create(
                        workspace=workspace,
                        user=user,
                        type="error",
                        message=f"Failed to create records in HubSpot: {str(e)}",
                    )

        # Log success and failure counts
        print(f"Successfully created {len(successful_records)} records.")
        print(f"Failed to create {len(failed_records)} records.")

        if error_logger:
            error_logger.log_info(
                "create_batch_records",
                f"Completed batch creation: {len(successful_records)} successful, {len(failed_records)} failed",
            )

        return successful_records, failed_records


def push_hubspot_object(access_token, object_type, inputs, inputs_associations=[]):
    response = requests.post(
        f"https://api.hubapi.com/crm/v3/objects/{object_type}/batch/create",
        headers={
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        },
        json={"inputs": inputs},
    )
    response_data = response.json()
    successful_records = []
    failed_records = []

    for result in response_data.get("results", []):
        if "id" in result:
            successful_records.append(result)
        else:
            failed_records.append(result)

    # Log success and failure counts
    print(f"Successfully created {len(successful_records)} records.")
    print(f"Failed to create {len(failed_records)} records.")
    if not response.ok:
        return (
            False,
            f"Failed push data to hubspot. Error {response.status_code},{response.text}",
        )

    api_client = HubSpot(access_token=access_token)
    if len(inputs_associations) > 0:
        create_sanka_id_properties = [
            {
                "object_type": "contacts",
                "name": "sanka_id",
                "label": "Sanka ID",
                "type": "number",
                "field_type": "number",
                "group_name": "contactinformation",
            },
            {
                "object_type": "companies",
                "name": "sanka_id",
                "label": "Sanka ID",
                "type": "number",
                "field_type": "number",
                "group_name": "companyinformation",
            },
        ]

        for prop in create_sanka_id_properties:
            try:
                property_create = PropertyCreate(
                    name=prop["name"],
                    label=prop["label"],
                    type=prop["type"],
                    field_type=prop["field_type"],
                    group_name=prop["group_name"],
                    options=[],
                )
                api_client.crm.properties.core_api.create(
                    prop["object_type"], property_create
                )
                print(
                    f"Successfully created property '{prop['name']}' for {prop['object_type']}."
                )
            except ApiException as e:
                if e.status == 409:  # Conflict: Property already exists
                    print(
                        f"Property '{prop['name']}' for {prop['object_type']} already exists. Skipping."
                    )
                else:
                    print(
                        f"Failed to create property '{prop['name']}' for {prop['object_type']} due to error: {e.body}"
                    )
            except Exception as e:
                print(f"Unexpected error: {str(e)}")

        for input in inputs_associations:
            object_id = input["id"]

            object_type_line_item = "line_items"
            object_type_company = "companies"
            object_type_contact = "contacts"

            try:
                # Search for the object by custom property (subscriptions_id)
                search_filter = {
                    "filters": [
                        {"propertyName": "id", "operator": "EQ", "value": object_id}
                    ]
                }
                search_body = {
                    "filterGroups": [search_filter],
                    "properties": [
                        "hs_object_id"
                    ],  # Retrieve the internal ID if it exists
                }

                search_response = api_client.crm.objects.search_api.do_search(
                    object_type, search_body
                )

                if search_response.results:
                    object_id = search_response.results[0].id  # Get HubSpot internal ID
                else:
                    raise Exception("Record doesn't exist")

                # Step 1: Create the line items (or retrieve existing ones)
                line_item_ids = []
                print(input.get("items", False))
                if input.get("items", False):
                    for item in input["items"]:
                        line_item_properties = {
                            "name": item["name"],
                            "description": item["description"],
                            "hs_sku": item["hs_sku"],
                            "price": item["price"],
                            "quantity": item["quantity"],
                        }
                        line_item_object = {"properties": line_item_properties}

                        try:
                            # Create the line item (or check if it exists first)
                            line_item_response = (
                                api_client.crm.objects.basic_api.create(
                                    object_type_line_item, line_item_object
                                )
                            )
                            line_item_id = line_item_response.id
                            line_item_ids.append(line_item_id)
                            print(f"Line item created with ID: {line_item_id}")

                        except ApiException as e:
                            print(f"Exception when creating line item: {e}")

                # Step 2b: Create the company/contact (or check if it exists first)
                if input.get("company", False):
                    company_contact = input["company"]
                    print(company_contact)
                    company_contact_properties = {
                        "sanka_id": company_contact["sanka_id"],
                        "name": company_contact["name"],
                        "email": company_contact["email"],
                        "phone": company_contact["phone"],
                    }
                    contact_object = {"properties": company_contact_properties}

                    try:
                        # Search or create the contact (or company)
                        contact_search_filter = {
                            "filters": [
                                {
                                    "propertyName": "sanka_id",
                                    "operator": "EQ",
                                    "value": int(company_contact["sanka_id"]),
                                }
                            ]
                        }
                        search_body = {
                            "filterGroups": [contact_search_filter],
                            "properties": [
                                "hs_object_id"
                            ],  # Retrieve internal ID if exists
                        }
                        print(search_body)
                        contact_search_response = (
                            api_client.crm.objects.search_api.do_search(
                                "companies", search_body
                            )
                        )

                        if contact_search_response.results:
                            contact_id = contact_search_response.results[0].id
                            print(
                                f"Company with sanka_id {company_contact['sanka_id']} exists with ID: {contact_id}"
                            )
                        else:
                            # Create new company
                            contact_response = api_client.crm.objects.basic_api.create(
                                "companies", contact_object
                            )
                            contact_id = contact_response.id
                            print(f"Company created with ID: {contact_id}")
                            time.sleep(2)

                    except ApiException as e:
                        print(f"Exception when creating/searching company: {e}")
                if input.get("contact", False):
                    company_contact = input["contact"]
                    print(company_contact)
                    company_contact_properties = {
                        "sanka_id": company_contact["sanka_id"],
                        "firstname": company_contact["name"],
                        "email": company_contact["email"],
                        "phone": company_contact["phone"],
                    }
                    contact_object = {"properties": company_contact_properties}

                    try:
                        # Search or create the contact (or company)
                        contact_search_filter = {
                            "filters": [
                                {
                                    "propertyName": "sanka_id",
                                    "operator": "EQ",
                                    "value": company_contact["sanka_id"],
                                }
                            ]
                        }
                        search_body = {
                            "filterGroups": [contact_search_filter],
                            "properties": [
                                "hs_object_id"
                            ],  # Retrieve internal ID if exists
                        }
                        contact_search_response = (
                            api_client.crm.objects.search_api.do_search(
                                object_type_contact, search_body
                            )
                        )

                        if contact_search_response.results:
                            contact_id = contact_search_response.results[0].id
                            print(
                                f"Contact with sanka_id {company_contact['sanka_id']} exists with ID: {contact_id}"
                            )
                        else:
                            # Create new contact
                            contact_response = api_client.crm.objects.basic_api.create(
                                object_type_contact, contact_object
                            )
                            contact_id = contact_response.id
                            print(f"Contact created with ID: {contact_id}")
                            time.sleep(2)

                    except ApiException as e:
                        print(f"Exception when creating/searching contact: {e}")

                # Step 3b: Create associations between the subscription and company/contact
                try:
                    # Associate company/contact with the subscription
                    if input.get("company", False):
                        batch_input_public_default_association_multi_post = (
                            associations.BatchInputPublicObjectId(
                                inputs=[
                                    {
                                        "from": {
                                            "id": object_id,
                                        },
                                        "to": {
                                            "id": contact_id,
                                        },
                                    }
                                ]
                            )
                        )
                        api_client.crm.associations.v4.batch_api.create_default(
                            object_type,
                            object_type_company,
                            batch_input_public_default_association_multi_post=batch_input_public_default_association_multi_post,
                        )
                        print(
                            f"Associated company {contact_id} with subscription {object_id}"
                        )
                    if input.get("contact", False):
                        batch_input_public_default_association_multi_post = (
                            associations.BatchInputPublicObjectId(
                                inputs=[
                                    {
                                        "from": {
                                            "id": object_id,
                                        },
                                        "to": {
                                            "id": contact_id,
                                        },
                                    }
                                ]
                            )
                        )
                        api_client.crm.associations.v4.batch_api.create_default(
                            object_type,
                            object_type_contact,
                            batch_input_public_default_association_multi_post=batch_input_public_default_association_multi_post,
                        )
                        print(
                            f"Associated contact {contact_id} with subscription {object_id}"
                        )

                except ApiException as e:
                    print(f"Exception when associating objects: {e}")

            except ApiException as e:
                print(f"Exception when checking/creating subscription {object_id}: {e}")
    return True, "Successfully push to Hubspot"


def patch_property(access_token, object_name, property_name, input):
    response = requests.patch(
        f"https://api.hubapi.com/crm/v3/properties/{object_name}/{property_name}",
        headers={
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        },
        json=input,
    )

    if response.ok:
        print(f"Successfully patched {property_name} field")
    else:
        return (
            False,
            f"Failed to patch {property_name}, status code: {response.status_code}\n{response.text}",
        )

    return True, f"Successfully patched {property_name}"


def create_bulk_properties(access_token, object_name, inputs, error_logger=None):
    with (
        error_logger.function_context("create_bulk_properties")
        if error_logger
        else nullcontext()
    ):
        if error_logger:
            error_logger.log_info(
                "create_bulk_properties",
                f"Creating {len(inputs)} properties for object {object_name}",
            )

        try:
            response = requests.post(
                f"https://api.hubapi.com/crm/v3/properties/{object_name}/batch/create",
                headers={
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json",
                },
                json={"inputs": inputs},
            )
        except Exception as e:
            error_msg = f"Network error during bulk properties creation: {str(e)}"
            if error_logger:
                error_logger.log_network_error("create_bulk_properties", error_msg, e)
            return False, error_msg

        if response.ok:
            try:
                response_data = response.json()
            except Exception as e:
                error_msg = f"Failed to parse response JSON: {str(e)}"
                if error_logger:
                    error_logger.log_api_error("create_bulk_properties", error_msg, e)
                return False, error_msg

            failed_properties = []

            for error in response_data.get("errors", []):
                if "error" in error.get("status", False):
                    if "OBJECT_ALREADY_EXISTS" not in error.get("category", False):
                        property_name = error.get("context", {}).get("name", "unknown")
                        failed_properties.append(
                            {"property": property_name, "error": error}
                        )
                        if error_logger:
                            error_logger.log_api_error(
                                "create_bulk_properties",
                                f"Failed to create property {property_name}: {error}",
                            )

            if not failed_properties:
                print("All properties created successfully")
                if error_logger:
                    error_logger.log_info(
                        "create_bulk_properties",
                        f"Successfully created all {len(inputs)} properties",
                    )
            else:
                error_msg = f"Some properties failed to create: {failed_properties}"
                if error_logger:
                    error_logger.log_api_error("create_bulk_properties", error_msg)
                return False, error_msg
        else:
            error_msg = f"Failed to create properties, status code: {response.status_code}\n{response.text}"
            if error_logger:
                error_logger.log_api_error("create_bulk_properties", error_msg)
            return False, error_msg

        return True, "All properties created successfully"


def create_group_name_properties(access_token, object_name, label):
    property_group_name = None
    body = {
        "name": f"{object_name}_information",
        "label": f"{label.title()} Information",
        "displayOrder": -1,
        "hidden": False,
    }
    responseGroupName = requests.post(
        f"https://api.hubapi.com/crm/v3/properties/{object_name}/groups",
        headers={
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        },
        json=body,
    )

    if responseGroupName.status_code == 201:
        property_group_name = responseGroupName.json()["name"]
    elif responseGroupName.status_code == 409:
        property_group_name = f"{object_name}_information"
    else:
        return (
            False,
            f"error: {responseGroupName.status_code}, {responseGroupName.text}",
        )

    return True, property_group_name


def get_records_object(access_token, object_type_id, properties, error_logger=None):
    with (
        error_logger.function_context("get_records_object")
        if error_logger
        else nullcontext()
    ):
        if error_logger:
            error_logger.log_info(
                "get_records_object",
                f"Fetching records for object type {object_type_id}",
            )

        start_id = 0
        filters = [
            {"propertyName": "hs_object_id", "operator": "GT", "value": start_id}
        ]

        inputs = {
            "limit": 100,
            "after": "0",
            "properties": properties,
            "filterGroups": [{"filters": filters}],
            "sorts": [{"propertyName": "hs_object_id", "direction": "ASCENDING"}],
        }

        print("Initial input:", inputs)

        # Container for all records
        records = []
        request_count = 0

        # Starting the loop to fetch records
        while True:
            request_count += 1
            if error_logger and request_count > 100:  # Safety limit
                error_msg = (
                    f"Too many requests ({request_count}) while fetching records"
                )
                error_logger.log_warning("get_records_object", error_msg)
                break

            try:
                response = requests.post(
                    f"https://api.hubapi.com/crm/v3/objects/{object_type_id}/search",
                    headers={
                        "Authorization": f"Bearer {access_token}",
                        "Content-Type": "application/json",
                    },
                    json=inputs,
                )
            except Exception as e:
                error_msg = f"Network error during records fetch (request {request_count}): {str(e)}"
                if error_logger:
                    error_logger.log_network_error("get_records_object", error_msg, e)
                return False, error_msg

            if response.status_code == 200:
                try:
                    responseResults = response.json()
                    results = responseResults.get("results", [])
                    records.extend(results)

                    if results:
                        try:
                            last_hs_object_id = results[-1]["properties"].get(
                                "hs_object_id", start_id
                            )
                            filters[0]["value"] = str(last_hs_object_id)
                            inputs["filterGroups"][0]["filters"] = filters
                        except (KeyError, IndexError) as e:
                            error_msg = f"Error processing pagination data: {str(e)}"
                            if error_logger:
                                error_logger.log_api_error(
                                    "get_records_object", error_msg, e
                                )
                            break
                    else:
                        break

                except Exception as e:
                    error_msg = f"Error parsing response JSON: {str(e)}"
                    if error_logger:
                        error_logger.log_api_error("get_records_object", error_msg, e)
                    return False, error_msg
            else:
                error_msg = f"Error: {response.status_code} - {response.text}"
                if error_logger:
                    error_logger.log_api_error("get_records_object", error_msg)
                print(f"Error: {response.status_code} - {response.text}")
                return False, error_msg

        # Final output
        print("total fetched records ", len(records))
        if error_logger:
            error_logger.log_info(
                "get_records_object",
                f"Successfully fetched {len(records)} records in {request_count} requests",
            )
        return True, records


def get_schema_properties(access_token, object_type_id, requiredProperties):
    responseProperties = requests.get(
        f"https://api.hubapi.com/crm/v3/properties/{object_type_id}",
        headers={
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        },
    )
    fields = []
    if responseProperties.status_code == 200:
        for prop in responseProperties.json()["results"]:
            if not prop.get("hubspotDefined", False):
                fields.append(
                    {
                        "name": prop["name"],
                        "label": prop["label"],
                        "type": prop["type"],
                        "group_name": prop["groupName"],
                        "required": True
                        if prop["name"] in requiredProperties
                        else False,
                    }
                )
            # Include specific HubSpot-defined properties that are commonly used in exports
            elif prop["name"] in ["hubspot_owner_id", "closed_date"]:
                fields.append(
                    {
                        "name": prop["name"],
                        "label": prop["label"],
                        "type": prop["type"],
                        "group_name": prop["groupName"],
                        "required": True
                        if prop["name"] in requiredProperties
                        else False,
                    }
                )
        return True, fields
    else:
        return (
            False,
            f"Error: {responseProperties.status_code} - {responseProperties.text}",
        )


def get_association_types(access_token, from_object_type):
    url = f"https://api.hubapi.com/crm/v4/associations/{from_object_type}/types"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }
    response = requests.get(url, headers=headers)
    response.raise_for_status()
    print("Log get assoc", url, response.text)
    return response.json()


def get_schema(access_token):
    client = HubSpot(access_token=access_token)
    for _ in range(3):  # Sometimes succeeds on the first or second try
        try:
            api_response = client.crm.schemas.core_api.get_all()
            break
        except:
            api_response = None
    data = {}
    if not api_response:
        return data
    for schema in api_response.results:
        properties = []
        for prop in schema.properties:
            properties.append(prop.name)
        data[schema.object_type_id] = {
            "name": schema.name,
            "properties": properties,
            "labels": {
                "plural": schema.labels.plural,
                "singular": schema.labels.singular,
            },
        }

    return data


def get_schema_detail(access_token, object_type_id):
    client = HubSpot(access_token=access_token)
    api_response = client.crm.schemas.core_api.get_by_id(object_type_id)
    return api_response.associations


def get_schema_detail_required_properties(access_token, object_type_name):
    response = requests.get(
        f"https://api.hubapi.com/crm/v3/schemas/{object_type_name}",
        headers={
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        },
    )
    requiredProperties = []
    if response.status_code == 200:
        requiredProperties = response.json()["requiredProperties"]
        return True, requiredProperties
    else:
        return False, f"Error: {response.status_code} - {response.text}"


def get_schema_detail_by_object_type_id(channel_id, object_type_id):
    channel = Channel.objects.get(id=channel_id)
    client = HubSpot(access_token=channel.access_token)
    for _ in range(3):  # Sometimes succeeds on the first or second try
        try:
            api_response = client.crm.schemas.core_api.get_by_id(object_type_id)
            break
        except:
            pass
    return api_response


def get_object_detail(access_token, schema, object_type_id, object_id):
    client = HubSpot(access_token=access_token)
    api_response = client.crm.objects.basic_api.get_by_id(
        object_type=object_type_id,
        object_id=object_id,
        properties=schema[object_type_id]["properties"],
    )
    return api_response


def get_schema_objects(
    access_token, schema, object_type_id, orders_type_id, items_type_id
):
    client = HubSpot(access_token=access_token)
    api_response = client.crm.objects.get_all(
        object_type=object_type_id,
        properties=schema[object_type_id]["properties"],
        associations=["2-30981860", "2-30981340"],
    )
    parsed_data = {}

    for item in api_response:
        data = {"properties": item.properties}
        items = []
        orders = []

        for association in item.associations:
            if "items" in association:
                results = item.associations[association].results
                ids = [custom_item.id for custom_item in results]
                for custom_object in results:
                    custom_object_detail = get_object_detail(
                        access_token, schema, items_type_id, custom_object.id
                    )

                    item_data = {
                        "id": custom_object_detail.id,
                        "properties": custom_object_detail.properties,
                    }
                    items.append(item_data)

            elif "orders" in association:
                results = item.associations[association].results
                ids = [custom_item.id for custom_item in results]
                for custom_object in results:
                    custom_object_detail = get_object_detail(
                        access_token, schema, orders_type_id, custom_object.id
                    )

                    order_data = {
                        "id": custom_object_detail.id,
                        "properties": custom_object_detail.properties,
                    }
                    orders.append(order_data)
        data["items"] = items
        data["orders"] = orders
        parsed_data[item.id] = data

    return parsed_data


def update_order_object(access_token, object_type_id, object_id, data):
    client = HubSpot(access_token=access_token)
    properties = data
    properties_object = {"properties": properties}
    try:
        # Update the custom object
        api_response = client.crm.objects.basic_api.update(
            object_type_id, object_id, properties_object
        )
        print("Order object updated successfully:", api_response)
        return True
    except ApiException as e:
        print("Exception when updating custom object: %s\n" % e)
        return False


def generate_hubspot_order(
    channel_id,
    order_items_type_id,
    orders_type_id,
    items_type_id,
    name,
    contact_id,
    items,
    quantities,
    ratios,
):
    channel = Channel.objects.get(pk=channel_id)
    platform = channel.integration.slug
    access_token = channel.access_token
    schema = get_schema_detail(access_token, order_items_type_id)
    order_schema = get_schema_detail(access_token, orders_type_id)

    item_associations = [
        x
        for x in schema
        if x.from_object_type_id == order_items_type_id
        and x.to_object_type_id == items_type_id
    ]
    item_association = item_associations[-1]

    order_associations = [
        x
        for x in schema
        if x.from_object_type_id == order_items_type_id
        and x.to_object_type_id == orders_type_id
    ]
    order_association = order_associations[-1]

    contact_associations = [
        x
        for x in order_schema
        if x.from_object_type_id == orders_type_id and "contact" in x.name
    ]
    contact_association = contact_associations[-1]
    api_client = HubSpot(access_token=access_token)

    order_properties = {"name": name}
    order_input = SimplePublicObjectInputForCreate(properties=order_properties)

    order_response = api_client.crm.objects.basic_api.create(
        object_type=orders_type_id, simple_public_object_input_for_create=order_input
    )
    order_id = order_response.id
    print(f"Order created successfully with ID: {order_id}")

    api_client.crm.objects.associations_api.create(
        object_type=orders_type_id,
        object_id=order_id,
        to_object_id=contact_id,
        to_object_type="contacts",
        association_type=contact_association.id,
    )

    for index, item_id in enumerate(items):
        quantity = quantities[index]
        wholesale_ratio = ratios[index]
        # CREATE ORDER ITEM

        order_item_properties = {
            "name": f"{name}-{item_id}x{quantity} ({wholesale_ratio}%)",
            "quantity": quantity,
            "wholesale_ratio": int(wholesale_ratio) / 100,
        }
        order_item_input = SimplePublicObjectInputForCreate(
            properties=order_item_properties
        )
        order_item_response = api_client.crm.objects.basic_api.create(
            object_type=order_items_type_id,
            simple_public_object_input_for_create=order_item_input,
        )
        order_item_id = order_item_response.id
        print(f"Order Item created successfully with ID: {order_item_id}")

        # CREATE ORDER ASSOCIATION

        api_client.crm.objects.associations_api.create(
            object_type=order_items_type_id,
            object_id=order_item_id,
            to_object_type=orders_type_id,
            to_object_id=order_id,
            association_type=order_association.id,
        )

        # CREATE ITEM ASSOCIATION
        api_client.crm.objects.associations_api.create(
            object_type=order_items_type_id,
            object_id=order_item_id,
            to_object_type=items_type_id,
            to_object_id=item_id,
            association_type=item_association.id,
        )


def check_required_properties(access_token):
    client = HubSpot(access_token=access_token)

    # Define the required properties for each object type
    required_properties = {
        "contact": [
            {
                "name": "sanka_id",
                "label": "Sanka ID",
                "type": "string",
                "field_type": "text",
            }
        ],
        "company": [
            {
                "name": "sanka_id",
                "label": "Sanka ID",
                "type": "string",
                "field_type": "text",
            },
            {"name": "email", "label": "Email", "type": "string", "field_type": "text"},
        ],
        "deal": [
            {
                "name": "sanka_id",
                "label": "Sanka ID",
                "type": "string",
                "field_type": "text",
                "has_unique_value": True,
            },
            {
                "name": "platform",
                "label": "Platform",
                "type": "string",
                "field_type": "text",
            },
            {
                "name": "platform_id",
                "label": "Platform ID",
                "type": "string",
                "field_type": "text",
            },
        ],
        "ticket": [
            {
                "name": "sanka_id",
                "label": "Sanka ID",
                "type": "string",
                "field_type": "text",
                "has_unique_value": True,
            },
        ],
    }

    # Iterate through each object type and ensure the required properties exist
    for object_type, properties in required_properties.items():
        try:
            # Fetch existing properties for the object type
            properties_response = client.crm.properties.core_api.get_all(object_type)
            existing_properties = {
                prop.name: prop for prop in properties_response.results
            }

            for prop in properties:
                if prop["name"] in existing_properties:
                    # Update the property if it needs to be unique
                    if (
                        prop.get("has_unique_value")
                        and not existing_properties[prop["name"]].has_unique_value
                    ):
                        try:
                            property_update = PropertyUpdate(
                                has_unique_value=True,
                                description=f"{prop['label']} used for unique identification of {object_type}",
                            )
                            client.crm.properties.core_api.update(
                                object_type, prop["name"], property_update
                            )
                            print(
                                f"Updated {prop['name']} property to be a unique identifier for {object_type}"
                            )
                        except Exception as e:
                            print(
                                f"Error updating {prop['name']} property for {object_type}: {e}"
                            )
                else:
                    # Create the property if it doesn't exist
                    try:
                        property_create = PropertyCreate(
                            name=prop["name"],
                            label=prop["label"],
                            type=prop["type"],
                            field_type=prop["field_type"],
                            group_name=f"{object_type}information",
                            has_unique_value=prop.get("has_unique_value", False),
                            description=f"{prop['label']} used for {object_type}",
                        )
                        client.crm.properties.core_api.create(
                            object_type, property_create
                        )
                        print(f"Created {prop['name']} property for {object_type}")
                    except Exception as e:
                        print(
                            f"Error creating {prop['name']} property for {object_type}: {e}"
                        )
        except Exception as e:
            print(f"Error checking/creating properties for {object_type}: {e}")


def refresh_hubspot_token(channel_id, error_logger=None):
    with (
        error_logger.function_context("refresh_hubspot_token")
        if error_logger
        else nullcontext()
    ):
        try:
            if error_logger:
                error_logger.log_info(
                    "refresh_hubspot_token",
                    f"Starting token refresh for channel {channel_id}",
                )
            else:
                logger.info(f"Starting token refresh for channel {channel_id}")

            channel = Channel.objects.get(id=channel_id)

            CLIENT_ID = settings.HUBSPOT_CLIENT_ID
            REDIRECT_URI = settings.HUBSPOT_REDIRECT_URI
            CLIENT_SECRET = settings.HUBSPOT_CLIENT_SECRET

            refresh_token = channel.refresh_token

            if not refresh_token:
                error_msg = f"No refresh token found for channel {channel_id}"
                if error_logger:
                    error_logger.log_validation_error(
                        "refresh_hubspot_token", error_msg
                    )
                raise Exception(error_msg)

            # Define the form data
            form_data = {
                "grant_type": "refresh_token",
                "client_id": CLIENT_ID,
                "client_secret": CLIENT_SECRET,
                "redirect_uri": REDIRECT_URI,
                "refresh_token": refresh_token,  # Adjust depending on how `req.query` is structured
            }

            # Send the POST request
            try:
                response = requests.post(
                    "https://api.hubapi.com/oauth/v1/token", data=form_data
                )
            except Exception as e:
                error_msg = f"Network error during token refresh: {str(e)}"
                if error_logger:
                    error_logger.log_network_error(
                        "refresh_hubspot_token", error_msg, e
                    )
                print("Error: ", e)
                return False

            # Handle the response
            if response.status_code == 200:
                try:
                    token_info = response.json()
                    # Process the tokens as needed
                    access_token = token_info.get("access_token")
                    refresh_token = token_info.get("refresh_token")
                    expires_in = token_info.get("expires_in")

                    if not access_token:
                        error_msg = "No access token received from HubSpot"
                        if error_logger:
                            error_logger.log_api_error(
                                "refresh_hubspot_token", error_msg
                            )
                        raise Exception(error_msg)

                    print("access token: ", access_token)

                    try:
                        responseApp = requests.get(
                            f"https://api.hubapi.com/oauth/v1/access-tokens/{access_token}"
                        )
                    except Exception as e:
                        error_msg = (
                            f"Network error during access token validation: {str(e)}"
                        )
                        if error_logger:
                            error_logger.log_network_error(
                                "refresh_hubspot_token", error_msg, e
                            )
                        raise Exception(error_msg)

                    print(responseApp.json())
                    if responseApp.status_code == 200:
                        try:
                            response_data = responseApp.json()
                            hub_id = response_data["hub_id"]
                            app_id = response_data["app_id"]
                            hubspot_domain = response_data["hub_domain"]
                        except KeyError as e:
                            error_msg = f"Missing required field in access token response: {str(e)}"
                            if error_logger:
                                error_logger.log_api_error(
                                    "refresh_hubspot_token", error_msg, e
                                )
                            raise Exception(error_msg)
                    else:
                        error_msg = f"Failed to validate access token: {responseApp.status_code} - {responseApp.text}"
                        if error_logger:
                            error_logger.log_api_error(
                                "refresh_hubspot_token", error_msg
                            )
                        print("Error: ", responseApp.text)
                        raise Exception("Cannot get Access token")

                    try:
                        channel.access_token = access_token
                        channel.api_key = CLIENT_ID
                        channel.account_id = hub_id
                        channel.app_id = app_id
                        channel.access_token_expires_in = expires_in
                        channel.refresh_token = refresh_token
                        channel.updated_at = timezone.now()
                        channel.save()
                    except Exception as e:
                        error_msg = f"Failed to save channel data: {str(e)}"
                        if error_logger:
                            error_logger.log_database_error(
                                "refresh_hubspot_token", error_msg, e
                            )
                        raise Exception(error_msg)

                    print("Channel saved", channel)
                    if error_logger:
                        error_logger.log_info(
                            "refresh_hubspot_token",
                            f"Successfully refreshed token for channel {channel_id}",
                        )
                    else:
                        logger.info(
                            f"Successfully refreshed token for channel {channel_id}"
                        )
                    return channel.access_token
                except Exception as e:
                    error_msg = f"Error processing token response: {str(e)}"
                    if error_logger:
                        error_logger.log_api_error(
                            "refresh_hubspot_token", error_msg, e
                        )
                    raise
            else:
                error_msg = (
                    f"Token refresh failed: {response.status_code} - {response.text}"
                )
                if error_logger:
                    error_logger.log_api_error("refresh_hubspot_token", error_msg)
                print("Error:", response.status_code, response.text)
                raise Exception(f"Error: {response.status_code} - {response.text}")
        except Exception as e:
            error_msg = f"Token refresh failed: {str(e)}"
            if error_logger:
                error_logger.log_api_error("refresh_hubspot_token", error_msg, e)
            print("Error: ", e)
            return False


def check_validation_token_hubspot(access_token):
    # check validation for hubspot access token
    try:
        response = requests.get(
            "https://api.hubapi.com/crm/v3/objects/contacts",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
            },
        )
        if response.status_code == 200:
            print("valid hubspot token")
            return True, "Success"
        elif response.status_code == 401:
            raise Exception("Invalid access token")
        else:
            raise Exception(f"Error: {response.status_code} - {response.text}")
    except Exception as e:
        print("error ", e)
        if str(e) == "Invalid access token":
            return False, "Invalid access token"
        else:
            return False, str(e)


def create_batch_associations(
    channel_id: str,
    from_object_type_ids: list,
    to_object_type_id: str,
    fromObjectType: str,
    toObjectType: str,
):
    channel = Channel.objects.get(id=channel_id)
    access_token = refresh_hubspot_token(channel.id)

    inputs = []

    for from_object_type_id in from_object_type_ids:
        inputs.append(
            {
                "from": {
                    "id": from_object_type_id,
                },
                "to": {
                    "id": to_object_type_id,
                },
            }
        )

    results = []
    errors = []
    for chunk in chunks_list(inputs, 100):
        try:
            api_response = requests.post(
                f"https://api.hubapi.com/crm/v4/associations/{fromObjectType}/{toObjectType}/batch/associate/default",
                headers={
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json",
                },
                json={"inputs": chunk},
            )

            if api_response.status_code == 200:
                response_data = api_response.json()
                print(
                    "Successfully batch created associations:",
                    response_data.get("results", []),
                )
                results.append(api_response.status_code)
            else:
                logger.error(f"Error {api_response.status_code}: {api_response.text}")
                errors.append(api_response.status_code)

        except Exception as e:
            logger.error(
                f"... ERROR === hubspot.py create_batch_associations function: {e}"
            )
            errors.append(e)
            return [], errors

    print(f"Failed to create associations: {len(errors)}")
    print(f"Successfully created associations: {len(results)}")
    return results, errors


def get_hubspot_users(channel_id):
    channel = Channel.objects.get(id=channel_id)
    access_token = refresh_hubspot_token(channel.id)

    url = "https://api.hubapi.com/settings/v3/users"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        return response.json()
    else:
        return {
            "error": f"Failed to fetch users: {response.status_code}",
            "details": response.text,
        }


def create_hubspot_deal(
    email,
    fullname,
    phone_number,
    org_name,
    user,
    deal_name,
    deal_stage,
    description,
    type,
):
    try:
        access_token = HUBSPOT_INTERNAL_ACCESS_TOKEN
        if access_token:
            client = HubSpot(access_token=access_token)
            contact_id = None
            company_id = None
            deal_id = None

            # 1. Find or Create/Update Contact
            try:
                # Search by email
                email_filter = Filter(property_name="email", operator="EQ", value=email)
                search_request = PublicObjectSearchRequest(
                    filter_groups=[FilterGroup(filters=[email_filter])]
                )
                contact_search = client.crm.contacts.search_api.do_search(
                    public_object_search_request=search_request
                )

                # Refined name splitting logic
                names = fullname.strip().split() if fullname else []
                first_name = names[0] if names else ""
                last_name = " ".join(names[1:]) if len(names) > 1 else ""

                if contact_search.total > 0:
                    contact_id = contact_search.results[0].id
                    print(f"Found existing Hubspot contact for signup: {contact_id}")
                    # Update existing contact's name and phone
                    update_properties = {
                        "firstname": first_name,
                        "lastname": last_name,
                        "phone": phone_number,
                    }
                    contact_input = ContactInput(properties=update_properties)
                    client.crm.contacts.basic_api.update(
                        contact_id=contact_id, simple_public_object_input=contact_input
                    )
                    print(
                        f"Updated existing Hubspot contact {contact_id} with signup info."
                    )
                else:
                    # Create new contact
                    print(
                        f"Split name for Hubspot: firstname='{first_name}', lastname='{last_name}' from fullname='{fullname}'"
                    )
                    contact_properties = {
                        "email": email,
                        "firstname": first_name,
                        "lastname": last_name,
                        "phone": phone_number,
                    }
                    contact_input = ContactInput(properties=contact_properties)
                    new_contact = client.crm.contacts.basic_api.create(
                        simple_public_object_input_for_create=contact_input
                    )
                    contact_id = new_contact.id
                    print(f"Created new Hubspot contact for signup: {contact_id}")
            except ContactsApiException as e:
                raise Exception(e)
            except Exception as e:
                raise Exception(e)

            # 2. Find or Create/Update Company
            domain = None
            # Define likely personal email domains (could be moved to settings/constants)
            personal_email_domains = [
                "aol.com",
                "yahoo.com",
                "online.nl",
                "live.com",
                "shaw.ca",
                "hotmail.",
                "gmail.com",
                "mail.com",
                "verizon.net",
                ".site",
                "mail.ru",
                "registry.godaddy",
                "gmx.com",
                "mailopenz.com",
                ".ru",
                "com.br",
            ]
            is_business_email = True
            if "@" in email:
                email_domain_part = email.split("@")[1]
                for personal_domain in personal_email_domains:
                    if personal_domain in email_domain_part:
                        is_business_email = False
                        break
                if is_business_email:
                    domain = email_domain_part

            if domain:
                try:
                    # Search company by domain
                    domain_filter = Filter(
                        property_name="domain", operator="EQ", value=domain
                    )
                    search_request = PublicObjectSearchRequest(
                        filter_groups=[FilterGroup(filters=[domain_filter])]
                    )
                    company_search = client.crm.companies.search_api.do_search(
                        public_object_search_request=search_request
                    )

                    if company_search.total > 0:
                        company_id = company_search.results[0].id
                        print(
                            f"Found existing Hubspot company for signup: {company_id} for domain {domain}"
                        )
                        # Update existing company's name (if org_name provided)
                        if org_name:
                            update_properties = {"name": org_name}
                            company_input = CompanyInput(properties=update_properties)
                            client.crm.companies.basic_api.update(
                                company_id=company_id,
                                simple_public_object_input=company_input,
                            )
                            print(
                                f"Updated existing Hubspot company {company_id} name to '{org_name}'."
                            )
                    elif org_name:  # Only create if org_name is provided
                        # Create new company
                        company_properties = {
                            "name": org_name,
                            "domain": domain,
                            # "website": org_url if org_url else '' # org_url not available here
                        }
                        company_input = CompanyInput(properties=company_properties)
                        new_company = client.crm.companies.basic_api.create(
                            simple_public_object_input_for_create=company_input
                        )
                        company_id = new_company.id
                        print(f"Created new Hubspot company for signup: {company_id}")
                except CompaniesApiException as e:
                    raise Exception(e)
                except Exception as e:
                    raise Exception(e)
            else:
                print(
                    "Could not determine domain to find/create Hubspot company during signup."
                )

            # 3. Create Deal
            try:
                # Generate uuid to ensure unique sanka_id
                uuid = uuid4()
                if type == "signup":
                    sanka_id = f"user-{str(user.id)}--{str(uuid)}"
                elif type == "lead":
                    sanka_id = f"lead-{str(user.id)}--{str(uuid)}"
                else:
                    sanka_id = f"{type}-{str(uuid)}"

                current_date = datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ")
                deal_properties = {
                    "dealname": deal_name,
                    "dealstage": deal_stage,
                    "amount": "0",
                    "closedate": current_date,
                    "description": description,
                    "sanka_id": sanka_id,
                }
                deal_input = SimplePublicObjectInputForCreateDeals(
                    properties=deal_properties
                )
                deal = client.crm.deals.basic_api.create(
                    simple_public_object_input_for_create=deal_input
                )
                deal_id = deal.id
                print(f"Created Hubspot deal with ID: {deal_id} for new user {user.id}")
            except DealsApiException as e:
                raise Exception(e)
            except Exception as e:
                raise Exception(e)

            # 4. Create Associations using V4 Batch API (Separate calls per type)
            def run_association_batch(
                from_object_type, to_object_type, association_pairs
            ):
                """Helper function to run a batch association create call."""
                if not association_pairs:
                    print(
                        f"No associations to create for {from_object_type} -> {to_object_type} (signup)"
                    )
                    return

                batch_inputs = [
                    PublicDefaultAssociation(_from=pair[0], to=pair[1])
                    for pair in association_pairs
                ]
                batch_association_input = BatchInputPublicAssociationMultiPost(
                    inputs=batch_inputs
                )

                try:
                    print(
                        f"Attempting {from_object_type} -> {to_object_type} association (signup): {batch_inputs}"
                    )
                    api_response = client.crm.associations.v4.batch_api.create_default(
                        from_object_type=from_object_type,
                        to_object_type=to_object_type,
                        batch_input_public_default_association_multi_post=batch_association_input,
                    )
                    print(
                        f"Batch API response ({from_object_type}->{to_object_type}, signup): {api_response.status}"
                    )
                    if (
                        hasattr(api_response, "status")
                        and api_response.status != "COMPLETE"
                    ):
                        print(f"  Details (signup): {api_response.results}")
                    else:
                        print(
                            f"  {from_object_type} -> {to_object_type} associations created (signup)."
                        )
                except AssociationsApiException as e:
                    raise Exception(e)
                except Exception as e:
                    raise Exception(e)

            # Prepare association pairs
            contact_to_company_pairs = []
            contact_to_deal_pairs = []
            company_to_deal_pairs = []

            if contact_id and company_id:
                contact_to_company_pairs.append((contact_id, company_id))

            if contact_id and deal_id:
                contact_to_deal_pairs.append((contact_id, deal_id))

            if company_id and deal_id:
                company_to_deal_pairs.append((company_id, deal_id))

            # Run batch calls
            run_association_batch(
                from_object_type="contacts",
                to_object_type="companies",
                association_pairs=contact_to_company_pairs,
            )
            run_association_batch(
                from_object_type="contacts",
                to_object_type="deals",
                association_pairs=contact_to_deal_pairs,
            )
            run_association_batch(
                from_object_type="companies",
                to_object_type="deals",
                association_pairs=company_to_deal_pairs,
            )

    except Exception as e:
        raise Exception(e)
